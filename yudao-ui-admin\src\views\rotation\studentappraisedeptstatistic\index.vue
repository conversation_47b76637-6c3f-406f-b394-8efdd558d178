<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="70px">
      <el-form-item label="科室类型" prop="deptType">
        <el-select v-model="queryParams.deptType" placeholder="请选择科室类型" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_DEPARTMENT_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="科室名称" prop="rotationDepartmentId">
        <el-select v-model="queryParams.rotationDepartmentId" placeholder="请选择轮转科室" filterable clearable size="small">
          <el-option
              v-for="d in departmentList"
              :key="d.id"
              :label="d.name"
              :value="d.id"
            ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="评价时间" prop="appraiseDate">
        <el-date-picker
          style="width: 100%"
          v-model="queryParams.appraiseDate"
          type="datetimerange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="warning" plain icon="el-icon-download" :loading="exportLoading" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" >
      <el-table-column label="科室名称" prop="rotationDepartmentName" align="center"></el-table-column>
      <el-table-column label="科室类型" prop="deptType" align="center">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_DEPARTMENT_TYPE" :value="scope.row.deptType" />
        </template>
      </el-table-column>
      <el-table-column label="综合得分" prop="score" align="center"></el-table-column>
      <!-- <el-table-column label="排名" prop="rank" align="center"></el-table-column> -->
      <el-table-column label="学员评价数" prop="studentAppraiseCount" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="viewStudentList(scope.row)"
            >{{scope.row.studentAppraiseCount}}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog custom-class="student-appraise-list-dialog" :title="listTitle" :visible="listOpen" width="1000px" v-dialogDrag append-to-body @close="cancelDialog">
      <div class="top">
        <div class="left">
          <div>
            <label>评价对象：</label><span>{{curRow && curRow.rotationDepartmentName}}</span>
          </div>
          <div>
            <label>综合得分：</label><span>{{curRow && curRow.score}}</span>
          </div>
        </div>
        <div class="right">
          <!-- <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
            >导出</el-button> -->
        </div>
      </div>
      <el-table v-loading="loadingStudent" :data="studentList" >
        <el-table-column label="学员姓名" prop="studentNickName" align="center"></el-table-column>
        <el-table-column label="学员类型" prop="studentType" align="center">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType" />
          </template>
        </el-table-column>
        <el-table-column label="轮转科室" prop="rotationDepartmentName" align="center"></el-table-column>
        <el-table-column label="评价得分" prop="score" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleView(scope.row)"
              >{{scope.row.score}}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="评价建议" prop="comments" align="center"></el-table-column>
        <el-table-column label="评价时间" prop="appraiseDate" align="center">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.appraiseDate) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="studentTotal > 0" :total="studentTotal" :page.sync="queryStudentParams.pageNo" :limit.sync="queryStudentParams.pageSize"
                @pagination="getStudentList"/>
    </el-dialog>

    <AppraiseDialog
      v-if="formData"
      :title="title"
      :open="open"
      :data="formData"
      :appraiseSourceId="curRow && curRow.appraiseSourceId"
      :appraiseTargetId="curRow && curRow.appraiseTargetId"
      :disabled="appraiseDisabled"
      @setOpen="setOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import {DICT_TYPE, getDictDatas} from "@/utils/dict";
import AppraiseDialog from "@/views/components/appraiseDialog";
import { getRotationDepartmentSimpleList } from '@/api/system/department'
import { getAppraiseResult } from "@/api/rotation/studentappraiseteacher";
import { getStudentappraisedeptstatistic, getAppraiseStudentList, exportStudentAppraiseDeptStatistic } from "@/api/rotation/studentappraisedeptstatistic";

export default {
  name: "Studentappraisedeptstatistic",
  components: {
    AppraiseDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 50,
        nickname: "",
        deptType: getDictDatas(DICT_TYPE.SYSTEM_DEPARTMENT_TYPE)[0].value,
        studentType: getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)[0].value,
        appraiseDate: [],
        rotationDepartmentId: ''
      },
      formData: null,
      open: false,
      title: '',
      curRow: null,
      appraiseDisabled: false,
      listTitle: '学员评价列表',
      listOpen: false,
      studentTotal: 0,
      studentList: [],
      loadingStudent: true,
      queryStudentParams: {
        pageNo: 1,
        pageSize: 10,
        deptType: '',
        maxAppraiseDate: '',
        minAppraiseDate	: '',
        rotationDepartmentId: '',
        studentType: '',
      },
      exportLoading: false,
      departmentList: []
    };
  },
  created() {
    getRotationDepartmentSimpleList().then(res => this.departmentList = res.data);
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getStudentappraisedeptstatistic(this.queryParams).then(response => {
        const list = response.data.list;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getStudentList() {
      this.loadingStudent = true;
      // 执行查询
      getAppraiseStudentList(this.queryStudentParams).then(response => {
        const list = response.data.list;
        this.studentList = list;
        this.studentTotal = response.data.total;
        this.loadingStudent = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleView(row) {
      const id = row.appraise360ResultId;
      this.appraiseDisabled = true
      getAppraiseResult(id).then(response => {
        this.formData = response.data;
        this.open = true;
        this.title = `查看评价-${row.rotationDepartmentName}`;
      });
    },
    viewStudentList(row) {
      this.curRow = row;
      this.loadingStudent = true;
      this.queryStudentParams = {
        pageNo: 1,
        pageSize: 10,
        deptType: row.deptType,
        maxAppraiseDate: row.maxAppraiseDate,
        minAppraiseDate	: row.minAppraiseDate,
        rotationDepartmentId: row.rotationDepartmentId,
        studentType: row.studentType
      }
      getAppraiseStudentList(this.queryStudentParams).then(response => {
        const list = response.data.list;
        this.studentList = list;
        this.studentTotal = response.data.total;
        this.loadingStudent = false;
        this.listOpen = true;
      });
    },
    cancelDialog() {
      this.listOpen = false;
    },
    setOpen(flag) {
      this.open = flag;
      this.editForm = null
      this.curRow = null
    },
    // 导出统计数据
    handleExport() {
      this.$modal.confirm('是否确认导出学员评价科室统计?').then(() => {
        this.exportLoading = true;
        return exportStudentAppraiseDeptStatistic(this.queryParams);
      }).then(response => {
        this.$download.excel(response, '学员评价科室统计.xlsx');
        this.exportLoading = false;
      }).catch(() => {});
    },
  }
};
</script>
<style lang="scss">
.student-appraise-list-dialog{
  .top{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    margin-top: -20px;

    .left{
      div{
        display: inline-block;
        margin-right: 20px;
      }
    }
    .right{

    }
  }
}
</style>
