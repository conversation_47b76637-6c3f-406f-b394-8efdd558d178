import request from '@/utils/request'

// 创建教学活动
export function createTeachingActive(data) {
  return request({
    url: '/rotation/teacher-teaching-active/create',
    method: 'post',
    data: data
  })
}

// 更新教学活动
export function updateTeachingActive(data) {
  return request({
    url: '/rotation/teacher-teaching-active/update',
    method: 'put',
    data: data
  })
}

// 删除教学活动
export function deleteTeachingActive(id) {
  return request({
    url: '/rotation/teacher-teaching-active/delete?id=' + id,
    method: 'delete'
  })
}

// 获得教学活动
export function getTeachingActive(id) {
  return request({
    url: '/rotation/teacher-teaching-active/get?id=' + id,
    method: 'get'
  })
}

// 获得教学活动分页
export function getTeachingActivePage(query) {
  return request({
    url: '/rotation/teacher-teaching-active/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/teachingActiveDevelopment/index'}
  })
}

// 导出教学活动 Excel
export function exportTeachingActiveExcel(query) {
  return request({
    url: '/rotation/teacher-teaching-active/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得参加学员列表
export function getStudentsList(query) {
  return request({
    url: '/rotation/teacher-teaching-active/list-students',
    method: 'get',
    params: query
  })
}

// 确认参加
export function confirmJoin(data) {
  return request({
    url: '/rotation/teaching-active-student/confirm-join',
    method: 'put',
    data: data
  })
}

// 撤销参加
export function revokeJoin(data) {
  return request({
    url: '/rotation/teaching-active-student/revoke-join',
    method: 'put',
    data: data
  })
}
