import request from "@/utils/request";

// 审核通过
export function examineApprove(data) {
  return request({
    url: "/rotation/intern-case-record-examine/examine-approve",
    method: "post",
    data: data,
  });
}

// 审核不通过
export function examineReject(data) {
  return request({
    url: "/rotation/intern-case-record-examine/examine-reject",
    method: "post",
    data: data,
  });
}

// 获得见习病例书写审核分页
export function getInternCaseRecordExaminePage(query) {
  return request({
    url: "/rotation/intern-case-record-examine/page",
    method: "get",
    params: query,
  });
}
