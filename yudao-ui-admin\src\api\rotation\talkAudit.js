import request from '@/utils/request'

// 获得导师谈话记录待审核分页
export function getAuditStudentPage(query) {
    return request({
      url: '/rotation/mentor-interview-notes/studentPage',
      method: 'get',
      params: query
    })
}

// 获得导师谈话记录待审核分页
export function getToAuditPagePage(query) {
  return request({
    url: '/rotation/mentor-interview-notes/auditPage',
    method: 'get',
    params: query
  })
}

// 获得导师谈话记录某个学生完成情况分页
export function getCompleteStatusPage(query) {
  return request({
    url: '/rotation/mentor-interview-notes/completeStatusPage',
    method: 'get',
    params: query
  })
}

// 更新导师谈话记录
export function auditMentorNotes(data) {
  return request({
    url: '/rotation/mentor-interview-notes/audit',
    method: 'put',
    data: data
  })
}


































