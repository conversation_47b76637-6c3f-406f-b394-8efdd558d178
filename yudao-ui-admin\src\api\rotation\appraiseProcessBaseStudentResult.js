import request from "@/utils/request";

// 获得专业基地评价学员分页
export function getBaseStudentPage(query) {
  return request({
    url: "/rotation/appraise-process-result/baseStudentPage",
    method: "get",
    params: query,
    headers: { component: "rotation/appraiseProcessBaseStudentResult/index" },
  });
}

// 获得过程评价结果项集合
export function getAppraiseForm(id) {
  return request({
    url:
      "/rotation/appraise-process-result/itemList?appraiseProcessResultId=" +
      id,
    method: "get",
  });
}
