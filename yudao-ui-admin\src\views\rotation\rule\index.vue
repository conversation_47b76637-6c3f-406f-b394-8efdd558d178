<template>
  <div class="app-container" v-loading="loading">
    <h2 style="margin-top: 0">{{ schemeInfo.name }}</h2>

    <el-form inline>
      <el-form-item label="学员类型：" style="width: 200px">
        <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="schemeInfo.studentType" />
      </el-form-item>
      <el-form-item label="培训专业：" style="width: 200px">{{ getMajorName(schemeInfo.major) }}</el-form-item>
      <el-form-item label="轮转时长：" style="width: 200px">
        {{ schemeInfo.rotationTime }}
        <dict-tag :type="DICT_TYPE.ROTATION_CYCLE" :value="schemeInfo.rotationCycle" />
      </el-form-item>
      <el-form-item v-if="schemeInfo.publishStatus === 0">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="addGroup">新增</el-button>
      </el-form-item>
    </el-form>

    <el-card class="group-card" v-for="item in groupList" :key="item.id">
      <template slot="header">
        <span>{{ item.name }}</span>
        <span v-if="schemeInfo.publishStatus === 0">
          <el-button type="text" @click="editGroup(item)">编辑</el-button>
          <el-button type="text" @click="deleteGroup(item)">删除</el-button>
        </span>
      </template>
      <el-table :data="item.ruleDepartmentRespVOS" border>
        <el-table-column label="标准科室" prop="standardDepartmentName" width="150px"></el-table-column>
        <el-table-column prop="rotationTime" width="150px">
          <span slot="header">轮转时长(<dict-tag :type="DICT_TYPE.ROTATION_CYCLE" :value="schemeInfo.rotationCycle" />)</span>
        </el-table-column>
        <el-table-column label="备注" prop="remark"></el-table-column>
        <el-table-column label="操作" :width="schemeInfo.publishStatus === 0 ? '220px' : '180px'">
          <template v-slot="scope">
            <el-button size="mini" type="text" icon="el-icon-notebook-2" @click="handleRequire(scope.row)">轮转要求</el-button>
            <el-button size="mini" type="text" icon="el-icon-guide" @click="handleStandard(scope.row)">轮转规范</el-button>
            <el-button v-if="schemeInfo.publishStatus === 0" size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
        <div class="table-append" slot="append">
          <div><span>备注</span>{{ item.remark }}</div>
          <div><span>合计</span>轮转时长：{{ item.rotationTime }}</div>
        </div>
      </el-table>
    </el-card>
    <el-empty v-if="groupList.length === 0" description="暂未设置轮转规则"></el-empty>

    <!-- 配置方案标准科室 -->
    <el-dialog title="配置方案标准科室" :visible.sync="groupVisible" width="800px" v-dialogDrag append-to-body>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input v-model="departmentFilterKey" placeholder="输入科室名称查询" clearable></el-input>
          <el-checkbox-group class="department-checkbox-group" v-model="selectedDepartments">
            <el-checkbox
              v-for="item in departmentList.filter(item => item.name.indexOf(departmentFilterKey) > -1)"
              :key="item.id"
              :label="item.id"
              @change="handleDepartmentChange($event, item)"
            >{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </el-col>
        <el-col :span="18">
          <el-form class="rotation-rule-group-form" :model="groupForm" :rules="groupRules" label-width="110px" inline-message ref="groupForm">
            <el-form-item label="科室名称" style="background: #fcfcfc">
              <div class="small-col">轮转时长(<dict-tag :type="DICT_TYPE.ROTATION_CYCLE" :value="schemeInfo.rotationCycle" />)</div>
              <div class="larger-col">备注</div>
            </el-form-item>
            <vue-draggable v-model="groupForm.ruleDepartmentRespVOS">
              <el-form-item
                v-for="item in groupForm.ruleDepartmentRespVOS"
                :key="item.standardDepartmentId"
                :label="item.standardDepartmentName"
              >
                <div class="small-col">
                  <el-input-number
                    v-model="item.rotationTime"
                    controls-position="right"
                    style="width: 100%"
                    :step="schemeInfo.rotationCycle === '1' ? 0.5 : 1"
                    :min="schemeInfo.rotationCycle === '1' ? 0.5 : 1"
                    step-strictly
                  ></el-input-number>
                </div>
                <div class="larger-col">
                  <el-input v-model="item.remark" style="width: 100%"></el-input>
                </div>
              </el-form-item>
            </vue-draggable>
            <el-form-item label-width="-1px" v-if="groupForm.ruleDepartmentRespVOS.length === 0">
              <div style="text-align: center">请选择轮转科室</div>
            </el-form-item>
            <el-form-item label="组合信息" style="background: #fcfcfc"></el-form-item>
            <el-form-item class="padding-form-content" label="组合名称" prop="name">
              <el-input v-model="groupForm.name"></el-input>
            </el-form-item>
            <el-form-item class="padding-form-content" label="排序显示" prop="sort">
              <el-input-number v-model="groupForm.sort" controls-position="right" style="width: 111px"></el-input-number>
            </el-form-item>
            <el-form-item class="padding-form-content" label="组合轮转时长" prop="rotationTime">
              <el-input-number
                v-model="groupForm.rotationTime"
                controls-position="right"
                style="width: 111px"
                :step="schemeInfo.rotationCycle === '1' ? 0.5 : 1"
                :min="schemeInfo.rotationCycle === '1' ? 0.5 : 1"
                step-strictly
              ></el-input-number>
            </el-form-item>
            <el-form-item class="padding-form-content" label="组合备注" prop="remark">
              <el-input type="textarea" v-model="groupForm.remark"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGroup">确 定</el-button>
        <el-button @click="cancelGroup">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 轮转要求 -->
    <el-dialog
      v-dialogDrag
      title="配置轮转要求"
      width="800px"
      append-to-body
      :visible.sync="requireVisible"
    >
      <el-row style="margin-bottom: 20px; font-size: 16px;">
        <el-col :span="12">方案名称: {{schemeInfo.name}}</el-col>
        <el-col :span="12">标准科室: {{configDepartment && configDepartment.standardDepartmentName}}</el-col>
      </el-row>
      <el-row style="margin-bottom: 20px;">
        <el-col :span="6" v-for="(item, index) in activeItems" :key="index">
          {{ item.activeItemName }}
          <el-input-number
            style="width: 90px"
            size="small"
            controls-position="right"
            v-model="item.cases"
            :min="0"
            @change="saveActiveItemCases(item)"
          /> 次
        </el-col>
        <el-col :span="6" v-if="itemsGraduationRadio">
          <el-tooltip content="必须完成对应比例轮转数据登记要求后才能提交出科申请" placement="top">
            <i class="el-icon-info"></i>
          </el-tooltip>完成比例≥
          <el-input-number
            style="width: 90px"
            size="small"
            controls-position="right"
            v-model="itemsGraduationRadio.rotationItemsGraduationRadio"
            :min="0"
            :max="100"
            :step="1"
            step-strictly
            @change="saveItemsGraduationRadio"
          /> %
        </el-col>
      </el-row>
      <el-tabs v-model="curRotationItem" @tab-click="getSubItemList">
        <el-tab-pane v-for="item in rotationItems" :key="item.value" :label="item.label" :name="item.value">
          <div style="display: flex; justify-content: flex-end; margin-bottom: 10px;">
            <el-button type="primary" size="small" @click="addSubItem" v-if="hasSubItem(item.value) || !subItemList.length">新增</el-button>
          </div>
          <el-table :data="subItemList" style="width: 760px">
            <el-table-column label="子项名称" prop="subitemName" v-if="hasSubItem(item.value)"></el-table-column>
            <el-table-column label="要求例数" prop="cases"></el-table-column>
            <el-table-column label="要求说明" prop="description"></el-table-column>
            <el-table-column label="操作">
              <template v-slot="scope">
                <el-button type="text" icon="el-icon-edit" @click="editSubItem(scope.row)">编辑</el-button>
                <el-button type="text" icon="el-icon-delete" @click="deleteSubItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" style="text-align: center">
        <el-button @click="requireVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 新增编辑子项 -->
    <el-dialog :title="`${subItemForm.id ? '编辑' : '新增'}${hasSubItem(curRotationItem) ? '子项' : ''}要求`" width="500px" :visible.sync="subItemVisible" v-dialog-drag append-to-body>
      <el-form :model="subItemForm" :rules="subItemRule" label-width="80px" ref="subItemForm">
        <el-form-item label="子项名称" prop="subitemName" v-if="hasSubItem(curRotationItem)">
          <el-input v-model="subItemForm.subitemName"></el-input>
        </el-form-item>
        <el-form-item label="要求例数" prop="cases">
          <el-input-number controls-position="right" v-model="subItemForm.cases" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="要求说明" prop="description">
          <el-input type="textarea" v-model="subItemForm.description"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="saveSubItem">确定</el-button>
        <el-button @click="subItemVisible = false">取消</el-button>
      </span>
    </el-dialog>

    <!-- 轮转规范 -->
    <el-dialog title="轮转规范" :visible.sync="standardVisible" width="800px" v-dialog-drag append-to-body>
      <editor v-model="standardForm.rotationStandard" :min-height="192" />
      <span slot="footer">
        <el-button type="primary" @click="saveStandard">确定</el-button>
        <el-button @click="standardVisible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getStandardScheme } from '@/api/rotation/standardScheme'
import { getStandardDepartmentSimpleList } from '@/api/system/standardDepartment'
import {
  getRuleList,
  createRule,
  updateRule,
  getRuleActiveList,
  getRuleRotationSubItemList,
  saveRuleActive,
  updateRuleRotationSubItem,
  createRuleRotationSubItem,
  deleteRuleRotationSubItem,
  getRuleRotationStandard,
  updateRuleRotationStandard,
  deleteRule,
  deleteRuleDepartment,
  getRuleRotationItems,
  getItemsGraduationRadio,
  updateItemsGraduationRadio,
} from '@/api/rotation/rule'
import { getSimpleMajorList } from '@/api/system/major'
import VueDraggable from 'vuedraggable'
import Editor from '@/components/Editor';

export default {
  name: "Rule",
  components: {
    VueDraggable,
    Editor
  },
  data() {
    return {
      // 标准方案
      schemeId: this.$route.query.id,
      schemeInfo: {},
      majorList: [],
      // 遮罩层
      loading: true,
      // 方案规则组合列表
      groupList: [],
      // 方案新增编辑
      groupVisible: false,
      departmentFilterKey: "",
      departmentList: [],
      selectedDepartments: [],
      groupForm: {
        ruleDepartmentRespVOS: [],
        name: "",
        sort: 0,
        rotationTime: 0,
        remark: "",
      },
      groupRules: {
        ruleDepartmentRespVOS: [{ required: true, message: "科室至少选一个", trigger: "change" }],
        name: [{ required: true, message: "组合名称必填", trigger: "blur" }],
        sort: [{ required: true, message: "排序必填", trigger: "blur" }],
        rotationTime: [{ required: true, message: "组合轮转时长必填", trigger: "blur" }],
      },
      // 配置轮转要求
      requireVisible: false,
      configDepartment: null,
      activeItems: [],
      itemsGraduationRadio: null,
      rotationItems: [],
      curRotationItem: null,
      subItemList: [],
      subItemVisible: false,
      subItemForm: {
        id: "",
        subitemName: "",
        cases: 0,
        description: "",
      },
      subItemRule: {
        subitemName: [{ required: true, message: "子项名称必填", trigger: "blur" }],
        cases: [{ required: true, message: "要求例数必填", trigger: "change" }]
      },
      // 轮转规范
      standardVisible: false,
      standardForm: {
        id: "",
        rotationStandard: ""
      },
    };
  },
  created() {
    this.schemeId = this.$route.query.id;
    this.getSchemeInfo();
    this.getList();
    this.getDepartmentList();
    this.getRotationItems();
  },
  methods: {
    /** 查询标准方案信息 */
    getSchemeInfo() {
      getStandardScheme(this.schemeId).then(res => {
        this.schemeInfo = res.data
        getSimpleMajorList({ studentType: res.data.studentType }).then(res => this.majorList = res.data)
      })
    },
    getMajorName(major) {
      return this.majorList.find(item => item.code === major)?.name
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getRuleList({ standardSchemeId: this.schemeId }).then(response => {
        this.groupList = response.data;
        this.loading = false;
      });
    },
    /** 新增配置方案 */
    addGroup() {
      this.groupVisible = true;
      this.groupForm = {
        ruleDepartmentRespVOS: [],
        name: "",
        sort: 0,
        rotationTime: 0,
        remark: "",
      };
      this.selectedDepartments = [];
    },
    /** 获取科室列表 */
    getDepartmentList() {
      getStandardDepartmentSimpleList(0).then(res => {
        this.departmentList = res.data;
      })
    },
    /** 左侧科室选中状态改变 */
    handleDepartmentChange(selected, department) {
      if (selected) {
        this.groupForm.ruleDepartmentRespVOS.push({
          standardDepartmentId: department.id,
          standardDepartmentName: department.name,
          rotationTime: 0,
          remark: "",
          ruleId: "",
          sort: 0,
          standardSchemeId: this.schemeId,
        })
      } else {
        const index = this.groupForm.ruleDepartmentRespVOS
          .findIndex(item => item.standardDepartmentId === department.id);
        if (index > -1) {
          this.groupForm.ruleDepartmentRespVOS.splice(index, 1);
        }
      }
    },
    /** 方案标准科室组 */
    submitGroup() {
      this.$refs.groupForm.validate((valid) => {
        if (valid) {
          const isUpdate = this.groupForm.id;
          const saveFn = isUpdate ? updateRule : createRule;
          saveFn({ ...this.groupForm, standardSchemeId: this.schemeId }).then(() => {
            this.getList();
            this.getSchemeInfo();
            this.cancelGroup();
          })
        }
      })
    },
    cancelGroup() {
      this.groupVisible = false;
    },
    editGroup(item) {
      this.groupVisible = true;
      this.groupForm = { ...item };
      this.selectedDepartments = item.ruleDepartmentRespVOS.map(item => item.standardDepartmentId);
    },
    deleteGroup(item) {
      this.$modal.confirm('是否确认删除轮转规则为"' + item.name + '"的数据项?').then(function() {
        return deleteRule(item.id);
      }).then(() => {
        this.getList();
        this.getSchemeInfo();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 轮转要求 */
    handleRequire(item) {
      this.configDepartment = item;
      this.getActiveItems();
      this.getItemsGraduationRadio();
      this.curRotationItem = this.rotationItems[0]?.value;
      this.getSubItemList();
      this.requireVisible = true;
    },
    getRotationItems() {
      getRuleRotationItems(this.schemeId).then(res => {
        this.rotationItems = res.data;
      });
    },
    getActiveItems() {
      getRuleActiveList(this.configDepartment.id).then((res) => {
        this.activeItems = res.data;
      })
    },
    saveActiveItemCases(item) {
      saveRuleActive(item).then(() => {
        this.$message.success(`${item.activeItemName}次数更新成功`);
      });
    },
    getItemsGraduationRadio() {
      getItemsGraduationRadio(this.configDepartment.id).then(res => {
        this.itemsGraduationRadio = res.data;
      });
    },
    saveItemsGraduationRadio() {
      updateItemsGraduationRadio(this.itemsGraduationRadio).then(res => {
        this.$message.success("完成比例设置成功");
      });
    },
    getSubItemList() {
      getRuleRotationSubItemList(this.configDepartment.id, this.curRotationItem).then((res) => {
        this.subItemList = res.data.list;
      })
    },
    addSubItem() {
      this.subItemForm = {
        id: "",
        subitemName: "",
        cases: 0,
        description: "",
      };
      this.subItemVisible = true;
    },
    saveSubItem() {
      const saveFn = this.subItemForm.id ? updateRuleRotationSubItem : createRuleRotationSubItem;
      const data = {
        ...this.subItemForm,
        standardSchemeId: this.schemeId,
        ruleDepartmentId: this.configDepartment.id,
        rotaionItem: this.curRotationItem,
      };
      this.$refs.subItemForm.validate(valid => {
        if (valid) {
          saveFn(data).then(() => {
            this.$message.success("要求保存成功");
            this.getSubItemList();
            this.subItemVisible = false;
          })
        }
      });
    },
    editSubItem(item) {
      this.subItemForm = { ...item };
      this.subItemVisible = true;
    },
    deleteSubItem(item) {
      this.$modal.confirm('是否确认删除该条数据项?').then(function() {
        return deleteRuleRotationSubItem(item.id);
      }).then(() => {
        this.getSubItemList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    hasSubItem(itemId) {
      return ["2", "3", "4"].includes(itemId);
    },
    /** 轮转规范 */
    handleStandard(item) {
      getRuleRotationStandard(item.id).then(res => {
        this.standardForm = res.data;
        this.standardVisible = true;
      });
    },
    saveStandard() {
      updateRuleRotationStandard(this.standardForm).then(() => {
        this.$message.success('保存轮转规范成功');
        this.standardVisible = false;
      });
    },
    /** 删除方案标准科室 */
    handleDelete(item) {
      this.$modal.confirm('是否确认删除标准方案为"' + item.standardDepartmentName + '"的数据项?').then(function() {
        return deleteRuleDepartment(item.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  }
};
</script>

<style lang="scss">
.rotation-rule-group-form {
  border-bottom: 1px solid #eee;

  .el-form-item {
    margin-bottom: 0;
    border: 1px solid #eee;
    border-bottom: none;
    background: #fff;
  }

  .el-form-item__content {
    border-left: 1px solid #eee;
  }

  .padding-form-content .el-form-item__content {
    padding: 5px 10px;
  }

  .small-col {
    display: inline-block;
    width: 30%;
    padding: 5px 10px;
    border-right: 1px solid #eee;
  }

  .larger-col {
    display: inline-block;
    width: 70%;
    padding: 5px 10px;
  }
}

.group-card {
  margin-bottom: 30px;

  .el-card__header {
    padding: 7px 15px;
    font-size: 16px;
    line-height: 36px;
    display: flex;
    justify-content: space-between;
  }
}

.table-append {
  div {
    width: 100%;
    line-height: 23px;
    border-bottom: 1px solid #dfe6ec;

    span {
      display: inline-block;
      width: 150px;
      border-right: 1px solid #dfe6ec;
      padding: 10px 0 10px 10px;
      margin-right: 10px;
    }
  }
}

.department-checkbox-group {
  border: 1px solid #DCDFE6;
  margin-top: 10px;
  height: 282px;
  padding-top: 4px;
  overflow-y: auto;

  .el-checkbox {
    width: 100%;
    margin-right: 0;
    padding: 5px 10px;
  }
}
</style>
