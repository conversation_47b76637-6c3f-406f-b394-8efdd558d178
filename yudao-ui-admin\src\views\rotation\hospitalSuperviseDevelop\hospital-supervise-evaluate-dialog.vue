<template>
  <el-dialog
    title="督导评价"
    :visible="visible"
    width="900px"
    @close="handleCancel"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="(item, index) in evaluateForm.resultFormCreateReqVOS || []"
        :key="index"
        :name="index.toString()"
        :label="item.superviseFormName"
        :id="'hospital-supervise-form-' + index"
      >
        <el-form inline label-width="84px" class="export-item">
          <el-form-item class="half-item" label="督导类型：">
            {{
              getDictDataLabel(
                DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE,
                superviseObject.formType
              )
            }}
          </el-form-item>
          <el-form-item class="half-item" label="督导对象：">
            {{ superviseObject.developObjectName }}
          </el-form-item>
          <el-form-item class="mb5" label="督导时间：">
            {{ superviseObject.beginEndTime }}
          </el-form-item>
        </el-form>

        <p class="export-item">
          注意：<i class="el-icon-star-on star-icon"></i>代表该指标为核心指标
        </p>

        <div
          class="form-item-wrapper export-item"
          v-if="item.resultExts.length > 0"
        >
          <div
            class="form-item"
            v-for="(ext, index) in item.resultExts"
            :key="index"
          >
            <span class="form-label">{{ ext.superviseFormExtName }}：</span>
            <span v-if="isView">{{ ext.superviseFormExtVal }}</span>
            <el-input
              v-else
              v-model="ext.superviseFormExtVal"
              placeholder="请输入"
            ></el-input>
          </div>
        </div>
        <div
          class="mb20 export-item"
          v-for="(resultItem, i) in item.resultItems"
          :key="i"
        >
          <header class="table-header">
            <span class="mr20"
              >评分项目：{{ resultItem.superviseFormItemName }}</span
            >
            <span class="mr20"
              >总分：{{ resultItem.superviseFormItemScore }}</span
            >
            <span>督导得分：{{ resultItem.score }}</span>
          </header>
          <el-table :data="resultItem.resultFormSubItems" border>
            <el-table-column label="评分要素" prop="superviseFormSubItemName">
              <template v-slot="scope">
                <i
                  class="el-icon-star-on star-icon"
                  v-if="scope.row.isCore"
                ></i>
                {{ scope.row.superviseFormSubItemName }}
              </template>
            </el-table-column>
            <el-table-column
              label="分值"
              prop="superviseFormSubItemScore"
              width="100px"
            ></el-table-column>
            <el-table-column label="得分" width="150px">
              <template v-slot="scope">
                <el-input-number
                  v-model="scope.row.score"
                  class="w-100"
                  controls-position="right"
                  :min="0"
                  :max="scope.row.superviseFormSubItemScore"
                  :step="0.1"
                  :disabled="isView"
                  @change="handleScoreChange(resultItem, item)"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="备注" width="150px">
              <template v-slot="scope">
                <el-input
                  v-model="scope.row.remark"
                  class="w-100"
                  :disabled="isView"
                ></el-input>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="float-right mb20 export-item">
          <span style="margin-right: 20px"
            >督导专家：{{ superviseObject.superviseNickname }}</span
          >
          督导总分：{{ item.score }}
        </div>

        <el-form label-width="100px" class="export-item">
          <el-form-item label="督导意见：" required>
            <el-input
              v-model="item.opinion"
              class="w-100"
              type="textarea"
              :rows="3"
              :disabled="isView"
            ></el-input>
          </el-form-item>
          <el-form-item label="照片上传：" required>
            <imageUpload
              v-model="item.pictures"
              :limit="9999"
              :disabled="isView"
            />
            <span
              v-if="isView && (item.pictures || []).length === 0"
              style="opacity: 0.6"
              >未上传</span
            >
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <span slot="footer">
      <el-button v-if="!isView" type="primary" @click="handleSave"
        >确定</el-button
      >
      <el-button v-if="!isView" @click="handleCancel">取消</el-button>
      <el-button
        v-if="isView"
        type="primary"
        :loading="exportloading"
        @click="handleDown()"
        >导出</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import ImageUpload from "@/components/ImageUpload";
import {
  getSuperviseFormsList,
  createSuperviseResult,
  getSuperviseResult,
} from "@/api/rotation/teachingActiveSupervise";
import { exportPDF } from "@/utils/exportUtils";

export default {
  name: "hospital-supervise-evaluate-dialog",
  components: { ImageUpload },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    superviseObject: {
      type: Object,
      required: true,
    },
    // 传表单id时为填写督导评价，不传时为查看督导评价
    superviseFormIds: Array,
  },
  data() {
    return {
      activeName: "0",
      evaluateForm: {},
      exportloading: false,
    };
  },
  computed: {
    isView() {
      return !this.superviseFormIds;
    },
  },
  methods: {
    /** 获得表单数据 */
    getForms() {
      getSuperviseFormsList({
        superviseFormIds: this.superviseFormIds.join(","),
        superviseObjectId: this.superviseObject.hospitalTrainingId,
      }).then((res) => {
        const data = res.data;
        data.costTime = 0;
        data.resultFormCreateReqVOS.forEach((item) => {
          item.costTime = 0;
        });
        this.evaluateForm = data;
      });
    },
    getResults() {
      getSuperviseResult({
        id: this.superviseObject.superviseResultId,
        isComprehensive: this.superviseObject.isComprehensive,
      }).then((res) => {
        this.evaluateForm = res.data;
      });
    },
    /** 指标打分计算项目总分和督导总分 */
    handleScoreChange(resultTarget, target) {
      let resultItemScore = 0,
        totalScore = 0;
      resultTarget.resultFormSubItems.forEach((subItem) => {
        resultItemScore += subItem.score;
      });
      resultTarget.score = resultItemScore;
      target.resultItems.forEach((item) => {
        totalScore += item.score;
      });
      target.score = totalScore;
    },
    /** 提交督导评价 */
    handleSave() {
      // console.log('this.auditForm===', this.auditForm)
      const { resultFormCreateReqVOS = [] } = this.evaluateForm;

      let opinionFlag = true;
      let picturesFlag = true;
      let superviseFormName = "";
      resultFormCreateReqVOS.forEach((item) => {
        if (!item.opinion) {
          opinionFlag = false;
          superviseFormName = item.superviseFormName;
        }
        if (!item.pictures) {
          picturesFlag = false;
          superviseFormName = item.superviseFormName;
        }
      });
      if (!opinionFlag) {
        return this.$modal.msgWarning(`请输入${superviseFormName}的督导意见!`);
      }
      if (!picturesFlag) {
        return this.$modal.msgWarning(`请上传${superviseFormName}的照片!`);
      }

      createSuperviseResult(this.evaluateForm).then(() => {
        this.$message.success("操作成功");
        this.$emit("saved");
        this.handleCancel();
      });
    },
    handleCancel() {
      this.evaluateForm = {};
      this.$emit("update:visible", false);
    },

    handleDown() {
      this.exportloading = true;
      // console.log("id====", `hospital-supervise-form-${this.activeName}`);
      exportPDF(
        `hospital-supervise-form-${this.activeName}`,
        `督导评价-${
          this.evaluateForm.resultFormCreateReqVOS[this.activeName]
            .superviseFormName
        }`,
        () => {
          this.exportloading = false;
        }
      );
    },
  },
  watch: {
    visible(val) {
      if (!val) return;

      if (this.isView) {
        this.getResults();
      } else {
        this.getForms();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.half-item {
  width: 50%;
  margin-right: 0 !important;
  margin-bottom: 5px;
}

.star-icon {
  font-size: 18px;
  color: orange;
}

.table-header {
  padding: 10px;
  border: 1px solid #dfe6ec;
  background: #f8f8f9;
  position: relative;
  top: 1px;
}
</style>
