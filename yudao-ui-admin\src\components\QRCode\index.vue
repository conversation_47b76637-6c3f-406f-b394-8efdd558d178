<template>
  <div ref="QRCode"></div>
</template>

<script>
import QRCode from "qrcodejs2";

export default {
  name: 'QRCode',
  props: {
    // 二维码包含的信息
    text: {
      type: String,
      default: "",
    },
    // 二维码的宽度
    width: {
      type: Number,
      default: 256,
    },
    // 二维码的高度
    height: {
      type: Number,
      default: 256,
    },
    // 二维码颜色
    colorDark: {
      type: String,
      default: "#000000",
    },
    // 二维码背景色
    colorLight: {
      type: String,
      default: "#ffffff",
    },
    // 模糊程度，由低到高
    correctLevel: {
      validator(value) {
        return ["M", "L", "H", "Q"].includes(value);
      },
      default: "H",
    },
  },
  computed: {
    options() {
      return {
        text: this.text,
        width: this.width,
        height: this.height,
        colorDark: this.colorDark,
        colorLight: this.colorLight,
        correctLevel: QRCode.CorrectLevel[this.correctLevel],
      };
    }
  },
  methods: {
    instanceQRCode() {
      new QRCode(this.$refs.QRCode, this.options);
    },
    clear() {
      this.$refs.QRCode.innerHTML = "";
    },
  },
  mounted() {
    this.instanceQRCode();
  },
  watch: {
    options() {
      this.clear();
      this.instanceQRCode();
    }
  },
}
</script>

<style scoped>

</style>
