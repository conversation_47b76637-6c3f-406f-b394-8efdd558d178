import request from "@/utils/request";

// 获得招录住培信息审核分页
export function getRpregistrationEnrollExamPage(query) {
  return request({
    url: "/recruitment/rpregistration-enroll-examine/page",
    method: "get",
    params: query,
  });
}

// 获得通知书发放
export function getNoticeContent(query) {
  return request({
    url: "/recruitment/rpregistration-enroll-examine/get-notice-content",
    method: "get",
    params: query,
  });
}

// 提交通知书
export function sendNotice(data) {
  return request({
    url: "/recruitment/rpregistration-enroll-examine/send-notice",
    method: "put",
    data: data,
  });
}

// 审核
export function auditRpregistrationEnrollExam(data) {
  return request({
    url: "/recruitment/rpregistration-enroll-examine/examine-no-enroll",
    method: "put",
    data: data,
  });
}

// 修改面试成绩
export function updateInterviewResult(data) {
  return request({
    url: "/recruitment/rpregistration-enroll-examine/update-interview-result",
    method: "put",
    data: data,
  });
}

// 修改笔试成绩
export function updateWrittenResult(data) {
  return request({
    url: "/recruitment/rpregistration-enroll-examine/update-written-result",
    method: "put",
    data: data,
  });
}
