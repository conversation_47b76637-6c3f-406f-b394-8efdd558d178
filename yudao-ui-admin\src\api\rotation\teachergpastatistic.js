import request from '@/utils/request'

// 获得统计分页
export function getTeachergpastatisticPage(query) {
  return request({
    url: '/rotation/teachergpastatistic/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/teachingActiveDevelopment/index'}
  })
}

// 导出 Excel
export function exportTeachergpastatistic(query) {
  return request({
    url: '/rotation/teachergpastatistic/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

