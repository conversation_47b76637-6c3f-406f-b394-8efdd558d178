<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="推送用户" prop="touser">
        <el-input v-model="queryParams.touser" placeholder="请输入推送用户" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="消息模板编号" prop="templateId">
        <el-input v-model="queryParams.templateId" placeholder="请输入消息模板编号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="消息体" prop="data">
        <el-input v-model="queryParams.data" placeholder="请输入消息体" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="返回码" prop="errcode">
        <el-input v-model="queryParams.errcode" placeholder="请输入返回码" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="返回消息" prop="errmsg">
        <el-input v-model="queryParams.errmsg" placeholder="请输入返回消息" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="业务" prop="biz">
        <el-input v-model="queryParams.biz" placeholder="请输入业务" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['system:wx-msg-log:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['system:wx-msg-log:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="推送用户" align="center" prop="touser" />
      <el-table-column label="消息模板编号" align="center" prop="templateId" />
      <el-table-column label="消息体" align="center" prop="data" />
      <el-table-column label="返回码" align="center" prop="errcode" />
      <el-table-column label="返回消息" align="center" prop="errmsg" />
      <el-table-column label="业务" align="center" prop="biz" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['system:wx-msg-log:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['system:wx-msg-log:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="推送用户" prop="touser">
          <el-input v-model="form.touser" placeholder="请输入推送用户" />
        </el-form-item>
        <el-form-item label="消息模板编号" prop="templateId">
          <el-input v-model="form.templateId" placeholder="请输入消息模板编号" />
        </el-form-item>
        <el-form-item label="消息体" prop="data">
          <el-input v-model="form.data" placeholder="请输入消息体" />
        </el-form-item>
        <el-form-item label="返回码" prop="errcode">
          <el-input v-model="form.errcode" placeholder="请输入返回码" />
        </el-form-item>
        <el-form-item label="返回消息" prop="errmsg">
          <el-input v-model="form.errmsg" placeholder="请输入返回消息" />
        </el-form-item>
        <el-form-item label="业务" prop="biz">
          <el-input v-model="form.biz" placeholder="请输入业务" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createWxMsgLog, updateWxMsgLog, deleteWxMsgLog, getWxMsgLog, getWxMsgLogPage, exportWxMsgLogExcel } from "@/api/system/wxMsgLog";

export default {
  name: "WxMsgLog",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户微信消息日志列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        touser: null,
        templateId: null,
        data: null,
        errcode: null,
        errmsg: null,
        biz: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        touser: [{ required: true, message: "推送用户不能为空", trigger: "blur" }],
        templateId: [{ required: true, message: "消息模板编号不能为空", trigger: "blur" }],
        data: [{ required: true, message: "消息体不能为空", trigger: "blur" }],
        errcode: [{ required: true, message: "返回码不能为空", trigger: "blur" }],
        errmsg: [{ required: true, message: "返回消息不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getWxMsgLogPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        touser: undefined,
        templateId: undefined,
        data: undefined,
        errcode: undefined,
        errmsg: undefined,
        biz: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户微信消息日志";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getWxMsgLog(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户微信消息日志";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateWxMsgLog(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createWxMsgLog(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除用户微信消息日志编号为"' + id + '"的数据项?').then(function() {
          return deleteWxMsgLog(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有用户微信消息日志数据项?').then(() => {
          this.exportLoading = true;
          return exportWxMsgLogExcel(params);
        }).then(response => {
          this.$download.excel(response, '用户微信消息日志.xls');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
