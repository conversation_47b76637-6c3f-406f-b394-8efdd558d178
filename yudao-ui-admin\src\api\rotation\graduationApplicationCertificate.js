import request from '@/utils/request';
import { Base64 } from "js-base64";
import { getAccessToken } from '@/utils/auth';

// 获得结业申请条件和学员数据
export function getCertificatePage(query) {
  return request({
    url: '/rotation/graduation-certificate/page',
    method: 'get',
    params: query
  })
}

export function createCertificate(data) {
    return request({
      url: '/rotation/graduation-certificate/certificate',
      method: 'post',
      data: data
    })
}

export function batchCreateCertificate(ids) {
  return request({
    url: `/rotation/graduation-certificate/batch-certificate?ids=${ids}`,
    method: 'post',
    data: {}
  })
}

export function updateCertificate(data) {
  return request({
    url: '/rotation/graduation-certificate/update',
    method: 'put',
    data: data
  })
}

// 获得结业证书
export function getGraduationCertificate(id) {
  return request({
    url: '/rotation/graduation-certificate/get?id=' + id,
    method: 'get'
  })
}

// 下载用户导入模板
export function importTemplate() {
  return request({
    url: '/rotation/graduation-certificate/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

export function getTemplateByType(query) {
  return request({
    url: '/system/template/get-by-type',
    method: 'get',
    params: query
  })
}


/**
 * 数据开放服务： kkFile预览证书
 * @param id 编号
 * @param objectId 结业申请id或者结业证书id
 * @param name 文件名称
 * @returns {string}
 */
export const previewDocTemplateUrl = (id, objectId, name) => {
  const baseUrl = process.env.VUE_APP_KKFILE_API;
  const previewUrl = `${baseUrl}/kkfileview/onlinePreview?url=`;
  const url = `${baseUrl}/admin-api/system/template/get-doc-content?id=${id}&objectId=${
    objectId}&fullfilename=${name}.docx&token=${getAccessToken()}`;
  console.log(url);
  return previewUrl + encodeURIComponent(Base64.encode(url));
};

