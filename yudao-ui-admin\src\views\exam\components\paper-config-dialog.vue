<template>
  <el-dialog title="试卷配置" :visible.sync="open" width="800px" v-dialogDrag append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item class="half-item" label="开始时间" prop="beginTime">
        <el-date-picker style="width: 100%" v-model="form.beginTime" type="datetime" value-format="timestamp" placeholder="选择开始时间" />
      </el-form-item>
      <el-form-item class="half-item padding-left" label="结束时间" prop="endTime">
        <el-date-picker style="width: 100%" v-model="form.endTime" type="datetime" value-format="timestamp" placeholder="选择结束时间" />
      </el-form-item>
      <el-form-item label="限制次数" prop="limitCount">
        <el-input-number v-model="form.limitCount" placeholder="请输入次数" :min="0" controls-position="right" />
        <el-tooltip class="ml10" content="不配置或配置为0，则不限制次数" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
      </el-form-item>
      <el-form-item class="half-item" label="作答后显示成绩" label-width="120px" prop="isShowScoreAfterAnswer">
        <el-switch v-model="form.isShowScoreAfterAnswer" @change="handleShowScoreChange"></el-switch>
      </el-form-item>
      <el-form-item class="half-item" label="允许试卷查看" label-width="120px" prop="isAllowView">
        <el-switch v-model="form.isAllowView" @change="handleAllowViewChange"></el-switch>
      </el-form-item>
      <el-form-item label="试卷开放设置" label-width="100px">
        <el-checkbox-group
          :value="form.openingSettings ? form.openingSettings.split(',') : []"
          @input="val => form.openingSettings = val.join(',')"
        >
          <el-checkbox label="1">WEB端</el-checkbox>
          <el-checkbox label="2">手机端小程序</el-checkbox>
          <el-checkbox label="3">电脑端小程序</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="开启考前认证" label-width="100px">
        <el-radio-group v-model="form.isAuthentication">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="开启防作弊" label-width="100px">
        <el-radio-group v-model="form.isPreventionCheat">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label-width="20px" v-if="form.isPreventionCheat">
        <div class="prevention-cheat-config">
          当系统检测到小程序切屏
          <el-input-number v-model="form.alarmScreenNum" :min="1" :step="1"
                           step-strictly controls-position="right"></el-input-number>
          次，提示内容
          <el-input v-model="form.alarmContent" placeholder="请在此处输入告警内容"></el-input>
        </div>
      </el-form-item>
      <el-form-item label-width="20px" v-if="form.isPreventionCheat">
        <div class="prevention-cheat-config">
          当系统检测到小程序切屏
          <el-input-number v-model="form.lockScreenNum" :min="1" :step="1"
                           step-strictly controls-position="right"></el-input-number>
          次，锁定屏幕并提示
          <el-input v-model="form.lockHint" placeholder="请在此处输入锁定提示"></el-input>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="open = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getPaperConfig, savePaperConfig } from "@/api/exam/paperConfig";

export default {
  name: 'paper-config-dialog',
  data() {
    return {
      open: false,
      form: {
        beginTime: undefined,
        endTime: undefined,
        limitCount: undefined,
        isShowScoreAfterAnswer: undefined,
        isAllowView: undefined,
        openingSettings: undefined,
        isAuthentication: false,
        isPreventionCheat: false,
        alarmScreenNum: undefined,
        alarmContent: undefined,
        lockScreenNum: undefined,
        lockHint: undefined,
      },
      rules: {},
    }
  },
  methods: {
    handleConfig(id) {
      getPaperConfig(id).then(res => {
        this.form = res.data;
        this.open = true;
      });
    },
    /** 改变作答后显示成绩状态 */
    handleShowScoreChange(val) {
      if (!val) {
        this.form.isAllowView = false;
      }
    },
    /** 改变允许试卷查看状态 */
    handleAllowViewChange(val) {
      if (val) {
        this.form.isShowScoreAfterAnswer = true;
      }
    },
    handleSubmit() {
      savePaperConfig(this.form).then(() => {
        this.$message.success("配置保存成功");
        this.$emit("saved");
        this.open = false;
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.half-item {
  display: inline-block;
  width: 50%;
  padding-right: 20px;
}

.half-item.padding-left {
  padding-right: 0;
  padding-left: 20px;
}

.prevention-cheat-config {
  display: flex;

  ::v-deep > .el-input-number {
    width: 120px;
    margin: 0 5px;
  }

  ::v-deep > .el-input {
    width: 300px;
    margin: 0 5px;
  }
}
</style>
