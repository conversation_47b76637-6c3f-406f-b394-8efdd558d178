import request from '@/utils/request'

// 获得学员评价带教统计分页
export function getDeptappraisestudentstatistic(query) {
  return request({
    url: '/rotation/deptappraisestudentstatistic/page',
    method: 'get',
    params: query
  })
}

// 获得学员评价带教详情分页
export function getAppraiseStudentList(query) {
    return request({
      url: '/rotation/deptappraisestudentstatistic/page-student',
      method: 'get',
      params: query
    })
}

// 导出科室评价学员统计数据
export function exportDeptAppraiseStudentStatistic(query) {
  return request({
    url: '/rotation/deptappraisestudentstatistic/export',
    method: 'get',
    responseType: 'blob',
    params: query
  })
}
