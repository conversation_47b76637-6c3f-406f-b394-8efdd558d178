<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="1000px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <residency-audit-step
      :recruitmentRegistrationId="recruitmentRegistrationId"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ResidencyAuditStep from "../../fill/residency-audit-step.vue";

export default {
  name: "RegistrationInfoDialog",
  components: { ResidencyAuditStep },
  props: {
    title: {
      type: String,
    },
    openAuditInfo: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      open: this.openAuditInfo,
    };
  },
  computed: {
    recruitmentRegistrationId() {
      return this.formData?.recruitmentRegistrationId?.toString();
    },
  },
  watch: {
    openAuditInfo(newVal) {
      this.open = newVal;
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:openAuditInfo", false);
    },
  },
};
</script>

<style lang="scss" scoped></style>
