import request from '@/utils/request'

// 获得发布的标准方案(医院方案)分页
export function getHospitalSchemePage(query) {
  return request({
    url: '/rotation/department-relationship/page',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/hospitalScheme/index'}
  })
}

// 获得标准方案标准与轮转科室对应关系
export function getDepartmentRelationships(query) {
  return request({
    url: '/rotation/department-relationship/list',
    method: 'get',
    params: query
  })
}

// 保存标准方案标准与轮转科室对应关系
export function saveDepartmentRelationships(data) {
  return request({
    url: '/rotation/department-relationship/save',
    method: 'post',
    data
  })
}
