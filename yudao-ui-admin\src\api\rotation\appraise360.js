import request from '@/utils/request'

// 创建360评价项
export function createAppraise360Item(data) {
  return request({
    url: '/rotation/appraise360/create',
    method: 'post',
    data: data
  })
}

// 更新360评价项
export function updateAppraise360Item(data) {
  return request({
    url: '/rotation/appraise360/update',
    method: 'put',
    data: data
  })
}

// 删除360评价项
export function deleteAppraise360Item(id) {
  return request({
    url: '/rotation/appraise360/delete?id=' + id,
    method: 'delete'
  })
}

// 获得360评价项
export function getAppraise360Item(id) {
  return request({
    url: '/rotation/appraise360/get?id=' + id,
    method: 'get'
  })
}

// 获得360评价项分页
export function getAppraise360ItemPage(query) {
  return request({
    url: '/rotation/appraise360/page',
    method: 'get',
    params: query
  })
}

// 导出360评价项 Excel
export function exportAppraise360ItemExcel(query) {
  return request({
    url: '/rotation/appraise360/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
