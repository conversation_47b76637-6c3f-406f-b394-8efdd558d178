<template>
  <div class="app-container">
    <el-form class="top-info" inline>
      <el-form-item label="科室名称:">{{ r.rotationDepartmentName}}</el-form-item>
      <el-form-item label="轮转时间:">
        {{ r.rotationTime }}
        <dict-tag :type="DICT_TYPE.ROTATION_CYCLE" :value="r.rotationCycle"></dict-tag>
      </el-form-item>
      <el-form-item label="带教老师:">{{ r.tutor }}</el-form-item>
      <el-form-item label="教学主任:">{{ r.teachingDirector }}</el-form-item>
    </el-form>

    <el-steps class="steps" :active="currentStep" simple :space="10" process-status="finish" finish-status="success">
      <el-step
        v-show="r.enrollmentEduStatus === '1' && config.enrollmentEduConfigStatus"
        title="入科教育"
        @click.native="currentStep = 0"
      ></el-step>
      <el-step
        title="日常数据"
        v-show="r.dailyDataStatus === '1'"
        @click.native="currentStep = 1"
      ></el-step>
      <el-step
        v-show="r.appraiseStatus === '1' && config.appraiseConfigStatus"
        title="360评价"
        @click.native="currentStep = 2"
      ></el-step>
      <el-step
        v-show="r.graduationAssessmentStatus === '1' && config.examineConfigStatus"
        title="出科考核"
        @click.native="currentStep = 3"
      ></el-step>
      <el-step
        v-show="r.graduationApplyStatus === '1'"
        title="出科申请"
        @click.native="currentStep = 4"
      ></el-step>
    </el-steps>

    <enrollment-edu v-if="currentStep === 0" :info="r" :config="config" />
    <daily-data v-if="currentStep === 1" :config="config" />
    <appraise v-if="currentStep === 2" :info="r" :config="config" />
    <graduation-assessment v-if="currentStep === 3" :info="r" :config="config" />
    <graduation-apply v-if="currentStep === 4" :info="r" :config="config" />
  </div>
</template>

<script>
import EnrollmentEdu from './enrollmentEdu'
import DailyData from './dailyData'
import Appraise from './appraise'
import GraduationAssessment from './graduationAssessment'
import GraduationApply from './graduationApply'
import { getScheduleSteps } from '@/api/rotation/manual'
import { getConfigByScheduleDetailsId } from '@/api/rotation/standardScheme'

export default {
  name: 'index',
  components: {
    EnrollmentEdu,
    DailyData,
    Appraise,
    GraduationAssessment,
    GraduationApply
  },
  data() {
    return {
      currentStep: 0,
      r: {},
      config: {},
    }
  },
  created() {
    const id = this.$route.query.id
    getScheduleSteps(id).then(res => {
      this.currentStep = +res.data.currentStep - 1
      this.r = res.data
    })
    getConfigByScheduleDetailsId(id).then(res => {
      this.config = res.data;
    })
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  font-size: 16px;
  margin-bottom: 20px;

  ::v-deep .el-form-item {
    margin-bottom: 0;
    margin-right: 100px;
  }
}

.steps {
  margin-bottom: 20px;

  ::v-deep .el-step {
    cursor: pointer;
  }
}
</style>
