<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="培训名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入培训名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训级别" prop="trainingLevel">
        <el-select
          v-model="queryParams.trainingLevel"
          placeholder="请选择培训级别"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训类型" prop="trainingType">
        <el-select
          v-model="queryParams.trainingType"
          placeholder="请选择培训类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训对象" prop="trainingType">
        <el-select
          v-model="queryParams.trainingType"
          placeholder="请选择培训类型"
          clearable
          size="small"
        >
          <el-option
            v-if="dict.value != 0"
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训人" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入培训人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布状态" prop="publishStatus">
        <el-select
          v-model="queryParams.publishStatus"
          placeholder="请选择发布状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_PUBLISH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开展时间" prop="developDates">
        <el-date-picker
          clearable
          v-model="queryParams.developDates"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="选择开展时间"
        />
      </el-form-item>
      <el-form-item label="督导状态" prop="superviseStatus">
        <el-select
          v-model="queryParams.superviseStatus"
          placeholder="请选择督导状态"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SUPERVISE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:hospital-training-supervise:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="培训名称"
        align="center"
        prop="name"
        fixed
        width="180"
      />
      <el-table-column
        label="培训级别"
        align="center"
        prop="trainingLevel"
        width="130"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_TRAINING_LEVEL"
            :value="scope.row.trainingLevel"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="培训类型"
        align="center"
        prop="trainingType"
        width="120"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_TRAINING_TYPE"
            :value="scope.row.trainingType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="开展时间"
        align="center"
        prop="startTime"
        width="300"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }} ~ {{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发布状态"
        align="center"
        prop="publishStatus"
        width="130"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_PUBLISH_STATUS"
            :value="scope.row.publishStatus"
          />
        </template>
      </el-table-column>
      <el-table-column label="培训人" align="center" prop="nickname">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewSpeakerSuperviseRecord(scope.row)"
            >{{ scope.row.nickname || "--" }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="培训地点"
        align="center"
        prop="trainingAddress"
        width="150"
      />
      <el-table-column
        label="出勤率"
        align="center"
        prop="attendance"
        width="150"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewStudentDetail(scope.row)"
            >{{ scope.row.attendance || "--" }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="评价得分"
        align="center"
        prop="evaluationScore"
        width="140"
      >
        <template slot-scope="scope">
          <el-rate
            v-model="scope.row.evaluationScore"
            disabled
            show-score
            text-color="#ff9900"
            :max="5"
            score-template="{value}"
          >
          </el-rate>
        </template>
      </el-table-column>
      <el-table-column
        label="督导专家"
        align="center"
        prop="superviseNicknames"
        width="120"
      >
      </el-table-column>
      <el-table-column
        label="督导得分"
        align="center"
        prop="avgSuperviseScore"
        width="120"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewSuperviseRecord(scope.row)"
            >{{ scope.row.avgSuperviseScore || "--" }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.superviseResultId"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['rotation:hospital-training-supervise:query']"
            >查看详情</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleSupervise(scope.row)"
            v-hasPermi="['rotation:supervise-result:create']"
            >督导安排</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handlePics(scope.row)"
            v-hasPermi="['rotation:hospital-training-supervise:query']"
          >
            <el-badge type="success" is-dot :hidden="!scope.row.pictures">
              <div style="padding: 2px">查看照片</div>
            </el-badge>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="980px"
      v-dialogDrag
      append-to-body
      custom-class="hospitalTraining-supform-choice"
    >
      <el-form inline>
        <el-form-item label="培训名称">
          <el-input
            v-model="superviseRow.name"
            placeholder="请输入培训名称"
            disabled
          />
        </el-form-item>
        <el-form-item label="培训类型">
          <el-select
            v-model="superviseRow.trainingType"
            filterable
            placeholder="请选择培训类型"
            disabled
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.TEACHING_TRAINING_TYPE
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="表单名称" prop="name">
          <el-input
            v-model="superviseQueryParams.formName"
            placeholder="输入表单名称快速检索表单"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="querySimpleFormList"
            >表单检索</el-button
          >
        </el-form-item>
      </el-form>
      <el-transfer
        v-model="selectFormList"
        :data="superviseFormList"
        :titles="['表单列表', '已选表单']"
      >
        <span slot-scope="{ option }" :title="option.label">{{
          option.label
        }}</span>
      </el-transfer>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleStartSupervise"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="督导评价" :visible.sync="auditVisible" width="900px">
      <el-tabs>
        <el-tab-pane
          v-for="item in auditForm.resultFormCreateReqVOS || []"
          :label="item.superviseFormName"
        >
          <el-form inline label-width="90px">
            <el-form-item
              label="培训名称："
              style="width: 100%; white-space: nowrap"
              >{{ superviseRow.name }}</el-form-item
            >
            <el-form-item label="培训级别：" style="width: 32%">
              <dict-tag
                :type="DICT_TYPE.TEACHING_TRAINING_LEVEL"
                :value="superviseRow.trainingLevel"
              ></dict-tag>
            </el-form-item>
            <el-form-item label="培训类型：" style="width: 32%">
              <dict-tag
                :type="DICT_TYPE.TEACHING_TRAINING_TYPE"
                :value="superviseRow.trainingType"
              ></dict-tag>
            </el-form-item>
            <el-form-item label="培训人：" style="width: 32%">{{
              superviseRow.nickname
            }}</el-form-item>
            <el-form-item label="开展时间：" style="width: 100%">{{
              superviseRow.startTime && superviseRow.endTime
                ? parseTime(superviseRow.startTime, "{y}-{m}-{d} {h}:{i}") +
                  " ~ " +
                  parseTime(superviseRow.endTime, "{y}-{m}-{d} {h}:{i}")
                : "--"
            }}</el-form-item>
            <el-form-item label="培训地点：" style="width: 100%">{{
              superviseRow.trainingAddress
            }}</el-form-item>
          </el-form>

          <div class="form-item-wrapper" v-if="item.resultExts.length > 0">
            <div class="form-item" v-for="(ext, index) in item.resultExts" :key="index">
              <span class="form-label">{{ ext.superviseFormExtName }}：</span>
              <span v-if="isView">{{ ext.superviseFormExtVal }}</span>
              <el-input v-else v-model="ext.superviseFormExtVal" placeholder="请输入"></el-input>
            </div>
          </div>
          <div
            v-for="(resultItem, i) in item.resultItems"
            :key="i"
            style="margin-bottom: 20px"
          >
            <header class="table-header">
              <span style="margin-right: 20px"
                >评分项目：{{ resultItem.superviseFormItemName }}</span
              >
              <span style="margin-right: 20px"
                >总分：{{ resultItem.superviseFormItemScore }}</span
              >
              <span>督导得分：{{ resultItem.score }}</span>
            </header>
            <el-table :data="resultItem.resultFormSubItems" border>
              <el-table-column
                label="评分要素"
                prop="superviseFormSubItemName"
              ></el-table-column>
              <el-table-column
                label="分值"
                prop="superviseFormSubItemScore"
                width="100px"
              ></el-table-column>
              <el-table-column label="得分" width="150px">
                <template v-slot="scope">
                  <el-input-number
                    style="width: 120px"
                    v-model="scope.row.score"
                    controls-position="right"
                    :min="0"
                    :max="scope.row.superviseFormSubItemScore"
                    :disabled="isView"
                    @change="handleSubItemScoreChange(resultItem, item)"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="备注" width="150px">
                <template v-slot="scope">
                  <el-input
                    style="width: 120px"
                    v-model="scope.row.remark"
                    :disabled="isView"
                    @change="handleSubItemRemarkChange(resultItem, item)"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div style="text-align: right; margin-bottom: 20px">
            督导总分：{{ item.score }}
          </div>

          <el-form label-width="100px">
            <el-form-item
              label="活动自评："
              style="width: 100%; white-space: nowrap"
              v-if="item.selfAssessment"
            >
              <el-input
                type="textarea"
                v-model="item.selfAssessment"
                :disabled="true"
                style="width: 100%"
              ></el-input>
            </el-form-item>
            <el-form-item label="督导意见：" required style="width: 100%">
              <el-input
                type="textarea"
                v-model="item.opinion"
                :disabled="isView"
                style="width: 100%"
                :maxlength="500"
                :minlength="0"
                show-word-limit
                :rows="4"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="照片上传："
              required
              style="width: 100%; white-space: nowrap"
            >
              <imageUpload
                v-model="item.pictures"
                :limit="9999"
                :disabled="isView"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" v-if="!isView">
        <el-button type="primary" @click="handleSaveSuperviseForm"
          >确定</el-button
        >
        <el-button @click="auditVisible = false">取消</el-button>
      </span>
    </el-dialog>

    <!--    <el-dialog title="督导记录" :visible.sync="superviseRecordVisible" width="600px" v-dialogDrag append-to-body>
      <div>
        <p style="margin-top: 0;">培训名称：{{superviseRow.name}}</p>
        <el-table v-loading="superviseRecordListLoading" :data="superviseRecordList">
          <el-table-column label="督导人" align="center" prop="nickname" />
          <el-table-column label="评分" align="center" prop="score" />
          <el-table-column label="反馈进度" align="center" prop="feedbackCount" >
            <template v-slot="scope">{{scope.row.feedbackCount == 0 ? '未反馈' : '已反馈'}}</template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleView(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>-->

    <el-dialog
      :title="superviseRow.nickname + '的督导记录'"
      :visible.sync="superviseSpeakerRecordVisible"
      width="600px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <el-table
          v-loading="superviseSpeakerRecordListLoading"
          :data="superviseSpeakerRecordList"
        >
          <el-table-column
            label="培训名称"
            align="center"
            prop="teachingActiveName"
          />
          <el-table-column
            label="督导得分"
            align="center"
            prop="avgSuperviseScore"
          />
          <el-table-column label="督导专家" align="center" prop="expertNames" />
        </el-table>
      </div>
    </el-dialog>

    <el-dialog
      title="培训照片"
      :visible.sync="openPics"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <imageUpload
          v-model="form.pictures"
          :limit="9999"
          activeTypeName=""
          :disabled="true"
        />
      </div>
    </el-dialog>

    <el-dialog
      title="参加学员数详情页面"
      :visible.sync="openStudentDetail"
      width="1000px"
      v-dialogDrag
      append-to-body
      custom-class="student-detail-dialog"
    >
      <el-tabs v-model="activeTabName" @tab-click="handleTabClick">
        <el-tab-pane label="学员" name="student">
          <div class="student-tab">
            <el-form
              :model="queryStudentParams"
              ref="queryStudentForm"
              size="small"
              :inline="true"
              label-width="68px"
            >
              <el-form-item label="学员姓名" prop="nickname">
                <el-input
                  v-model="queryStudentParams.nickname"
                  placeholder="请输入学员姓名"
                  clearable
                  @keyup.enter.native="handleStudentQuery"
                  style="width: 120"
                />
              </el-form-item>
              <el-form-item label="学员类型" prop="studentType">
                <el-select
                  v-model="queryStudentParams.studentType"
                  placeholder="请选择学员类型"
                  filterable
                  clearable
                  size="small"
                  @change="handleQueryStudentTypeChange"
                  style="width: 120"
                >
                  <el-option
                    v-for="dict in this.getDictDatas(
                      DICT_TYPE.SYSTEM_STUDENT_TYPE
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="培训专业" prop="major">
                <el-select
                  v-model="queryStudentParams.major"
                  placeholder="请选择培训专业"
                  filterable
                  clearable
                  size="small"
                  style="width: 120"
                >
                  <el-option
                    v-for="item in simpleMajorList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="年级" prop="grade">
                <el-select
                  v-model="queryStudentParams.grade"
                  placeholder="请选择年级"
                  filterable
                  clearable
                  size="small"
                  style="width: 100"
                >
                  <el-option
                    v-for="grade in studentGradeList"
                    :key="grade"
                    :label="grade"
                    :value="grade"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleStudentQuery"
                  >搜索</el-button
                >
                <el-button icon="el-icon-refresh" @click="resetStudentQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>

            <div class="btns-box"></div>

            <el-table
              v-loading="studentDetailLoading"
              :data="studentDetailList"
            >
              <el-table-column
                label="学员姓名"
                align="center"
                prop="nickname"
              />
              <el-table-column
                label="学员类型"
                prop="studentType"
                align="center"
              >
                <template v-slot="scope">
                  <dict-tag
                    :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                    :value="scope.row.studentType"
                  ></dict-tag>
                </template>
              </el-table-column>
              <el-table-column
                label="培训专业"
                align="center"
                prop="majorName"
              />
              <el-table-column
                label="年级"
                align="center"
                prop="grade"
                width="130"
              />
              <el-table-column
                label="扫码时间"
                align="center"
                prop="scanningTime"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.scanningTime) }}</span>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="studentTotal > 0"
              :total="studentTotal"
              :page.sync="queryStudentParams.pageNo"
              :limit.sync="queryStudentParams.pageSize"
              @pagination="() => getStudentList()"
            />
          </div>
        </el-tab-pane>

        <el-tab-pane label="职工" name="worker">
          <div class="student-tab">
            <el-form
              :model="queryWorkerParams"
              ref="queryWorkerForm"
              size="small"
              :inline="true"
              label-width="68px"
            >
              <el-form-item label="职工姓名" prop="nickname">
                <el-input
                  v-model="queryWorkerParams.nickname"
                  placeholder="请输入职工姓名"
                  clearable
                  @keyup.enter.native="handleStudentQuery"
                  style="width: 120"
                />
              </el-form-item>
              <el-form-item label="职工工号" prop="username">
                <el-input
                  v-model="queryWorkerParams.username"
                  placeholder="请输入职工工号"
                  clearable
                  @keyup.enter.native="handleStudentQuery"
                  style="width: 120"
                />
              </el-form-item>
              <el-form-item label="角色" prop="roleId">
                <el-select
                  v-model="queryWorkerParams.roleId"
                  filterable
                  clearable
                  placeholder="请选择角色"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in roleOptions"
                    :key="parseInt(item.id)"
                    :label="item.name"
                    :value="parseInt(item.id)"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleWorkerQuery"
                  >搜索</el-button
                >
                <el-button icon="el-icon-refresh" @click="resetWorkerQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>

            <div class="btns-box"></div>

            <el-table v-loading="worderDetailLoading" :data="worderDetailList">
              <el-table-column
                label="职工姓名"
                align="center"
                prop="nickname"
              />
              <el-table-column
                label="职工工号"
                align="center"
                prop="username"
              />
              <el-table-column label="角色" align="center" prop="roleNames" />
              <el-table-column
                label="扫码时间"
                align="center"
                prop="scanningTime"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.scanningTime) }}</span>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="workerTotal > 0"
              :total="workerTotal"
              :page.sync="queryWorkerParams.pageNo"
              :limit.sync="queryWorkerParams.pageSize"
              @pagination="getWorkerList"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <supervise-record-dialog
      :visible.sync="superviseRecordVisible"
      :supervise-object="superviseRecordRow"
    ></supervise-record-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";
import SuperviseRecordDialog from "./supervise-record-dialog";
import { getStudentGradeList } from "@/api/system/userStudent";
import { getUserWorkerSimpleList } from "@/api/system/userWorker";
import { getSimpleMajorList } from "@/api/system/major";
import { listSimpleRoles } from "@/api/system/role";
import {
  createHospitalTraining,
  updateHospitalTraining,
  deleteHospitalTraining,
  getHospitalTraining,
  getHospitalTrainingPage,
  exportHospitalTrainingExcel,
  getTrainingSelectedNumber,
  hospitalTrainingRevoke,
  hospitalTrainingPublish,
  getStudentPage,
  getWorkerPage,
  confirmJoin,
  revokeJoin,
  getHosSuperviseRecordList,
  getTrainerSuperviseRecordList,
} from "@/api/rotation/hospitalTrainingSupervise";
import {
  getSimpleFormList,
  getSuperviseFormsList,
  createSuperviseResult,
  getSuperviseResult,
} from "@/api/rotation/teachingActiveSupervise";

export default {
  name: "HospitalTrainingSupervise",
  components: {
    FileUpload,
    ImageUpload,
    SuperviseRecordDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级培训列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        trainingLevel: null,
        trainingType: null,
        majorCode: null,
        publishStatus: null,
        nickname: null,
        developDates: [],
        superviseStatus: "0",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "培训名称不能为空", trigger: "blur" },
        ],
        trainingLevel: [
          { required: true, message: "培训级别不能为空", trigger: "change" },
        ],
        trainingType: [
          { required: true, message: "培训类型不能为空", trigger: "change" },
        ],
        majorCode: [
          { required: true, message: "培训专业不能为空", trigger: "change" },
        ],
        startTime: [
          { required: true, message: "开展开始时间不能为空", trigger: "blur" },
        ],
        endTime: [
          { required: true, message: "开展结束时间不能为空", trigger: "blur" },
        ],
        trainingUserType: [
          { required: true, message: "培训人类型不能为空", trigger: "change" },
        ],
        courtTrainingUserId: [
          { required: true, message: "培训人不能为空", trigger: "change" },
        ],
        outerCourtyardTrainingUserName: [
          { required: true, message: "培训人不能为空", trigger: "change" },
        ],
        trainingAddress: [
          { required: true, message: "培训地点不能为空", trigger: "blur" },
        ],
        isManualSelection: [
          {
            required: true,
            message: "是否手动选择培训对象不能为空",
            trigger: "blur",
          },
        ],
        trainingObjects: [
          { required: true, message: "培训对象不能为空", trigger: "change" },
        ],
        roleIds: [
          {
            type: "array",
            required: true,
            message: "角色不能为空",
            trigger: "change",
          },
        ],
        studentTypes: [
          {
            type: "array",
            required: true,
            message: "学员类型不能为空",
            trigger: "change",
          },
        ],
        trainingSelectedNumber: [
          { required: true, message: "选择培训人数不能为空", trigger: "blur" },
        ],
        isSelfRegistration: [
          {
            required: true,
            message: "是否允许自主报名参加不能为空",
            trigger: "blur",
          },
        ],
        registrationLimitNumber: [
          { required: true, message: "报名人数限制不能为空", trigger: "blur" },
        ],
        publishStatus: [
          { required: true, message: "发布状态不能为空", trigger: "change" },
        ],
      },
      studentGradeList: [],
      userWorkerOptions: [],
      // queryMajorList: [],
      roleOptions: [],
      openQrCode: false,
      curQrcode: "",
      openPics: false,
      curActive: {},
      activeTabName: "student",
      simpleMajorList: [],
      openStudentDetail: false,
      queryStudentParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: "",
        studentType: "",
        major: "",
        grade: "",
      },
      studentTotal: 0,
      studentDetailLoading: false,
      studentDetailList: [],
      queryWorkerParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: "",
        username: "",
        roleId: "",
      },
      workerTotal: 0,
      worderDetailLoading: false,
      worderDetailList: [],
      superviseQueryParams: {
        name: "",
        formType: "2",
        superviseType: "1",
        developForm: "",
      },
      superviseFormList: [],
      selectFormList: [],
      superviseRow: {},
      auditForm: {},
      auditVisible: false,
      isView: false,
      superviseRecordRow: null,
      superviseRecordVisible: false,
      superviseRecordListLoading: false,
      superviseRecordList: [],
      superviseSpeakerRecordVisible: false,
      superviseSpeakerRecordListLoading: false,
      superviseSpeakerRecordList: [],
    };
  },
  created() {
    this.getList();
    getStudentGradeList().then((res) => {
      this.studentGradeList = res.data;
    });
    this.getUserworkData();
    // 获得角色列表
    this.roleOptions = [];
    listSimpleRoles().then((response) => {
      const { data } = response;
      let list = [];
      data.forEach((item) => {
        if (item.code !== "super_admin" && item.code !== "student") {
          list.push(item);
        }
      });
      this.roleOptions = list;
    });
    getSimpleMajorList().then((res) => (this.majorList = res.data));
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getHospitalTrainingPage(this.queryParams).then((response) => {
        const list = response.data.list;
        list.forEach((item) => {
          if (!item.score) {
            item.evaluationScore = 0;
          } else {
            const _score = (item.evaluationScore / item.score) * 5;
            item.evaluationScore = _score;
          }
        });
        this.list = list;
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        trainingLevel: undefined,
        trainingType: undefined,
        majorCode: undefined,
        timeArr: [],
        startTime: undefined,
        endTime: undefined,
        trainingUserType: undefined,
        courtTrainingUserId: undefined,
        outerCourtyardTrainingUserName: undefined,
        trainingAddress: undefined,
        isManualSelection: false,
        trainingObjects: [],
        roleIds: [],
        studentTypes: [],
        studentMajors: [],
        grades: [],
        trainingSelectedNumber: 0,
        isSelfRegistration: true,
        registrationLimitNumber: 0,
        coursewares: undefined,
        pictures: undefined,
        publishStatus: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleStudentQuery() {
      this.queryStudentParams.pageNo = 1;
      this.getStudentList();
    },
    resetStudentQuery() {
      this.resetForm("queryStudentForm");
      this.handleStudentQuery();
    },
    handleWorkerQuery() {
      this.queryWorkerParams.pageNo = 1;
      this.getWorkerList();
    },
    resetWorkerQuery() {
      this.resetForm("queryWorkerForm");
      this.handleWorkerQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加院级培训";
    },
    /** 修改按钮操作 */
    handleSupervise(row) {
      this.superviseRow = row;
      this.superviseQueryParams.developForm = row.trainingType;
      this.open = true;
      this.title = "督导表单选择";
      this.selectFormList = [];
      this.querySimpleFormList();
    },
    querySimpleFormList() {
      getSimpleFormList(this.superviseQueryParams).then((res) => {
        this.superviseFormList = res.data.map((item) => ({
          key: item.id,
          label: item.name,
        }));
      });
    },
    handleStartSupervise() {
      if (this.selectFormList.length === 0) {
        this.$message.warning("请选择至少一个督导表单");
        return;
      }
      getSuperviseFormsList({
        superviseFormIds: this.selectFormList.join(","),
        superviseObjectId: this.superviseRow.id,
      }).then((res) => {
        const _data = res.data;
        _data.costTime = 0;
        _data.resultFormCreateReqVOS.forEach((item) => {
          item.costTime = 0;
        });
        this.auditForm = _data;
        this.open = false;
        this.auditVisible = true;
        this.isView = false;
      });
    },
    handleSubItemScoreChange(resultTarget, target) {
      let resultItemScore = 0,
        totalScore = 0;
      resultTarget.resultFormSubItems.forEach((subItem) => {
        resultItemScore += subItem.score;
      });
      resultTarget.score = resultItemScore;
      target.resultItems.forEach((item) => {
        totalScore += item.score;
      });
      target.score = totalScore;
    },
    handleSubItemRemarkChange() {},
    handleSaveSuperviseForm() {
      // console.log('this.auditForm===', this.auditForm)
      const { resultFormCreateReqVOS = [] } = this.auditForm;

      let opinionFlag = true;
      let picturesFlag = true;
      let superviseFormName = "";
      resultFormCreateReqVOS.forEach((item) => {
        if (!item.opinion) {
          opinionFlag = false;
          superviseFormName = item.superviseFormName;
        }
        if (!item.pictures) {
          picturesFlag = false;
          superviseFormName = item.superviseFormName;
        }
      });
      if (!opinionFlag) {
        return this.$modal.msgWarning(`请输入${superviseFormName}的督导意见!`);
      }
      if (!picturesFlag) {
        return this.$modal.msgWarning(`请上传${superviseFormName}的照片!`);
      }

      createSuperviseResult(this.auditForm).then(() => {
        this.$message.success("操作成功");
        this.auditVisible = false;
        this.getList();
      });
    },
    /** 考核督导详情 */
    handleView(row) {
      this.isView = true;
      this.superviseRow = row;
      getSuperviseResult({ id: row.superviseResultId }).then((res) => {
        this.auditForm = res.data;
        this.auditVisible = true;
      });
    },
    viewSuperviseRecord(row) {
      this.superviseRecordRow = row;
      this.superviseRecordVisible = true;
    },
    viewSpeakerSuperviseRecord(row) {
      this.superviseRow = row;
      const params = {
        pageNo: 1,
        pageSize: 9999,
        hospitalTrainingId: row.id,
      };
      this.superviseSpeakerRecordListLoading = true;
      getTrainerSuperviseRecordList(params).then((res) => {
        this.superviseSpeakerRecordList = res.data.list;
        this.superviseSpeakerRecordVisible = true;
        this.superviseSpeakerRecordListLoading = false;
      });
    },
    handlePics(row) {
      this.reset();
      const id = row.id;
      this.curActive = row;
      getHospitalTraining(id).then((response) => {
        const { data } = response;
        const { startTime, endTime } = data;
        this.form = response.data;
        this.form.timeArr = [startTime, endTime];
        this.form.trainingObjects = data.trainingObjects
          ? data.trainingObjects.split(",")
          : [];
        this.form.roleIds = data.roleIds ? data.roleIds.split(",") : [];
        this.form.studentTypes = data.studentTypes
          ? data.studentTypes.split(",")
          : [];
        this.form.studentMajors = data.studentMajors
          ? data.studentMajors.split(",")
          : [];
        this.form.grades = data.grades ? data.grades.split(",") : [];
        this.form.coursewares = this.form.coursewares
          ? JSON.parse(this.form.coursewares)
          : [];
        this.openPics = true;
      });
    },
    cancelSubmitPics() {
      this.openPics = false;
    },
    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = {
          name: this.form.name,
          trainingLevel: this.form.trainingLevel,
          trainingType: this.form.trainingType,
          majorCode: this.form.majorCode,
          startTime: this.form.timeArr[0],
          endTime: this.form.timeArr[1],
          trainingUserType: this.form.trainingUserType,
          courtTrainingUserId: this.form.courtTrainingUserId,
          outerCourtyardTrainingUserName:
            this.form.outerCourtyardTrainingUserName,
          trainingAddress: this.form.trainingAddress,
          isManualSelection: this.form.isManualSelection,
          trainingObjects: this.form.trainingObjects.join(","),
          roleIds: this.form.roleIds.join(","),
          studentTypes: this.form.studentTypes.join(","),
          studentMajors: this.form.studentMajors.join(","),
          grades: this.form.grades.join(","),
          trainingSelectedNumber: this.form.trainingSelectedNumber,
          isSelfRegistration: this.form.isSelfRegistration,
          registrationLimitNumber: this.form.registrationLimitNumber,
          coursewares: JSON.stringify(this.form.coursewares),
          pictures: this.form.pictures,
          publishStatus: type === "save" ? 0 : 1,
        };
        // 修改的提交
        if (this.form.id != null) {
          params.id = this.form.id;
          updateHospitalTraining(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createHospitalTraining(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    submitPics() {
      const params = {
        id: this.form.id,
        name: this.form.name,
        trainingLevel: this.form.trainingLevel,
        trainingType: this.form.trainingType,
        majorCode: this.form.majorCode,
        startTime: this.form.timeArr[0],
        endTime: this.form.timeArr[1],
        trainingUserType: this.form.trainingUserType,
        courtTrainingUserId: this.form.courtTrainingUserId,
        outerCourtyardTrainingUserName:
          this.form.outerCourtyardTrainingUserName,
        trainingAddress: this.form.trainingAddress,
        isManualSelection: this.form.isManualSelection,
        trainingObjects: this.form.trainingObjects.join(","),
        roleIds: this.form.roleIds.join(","),
        studentTypes: this.form.studentTypes.join(","),
        studentMajors: this.form.studentMajors.join(","),
        grades: this.form.grades.join(","),
        trainingSelectedNumber: this.form.trainingSelectedNumber,
        isSelfRegistration: this.form.isSelfRegistration,
        registrationLimitNumber: this.form.registrationLimitNumber,
        coursewares: this.form.coursewares,
        pictures: this.form.pictures,
        publishStatus: this.curActive.publishStatus,
      };
      updateHospitalTraining(params).then((response) => {
        this.$modal.msgSuccess("保存成功");
        this.openPics = false;
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const { id, name } = row;
      this.$modal
        .confirm(`是否确认删除"${name}"?`)
        .then(function () {
          return deleteHospitalTraining(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    handleUnPublish(row) {
      const { id, name } = row;
      this.$modal
        .confirm(`是否确认撤销"${name}"?`)
        .then(function () {
          return hospitalTrainingRevoke({ id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(`撤销成功`);
        })
        .catch(() => {});
    },
    handlePublish(row) {
      const { id, name } = row;
      this.$modal
        .confirm(`是否确认发布"${name}"?`)
        .then(function () {
          return hospitalTrainingPublish({ id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(`发布成功`);
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有院级培训督导数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportHospitalTrainingExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "院级培训督导.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    getUserworkData() {
      getUserWorkerSimpleList().then((res) => {
        this.userWorkerOptions = [];
        this.userWorkerOptions.push(...res.data);
      });
    },
    getSelectedNumber() {
      const params = {
        trainingObjects: this.form.trainingObjects.join(","),
        roleIds: this.form.roleIds.join(","),
        studentTypes: this.form.studentTypes.join(","),
        studentMajors: this.form.studentMajors.join(","),
        grades: this.form.grades.join(","),
      };
      getTrainingSelectedNumber(params).then((res) => {
        this.form.trainingSelectedNumber = res.data || 0;
      });
    },
    handleViewQrcode(row) {
      this.curQrcode = row.qrcode;
      this.openQrCode = true;
    },
    viewStudentDetail(row) {
      if (!row.attendance) {
        return;
      }
      this.activeTabName = "student";
      this.curActive = row;
      this.getStudentList(() => {
        this.openStudentDetail = true;
      });
    },
    getStudentList(callback = null) {
      this.studentDetailLoading = true;
      const params = {
        ...this.queryStudentParams,
        hospitalTrainingId: this.curActive.id,
      };
      getStudentPage(params).then((response) => {
        this.studentDetailList = response.data.list || [];
        if (callback) {
          callback();
        }
        this.studentDetailLoading = false;
        this.studentTotal = response.data.total;
      });
    },
    getWorkerList() {
      this.worderDetailLoading = true;
      const params = {
        ...this.queryWorkerParams,
        hospitalTrainingId: this.curActive.id,
      };
      getWorkerPage(params).then((response) => {
        this.worderDetailList = response.data.list || [];
        this.worderDetailLoading = false;
        this.workerTotal = response.data.total;
      });
    },
    handleTabClick(tab, event) {
      // console.log('handleTabClick================', tab, event);
      this.activeTabName = tab.name;
      if (tab.name === "student") {
        this.getStudentList();
      } else {
        this.getWorkerList();
      }
    },
    /** 查询学员类型改变 */
    handleQueryStudentTypeChange(value) {
      this.queryParams.major = null;
      this.simpleMajorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.simpleMajorList = res.data;
      });
    },
    handleJoin(row) {
      const params = {
        hospitalTrainingId: row.hospitalTrainingId,
        userId: row.userId,
      };
      confirmJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        if (this.activeTabName === "student") {
          this.getStudentList();
        } else {
          this.getWorkerList();
        }
      });
    },
    handleRevoke(row) {
      const params = {
        hospitalTrainingId: row.hospitalTrainingId,
        userId: row.userId,
      };
      revokeJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        if (this.activeTabName === "student") {
          this.getStudentList();
        } else {
          this.getWorkerList();
        }
      });
    },
    exportStudent() {},
    exportWorker() {},
  },
};
</script>

<style lang="scss" >
.hospitalTraining-dialog {
  .el-dialog__body {
    padding-right: 100px;
  }
}

.student-tab {
  .btns-box {
    margin-bottom: 10px;
    text-align: right;
  }
}

.student-detail-dialog {
  .el-dialog__body {
    padding-top: 10px;

    .el-tabs__header {
      margin-bottom: 0;
    }

    .el-tabs__content {
      padding-top: 15px;
      height: calc(100vh - 220px);
      overflow-y: auto;
      margin-right: -15px;
      padding-right: 15px;
    }
  }
}

.table-header {
  padding: 10px;
  border: 1px solid #dfe6ec;
  background: #f8f8f9;
  position: relative;
  top: 1px;
}

.hospitalTraining-supform-choice {
  .el-transfer-panel {
    width: 43%;
  }

  .el-transfer__buttons {
    display: inline-flex;
    flex-direction: column;

    .el-button + .el-button {
      margin-left: 0;
    }
  }
}
</style>

<style lang="scss" scoped>
.form-item-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;

  .form-item {
    flex-basis: calc(33.3% - 20px);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }

  .form-label {
    flex-shrink: 0;
    font-size: 14px;
    color: #606266;
  }
}
</style>
