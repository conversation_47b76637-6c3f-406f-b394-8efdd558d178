import request from "@/utils/request";

// 创建学员用户
export function createUserStudent(data) {
  return request({
    url: "/system/user-student/create",
    method: "post",
    data: data,
  });
}

// 更新学员用户
export function updateUserStudent(data) {
  return request({
    url: "/system/user-student/update",
    method: "put",
    data: data,
  });
}

// 删除学员用户
export function deleteUserStudent(id) {
  return request({
    url: "/system/user-student/delete?id=" + id,
    method: "delete",
  });
}

// 获得学员用户
export function getUserStudent(id) {
  return request({
    url: "/system/user-student/get?id=" + id,
    method: "get",
  });
}

// 获得学员用户分页
export function getUserStudentPage(query) {
  return request({
    url: "/system/user-student/page",
    method: "get",
    params: query,
  });
}

// 导出学员用户 Excel
export function exportUserStudentExcel(query) {
  return request({
    url: "/system/user-student/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: "/infra/file/upload",
    method: "post",
    data: data,
  });
}

// 获取学员用户精简方案列表
export function getUserStudentList(query, component) {
  return request({
    url: "/system/user-student/list-all-simple",
    method: "get",
    params: query,
    headers: component ? { component } : {},
  });
}

// 获取身份证相关信息
export function getIdcardInfo(query) {
  return request({
    url: "/system/user-student/get-idcard-info",
    method: "get",
    params: query,
  });
}

// 获取标准精简方案列表
export function getStandardSchemeList(query) {
  return request({
    url: "/rotation/standard-scheme/list-all-simple",
    method: "get",
    params: query,
  });
}

// 获取学员用户年级列表
export function getStudentGradeList() {
  return request({
    url: "/system/user-student/list-all-grade",
    method: "get",
  });
}

// 导入模版
export function exportTemplate() {
  return request({
    url: "/system/user-student/get-import-template",
    method: "get",
    responseType: "blob",
  });
}

// 获得待参加学员列表
export function getNotJoinedStuList(query) {
  return request({
    url: "/rotation/teaching-active-student-plan/list-students-not-joined",
    method: "get",
    params: query,
  });
}

// 修改用户状态
export function updateUserStatus(id, status) {
  const data = {
    id,
    status,
  };
  return request({
    url: "/system/user-student/update-status",
    method: "put",
    data: data,
  });
}

// 根据学员类型获得年级列表
export function getGradeByStudentType(query) {
  return request({
    url: "/system/user-student/list-grade",
    method: "get",
    params: query,
  });
}
