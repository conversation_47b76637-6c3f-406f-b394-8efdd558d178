<template>
  <div>
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="培训科室" prop="departmentId">
        <el-select
          v-model="queryParams.departmentId"
          filterable
          clearable
          placeholder="请选择培训科室"
          size="small"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="开展时间" prop="developDates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.developDates"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item
        label="开展状态"
        prop="activeDevolepStatus"
        label-width="80px"
      >
        <el-select
          v-model="queryParams.activeDevolepStatus"
          filterable
          clearable
          placeholder="请选择开展状态"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ACTIVE_DEVOLEP_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:teaching-active-statistics:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      show-summary
      :summary-method="summaryMethod"
    >
      <!-- <el-table-column
        label="培训人"
        align="center"
        width="130"
        prop="nickname"
        fixed
      />
      <el-table-column
        label="用户名"
        align="center"
        width="100"
        prop="username"
        fixed
      /> -->
      <el-table-column
        label="培训科室"
        align="center"
        min-width="130"
        prop="departmentNames"
        fixed
      />
      <template
        v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
      >
        <!--        <el-table-column :label="`${dict.label}(开展数)`" align="center" :prop="`active_type_${dict.value}`" width="130" />-->
        <el-table-column
          :label="`${dict.label}`"
          align="center"
          :prop="`active_type_effective_${dict.value}`"
          width="150"
        >
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click="viewDetail(scope.row, dict.value)"
              >{{ scope.row["active_type_effective_" + dict.value] }}</el-link
            >
          </template>
        </el-table-column>
      </template>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="活动详情页面"
      :visible.sync="openDetail"
      width="900px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <el-table v-loading="detailLoading" :data="detailList">
          <el-table-column label="活动名称" align="center" prop="name" />
          <el-table-column label="活动类型" prop="activeType" align="center">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.ROTATION_ACTIVE_TYPE"
                :value="scope.row.activeType"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="培训科室"
            align="center"
            prop="departmentName"
          />
          <el-table-column
            label="培训人"
            align="center"
            prop="speakerUsername"
          />
          <el-table-column label="培训时间" align="center" width="260">
            <template v-slot="scope">
              <span>{{ scope.row.startTime }} ~ {{ scope.row.endTime }}</span>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="detailTotal > 0"
          :total="detailTotal"
          :page.sync="queryDetailParams.pageNo"
          :limit.sync="queryDetailParams.pageSize"
          @pagination="getList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDepartmentSimpleList } from "@/api/system/department";
import { getActivePage } from "@/api/rotation/teachingActiveStatistic";
import {
  getTeachingActivePageDepartment,
  exportTeachingActiveExcelDepartment,
} from "@/api/rotation/teachingActiveStatisticTab";

export default {
  name: "ByDepartment",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员入科列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 50,
        nickname: "",
        departmentId: "",
        developDates: [],
        activeDevolepStatus: "1",
      },
      // 表单参数
      departmentOptions: [],
      currentRow: {},
      isNotInTime: true,

      openDetail: false,
      detailLoading: false,
      detailList: [],
      detailTotal: 0,
      queryDetailParams: {
        pageNo: 1,
        pageSize: 10,
        activeDevolepStatus: 1,
      },
      activeTypeVal: "",
    };
  },
  created() {
    this.getList();
    this.getDepartment();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getTeachingActivePageDepartment(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportTeachingActiveExcelDepartment(params);
        })
        .then((response) => {
          this.$download.excel(response, "教学活动按培训科室统计.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    /** 合计计算方法 */
    summaryMethod({ columns, data }) {
      return columns.map((column) => {
        if (column.property === "departmentNames") {
          return "合计";
        } else if (
          column.property.startsWith("active_type_effective_") ||
          column.property.startsWith("subidy_")
        ) {
          return data.reduce((prev, cur) => prev + cur[column.property], 0);
        } else {
          return null;
        }
      });
    },

    viewDetail(item, activeType) {
      this.currentRow = item;
      this.activeTypeVal = activeType;
      this.getDetailList(() => {
        this.openDetail = true;
      });
    },

    getDetailList(callback) {
      this.detailLoading = true;
      const params = {
        ...this.queryDetailParams,
        activeType: this.activeTypeVal,
        developDates: this.queryParams.developDates,
        staffRoomValue: this.queryParams.staffRoomValue,
        departmentId: this.currentRow.departmentId,
        speakerUsername: this.queryParams.nickname,
      };
      getActivePage(params).then((response) => {
        this.detailList = response.data.list;
        this.detailTotal = response.data.total;
        this.detailLoading = false;
        if (callback) {
          callback();
        }
      });
    },
  },
};
</script>
<style lang="scss"></style>
