<template>
  <div class="app-container">
    <h3 class="part-title">规培信息</h3>
    <el-form class="info-form" inline label-width="120px">
      <el-form-item label="学员姓名：" style="width: 32%">{{ info.nickname || '-' }}</el-form-item>
      <el-form-item label="学员类型：" style="width: 32%">{{ info.studentTypeName || '-' }}</el-form-item>
      <el-form-item label="人员类型：" style="width: 32%">
        <dict-tag :type="DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE" :value="info.personnelType" />
      </el-form-item>
      <el-form-item label="派送单位：" style="width: 65%">{{ info.dispatchingUnit || '-' }}</el-form-item>
      <el-form-item label="分组：" style="width: 32%">{{ info.studentGroup || '-' }}</el-form-item>
      <el-form-item label="培训开始日期：" style="width: 32%">{{ info.trainingStartDate || '-' }}</el-form-item>
      <el-form-item label="年级：" style="width: 32%">{{ info.grade || '-' }}</el-form-item>
      <el-form-item label="培训专业：" style="width: 32%">{{ info.majorName || '-' }}</el-form-item>
      <el-form-item label="培训方案：" style="width: 32%">{{ info.standardSchemeName || '-' }}</el-form-item>
      <el-form-item label="培训时长：" style="width: 32%">{{ info.rotationTime || '-' }}</el-form-item>
      <el-form-item label="结业年份：" style="width: 32%">{{ info.graduationYear || '-' }}</el-form-item>
      <el-form-item label="结业时间：" style="width: 32%">{{ info.graduationDate || '-' }}</el-form-item>
      <el-form-item label="责任导师：" style="width: 32%">{{ info.responsibleMentorUserNickname || '-' }}</el-form-item>
    </el-form>

    <h3 class="part-title">规培进度</h3>
    <div class="process-part">
      <div class="flex-item" v-if="admission.isShow">
        <div class="card-title">入院管理</div>
        <div class="color-line color-1"></div>
        <div class="content-card">
          <div>
            <div v-if="admission.isShowaAmissionEducation">
              入院教育：
              <span class="link color-1" @click="toHospitalTraining(1)">
                {{ admission.isCompletedAmissionEducation ? '已完成' : '未完成' }}
              </span>
            </div>
            <div v-if="admission.isShowEnteringProfessionalBaseEducation">
              入专业基地教育：
              <span class="link color-1" @click="toHospitalTraining(4)">
                {{ admission.isCompletedEnteringProfessionalBaseEducation ? '已完成' : '未完成' }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="flex-item" v-if="rotation.isShow">
        <div class="card-title">轮转管理</div>
        <div class="color-line color-2"></div>
        <div class="content-card">
          <div style="width: 80%">
            <div class="flex-space">轮转进度：<span class="link color-2" @click="toManual">{{ rotation.rotatoinProgress }}</span></div>
            <el-progress :percentage="Math.round((rotation.rotatoinProgressPercent || 0) * 100)"></el-progress>
            <div class="flex-space">轮转数据：<span class="link color-2" @click="toManual">{{ rotation.rotaionItemProgress }}</span></div>
            <el-progress :percentage="Math.round((rotation.rotaionItemProgressPercent || 0) * 100)"></el-progress>
            <div class="flex-space">教学活动参与率：<span class="link color-2" @click="toTeachingActive">{{ rotation.activeProgress }}</span></div>
            <el-progress :percentage="Math.round((rotation.activeProgressPercent || 0) * 100)"></el-progress>
          </div>
        </div>
      </div>
      <div class="flex-item" v-if="midTerm.isShow">
        <div class="card-title">中期考核</div>
        <div class="color-line color-3"></div>
        <div class="content-card">
          <div v-if="midTerm.showStageAssessmentCount">
            阶段考核：<span>{{ midTerm.stageAssessmentCount }}</span>次
          </div>
          <div v-if="midTerm.showAnnualAssessmentCount">
            年度考核：<span>{{ midTerm.annualAssessmentCount }}</span>次
          </div>
        </div>
      </div>
      <div class="flex-item" v-if="grad.isShow">
        <div class="card-title">结业管理</div>
        <div class="color-line color-4"></div>
        <div class="content-card">
          <div v-if="grad.showGradApply">
            结业申请：
            <span>{{ grad.gradApplyStatus || '-' }}</span>
          </div>
          <div v-if="grad.showGradCertificate">
            结业证书：<span>{{ grad.gradCertificateStatus || '-' }}</span>
          </div>
          <div v-if="grad.showGradFeedback">
            结业反馈：<span>{{ grad.gradFeedbackStatus || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRegulatoryTrainingInfo, getRotationProgress } from '@/api/rotation/progress';

export default {
  name: 'process',
  data() {
    return {
      info: {},
      admission: {}, // 入院管理
      rotation: {}, // 轮转管理
      midTerm: {}, // 中期考核
      grad: {}, // 结业管理
    }
  },
  methods: {
    toHospitalTraining(trainingType) { // 1.入院教育 4.入专业基地教育
      this.$router.push(`/teaching/studentHospitalTraining?trainingType=${trainingType}`);
    },
    toManual() {
      this.$router.push('/rotation/manual');
    },
    toTeachingActive() {
      this.$router.push('/teaching/student-teaching-active');
    }
  },
  mounted() {
    getRegulatoryTrainingInfo().then(res => {
      this.info = res.data;
    });
    getRotationProgress().then(res => {
      this.admission = res.data.admissionManagementRespVO;
      this.rotation = res.data.rotationManagementRespVO;
      this.midTerm = res.data.midTermExamineRespVO;
      this.grad = res.data.gradRespVO;
    });
  },
}
</script>

<style lang="scss" scoped>
.part-title {
  font-size: 18px;
  margin-bottom: 20px;
  color: #000;
  border-bottom: 1px dashed #efefef;
  padding-bottom: 10px;
}

.info-form {
  margin-bottom: 30px;
  ::v-deep .el-form-item {
    margin-bottom: 10px;
  }
}

.process-part {
  display: flex;
  flex-wrap: nowrap;
  gap: 20px;

  .flex-item {
    flex-basis: 25%;
  }

  .card-title {
    font-size: 15px;
    margin-bottom: 8px;
    color: #111159;
  }

  .color-line {
    width: 100%;
    height: 4px;
    border-radius: 1px;
    margin-bottom: 12px;
    &.color-1 {
      background: #4F6479;
    }
    &.color-2 {
      background: #38A191;
    }
    &.color-3 {
      background: #F1A393;
    }
    &.color-4 {
      background: #3A73E9;
    }
  }

  .content-card {
    min-height: 200px;
    border: 1px solid #f8f8f8;
    border-radius: 8px;
    box-shadow: 0 0 8px rgba(0,0,0,.02);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    line-height: 2;

    ::v-deep .el-progress {
      width: 106%;
      margin-bottom: 5px;
    }
  }

  .flex-space {
    display: flex;
    justify-content: space-between;
  }

  .link {
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
    &.color-1 {
      color: #4F6479;
    }
    &.color-2 {
      color: #38A191;
    }
  }
}
</style>
