<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="方案名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入培训方案名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          clearable
          size="small"
          @change="handleQueryStudentTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          clearable
          filterable
        >
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态" prop="publishStatus">
        <el-select
          v-model="queryParams.publishStatus"
          placeholder="请选择发布状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_PUBLISH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:standard-scheme:create']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:standard-scheme:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="培训方案名称" align="center" prop="name" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="培训专业"
        align="center"
        prop="majorName"
      ></el-table-column>
      <el-table-column
        label="轮转时长（总时间）"
        align="center"
        prop="rotationTime"
      >
        <template v-slot="scope">
          {{ scope.row.rotationTime }}
          <dict-tag
            :type="DICT_TYPE.ROTATION_CYCLE"
            :value="scope.row.rotationCycle"
          />
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center" prop="publishStatus">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_PUBLISH_STATUS"
            :value="scope.row.publishStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.publishStatus === 0">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['rotation:standard-scheme:update']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['rotation:standard-scheme:delete']"
              >删除</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-position"
              @click="handleIssue(scope.row)"
              v-hasPermi="['rotation:standard-scheme:issue']"
              >发布</el-button
            >
          </template>
          <template v-else>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-remove-outline"
              @click="handleUnpublish(scope.row)"
              v-hasPermi="['rotation:standard-scheme:update']"
              >取消发布</el-button
            >
          </template>
          <el-dropdown
            v-hasPermi="['rotation:standard-scheme:more']"
            style="margin-left: 10px"
          >
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right"
              >更多</el-button
            >
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleCopy(scope.row)"
                >复制方案</el-dropdown-item
              >
              <el-dropdown-item @click.native="handleRule(scope.row)"
                >轮转规则</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        :disabled="form.isView"
      >
        <el-form-item label="方案名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入培训方案名称"
            ref="nameInput"
          />
        </el-form-item>
        <el-form-item
          label="学员类型"
          prop="studentType"
          style="display: inline-block; width: 51%"
        >
          <el-select
            v-model="form.studentType"
            placeholder="请选择学员类型"
            @change="handleFormStudentTypeChange"
          >
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="培训专业"
          prop="major"
          style="display: inline-block; width: 49%"
        >
          <el-select
            v-model="form.major"
            filterable
            placeholder="请选择培训专业"
          >
            <el-option
              v-for="item in formMajorList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="方案类型"
          prop="schemeType"
          style="display: inline-block; width: 51%"
        >
          <el-select v-model="form.schemeType" placeholder="请选择方案类型">
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_SCHEME_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="轮转周期"
          prop="rotationCycle"
          style="display: inline-block; width: 49%"
        >
          <el-radio-group v-model="form.rotationCycle">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_CYCLE)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="入院管理" prop="admissionManagementConfigStatus">
          <el-radio-group v-model="form.admissionManagementConfigStatus">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label=""
          prop="admissionManagementItems"
          v-show="form.admissionManagementConfigStatus === 1"
          style="margin-top: -10px"
        >
          <el-checkbox-group v-model="form.admissionManagementItems">
            <el-checkbox
              v-for="dict in this.getDictDatas(
                DICT_TYPE.ROTATION_ADMISSION_MANAGEMENT
              )"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          label="入科教育"
          prop="enrollmentEduConfigStatus"
          style="display: inline-block; width: 51%"
        >
          <el-radio-group v-model="form.enrollmentEduConfigStatus">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="关联出科"
          prop="enrollmentEduGraduationRelated"
          style="display: inline-block; width: 49%"
          v-if="form.enrollmentEduConfigStatus === 1"
        >
          <el-switch v-model="form.enrollmentEduGraduationRelated"></el-switch>
        </el-form-item>
        <el-form-item
          label=""
          prop="enrollmentEduItems"
          v-show="form.enrollmentEduConfigStatus === 1"
          style="margin-top: -10px"
        >
          <el-checkbox-group v-model="form.enrollmentEduItems">
            <el-checkbox
              v-for="dict in this.getDictDatas(
                DICT_TYPE.ROTATION_ENROLLMENT_EDU
              )"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          prop="rotationItemsConfigStatus"
          style="display: inline-block; width: 51%"
        >
          <span slot="label" style="position: relative"
            >轮转数据
            <span style="position: absolute; right: 0; top: 12px; width: 100%"
              >项配置</span
            >
          </span>
          <el-radio-group v-model="form.rotationItemsConfigStatus">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="关联出科"
          prop="rotationItemsGraduationRelated"
          style="display: inline-block; width: 49%"
          v-if="form.rotationItemsConfigStatus === 1"
        >
          <el-switch v-model="form.rotationItemsGraduationRelated"></el-switch>
        </el-form-item>
        <el-form-item
          label=""
          prop="rotationItems"
          v-show="form.rotationItemsConfigStatus === 1"
        >
          <el-checkbox-group v-model="form.rotationItems">
            <el-checkbox
              v-for="dict in this.getDictDatas(DICT_TYPE.ROTAION_ITEM)"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          label="活动配置"
          prop="activeConfigStatus"
          style="display: inline-block; width: 51%"
        >
          <el-radio-group v-model="form.activeConfigStatus">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="关联出科"
          prop="activeGraduationRelated"
          style="display: inline-block; width: 49%"
          v-if="form.activeConfigStatus === 1"
        >
          <el-switch v-model="form.activeGraduationRelated"></el-switch>
        </el-form-item>
        <el-form-item
          label=""
          prop="activeItems"
          v-show="form.activeConfigStatus === 1"
          style="margin-top: -10px"
        >
          <el-checkbox-group v-model="form.activeItems">
            <el-checkbox
              v-for="dict in this.getDictDatas(DICT_TYPE.ACTIVE_ITEM)"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>

        <!-- <el-form-item label="评价配置" prop="appraiseConfigStatus" style="display: inline-block; width: 51%">
          <el-radio-group v-model="form.appraiseConfigStatus">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                      :key="dict.value" :label="parseInt(dict.value)">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="关联出科" prop="appraiseGraduationRelated" style="display: inline-block; width: 49%" v-if="form.appraiseConfigStatus === 1">
          <el-switch v-model="form.appraiseGraduationRelated"></el-switch>
        </el-form-item>
        <el-form-item label="" prop="appraiseItems" v-show="form.appraiseConfigStatus === 1" style="margin-top: -10px;">
          <el-checkbox-group v-model="form.appraiseItems">
            <el-checkbox v-for="dict in this.getDictDatas(DICT_TYPE.APPRAISE_ITEM)"
                         :key="dict.value" :label="dict.value">{{dict.label}}</el-checkbox>
          </el-checkbox-group>
        </el-form-item> -->
        <el-form-item
          label="评价配置"
          prop="appraiseConfigStatus"
          style="display: inline-block; width: 51%"
        >
          <el-radio-group v-model="form.appraiseConfigStatus">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label=""
          prop="appraiseItems"
          v-show="form.appraiseConfigStatus === 1"
          style="margin-top: -10px"
        >
          <el-checkbox-group v-model="form.appraiseItems">
            <span
              class="examine-item"
              v-for="dict in this.getDictDatas(DICT_TYPE.APPRAISE_ITEM)"
              :key="dict.value"
            >
              <el-checkbox
                :key="dict.value"
                :label="dict.value"
                @change="handleAppraiseItemChange(dict.value, $event)"
                >{{ dict.label }}</el-checkbox
              >
              <el-switch
                v-show="(form.appraiseItems || []).indexOf(dict.value) > -1"
                :value="
                  (form.appraiseItemsGraduationRelatedItems || []).indexOf(
                    dict.value
                  ) > -1
                "
                active-text="关联出科"
                @change="handleAppraiseItemRelatedChange(dict.value, $event)"
              ></el-switch>
            </span>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="考核配置" prop="examineConfigStatus">
          <el-radio-group v-model="form.examineConfigStatus">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label=""
          prop="examineItems"
          v-show="form.examineConfigStatus === 1"
          style="margin-top: -10px"
        >
          <el-checkbox-group v-model="form.examineItems">
            <span
              class="examine-item"
              v-for="dict in this.getDictDatas(DICT_TYPE.EXAMINE_ITEM)"
              :key="dict.value"
            >
              <el-checkbox
                :label="dict.value"
                @change="handleExamineItemChange(dict.value, $event)"
                >{{ dict.label }}</el-checkbox
              >
              <el-switch
                v-show="(form.examineItems || []).indexOf(dict.value) > -1"
                :value="
                  (form.examineItemsGraduationRelatedItems || []).indexOf(
                    dict.value
                  ) > -1
                "
                active-text="关联出科"
                @change="handleExamineItemRelatedChange(dict.value, $event)"
              ></el-switch>
            </span>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="出科管理" prop="graduationConfigStatus">
          <el-radio-group v-model="form.graduationConfigStatus">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label=""
          v-show="form.graduationConfigStatus === 1"
          style="margin-top: -10px"
        >
          <el-form-item
            label="出科小结最少字数限制"
            label-width="160px"
            style="margin-bottom: 10px"
          >
            <el-input-number
              v-model="form.graduationPersonalSummaryMinWord"
              :min="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="出科小结提示" label-width="160px">
            <el-input
              type="textarea"
              v-model="form.graduationPersonalSummaryTip"
              :maxlength="200"
            ></el-input>
          </el-form-item>
        </el-form-item>
        <el-form-item label="中期考核" prop="midTermExamineConfigStatus">
          <el-radio-group v-model="form.midTermExamineConfigStatus">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label=""
          prop="midTermExamineItems"
          v-show="form.midTermExamineConfigStatus === 1"
          style="margin-top: -10px"
        >
          <el-checkbox-group v-model="form.midTermExamineItems">
            <el-checkbox
              v-for="dict in this.getDictDatas(
                DICT_TYPE.ROTATION_MID_TERM_EXAMINE
              )"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="结业管理" prop="gradConfigStatus">
          <el-radio-group v-model="form.gradConfigStatus">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label=""
          prop="gradItems"
          v-show="form.gradConfigStatus === 1"
          style="margin-top: -10px"
        >
          <el-checkbox-group v-model="form.gradItems">
            <el-checkbox
              v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_GRAD)"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="方案说明">
          <editor
            v-model="form.description"
            :min-height="192"
            :readOnly="form.isView"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!form.isView">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createStandardScheme,
  updateStandardScheme,
  deleteStandardScheme,
  getStandardScheme,
  getStandardSchemePage,
  exportStandardSchemeExcel,
  publishStandardScheme,
  unpublishStrandardScheme,
} from "@/api/rotation/standardScheme";
import { getSimpleMajorList } from "@/api/system/major";
import Editor from "@/components/Editor";

export default {
  name: "StandardScheme",
  components: {
    Editor,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 标准方案列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        studentType: null,
        major: null,
        publishStatus: null,
      },
      // 查询培训专业列表
      queryMajorList: [],
      // 表单参数
      form: {},
      // 表单培训专业列表
      formMajorList: [],
      // 表单校验
      rules: {
        name: [
          { required: true, message: "标准方案名称不能为空", trigger: "blur" },
        ],
        studentType: [
          { required: true, message: "学员类型不能为空", trigger: "change" },
        ],
        major: [
          { required: true, message: "培训专业不能为空", trigger: "change" },
        ],
        schemeType: [
          { required: true, message: "方案类型不能为空", trigger: "change" },
        ],
        rotationCycle: [
          { required: true, message: "轮转周期不能为空", trigger: "change" },
        ],
        admissionManagementConfigStatus: [
          { required: true, message: "入院管理配置不能为空", trigger: "blur" },
        ],
        enrollmentEduConfigStatus: [
          { required: true, message: "入科教育配置不能为空", trigger: "blur" },
        ],
        enrollmentEduGraduationRelated: [
          { required: true, message: "是否关联出科不能为空", trigger: "blur" },
        ],
        rotationItemsConfigStatus: [
          {
            required: true,
            message: "轮转数据项配置不能为空",
            trigger: "blur",
          },
        ],
        rotationItemsGraduationRelated: [
          { required: true, message: "是否关联出科不能为空", trigger: "blur" },
        ],
        activeConfigStatus: [
          { required: true, message: "活动配置状态不能为空", trigger: "blur" },
        ],
        activeGraduationRelated: [
          { required: true, message: "是否关联出科不能为空", trigger: "blur" },
        ],
        appraiseConfigStatus: [
          { required: true, message: "评价配置状态不能为空", trigger: "blur" },
        ],
        appraiseGraduationRelated: [
          { required: true, message: "是否关联出科不能为空", trigger: "blur" },
        ],
        examineConfigStatus: [
          { required: true, message: "考核配置状态不能为空", trigger: "blur" },
        ],
        graduationConfigStatus: [
          { required: true, message: "出科管理状态不能为空", trigger: "blur" },
        ],
        midTermExamineConfigStatus: [
          { required: true, message: "中期考核状态不能为空", trigger: "blur" },
        ],
        gradConfigStatus: [
          { required: true, message: "结业管理状态不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getStandardSchemePage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        studentType: undefined,
        major: undefined,
        schemeType: undefined,
        rotationCycle: 1,
        admissionManagementConfigStatus: 1,
        admissionManagementItems: [],
        enrollmentEduConfigStatus: 1,
        enrollmentEduGraduationRelated: false,
        enrollmentEduItems: [],
        rotationItemsConfigStatus: 1,
        rotationItemsGraduationRelated: false,
        rotationItems: [],
        activeConfigStatus: 1,
        activeGraduationRelated: false,
        activeItems: [],
        appraiseConfigStatus: 1,
        // appraiseGraduationRelated: false,
        appraiseItemsGraduationRelatedItems: [],
        appraiseItems: [],
        examineConfigStatus: 1,
        examineItems: [],
        examineItemsGraduationRelatedItems: [],
        graduationConfigStatus: 1,
        graduationPersonalSummaryMinWord: 0,
        graduationPersonalSummaryTip: "",
        midTermExamineConfigStatus: 1,
        midTermExamineItems: [],
        gradConfigStatus: 1,
        gradItems: [],
        publishStatus: 1,
        description: undefined,
      };
      this.resetForm("form");
    },
    /** 表单内,分隔字符串和数组转换 */
    dealFormStringToArrayTransform(form, reverse) {
      const dealValue = (value) =>
        reverse ? value.join(",") : value ? value.split(",") : [];
      form.admissionManagementItems = dealValue(form.admissionManagementItems);
      form.enrollmentEduItems = dealValue(form.enrollmentEduItems);
      form.rotationItems = dealValue(form.rotationItems);
      form.activeItems = dealValue(form.activeItems);
      form.appraiseItems = dealValue(form.appraiseItems);
      form.examineItems = dealValue(form.examineItems);
      form.appraiseItemsGraduationRelatedItems = dealValue(
        form.appraiseItemsGraduationRelatedItems
      );
      form.examineItemsGraduationRelatedItems = dealValue(
        form.examineItemsGraduationRelatedItems
      );
      form.midTermExamineItems = dealValue(form.midTermExamineItems);
      form.gradItems = dealValue(form.gradItems);
      return form;
    },
    /** 查询学员类型改变 */
    handleQueryStudentTypeChange(value) {
      this.queryParams.major = null;
      this.queryMajorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.queryMajorList = res.data;
      });
    },
    /** 表单学员类型改变 */
    handleFormStudentTypeChange(value) {
      this.form.major = undefined;
      this.formMajorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.formMajorList = res.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加标准方案";
      this.$nextTick(() => this.$refs.nameInput.focus());
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getStandardScheme(id).then((response) => {
        this.form = this.dealFormStringToArrayTransform(response.data);
        this.open = true;
        this.title = "修改标准方案";
        getSimpleMajorList({ studentType: this.form.studentType }).then(
          (res) => {
            this.formMajorList = res.data;
          }
        );
        this.$nextTick(() => this.$refs.nameInput.focus());
      });
    },
    /** 评价配置项切换 */
    handleAppraiseItemChange(target, value) {
      if (!value) {
        const index =
          this.form.appraiseItemsGraduationRelatedItems.indexOf(target);
        if (index > -1) {
          this.form.appraiseItemsGraduationRelatedItems.splice(index, 1);
        }
      }
    },
    /** 评价配置关联出科项切换 */
    handleAppraiseItemRelatedChange(target, value) {
      if (value) {
        this.form.appraiseItemsGraduationRelatedItems.push(target);
      } else {
        const index =
          this.form.appraiseItemsGraduationRelatedItems.indexOf(target);
        if (index > -1) {
          this.form.appraiseItemsGraduationRelatedItems.splice(index, 1);
        }
      }
    },

    /** 考核配置项切换 */
    handleExamineItemChange(target, value) {
      if (!value) {
        const index =
          this.form.examineItemsGraduationRelatedItems.indexOf(target);
        if (index > -1) {
          this.form.examineItemsGraduationRelatedItems.splice(index, 1);
        }
      }
    },
    /** 考核配置关联出科项切换 */
    handleExamineItemRelatedChange(target, value) {
      if (value) {
        this.form.examineItemsGraduationRelatedItems.push(target);
      } else {
        const index =
          this.form.examineItemsGraduationRelatedItems.indexOf(target);
        if (index > -1) {
          this.form.examineItemsGraduationRelatedItems.splice(index, 1);
        }
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const form = this.dealFormStringToArrayTransform(
          { ...this.form },
          true
        );
        // 修改的提交
        if (form.id != null) {
          updateStandardScheme(form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createStandardScheme(form).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除标准方案为"' + row.name + '"的数据项?')
        .then(function () {
          return deleteStandardScheme(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有标准方案数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportStandardSchemeExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "标准方案.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    /** 发布按钮操作 */
    handleIssue(row) {
      this.$modal
        .confirm('是否确认发布标准方案为"' + row.name + '"的数据项?')
        .then(function () {
          return publishStandardScheme(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("发布成功");
        })
        .catch(() => {});
    },
    /** 规则按钮操作 */
    handleRule(row) {
      this.$router.push({
        path: "rule",
        query: { id: row.id },
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id;
      getStandardScheme(id).then((response) => {
        this.form = this.dealFormStringToArrayTransform(response.data);
        this.form.isView = true;
        this.open = true;
        this.title = "查看标准方案";
      });
    },
    /** 取消发布 */
    handleUnpublish(row) {
      this.$modal
        .confirm('是否确认取消发布标准方案"' + row.name + "?")
        .then(function () {
          return unpublishStrandardScheme(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("取消发布成功");
        })
        .catch(() => {});
    },
    /** 复制方案 */
    handleCopy(row) {},
  },
};
</script>

<style lang="scss" scoped>
.examine-item {
  display: inline-flex;
  align-items: center;
  margin-right: 30px;

  ::v-deep .el-switch {
    margin-left: 15px;
  }
}
</style>
