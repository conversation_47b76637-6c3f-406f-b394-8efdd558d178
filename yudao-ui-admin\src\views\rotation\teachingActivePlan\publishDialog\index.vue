<template>
  <el-dialog
    :title="publishDialogTitle"
    :visible="open"
    width="800px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="110px">
      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="教学活动名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入教学活动名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :md="10" :lg="10" :xl="10">
          <el-form-item label="活动类型" prop="activeType">
            <el-select
              v-model="form.activeType"
              filterable
              placeholder="请选择活动类型"
              @change="handleQueryPaperOptions"
            >
              <el-option
                v-for="dict in this.getDictDatas(
                  DICT_TYPE.ROTATION_ACTIVE_TYPE
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="14" :lg="14" :xl="14">
          <el-form-item label="培训科室" prop="departmentId" label-width="80px">
            <el-select
              v-model="form.departmentId"
              filterable
              placeholder="请选择培训科室"
              @change="getUserworkData"
              style="width: 100%"
            >
              <el-option
                v-for="item in departmentPermissionOptions"
                :key="parseInt(item.id)"
                :label="item.name"
                :value="parseInt(item.id)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :md="10" :lg="10" :xl="10">
          <el-form-item label="培训人" prop="speakerUserId">
            <el-select
              v-model="form.speakerUserId"
              filterable
              placeholder="请选择培训人"
              @change="getStudentTypesList"
            >
              <el-option
                v-for="user in userWorkerOptions"
                :key="user.id"
                :label="user.nickname"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="14" :lg="14" :xl="14">
          <el-form-item
            label="培训时间"
            prop="timeValuePlan"
            label-width="80px"
          >
            <el-date-picker
              style="width: 100%"
              v-model="form.timeValuePlan"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :md="10" :lg="10" :xl="10">
          <el-form-item label="开展方式" prop="developWay">
            <el-select
              v-model="form.developWay"
              filterable
              placeholder="请选择开展方式"
            >
              <el-option
                v-for="dict in this.getDictDatas(
                  DICT_TYPE.ROTATION_DEVELOP_WAY
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :md="14" :lg="14" :xl="14">
          <el-form-item label="培训时间" prop="timeValue" label-width="80px">
            <el-date-picker
              style="width: 100%"
              v-model="form.timeValue"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              @input="timeValueChange"
            />
          </el-form-item>
        </el-col> -->
      </el-row>

      <el-row :gutter="10" v-if="form.developWay == 2">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="参与方式" prop="joinWay" label-width="110px">
            <el-input
              v-model="form.joinWay"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="通知对象" prop="studentTypes">
            <el-select
              v-model="form.studentTypes"
              multiple
              filterable
              placeholder="请选择通知对象"
              style="width: 100%"
            >
              <el-option
                v-for="user in studentTypesOptions"
                :key="user.value"
                :label="user.label"
                :value="user.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="活动试卷" prop="paperId">
            <el-select
              v-model="form.paperId"
              filterable
              clearable
              placeholder="请选择活动试卷"
              style="width: 100%"
            >
              <el-option
                v-for="user in paperOptions"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="学员专业" prop="studentMajors">
            <el-select
              v-model="form.studentMajors"
              placeholder="请选择学员专业"
              multiple
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in queryMajorList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  <dict-tag
                    :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                    :value="item.studentType"
                  />
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="学员年级" prop="grades">
            <el-select
              v-model="form.grades"
              multiple
              filterable
              placeholder="请选择年级"
              style="width: 100%"
            >
              <el-option
                v-for="grade in studentGradeList"
                :key="grade"
                :label="grade"
                :value="grade"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="活动地点" prop="adress">
            <el-input
              v-model="form.adress"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="活动课件">
            <FileUpload
              v-model="form.coursewares"
              :limit="999"
              :fileSize="50"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <!-- <el-button type="primary" @click="submitForm('save')">保 存</el-button> -->
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm('savePublish')"
        >发布</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import {
  createPublishTeachingActive,
  getStudentTypeListByUser,
} from "@/api/rotation/teachingActivePlan";
import { getUserWorkerPermissionList } from "@/api/system/userWorker";
import { getStudentTypes } from "@/api/system/user";
import { getSimpleMajorList } from "@/api/system/major";
import { getStudentGradeList } from "@/api/system/userStudent";
import { getDepartmentPermissionList } from "@/api/system/department";
import { getAppraiseActiveNote } from "@/api/rotation/appraiseActive";
import { getTeachingActivityPaper } from '@/api/exam/paper'

export default {
  name: "PublishDialog",
  components: {
    FileUpload,
  },
  props: {
    publishDialogTitle: {
      type: String,
    },
    openPublish: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {},
    },
    curRow: {
      type: Object,
      default: () => {},
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      open: this.openPublish,
      userWorkerOptions: [],
      departmentPermissionOptions: [],
      studentTypesOptions: [],
      studentGradeList: [],
      queryMajorList: [],
      pickerOptions: {},
      paperOptions: [],
      // 表单参数
      form: {
        studentTypes: [],
        paperId: undefined,
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "教学活动名称不能为空", trigger: "blur" },
        ],
        activeType: [
          { required: true, message: "活动类型不能为空", trigger: "change" },
        ],
        departmentId: [
          { required: true, message: "培训科室不能为空", trigger: "change" },
        ],
        speakerUserId: [
          { required: true, message: "培训人不能为空", trigger: "blur" },
        ],
        studentTypes: [
          {
            type: "array",
            required: true,
            message: "学员类型不能为空",
            trigger: "change",
          },
        ],
        timeValuePlan: [
          { required: true, message: "请选择培训时间", trigger: "change" },
        ],
        developWay: [
          { required: true, message: "开展方式不能为空", trigger: "change" },
        ],
        joinWay: [
          { required: true, message: "参与方式不能为空", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    openPublish(newVal) {
      this.open = newVal;
    },
  },
  created() {
    this.getPermissionDepartment();
    // this.getStudentTypesList();
    getStudentGradeList().then((res) => {
      this.studentGradeList = res.data;
    });
    getSimpleMajorList().then((res) => {
      this.queryMajorList = res.data;
    });
  },
  methods: {
    cancel() {
      this.$emit("update:openPublish", false);
    },
    // timeValueChange(values) {
    //   console.log("timeValueChange====", values);
    //   this.form.startTime = undefined;
    //   this.form.endTime = undefined;
    //   this.form.timeValue = null;
    //   if (values) {
    //     this.form.startTime = values[0];
    //     this.form.endTime = values[1];
    //     this.form.timeValue = values;
    //   }
    //   this.$forceUpdate();
    // },
    getPermissionDepartment() {
      // 获得科室列表
      const params = { component: "rotation/teachingActivePlan/index" };
      getDepartmentPermissionList(params).then((res) => {
        // 处理 roleOptions 参数
        this.departmentPermissionOptions = [];
        this.departmentPermissionOptions.push(...res.data);
      });
    },
    getStudentTypesList(speakerUserId) {
      const params = {
        userId: speakerUserId,
        component: "rotation/teachingActivePublish/index",
      };
      getStudentTypeListByUser(params).then((res) => {
        this.studentTypesOptions = [];
        this.studentTypesOptions.push(...res.data);
      });
    },
    handleQueryPaperOptions() {
      this.form.paperId = undefined;
      const { departmentId, activeType } = this.form;
      if (!departmentId || !activeType) {
        this.paperOptions = [];
        return;
      }
      getTeachingActivityPaper({ departmentId, teachingActiveType: activeType }).then(res => {
        this.paperOptions = res.data;
      });
    },
    async getUserworkData(val) {
      this.handleQueryPaperOptions();
      this.form.speakerUserId = "";
      const params = {
        departmentId: val,
        component: "rotation/teachingActivePublish/index",
      };
      const { data } = await getUserWorkerPermissionList(params);
      this.userWorkerOptions = data || [];
    },
    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = { ...this.form };
        console.log("this.form===", this.form);
        params.planStartTime = this.form.timeValuePlan[0];
        params.planEndTime = this.form.timeValuePlan[1];
        params.studentTypes = params.studentTypes
          ? params.studentTypes.join(",")
          : "";
        params.studentMajors = params.studentMajors
          ? params.studentMajors.join(",")
          : "";
        params.grades = params.grades ? params.grades.join(",") : "";
        params.coursewares = params.coursewares
          ? JSON.stringify(params.coursewares)
          : "";
        delete params.timeValue;
        delete params.timeValuePlan;

        // 保存发布提交
        if (type === "savePublish") {
          const submitFn = () =>
            createPublishTeachingActive(params).then((response) => {
              this.$modal.msgSuccess("发布成功");
              this.$emit("refresh");
              this.cancel();
            });
          submitFn();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
