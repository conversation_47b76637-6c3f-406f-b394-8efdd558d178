<template>
  <el-dialog
    :title="curRow.releaseYear + '年度完成情况'"
    :visible="visible"
    width="900px"
    v-dialogDrag
    append-to-body
    @close="$emit('update:visible', $event)"
  >
    <div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="学员姓名" prop="nickname">
          <el-input
            v-model="queryParams.nickname"
            placeholder="请输入职工姓名"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 120"
          />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="queryParams.username"
            placeholder="请输入职工工号"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 120"
          />
        </el-form-item>
        <el-form-item label="审核状态" prop="summaryStatus">
          <el-select
            v-model="queryParams.summaryStatus"
            placeholder="请选择审核状态"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.SUMMARY_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetWorkerQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <div>
        <el-table v-loading="queryLoading" :data="tableList">
          <el-table-column
            label="姓名"
            width="120"
            align="center"
            prop="nickname"
          />
          <el-table-column
            label="用户名"
            width="120"
            align="center"
            prop="username"
          />
          <el-table-column
            label="学员类型"
            width="120"
            align="center"
            prop="studentType"
          >
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                :value="scope.row.studentType"
              />
            </template>
          </el-table-column>
          <el-table-column label="专业" align="center" prop="major" />
          <el-table-column label="年级" align="center" prop="grade" />
          <el-table-column label="审核状态" align="center" prop="summaryStatus">
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.SUMMARY_STATUS"
                :value="scope.row.summaryStatus"
              />
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="() => getList()"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getCompletePage } from "@/api/rotation/annualSummaryConfig";

export default {
  name: "annualSummaryViewDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    curRow: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      queryLoading: false,
      total: 0,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: "",
        username: "",
        summaryStatus: "",
      },
      userIds: [],
      userTypes: [],
      tableList: [],
      roleOptions: [],
    };
  },
  watch: {
    visible(value) {
      if (value) {
        this.handleQuery();
      }
    },
  },
  created() {},
  methods: {
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    resetWorkerQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    getList() {
      this.queryLoading = true;
      const params = {
        ...this.queryParams,
        id: this.curRow.id,
      };
      getCompletePage(params).then((response) => {
        this.tableList = response.data.list || [];
        this.queryLoading = false;
        this.total = response.data.total;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
