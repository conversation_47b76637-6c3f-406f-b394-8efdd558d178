import request from '@/utils/request'

// 创建考试试题
export function createQuestion(data) {
  return request({
    url: '/exam/question/create',
    method: 'post',
    data: data
  })
}

// 更新考试试题
export function updateQuestion(data) {
  return request({
    url: '/exam/question/update',
    method: 'put',
    data: data
  })
}

// 删除考试试题
export function deleteQuestion(id) {
  return request({
    url: '/exam/question/delete?id=' + id,
    method: 'delete'
  })
}

// 获得考试试题
export function getQuestion(id) {
  return request({
    url: '/exam/question/get?id=' + id,
    method: 'get'
  })
}

// 获得考试试题分页
export function getQuestionPage(query) {
  return request({
    url: '/exam/question/page',
    method: 'get',
    params: query
  })
}

// 导出考试试题 Excel
export function exportQuestionExcel(query) {
  return request({
    url: '/exam/question/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得考试知识点树
export function getKnowledgePointTree() {
  return request({
    url: '/exam/knowledge-point/get-tree',
    method: 'get',
  })
}

// 获得考试试题数量
export function getQuestionCount(data) {
  return request({
    url: '/exam/question/get-count',
    method: 'post',
    data,
  })
}

// 获得考试试题列表
export function getQuestionList(ids) {
  return request({
    url: '/exam/question/list',
    method: 'get',
    params: { ids },
  })
}

// 获得试卷考试试题
export function getQuestionsByQuestionIds(questionIds) {
  return request({
    url: '/exam/question/get-questions-by-questionIds',
    method: 'get',
    params: { questionIds },
  })
}

// 获得固定试卷考试试题分页
export function getQuestionPageFixPaper(query) {
  return request({
    url: '/exam/question/page-fix-paper',
    method: 'get',
    params: query,
  })
}

// 获得试卷考试试题
export function getQuestionsByPaperId(paperId) {
  return request({
    url: '/exam/question/get-questions-by-paperId',
    method: 'get',
    params: { paperId },
  })
}

// 获得试卷批量替换考试试题
export function getBatchReplaceQuestions(query) {
  return request({
    url: '/exam/question/get-batch-replace-questions',
    method: 'get',
    params: query,
  })
}
