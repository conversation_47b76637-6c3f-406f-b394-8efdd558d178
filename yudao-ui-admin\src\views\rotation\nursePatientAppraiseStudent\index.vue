<template>
  <div class="app-container">
    <el-tabs type="card">
      <el-tab-pane label="评价二维码">
        <el-form>
          <el-form-item label="轮转科室">
            <el-select style="width: 595px" v-model="currentDept" multiple filterable @change="queryQRCode">
              <el-option
                v-for="dept in deptList"
                :key="dept.id"
                :value="dept.id"
                :label="dept.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <div class="QR-codes">
          <div class="QR-item" v-loading="loading">
            <div class="QR-title">护士评价学员</div>
            <div class="QR-image">
              <img :style="{ opacity: nurseQR ? 1 : 0 }" :src="nurseQR" alt="二维码">
            </div>
            <div>请扫描二维码对科室学员进行评价</div>
            <el-button size="small" @click="handleDownloadQR('nurse')">下载二维码</el-button>
          </div>

          <div class="QR-item" v-loading="loading">
            <div class="QR-title">患者评价学员</div>
            <div class="QR-image">
              <img :style="{ opacity: patientQR ? 1 : 0 }" :src="patientQR" alt="二维码">
            </div>
            <div>请扫描二维码对科室学员进行评价</div>
            <el-button size="small" @click="handleDownloadQR('patient')">下载二维码</el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <div class="download-QR" ref="downloadQR">
      <div class="QR-title">{{ download === 'nurse' ? '护士' : '患者' }}评价学员</div>
      <div class="QR-dept">{{ deptName }}</div>
      <img :src="download === 'nurse' ? nurseQR : patientQR" alt="二维码">
      <div class="QR-desc">请扫描二维码对科室学员进行评价</div>
    </div>
  </div>
</template>

<script>
import { getRotationDepartmentPermissionList } from '@/api/system/department'
import { getMulQRCode } from '@/api/rotation/nursepatientappraise'
import html2canvas from 'html2canvas';

export default {
  name: 'NursePatientAppraiseStudent',
  data() {
   return {
     deptList: [],
     currentDept: [],
     nurseQR: '',
     patientQR: '',
     download: 'nurse',
     defineQRName: '',
     loading: false,
   }
  },
  computed: {
    deptName() {
      return this.currentDept.length > 1 ? this.defineQRName :
        this.deptList.find(item => item.id === this.currentDept[0])?.name || ''
    }
  },
  methods: {
    queryQRCode() {
      if (this.currentDept.length === 0) {
        this.$message.warning('轮转科室必须选择一个')
        this.currentDept = [this.deptList[0]?.id]
      }
      this.loading = true
      return getMulQRCode({
        rotationDepartmentIds: this.currentDept.join(','),
        url: process.env.VUE_APP_BASE_API + '/h5/#/rotation/pages/nursePatientAppraiseStudentList/index',
        qrcodeWidth: 2000,
        qrcodeHeight: 2000,
      }).then(res => {
        const base64Prefix = 'data:image/jpeg;base64,'
        this.nurseQR = base64Prefix + (res.data?.nurseQrcode || '')
        this.patientQR = base64Prefix + (res.data?.patientQrcode || '')
        this.loading = false
      })
    },
    handleDownloadQR(type) {
      this.download = type
      this.defineQRName = ''
      if (this.currentDept.length > 1) {
        this.$prompt('请给轮转科室集合命名（20字内）', '提示', {
          inputPattern: /^.{1,20}$/,
          inputErrorMessage: '名字不能为空且小于20个字',
        }).then(({ value }) => {
          this.defineQRName = value
          this.downloadQR()
        })
      } else {
        this.downloadQR()
      }
    },
    downloadQR() {
      this.$nextTick(() => {
        html2canvas(this.$refs.downloadQR).then((canvas) => {
          const url = canvas.toDataURL('image/png')
          const a = document.createElement('a')
          document.body.appendChild(a)
          a.href= url
          a.download = this.download === 'nurse' ? '护士评价学员' : '患者评价学员'
          a.click()
        })
      })
    },
  },
  created() {
    getRotationDepartmentPermissionList({
      component: "rotation/nursePatientAppraiseStudent/index"
    }).then(res => {
      this.deptList = res.data || []
      this.currentDept = [this.deptList[0]?.id]
      this.queryQRCode()
    })
  },
}
</script>

<style lang="scss" scoped>
.QR-codes {
  display: flex;
}

.QR-item {
  border: 1px solid #409EFF;
  margin-right: 80px;
  text-align: center;
  padding: 20px;
  font-size: 15px;
  line-height: 1.8;

  .QR-image, img {
    width: 250px;
    height: 250px;
  }

  ::v-deep .el-button {
    margin-top: 5px;
  }

  .QR-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
  }
}

.download-QR {
  position: fixed;
  top: 0;
  left: -9999px;
  width: 2480px;
  height: 3508px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .QR-title {
    font-size: 140px;
    margin-bottom: 60px;
    font-weight: bold;
  }

  .QR-dept {
    font-size: 100px;
    margin-bottom: 200px;
  }

  .QR-desc {
    font-size: 90px;
    margin-top: 90px;
  }
}
</style>
