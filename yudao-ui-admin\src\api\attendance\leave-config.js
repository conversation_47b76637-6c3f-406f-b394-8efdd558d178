import request from '@/utils/request'

// 创建请假配置
export function saveLeaveConfig(data) {
  return request({
    url: '/attendance/leave-config/create',
    method: 'post',
    data: data
  })
}

// 删除请假配置
export function deleteLeaveConfig(id) {
  return request({
    url: '/attendance/leave-config/delete',
    method: 'delete',
    params: { id }
  })
}

// 获得请假配置
export function getLeaveConfig(id) {
  return request({
    url: '/attendance/leave-config/get',
    method: 'get',
    params: { id }
  })
}

// 获得请假配置列表
export function getLeaveConfigList() {
  return request({
    url: '/attendance/leave-config/list',
    method: 'get',
  })
}

// 更新请假配置
export function updateLeaveConfig(data) {
  return request({
    url: '/attendance/leave-config/update',
    method: 'put',
    data: data
  })
}
