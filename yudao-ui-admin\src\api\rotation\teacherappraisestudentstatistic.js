import request from '@/utils/request'

// 获得学员评价带教统计分页
export function getTeacherappraisestudentstatistic(query) {
  return request({
    url: '/rotation/teacherappraisestudentstatistic/page',
    method: 'get',
    params: query
  })
}

// 获得学员评价带教详情分页
export function getAppraiseStudentList(query) {
    return request({
      url: '/rotation/teacherappraisestudentstatistic/page-student',
      method: 'get',
      params: query
    })
}

// 导出带教评价学员统计数据
export function exportTeacherAppraiseStudentStatistic(query) {
  return request({
    url: '/rotation/teacherappraisestudentstatistic/export',
    method: 'get',
    responseType: 'blob',
    params: query
  })
}


