<template>
  <div class="app-container talkAudit-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          clearable
          size="small"
        >
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          size="small"
          clearable
        >
          <el-option
            v-for="grade in gradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="queryParams.isToAudit">待审核</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5"></el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="学员姓名" align="center" prop="nickname" />
      <el-table-column label="培训专业" align="center" prop="majorName" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column
        label="谈话记录完成情况"
        align="center"
        prop="completedPercentage"
      >
        <template slot-scope="scope">
          <el-link
            v-hasPermi="['rotation:mentor-interview-notes:query']"
            type="primary"
            :underline="false"
            @click="viewRecordList(scope.row)"
          >
            {{
              `${scope.row.completed}/${scope.row.total} (${scope.row.completedPercentage})`
            }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="待审核记录数" align="center" prop="toAudit">
        <template slot-scope="scope">
          <el-link
            v-hasPermi="['rotation:mentor-interview-notes:audit']"
            type="primary"
            :underline="false"
            @click="toAudit(scope.row)"
            >{{ scope.row.toAudit || "--" }}</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="待审核记录"
      :visible.sync="openAuditList"
      width="800px"
      v-dialogDrag
      append-to-body
      custom-class="audit-list-dialog"
    >
      <div>
        <div class="table-top">
          <div>
            <span class="lable">学员姓名</span>
            <span>{{ curActive.nickname }}</span>
          </div>
          <div>
            <span class="lable">待审核数数据</span>
            <span>{{ curActive.toAudit }}</span>
          </div>
        </div>
        <el-table :data="auditList">
          <el-table-column
            label="交流日期"
            align="center"
            prop="communicationDate"
          />
          <el-table-column
            label="交流方式"
            prop="communicationWay"
            align="center"
          >
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.MENTOR_COMMUNICATION_WAY"
                :value="scope.row.communicationWay"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="交流地点"
            align="center"
            prop="communicationAddress"
          />
          <el-table-column label="填写时间" align="center" prop="createTime" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleRecordDetail(scope.row)"
                >查看详情</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleAudtit(scope.row, '1')"
                >通过</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleAudtit(scope.row, '2')"
                >不通过</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <el-dialog
      title="谈话记录查看"
      :visible.sync="openRecordList"
      width="1000px"
      v-dialogDrag
      append-to-body
      custom-class="audit-list-dialog"
    >
      <div>
        <el-form
          :model="queryRecordListParams"
          ref="queryRecordListForm"
          size="small"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="学员姓名">
            <span>{{ curActive.nickname }}</span>
          </el-form-item>
          <el-form-item
            label="交流日期"
            prop="communicationDate"
            style="width: 300px"
          >
            <el-date-picker
              type="daterange"
              clearable
              v-model="queryRecordListParams.communicationDate"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="searchRecordList"
              style="width: 220px"
            />
          </el-form-item>
          <el-form-item
            label="审核状态"
            prop="auditStatus"
            style="width: 200px"
          >
            <el-select
              v-model="queryRecordListParams.auditStatus"
              placeholder="请选择审核状态"
              clearable
              size="small"
              @change="searchRecordList"
              style="width: 120px"
            >
              <el-option
                v-for="dict in this.getDictDatas(
                  DICT_TYPE.ROTATION_AUDIT_STATUS
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="完成数/要求数"
            ><span>{{
              `${curActive.completed}/${curActive.total} (${curActive.completedPercentage})`
            }}</span></el-form-item
          >
        </el-form>
        <el-table v-loading="loadingRecordList" :data="recordList">
          <el-table-column
            label="交流日期"
            align="center"
            prop="communicationDate"
          />
          <el-table-column
            label="交流方式"
            prop="communicationWay"
            align="center"
          >
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.MENTOR_COMMUNICATION_WAY"
                :value="scope.row.communicationWay"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="交流地点"
            align="center"
            prop="communicationAddress"
          />
          <el-table-column label="审核状态" prop="auditStatus" align="center">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.ROTATION_AUDIT_STATUS"
                :value="scope.row.auditStatus"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column label="填写时间" align="center" prop="createTime" />
          <el-table-column label="审核时间" align="center" prop="auditTime" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleRecordDetail(scope.row)"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="recordTotal > 0"
          :total="recordTotal"
          :page.sync="queryRecordListParams.pageNo"
          :limit.sync="queryRecordListParams.pageSize"
          @pagination="getRecordList"
        />
      </div>
    </el-dialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="680px"
      v-dialogDrag
      append-to-body
      custom-class="talkRecord-dialog"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="180px"
        :disabled="opt === 'view'"
      >
        <el-form-item label="交流方式" prop="communicationWay">
          <el-select
            v-model="form.communicationWay"
            placeholder="请选择交流方式"
            style="width: 100%"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.MENTOR_COMMUNICATION_WAY
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="交流日期" prop="communicationDate">
          <el-date-picker
            clearable
            v-model="form.communicationDate"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择开展时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="交流地点" prop="communicationAddress">
          <el-input
            type="textarea"
            v-model="form.communicationAddress"
            placeholder="请输入交流地点"
          />
        </el-form-item>

        <el-form-item label="主要交流内容" prop="communicationContent">
          <el-input
            type="textarea"
            v-model="form.communicationContent"
            placeholder="请输入主要交流内容"
            :autosize="{ minRows: 2 }"
          />
        </el-form-item>

        <el-form-item label="导师评语" prop="mentorComment">
          <el-input
            type="textarea"
            v-model="form.mentorComment"
            placeholder="请输入导师评语"
            :autosize="{ minRows: 2 }"
          />
        </el-form-item>

        <el-form-item label="图片上传" prop="photo">
          <imageUpload
            v-model="form.photo"
            :limit="9999"
            activeTypeName=""
            :disabled="opt === 'view'"
          />
        </el-form-item>

        <el-form-item label="附件上传" prop="file">
          <FileUpload
            v-model="form.file"
            :limit="999"
            :fileSize="50"
            :disabled="opt === 'view'"
          />
        </el-form-item>
      </el-form>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('save')">提交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div> -->
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";
import { getSimpleMajorList } from "@/api/system/major";
import { getStudentGradeList } from "@/api/system/userStudent";
import {
  getAuditStudentPage,
  getToAuditPagePage,
  getCompleteStatusPage,
  auditMentorNotes,
} from "@/api/rotation/talkAudit";
import { getMentorNote } from "@/api/rotation/talkRecord";

export default {
  name: "TalkAudit",
  components: {
    FileUpload,
    ImageUpload,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级培训列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: null,
        major: null,
        grade: null,
        isToAudit: true,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communicationAddress: [
          { required: true, message: "交流地点不能为空", trigger: "blur" },
        ],
        communicationWay: [
          { required: true, message: "交流方式不能为空", trigger: "change" },
        ],
        communicationDate: [
          { required: true, message: "交流日期不能为空", trigger: "change" },
        ],
        communicationContent: [
          { required: true, message: "主要交流内容不能为空", trigger: "blur" },
        ],
      },
      curActive: {},
      opt: "",

      // 年级列表
      gradeList: [],
      queryMajorList: [],

      openAuditList: false,
      auditList: [],

      openRecordList: false,
      loadingRecordList: false,
      queryRecordListParams: {
        pageNo: 1,
        pageSize: 10,
        studentUserId: null,
        communicationDate: null,
        auditStatus: null,
      },
      recordList: [],
      recordTotal: 0,
    };
  },
  created() {
    getSimpleMajorList().then((res) => {
      this.queryMajorList = res.data;
    });
    getStudentGradeList().then((res) => (this.gradeList = res.data));
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getAuditStudentPage(this.queryParams).then((response) => {
        const list = response.data.list;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        nickname: undefined,
        major: undefined,
        grade: undefined,
        isToAudit: true,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    toAudit(row) {
      this.curActive = row;
      this.getToAuditList();
    },
    getToAuditList() {
      getToAuditPagePage({ studentUserId: this.curActive.studentUserId }).then(
        (response) => {
          const { data } = response;
          this.auditList = data.list || [];
          this.openAuditList = true;
        }
      );
    },
    viewRecordList(row) {
      this.curActive = row;
      this.openRecordList = true;
      this.queryRecordListParams.pageNo = 1;
      this.queryRecordListParams.studentUserId = row.studentUserId;
      this.getRecordList();
    },
    getRecordList() {
      this.loadingRecordList = true;
      // 执行查询
      getCompleteStatusPage(this.queryRecordListParams).then((response) => {
        const list = response.data.list;
        this.recordList = list;
        this.recordTotal = response.data.total;
        this.loadingRecordList = false;
      });
    },
    searchRecordList() {
      this.$nextTick(() => {
        this.queryRecordListParams.pageNo = 1;
        this.getRecordList();
      });
    },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.reset();
    //   this.opt = 'add';
    //   this.open = true;
    //   this.title = "新增谈话记录";
    // },

    /** 查看记录详情 */
    handleRecordDetail(row) {
      this.reset();
      const id = row.id;
      this.curActive = row;
      this.opt = "view";
      getMentorNote(id).then((response) => {
        const { data } = response;
        this.form = data;
        this.form.file = JSON.parse(this.form.file);
        this.open = true;
        this.title = "查看谈话记录";
      });
    },
    handleAudtit(row, auditStatus) {
      let content = "审核通过";
      if (auditStatus == 2) {
        content = "审核不通过";
      }

      this.$prompt("请输入导师评语", content, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "textarea",
        inputPattern: /^.{50,500}$/,
        inputErrorMessage: "长度在 50 到 500 个字符",
      })
        .then(({ value }) => {
          const params = {
            id: row.id,
            mentorComment: value,
            auditStatus,
          };
          auditMentorNotes(params).then((response) => {
            this.$modal.msgSuccess("审核成功");
            this.getToAuditList();
            this.getList();
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss">
.talkAudit-container {
}

.audit-list-dialog {
  .table-top {
    padding-bottom: 10px;
    display: flex;
    align-items: center;

    div {
      margin-right: 20px;

      span.lable {
        font-weight: bold;
      }
    }
  }
}

.talkRecord-dialog {
  .el-dialog__body {
    padding-right: 100px;
  }
}
</style>
