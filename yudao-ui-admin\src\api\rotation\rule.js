import request from '@/utils/request'

// 创建轮转规则
export function createRule(data) {
  return request({
    url: '/rotation/rule/create',
    method: 'post',
    data: data
  })
}

// 更新轮转规则
export function updateRule(data) {
  return request({
    url: '/rotation/rule/update',
    method: 'put',
    data: data
  })
}

// 删除轮转规则
export function deleteRule(id) {
  return request({
    url: '/rotation/rule/delete?id=' + id,
    method: 'delete'
  })
}

// 获得轮转规则
export function getRule(id) {
  return request({
    url: '/rotation/rule/get?id=' + id,
    method: 'get'
  })
}

// 获得轮转规则分页
export function getRuleList(query) {
  return request({
    url: '/rotation/rule/list',
    method: 'get',
    params: query
  })
}

// 导出轮转规则 Excel
export function exportRuleExcel(query) {
  return request({
    url: '/rotation/rule/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得活动数据项列表
export function getRuleActiveList(ruleDepartmentId) {
  return request(({
    url: '/rotation/rule-department-active-item/list',
    method: 'get',
    params: { ruleDepartmentId }
  }))
}

// 保存活动数据项
export function saveRuleActive(data) {
  return request({
    url: '/rotation/rule-department-active-item/save',
    method: 'put',
    data
  })
}

// 获取轮转数据项列表
export function getRuleRotationItems(standardSchemeId) {
  return request({
    url: '/rotation/rule-department-rotation-item/listRotationItems',
    method: 'get',
    params: { standardSchemeId }
  })
}

// 获得子项列表
export function getRuleRotationSubItemList(ruleDepartmentId, rotaionItem) {
  return request({
    url: '/rotation/rule-department-rotation-item/page',
    method: 'get',
    params: { ruleDepartmentId, rotaionItem, pageNum: 1, pageSize: 100 }
  })
}

// 创建子项
export function createRuleRotationSubItem(data) {
  return request({
    url: '/rotation/rule-department-rotation-item/create',
    method: 'post',
    data
  })
}

// 更新子项
export function updateRuleRotationSubItem(data) {
  return request({
    url: '/rotation/rule-department-rotation-item/update',
    method: 'put',
    data
  })
}

// 删除子项
export function deleteRuleRotationSubItem(subItemId) {
  return request({
    url: '/rotation/rule-department-rotation-item/delete',
    method: 'delete',
    params: { id: subItemId }
  })
}

// 获取轮转规范
export function getRuleRotationStandard(id) {
  return request({
    url: '/rotation/rule-department/getRotationStandard',
    method: 'get',
    params: { id }
  })
}

// 更新轮转规范
export function updateRuleRotationStandard(data) {
  return request({
    url: '/rotation/rule-department/updateRotationStandard',
    method: 'put',
    data
  })
}

// 删除轮转规则科室
export function deleteRuleDepartment(id) {
  return request({
    url: '/rotation/rule-department/delete',
    method: 'delete',
    params: { id }
  })
}

// 获取精简轮转规则列表
export function getRotationRuleSimpleList(standardSchemeId) {
  return request({
    url: '/rotation/rule/list-all-simple',
    method: 'get',
    params: { standardSchemeId }
  })
}

// 获取标准方案标准科室列表
export function getStandardSchemeDepartments(standardSchemeId, ruleId) {
  return request({
    url: '/rotation/rule-department/list-all-simple',
    method: 'get',
    params: { standardSchemeId, ruleId }
  })
}

// 获得轮转数据项关联出科比例
export function getItemsGraduationRadio(id) {
  return request(({
    url: '/rotation/rule-department/get-items-graduation-radio',
    method: 'get',
    params: { id }
  }))
}

// 保存活动数据项
export function updateItemsGraduationRadio(data) {
  return request({
    url: '/rotation/rule-department/update-items-graduation-radio',
    method: 'put',
    data
  })
}
