import request from "@/utils/request";

// 获得招录住培信息审核分页
export function getProfessionalBaseExamPage(query) {
  return request({
    url: "/recruitment/rpregistration-professional-base-examine/page",
    method: "get",
    params: query,
    headers: {
      component: "recruitment/rpregistrationProfessionalBaseExamine/index",
    },
  });
}

// 审核
export function auditProfessionalBaseExam(data) {
  return request({
    url: "/recruitment/rpregistration-professional-base-examine/examine",
    method: "put",
    data: data,
  });
}
