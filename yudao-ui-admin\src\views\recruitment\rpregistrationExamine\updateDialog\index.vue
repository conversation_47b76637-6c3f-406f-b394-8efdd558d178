<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="800px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row :gutter="10">
        <el-col :md="11" :lg="11" :xl="11">
          <h4>修改前</h4>
          <el-form-item label="报名专业" prop="reportExamineResult">
            <el-select
              v-model="form.oldMajorCode"
              placeholder="请选择报名专业"
              clearable
              filterable
            >
              <el-option
                v-for="item in majorList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :md="2" :lg="2" :xl="2">
          <el-divider direction="vertical"></el-divider>
        </el-col>

        <el-col :md="11" :lg="11" :xl="11">
          <h4>修改后</h4>
          <el-form-item label="报名专业" prop="majorCode">
            <el-select
              v-model="form.majorCode"
              placeholder="请选择报名专业"
              clearable
              filterable
            >
              <el-option
                v-for="item in majorList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm"> 确 认 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateRpregistrationExam } from "@/api/recruitment/rpregistrationExamine";

export default {
  name: "AuditDialog",
  components: {},
  props: {
    title: {
      type: String,
    },
    openUpdate: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
    majorList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      open: this.openUpdate,

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        majorCode: [
          {
            required: true,
            message: "请选择修改后专业",
            trigger: "change",
          },
        ],
      },
    };
  },
  watch: {
    openUpdate(newVal) {
      this.form = {
        recruitmentRegistrationId: this.formData.recruitmentRegistrationId,
        oldMajorCode: this.formData.majorCode || "",
      };
      this.open = newVal;
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:openUpdate", false);
    },

    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }

        updateRpregistrationExam(this.form).then((response) => {
          this.$modal.msgSuccess("修改成功");
          this.$emit("refresh");
          this.cancel();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-divider--vertical {
  height: 100px;
}
</style>
