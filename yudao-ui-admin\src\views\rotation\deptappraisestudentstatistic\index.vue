<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="70px"
    >
      <el-form-item label="科室类型" prop="deptType">
        <el-select
          v-model="queryParams.deptType"
          placeholder="请选择科室类型"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_DEPARTMENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          filterable
          clearable
          size="small"
          @change="handleQueryStudentTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="majorCode">
        <el-select
          v-model="queryParams.majorCode"
          placeholder="请选择培训专业"
          filterable
          clearable
          size="small"
        >
          <!-- <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_MAJOR)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/> -->
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="grade in studentGradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学员姓名" prop="studentUserNickName">
        <el-input
          v-model="queryParams.studentUserNickName"
          placeholder="请输入学员姓名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          :loading="exportLoading"
          @click="handleExport"
          >导出</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="学员姓名"
        prop="studentNickname"
        align="center"
      ></el-table-column>
      <el-table-column label="学员类型" prop="studentType" align="center">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="培训专业"
        prop="majorName"
        align="center"
      ></el-table-column>
      <el-table-column
        label="年级"
        prop="grade"
        align="center"
      ></el-table-column>
      <el-table-column
        label="科室评价数"
        prop="deptAppraiseCount"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="viewStudentList(scope.row)"
            >{{ scope.row.deptAppraiseCount }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        label="综合得分"
        prop="score"
        align="center"
      ></el-table-column>
      <!-- <el-table-column label="排名" prop="rank" align="center"></el-table-column> -->
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      custom-class="student-appraise-list-dialog"
      :title="listTitle"
      :visible.sync="listOpen"
      width="1000px"
      v-dialogDrag
      append-to-body
      @close="cancelDialog"
    >
      <div class="top">
        <div class="left">
          <div>
            <label>评价对象：</label
            ><span>{{ curRow && curRow.studentNickname }}</span>
          </div>
          <div>
            <label>学员类型：</label
            ><span>
              <dict-tag
                :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                :value="curRow && curRow.studentType"
              />
            </span>
          </div>
          <div>
            <label>培训专业：</label
            ><span>{{ curRow && curRow.majorName }}</span>
          </div>
          <div>
            <label>年级：</label><span>{{ curRow && curRow.grade }}</span>
          </div>
          <div>
            <label>综合得分：</label><span>{{ curRow && curRow.score }}</span>
          </div>
        </div>
        <div class="right">
          <!-- <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
            >导出</el-button> -->
        </div>
      </div>
      <el-table v-loading="loadingStudent" :data="studentList">
        <el-table-column
          label="评价科室"
          prop="rotationDepartmentName"
          align="center"
        ></el-table-column>
        <el-table-column
          label="轮转开始时间"
          prop="rotationBeginTime"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.rotationBeginTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="轮转结束时间"
          prop="rotationEndTime"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.rotationEndTime }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="学员类型" prop="studentType" align="center">
          <template slot-scope="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType" />
          </template>
        </el-table-column>
        <el-table-column label="轮转科室" prop="rotationDepartmentName" align="center"></el-table-column> -->
        <el-table-column label="评价得分" prop="score" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleView(scope.row)">{{
              scope.row.score
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="评价建议"
          prop="comments"
          align="center"
        ></el-table-column>
        <!-- <el-table-column label="评价时间" prop="appraiseDate" align="center">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.appraiseDate) }}</span>
          </template>
        </el-table-column> -->
      </el-table>
      <pagination
        v-show="studentTotal > 0"
        :total="studentTotal"
        :page.sync="queryStudentParams.pageNo"
        :limit.sync="queryStudentParams.pageSize"
        @pagination="getStudentList"
      />
    </el-dialog>

    <AppraiseDialog
      v-if="formData"
      :title="title"
      :open="open"
      :data="formData"
      :appraiseSourceId="curRow && curRow.appraiseSourceId"
      :appraiseTargetId="curRow && curRow.appraiseTargetId"
      :disabled="appraiseDisabled"
      @setOpen="setOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import { DICT_TYPE, getDictDatas } from "@/utils/dict";
import AppraiseDialog from "@/views/components/appraiseDialog";
import { getStudentGradeList } from "@/api/system/userStudent";
import { getSimpleMajorList } from "@/api/system/major";
import { getAppraiseResult } from "@/api/rotation/studentappraiseteacher";
import {
  getDeptappraisestudentstatistic,
  getAppraiseStudentList,
  exportDeptAppraiseStudentStatistic,
} from "@/api/rotation/deptappraisestudentstatistic";

export default {
  name: "DeptAppraiseStudentStatistic",
  components: {
    AppraiseDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      list: [],
      queryMajorList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 50,
        studentUserNickName: "",
        deptType: getDictDatas(DICT_TYPE.SYSTEM_DEPARTMENT_TYPE)[0].value,
        studentType: getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)[0].value,
        majorCode: "",
        grade: "",
      },
      formData: null,
      open: false,
      title: "",
      curRow: null,
      appraiseDisabled: false,
      listTitle: "学员评价列表",
      listOpen: false,
      studentTotal: 0,
      studentList: [],
      loadingStudent: true,
      queryStudentParams: {
        pageNo: 1,
        pageSize: 10,
        deptType: "",
        maxAppraiseDate: "",
        minAppraiseDate: "",
        rotationDepartmentId: "",
        studentType: "",
      },
      exportLoading: false,
      studentGradeList: [],
    };
  },
  created() {
    this.getList();
    getStudentGradeList().then((res) => {
      this.studentGradeList = res.data;
    });
    const studentType = getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)[0].value;
    this.handleQueryStudentTypeChange(studentType);
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getDeptappraisestudentstatistic(this.queryParams).then((response) => {
        const list = response.data.list;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getStudentList() {
      this.loadingStudent = true;
      // 执行查询
      getAppraiseStudentList(this.queryStudentParams).then((response) => {
        const list = response.data.list;
        this.studentList = list;
        this.studentTotal = response.data.total;
        this.loadingStudent = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryMajorList = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查询学员类型改变 */
    handleQueryStudentTypeChange(value) {
      this.queryParams.major = null;
      this.queryMajorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.queryMajorList = res.data;
      });
    },
    handleView(row) {
      const id = row.appraise360ResultId;
      this.appraiseDisabled = true;
      getAppraiseResult(id).then((response) => {
        this.formData = response.data;
        this.open = true;
        this.title = `查看评价-${row.rotationDepartmentName}`;
      });
    },
    viewStudentList(row) {
      this.curRow = row;
      this.loadingStudent = true;
      this.queryStudentParams = {
        pageNo: 1,
        pageSize: 10,
        deptType: row.deptType,
        maxAppraiseDate: row.maxAppraiseDate,
        minAppraiseDate: row.minAppraiseDate,
        rotationDepartmentId: row.rotationDepartmentId,
        studentType: row.studentType,
        userStudentId: row.userStudentId,
      };
      getAppraiseStudentList(this.queryStudentParams).then((response) => {
        const list = response.data.list;
        this.studentList = list;
        this.studentTotal = response.data.total;
        this.loadingStudent = false;
        this.listOpen = true;
      });
    },
    cancelDialog() {
      this.listOpen = false;
    },
    setOpen(flag) {
      this.open = flag;
      this.editForm = null;
      this.curRow = null;
    },
    // 导出统计数据
    handleExport() {
      this.$modal
        .confirm("是否确认导出科室评价学员统计?")
        .then(() => {
          this.exportLoading = true;
          return exportDeptAppraiseStudentStatistic(this.queryParams);
        })
        .then((response) => {
          this.$download.excel(response, "科室评价学员统计.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss">
.student-appraise-list-dialog {
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    margin-top: -20px;

    .left {
      div {
        display: inline-block;
        margin-right: 20px;
      }
    }
    .right {
    }
  }
}
</style>
