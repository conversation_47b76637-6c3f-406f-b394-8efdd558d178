import request from '@/utils/request'

export function getTrainingJoinPage(query) {
  return request({
    url: '/rotation/hospital-training/join/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/studentHospitalTraining/index'}
  })
}

// 报名参加
export function confirmJoin(data) {
  return request({
    url: `/rotation/hospital-training/join/registration?hospitalTrainingId=${data.hospitalTrainingId}&confirmed=${data.confirmed}`,
    method: 'post',
    data: data
  })
}

// 报名撤销
export function revokeJoin(data) {
  return request({
    url: `/rotation/hospital-training/join/revoke-registration?hospitalTrainingUserId=${data.hospitalTrainingUserId}`,
    method: 'post',
    data: data
  })
}

// 获得院级培训
export function getHospitalTraining(id) {
  return request({
    url: '/rotation/hospital-training/join/get-hospital-training?id=' + id,
    method: 'get'
  })
}