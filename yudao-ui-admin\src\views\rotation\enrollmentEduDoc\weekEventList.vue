<template>
    <el-table
        :data="list"
        class="week-event-list"
        border
        size="small"
        style="width: 100%"
    >   
        <el-table-column label="时间" align="center" prop="time" width="80"></el-table-column>
        <el-table-column label="地点" align="center" prop="addr">
            <template slot-scope="scope">
                <el-input
                    v-model="scope.row.addr"
                    size="mini"
                    placeholder="请输入地点"
                    @change="(val) => rowCellChange(val, scope.$index, 'addr')"
                />
            </template>
        </el-table-column>
        <el-table-column label="内容" align="center" prop="cont">
            <template slot-scope="scope">
                <el-input
                    v-model="scope.row.cont"
                    size="mini"
                    placeholder="请输入内容"
                    @change="(val) => rowCellChange(val, scope.$index, 'cont')"
                />
            </template>
        </el-table-column>
    </el-table>
</template>
  
<script>
  
export default {
    props: {
        formData: {
            type: Object
        }
    },
    data() {
      return {};
    },
    computed: {
      list() {
        return this.formData.departmentJourney
      },
    },
    created() {},
    methods: {
        rowCellChange(val, index, field) {
            let list = this.list
            list[index][field] = val
            this.$emit("change", list)
        }

    }
  };
</script>
<style scoped lang="scss">
  .week-event-list{
    .action-cell{
        cursor: pointer;
    }
  }
  
</style>
  