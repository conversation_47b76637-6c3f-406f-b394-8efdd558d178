import request from "@/utils/request";

// 更新师资聘用申请确认
export function updateAppointmentApplyConfirm(data) {
  return request({
    url: "/teachers/appointment-apply-confirm/update-selection-result",
    method: "put",
    data: data,
  });
}

// 获得师资聘用申请确认
export function getAppointmentApplyConfirm(id) {
  return request({
    url: "/teachers/appointment-apply-confirm/get?id=" + id,
    method: "get",
  });
}

// 获得师资聘用申请确认分页
export function getAppointmentApplyConfirmPage(query) {
  return request({
    url: "/teachers/appointment-apply-confirm/page",
    method: "get",
    params: query,
  });
}

// v
export function generateProof(id) {
  return request({
    url: "/teachers/appointment-apply-confirm/generate-proof?id=" + id,
    method: "post",
  });
}
