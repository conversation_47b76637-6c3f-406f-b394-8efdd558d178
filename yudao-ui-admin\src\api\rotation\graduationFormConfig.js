import request from '@/utils/request'

// 创建出科考核表单配置
export function createGraduationFormConfig(data) {
  return request({
    url: '/rotation/graduation-form-config/create',
    method: 'post',
    data: data
  })
}

// 更新出科考核表单配置
export function updateGraduationFormConfig(data) {
  return request({
    url: '/rotation/graduation-form-config/update',
    method: 'put',
    data: data
  })
}

// 删除出科考核表单配置
export function deleteGraduationFormConfig(id) {
  return request({
    url: '/rotation/graduation-form-config/delete?id=' + id,
    method: 'delete'
  })
}

// 获得出科考核表单配置
export function getGraduationFormConfig(id) {
  return request({
    url: '/rotation/graduation-form-config/get?id=' + id,
    method: 'get'
  })
}

// 获得出科考核表单配置分页
export function getGraduationFormConfigPage(query) {
  return request({
    url: '/rotation/graduation-form-config/page',
    method: 'get',
    params: query
  })
}

// 导出出科考核表单配置 Excel
export function exportGraduationFormConfigExcel(query) {
  return request({
    url: '/rotation/graduation-form-config/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得所有出科考核表单配置列表
export function getAllGraduationFormConfig() {
  return request({
    url: '/rotation/graduation-form-config/get-all',
    method: 'get',
  })
}

// 保存出科考核表单配置
export function saveGraduationFormConfig(data) {
  return request({
    url: '/rotation/graduation-form-config/save',
    method: 'put',
    data: data
  })
}
