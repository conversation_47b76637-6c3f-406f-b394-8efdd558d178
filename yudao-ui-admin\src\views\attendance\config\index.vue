<template>
  <div class="app-container">
    <h3 class="part-title">日常考勤配置</h3>
    <el-form ref="form" :model="form" :rules="rules" label-width="200px">
      <el-form-item label="工作日默认考勤状态：" prop="attendanceWorkingStatus">
        <el-radio-group v-model="form.attendanceWorkingStatus">
          <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.ATTENDANCE_STATUS)" v-if="dict.value != 2"
                    :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="非工作日默认考勤状态：" prop="attendanceNonWorkingStatus">
        <el-radio-group v-model="form.attendanceNonWorkingStatus">
          <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.ATTENDANCE_STATUS)"
                    :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer" style="padding: 0 0 30px 200px">
      <el-button type="primary" @click="submitForm">保存配置</el-button>
    </div>

    <h3 class="part-title">
      日常请假配置
      <el-button type="text" icon="el-icon-plus" @click="handleAddLeave">新增</el-button>
    </h3>
    <el-table style="margin-bottom: 20px" :data="leaveConfigList" v-loading="listLoading">
      <el-table-column label="请假须知" prop="leaveNotice" min-width="300">
        <template #default="scope">
          <pre style="white-space: pre-wrap;">{{ scope.row.leaveNotice }}</pre>
        </template>
      </el-table-column>
      <el-table-column label="学员类型" prop="studentTypeName"></el-table-column>
      <el-table-column label="可请假类型" prop="leaveTypeName"></el-table-column>
      <el-table-column label="年假周期" width="200">
        <template #default="scope">
          <span v-if="scope.row.leaveType.indexOf('4') > -1">{{ scope.row.annualStartDate }} ~ {{ scope.row.annualEndDate }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="年假天数限制" prop="annualLimit" align="center">
        <template #default="scope">
          <span v-if="scope.row.leaveType.indexOf('4') > -1">{{ scope.row.annualLimit }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="90">
        <template #default="scope">
          <el-button type="text" @click="handleEditLeave(scope.row)">编辑</el-button>
          <el-button type="text" @click="handleDeleteLeave(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      width="800px"
      :title="`${leaveForm.id ? '更新' : '新增'}日常请假配置`"
      :visible.sync="leaveOpen"
    >
      <el-form :model="leaveForm" :rules="leaveRules" label-width="110px" ref="leaveForm">
        <el-form-item label="学员类型" prop="studentType">
          <el-select v-model="leaveForm.studentType">
            <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请假须知" prop="leaveNotice">
          <el-input
            type="textarea"
            v-model="leaveForm.leaveNotice"
            autosize
            placeholder="请输入"
            :autosize="{ minRows: 3 }"
          ></el-input>
        </el-form-item>
        <el-form-item label="可请假类型" prop="leaveType">
          <el-checkbox-group :value="leaveForm.leaveType ? leaveForm.leaveType.split(',') : []" @input="leaveForm.leaveType = $event.join(',')">
            <el-checkbox v-for="dict in getDictDatas(DICT_TYPE.BPM_OA_LEAVE_TYPE)"
                         :key="dict.value" :label="dict.value" :value="dict.value">{{ dict.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="年假周期" prop="annualStartDate" v-if="(leaveForm.leaveType || '').indexOf('4') > -1">
          <month-day-picker
            v-model="leaveForm.annualStartDate"
            placeholder="起始日期"
            clearable
            @change="handleStartDateChange"
          ></month-day-picker>
          ~
          <month-day-picker v-model="leaveForm.annualEndDate" placeholder="结束日期"></month-day-picker>
        </el-form-item>
        <el-form-item label="年假天数限制" prop="annualLimit" v-if="(leaveForm.leaveType || '').indexOf('4') > -1">
          <el-input-number
            v-model="leaveForm.annualLimit"
            :min="0"
            :step="1"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="text-center">
          <el-button type="primary" @click="handleLeaveFormSure">确定</el-button>
          <el-button @click="leaveOpen = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getConfig, saveConfig } from "@/api/attendance/config";
import { saveLeaveConfig, deleteLeaveConfig, getLeaveConfig, getLeaveConfigList, updateLeaveConfig } from '@/api/attendance/leave-config'
import MonthDayPicker from "./month-day-picker";

export default {
  name: "Config",
  components: { MonthDayPicker },
  data() {
    return {
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        attendanceWorkingStatus: [{ required: true, message: "工作日默认考勤状态不能为空", trigger: "blur" }],
        attendanceNonWorkingStatus: [{ required: true, message: "非工作日默认考勤状态不能为空", trigger: "blur" }],
      },
      leaveConfigList: [],
      listLoading: false,
      leaveForm: {},
      leaveRules: {
        studentType: [{ required: true, message: "请选择学员类型", trigger: 'change' }],
        leaveNotice: [{ required: true, message: "请输入请假须知", trigger: 'change' }],
        leaveType: [{ required: true, message: "请选择可请假类型", trigger: 'change' }],
        annualStartDate: [{ required: true, message: "请选择年假周期", trigger: 'change' }],
        annualLimit: [{ required: true, message: "请输入年假天数限制", trigger: 'change' }],
      },
      leaveOpen: false,
    };
  },
  created() {
    getConfig().then(response => {
      if (response.data) {
        this.form = response.data;
      }
    });
    this.queryLeaveConfigList();
  },
  methods: {
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        saveConfig(this.form).then(response => {
          this.$modal.msgSuccess("保存成功");
        });
      });
    },
    /** 获取请假配置列表 */
    queryLeaveConfigList() {
      this.listLoading = true;
      getLeaveConfigList()
        .then(res => this.leaveConfigList = res.data)
        .finally(() => this.listLoading = false);
    },
    /** 初始化请假表单 */
    initLeaveForm() {
      this.leaveForm = {
        studentType: "",
        leaveNotice: "",
        leaveType: "",
        annualStartDate: "",
        annualEndDate: "",
        annualLimit: "",
      };
    },
    /** 新增编辑请假配置 */
    handleAddLeave() {
      this.initLeaveForm();
      this.leaveOpen = true;
      this.$nextTick(() => this.$refs.leaveForm?.clearValidate());
    },
    handleEditLeave(row) {
      this.leaveForm = { ...row };
      this.leaveForm.studentType = String(row.studentType);
      this.leaveOpen = true;
      this.$nextTick(() => this.$refs.leaveForm?.clearValidate());
    },
    // 年假周期起始日期改变
    handleStartDateChange(value) {
      const paddingZero = (value) => value > 9 ? `${value}` : `0${value}`;

      if (value) {
        const [month, day] = value.split("-");
        const m = Number(month);
        const d = Number(day);
        if (d > 1) {
          this.leaveForm.annualEndDate = `${paddingZero(m)}-${paddingZero(d - 1)}`;
        } else {
          const _m = m > 1 ? m - 1 : 12;
          if ([1, 3, 5, 7, 8, 10, 12].indexOf(_m) > -1) {
            this.leaveForm.annualEndDate = `${paddingZero(_m)}-31`;
          }
          if (2 === _m) {
            this.leaveForm.annualEndDate = `${paddingZero(_m)}-29`;
          }
          if ([4, 6, 9, 11].indexOf(_m) > -1) {
            this.leaveForm.annualEndDate = `${paddingZero(_m)}-30`;
          }
        }
      }
    },
    /** 保存请假配置 */
    handleLeaveFormSure() {
      const apiMethod = this.leaveForm.id ? updateLeaveConfig : saveLeaveConfig;
      this.$refs.leaveForm.validate(valid => {
        if (valid) {
          apiMethod(this.leaveForm).then(() => {
            this.$message.success("日常请假配置保存成功！");
            this.queryLeaveConfigList();
            this.leaveOpen = false;
          })
        }
      });
    },
    /** 删除请假配置 */
    handleDeleteLeave(id) {
      this.$confirm("确定删除该请假配置？", "提示").then(() => {
        deleteLeaveConfig(id).then(() => {
          this.$message.success("删除日常请假配置成功！");
          this.queryLeaveConfigList();
        });
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.part-title {
  font-size: 18px;
  line-height: 36px;
  margin-bottom: 20px;
  color: #000;
  border-bottom: 1px dashed #efefef;
  padding-bottom: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
