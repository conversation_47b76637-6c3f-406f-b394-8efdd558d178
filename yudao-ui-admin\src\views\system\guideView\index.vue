<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="目录" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入目录" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="编号" align="center" prop="id" width="100" />
      <el-table-column label="目录" align="center" prop="name">
        <template slot-scope="scope">
          <el-button type="text" @click="viewPdf(scope.row.file)">{{ scope.row.name }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

  </div>
</template>

<script>
import { getGuidePage } from "@/api/system/guide";
import FileUpload from '@/components/FileUploadInfo';
import { getAccessToken } from "@/utils/auth";
import axios from 'axios'

export default {
  name: "GuideView",
  components: {
    FileUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 操作指引列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        createTime: [],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getGuidePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    dealFile(fileString, type){
      const file = JSON.parse(fileString)[0];
      if(type == 'url'){
        return `${file?.url}?token=${getAccessToken()}`;
      }else{
        return file?.name;
      }
    },
    viewPdf(fileString){
      const file = JSON.parse(fileString)[0];
      const fileUrl = `${file.url}?token=${getAccessToken()}`
      axios.get(fileUrl, {responseType: 'blob'}).then((data) => {
        let responseData = data.data;
        let blob = new Blob([responseData], { type: 'application/pdf;charset=utf-8' });
        const href = URL.createObjectURL(blob);
        window.open(href, '_blank');
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    }
  }
};
</script>
