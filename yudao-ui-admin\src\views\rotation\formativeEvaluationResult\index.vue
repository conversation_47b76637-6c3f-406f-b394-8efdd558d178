<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select v-model="queryParams.major" placeholder="请选择学员类型" size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_MAJOR)" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select v-model="queryParams.grade" placeholder="请选择年级" size="small" clearable>
          <el-option v-for="grade in gradeList" :key="grade" :label="grade" :value="grade"/>
        </el-select>
      </el-form-item>
      <el-form-item label="学员姓名" prop="studentName">
        <el-input v-model="queryParams.studentName" placeholder="请输入学员姓名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="queryParams.isToApprais">未考核</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="我的学员" align="center" prop="studentName" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType"></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="培训专业" align="center" prop="majorName" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column label="带教开始时间" align="center" prop="startDate" width="120px" />
      <el-table-column label="带教结束时间" align="center" prop="endDate" width="120px" />
      <el-table-column label="轮转科室" align="center" prop="deptName" />
      <el-table-column label="考核状态" align="center" prop="appraised">
        <template v-slot="scope">{{ scope.row.appraised ? "已考核" : "未考核" }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180px">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleAudit(scope.row)">{{ scope.row.appraised ? "添加" : "进入" }}考核</el-button>
          <el-button v-if="scope.row.appraised" size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看考核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog title="形成性评价表单选择" :visible.sync="formChoiceVisible" width="500px">
      <el-radio-group v-model="selectFormId">
        <el-radio style="margin-bottom: 16px; min-width: 50%;" v-for="item in formList" :key="item.formId" :label="item.formId">{{ item.name }}</el-radio>
      </el-radio-group>
      <span slot="footer">
        <el-button type="primary" @click="handleFormChoiceSure">确定</el-button>
        <el-button @click="formChoiceVisible = false">取消</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="`正在评价${handleRow && handleRow.studentName}`" :visible.sync="formVisible" width="800px">
      <component v-if="selectFormId" :is="`form-${selectFormId}`" :form-data="formData" evaluate ref="form"></component>
      <span slot="footer">
        <el-button type="primary" @click="handleEvaluateSure">确定</el-button>
        <el-button @click="formVisible = false">取消</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="`查看考核-${handleRow && handleRow.studentName}`" :visible.sync="auditListVisible">
      <el-table :data="auditFormList">
        <el-table-column label="考核表单" prop="name">
          <template v-slot="scope">
            <el-link type="primary" @click="showEvaluationDetail(scope.row)">{{ scope.row.name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="考核时间" prop="createTime"></el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog title="查看形成行评价" :visible.sync="checkVisible" width="800px">
      <component v-if="selectFormId" :is="`form-${selectFormId}`" :form-data="formData" check></component>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFormativeEvaluationResultStudentList,
  getFormativeEvaluationResultFormList,
  createFormativeEvaluationResult,
  getFormativeEvaluationResultList
} from '@/api/rotation/formativeEvaluationResult'
import { getStudentGradeList } from '@/api/system/userStudent'

const requireComponents = require.context("@/views/rotation/common/formativeEvaluationForm", false, /\.vue$/);
const componentsObj = {};
requireComponents.keys().forEach(filePath => {
  const componentName = filePath.split("/")[1].replace(/\.vue$/, "");
  const componentConfig = requireComponents(filePath);
  componentsObj[componentName] = componentConfig.default || componentConfig;
});

export default {
  name: 'formativeEvaluationResult',
  components: { ...componentsObj },
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      list: [],
      gradeList: [],
      queryParams: {
        studentType: '',
        major: '',
        grade: '',
        studentName: '',
        isToApprais: false,
        pageNo: 1,
        pageSize: 10,
      },
      handleRow: null,
      formChoiceVisible: false,
      formList: [],
      selectFormId: null,
      formVisible: false,
      formData: {},
      auditListVisible: false,
      auditFormList: [],
      checkVisible: false,
    }
  },
  methods: {
    getList() {
      this.loading = true;
      getFormativeEvaluationResultStudentList(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAudit(row) {
      this.formChoiceVisible = true;
      this.formList = [];
      this.selectFormId = null;
      this.handleRow = row;
      getFormativeEvaluationResultFormList({ deptType: row.deptType, studentType: row.studentType }).then(res => {
        this.formList = res.data;
      });
    },
    handleFormChoiceSure() {
      if (!this.selectFormId) return;
      const formData = this.formList.find(item => item.formId === this.selectFormId).formdata;
      this.formData = JSON.parse(formData);
      this.formData.medical_name = this.handleRow.teacherName;
      this.formData.student_name = this.handleRow.studentName;
      this.formData.student_signature = this.handleRow.studentName;
      this.formData.mentor_signature = this.handleRow.teacherName;
      this.formVisible = true;
      this.formChoiceVisible = false;
    },
    handleEvaluateSure() {
      const valid = this.$refs.form.validData();
      if (valid) {
        this.$confirm("提交后不可再修改，是否继续提交？", "提示").then(() => {
          createFormativeEvaluationResult({
            formId: this.selectFormId,
            studentId: this.handleRow.studentId,
            teacherUserId: this.handleRow.teacherUserId,
            scheduleDetailsId: this.handleRow.scheduleDetailsId,
            result: JSON.stringify(this.formData),
          }).then(() => {
            this.formVisible = false;
            this.getList();
          });
        })
      }
    },
    handleView(row) {
      this.handleRow = row;
      this.auditListVisible = true;
      this.auditFormList = [];
      getFormativeEvaluationResultList(this.handleRow.scheduleDetailsId).then(res => {
        this.auditFormList = res.data;
      });
    },
    showEvaluationDetail(row) {
      this.selectFormId = row.formId;
      this.formData = JSON.parse(row.result);
      this.checkVisible = true;
    },
  },
  created() {
    getStudentGradeList().then(res => this.gradeList = res.data);
    this.getList();
  }
}
</script>

<style scoped lang="scss">

</style>
