<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          size="small"
          clearable
        >
          <el-option
            v-for="grade in gradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="轮转状态" prop="rotationStatus">
        <el-select
          v-model="queryParams.rotationStatus"
          placeholder="请选择轮转状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="轮转时间" prop="rotationDates">
        <el-date-picker
          v-model="queryParams.rotationDates"
          style="width: 240px"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="轮转科室" prop="rotationDepartmentName">
        <el-input
          v-model="queryParams.rotationDepartmentName"
          placeholder="请输入轮转科室"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="queryParams.isAudit"
          >仅展示待审核数据</el-checkbox
        >
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="学员姓名" align="center" prop="nickname" />
      <el-table-column
        label="手机号码"
        align="center"
        prop="mobile"
        width="120px"
      />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template v-slot="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="培训专业" align="center" prop="majorName" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column
        label="轮转科室"
        align="center"
        prop="rotationDepartmentName"
      />
      <el-table-column
        label="出科申请状态"
        align="center"
        prop="graduationAuditStatus"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.GRADUATION_AUDIT_STATUS"
            :value="scope.row.graduationAuditStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="轮转时间"
        align="center"
        prop="rotationBeginEndTime"
        width="200px"
      />
      <!-- <el-table-column label="出科申请状态" align="center" prop="graduationAuditStatus">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.GRADUATION_AUDIT_STATUS" :value="scope.row.graduationAuditStatus"></dict-tag>
        </template>
      </el-table-column> -->
      <el-table-column
        label="出科审核"
        align="center"
        class-name="small-padding fixed-width"
        width="130px"
      >
        <template v-slot="scope">
          <el-button
            v-has-permi="['rotation:dept-graduation-apply:audit']"
            v-if="scope.row.graduationAuditStatus === '3'"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAudit(scope.row)"
            >科室审核</el-button
          >
          <el-button
            v-if="+scope.row.graduationAuditStatus > 3"
            size="mini"
            type="text"
            icon="el-icon-eye"
            @click="handleView(scope.row)"
            >查看审核</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <dept-audit
      :visible.sync="visible"
      :is-view="isView"
      :row="curRow"
      @update-list="getList"
    ></dept-audit>
  </div>
</template>

<script>
import { getGraduationDeptAuditList } from "@/api/rotation/graduationAudit";
import { getStudentGradeList } from "@/api/system/userStudent";
import DeptAudit from "./deptAudit";

export default {
  name: "GraduationDeptAudit",
  components: { DeptAudit },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出科审核列表
      list: [],
      // 年级列表
      gradeList: [],
      // 查询参数
      queryParams: {
        studentType: null,
        grade: "",
        rotationStatus: "",
        rotationDates: "",
        rotationDepartmentName: "",
        nickname: "",
        isAudit: true,
        pageNo: 1,
        pageSize: 10,
      },
      // 是否查看审核
      isView: false,
      // 弹窗
      visible: false,
      // 当前操作行
      curRow: {},
    };
  },
  created() {
    getStudentGradeList().then((res) => (this.gradeList = res.data));
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getGraduationDeptAuditList(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAudit(row) {
      this.curRow = row;
      this.isView = false;
      this.visible = true;
    },
    handleView(row) {
      this.curRow = row;
      this.isView = true;
      this.visible = true;
    },
  },
};
</script>
