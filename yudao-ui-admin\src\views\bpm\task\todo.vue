<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="流程名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入流程名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="任务编号" align="center" prop="id" width="320" /> -->
      <el-table-column label="任务名称" align="center" prop="name" />
      <el-table-column label="所属流程" align="center" prop="processInstance.name" />
      <el-table-column label="流程发起人" align="center" prop="processInstance.startUserNickname">
        <template slot-scope="scope">
          <el-link
            v-if="scope.row.processInstance.processDefinitionKey === 'oa_leave'"
            type="primary"
            @click="showLeaveHistoryList(scope.row.processInstance)"
          >{{ scope.row.processInstance.startUserNickname }}</el-link>
          <span v-else>{{ scope.row.processInstance.startUserNickname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="version" width="80">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.suspensionState === 1">激活</el-tag>
          <el-tag type="warning" v-if="scope.row.suspensionState === 2">挂起</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleAudit(scope.row)"
                     v-hasPermi="['bpm:task:update']">审批</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog title="请假历史记录" :visible.sync="open" width="800px">
      <el-table :data="historyList" border>
        <el-table-column label="姓名" prop="nickname" align="center"></el-table-column>
        <el-table-column label="请假类型" prop="type" align="center">
          <template v-slot="scope">
            {{ getDictDataLabel(DICT_TYPE.BPM_OA_LEAVE_TYPE, scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column label="请假时间" prop="leaveTime" width="290px" align="center"></el-table-column>
        <el-table-column label="请假天数" prop="leaveDay" align="center"></el-table-column>
        <el-table-column label="轮转科室" prop="departmentName" align="center"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {getTodoTaskPage, getTaskHistoryList} from '@/api/bpm/task';

export default {
  name: "Todo",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 待办任务列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        createTime: []
      },
      // 请假历史记录
      open: false,
      historyList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 处理查询参数
      getTodoTaskPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 处理审批按钮 */
    handleAudit(row) {
      this.$router.push({ path: "/bpm/process-instance/detail", query: { id: row.processInstance.id}});
    },
    /** 请假历史记录 */
    showLeaveHistoryList(row) {
      getTaskHistoryList({
        processInstanceId: row.id,
        userId: row.startUserId
      }).then((res) => {
        this.historyList = res.data;
        this.open = true;
      })
    },
  }
};
</script>
