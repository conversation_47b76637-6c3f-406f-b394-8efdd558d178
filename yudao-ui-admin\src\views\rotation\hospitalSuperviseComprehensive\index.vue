<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="督导类型" prop="formType">
        <el-select v-model="queryParams.formType" placeholder="请选择督导类型" clearable size="small" @change="handleTypeChange">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="督导对象" prop="developObject">
        <el-select v-model="queryParams.developObject" placeholder="请选择督导对象" clearable filterable @change="handleDevelopObjectChange">
          <el-option v-for="item in queryDevelopObjectList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="督导专家" prop="nickname">
        <el-select v-model="queryParams.nickname" placeholder="请输入督导专家" filterable clearable>
          <el-option v-for="item in supervisorList" :key="item.value" :label="item.label" :value="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="督导时间" prop="superviseDates">
        <el-date-picker v-model="queryParams.superviseDates" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item label="项目名称" prop="hospitalSuperviseStatus">
        <el-input v-model="queryParams.hospitalSuperviseName" placeholder="请输入督导项目名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="toBeFeedback">
        <el-checkbox v-model="queryParams.toBeFeedback" label="待反馈"></el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="项目名称" align="center" prop="hospitalSuperviseName" width="160px"></el-table-column>
      <el-table-column label="督导类型" align="center" prop="formType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE" :value="scope.row.formType" />
        </template>
      </el-table-column>
      <el-table-column label="督导对象" align="center" prop="developObjectName" />
      <el-table-column label="督导专家" align="center" min-width="120" prop="nicknames" />
      <el-table-column label="督导时间" align="center" width="280" prop="beginEndTime" />
      <el-table-column label="综合表单合计得分" align="center" prop="comprehensiveScore" width="130">
        <template v-slot="scope">
          {{ scope.row.comprehensiveScore || "--" }}
        </template>
      </el-table-column>
      <el-table-column label="非综合表单得分" align="center" prop="score" width="120">
        <template slot="header">
          督导得分
          <el-tooltip placement="top">
            <template slot="content">
              除共享表单以外的表单，取权重平均分；若表单不是一百分，则换算成100分后再计算平均值。<br/>
              如有一张表总分200分，督导专家评分180分，那么按一百分换算可得到专家评分为90分。
            </template>
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template>
        <template v-slot="scope">
          {{ scope.row.score || "--" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="100px">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleDetail(scope.row)"
                     v-hasPermi="['rotation:hospital-supervise-comprehensive:update']" v-if="scope.row.rectificationFeedback">督导详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleFeedback(scope.row)"
                     v-hasPermi="['rotation:hospital-supervise-comprehensive:update']" v-else>督导反馈</el-button>
          </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 督导反馈/详情 -->
    <el-dialog :title="`督导${isView ? '详情' : '反馈'}`" :visible.sync="dialogOpen" width="700px" v-dialogDrag append-to-body>
      <el-form class="mb5" inline>
        <el-form-item class="mb5" style="width: 40%" label="督导对象:">{{ infos.developObjectName }}</el-form-item>
        <el-form-item class="mb5" label="督导时间:">{{ infos.startTime }}~{{ infos.endTime }}</el-form-item>
      </el-form>

      <p class="mb10" v-if="infos.resultInfoRespVOList && infos.resultInfoRespVOList.length > 0">
        综合表单合计得分：{{ infos.comprehensiveScore }}
        <el-tooltip placement="top">
          <template slot="content">
            不同综合表单求和，如若其中一张综合表有多人评分，则求平均后再与其他共享表做求和计算。
          </template>
          <i class="el-icon-info"></i>
        </el-tooltip>
      </p>

      <el-table class="mb20" :data="infos.resultInfoRespVOList">
        <el-table-column label="督导专家" prop="nickname"></el-table-column>
        <el-table-column label="综合表单评分" prop="comprehensiveScore" width="120px" align="center">
            <template v-slot="scope">
              <el-link type="primary" @click="handleScoreClick(scope.row, true)">{{ scope.row.comprehensiveScore === null ? "--" : scope.row.comprehensiveScore }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="非综合表单评分" prop="score" width="120px" align="center">
            <template v-slot="scope">
              <el-link type="primary" @click="handleScoreClick(scope.row, false)">{{ scope.row.score === null ? "--" : scope.row.score }}</el-link>
            </template>
          </el-table-column>
        <el-table-column label="督导意见" prop="opinions" min-width="240px"></el-table-column>
      </el-table>

      <el-form label-width="100px" :model="form" :rules="rules" ref="form">
        <el-form-item label="综合督导意见">
          <el-input type="textarea" :value="infos.comprehensiveOpinion" disabled></el-input>
        </el-form-item>
        <el-form-item label="整改要求" v-if="infos.isNeedRectification">
          <el-input type="textarea" :value="infos.rectificationRequire" disabled></el-input>
        </el-form-item>
        <el-form-item label="督导反馈" v-if="isView">
          <el-input type="textarea" v-model="infos.rectificationFeedback" disabled></el-input>
        </el-form-item>
        <el-form-item label="督导反馈" prop="rectificationFeedback" v-else>
          <el-input type="textarea" v-model="form.rectificationFeedback"></el-input>
        </el-form-item>
        <el-form-item label="整改附件" v-if="isView">
          <template v-if="infos.files">
            <el-link
              style="line-height: 1.2"
              v-for="file in safeJsonParseFiles(infos.files)"
              :key="file.url"
              :href="file.url"
              type="primary"
              download
            >{{ file.name }}</el-link>
          </template>
          <span v-else>暂无</span>
        </el-form-item>
        <el-form-item label="整改附件" prop="files" v-else>
          <file-upload 
            v-model="form.files" 
            :limit="9999" 
            :file-size="50" 
            :fileType="fileType"></file-upload>
        </el-form-item>
      </el-form>

      <span slot="footer" v-if="!isView">
        <el-button type="primary" @click="submitFeedback">确定</el-button>
        <el-button @click="cancelFeedback">取消</el-button>
      </span>
    </el-dialog>

    <hospital-supervise-evaluate-dialog
      :visible.sync="scoreOpen"
      :supervise-object="superviseObject"
    ></hospital-supervise-evaluate-dialog>
  </div>
</template>

<script>
import { getSupervisionExpertList, getHospitalSuperviseInfos } from "@/api/rotation/hospitalSupervise";
import { getDepartmentSimpleList } from "@/api/system/department";
import { getHospitalSuperviseComprehensivePage, updateHospitalSuperviseComprehensive } from "@/api/rotation/hospitalSuperviseComprehensive";
import FileUpload from "@/components/FileUploadInfo";
import HospitalSuperviseEvaluateDialog from "../hospitalSuperviseDevelop/hospital-supervise-evaluate-dialog";

export default {
  name: "HospitalSupervise",
  components: {
    FileUpload,
    HospitalSuperviseEvaluateDialog,
  },
  data() {
    return {
      fileType: ["doc", "xls", "ppt", "pptx", "txt", "pdf", 'png', 'jpg', 'jpeg'],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级督导列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        formType: null,
        developObject: null,
        nickname: null,
        superviseDates: [],
        hospitalSuperviseName: "",
        toBeFeedback: false,
      },
      // 查询条件督导对象列表
      queryDevelopObjectList: [],
      // 专业基地列表
      professionalBaseList: this.getDictDatas(this.DICT_TYPE.PROFESSIONAL_BASE),
      // 科室列表
      departmentOptions: [],
      // 督导专家列表
      supervisorList: [],
      // 督导反馈弹窗
      dialogOpen: false,
      // 是否为查看弹窗
      isView: false,
      // 督导信息
      infos: {},
      // 反馈表单
      form: {},
      // 表单验证
      rules: {
        rectificationFeedback: [{ required: true, message: "督导反馈不能为空", trigger: "blur" }],
      },
      // 当前行
      currentRow: null,
      // 评分详情弹窗
      scoreOpen: false,
      // 督导对象
      superviseObject: {},
    };
  },
  created() {
    this.getList();
    this.getDepartment();
  },
  methods: {
    safeJsonParseFiles(str) {
      try {
        const rs = JSON.parse(str);
        if (typeof rs === "string") {
          return rs.split(",").map(url => ({ url, name: url.split("/").pop() }))
        } else {
          return rs;
        }
      } catch (e) {
        console.log(e);
        return str.split(",").map(url => ({ url, name: url.split("/").pop() }))
      }
    },
    // 获得科室列表
    getDepartment() {
      getDepartmentSimpleList(0).then(res => {
        const {data = []} = res
        // 处理 roleOptions 参数
        const list = [];
        data.forEach(item => {
          list.push({
            label: item.name,
            value: item.id
          })
        })
        this.departmentOptions = list;
      })
    },
    // 获取督导专家列表
    getSupervisorList(developObject) {
      getSupervisionExpertList({ developObject }).then(res => {
        this.supervisorList = (res.data || []).list.map(item => ({
          label: item.nickname,
          value: item.id,
        }));
      });
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      const params = { ...this.queryParams };
      params.toBeFeedback = params.toBeFeedback || null;
      getHospitalSuperviseComprehensivePage(params).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 查询督导类型改变 */
    handleTypeChange(val) {
      switch (val) {
        case "1":
          this.queryDevelopObjectList = this.professionalBaseList;
          break;
        case "2":
          this.queryDevelopObjectList = this.getDictDatas(this.DICT_TYPE.STAFF_ROOM);
          break;
        case "3":
          this.queryDevelopObjectList = this.departmentOptions;
          break;
        default:
          this.queryDevelopObjectList = [];
      }
      this.queryParams.developObject = undefined;
    },
    /** 督导对象改变 */
    handleDevelopObjectChange(val) {
      this.getSupervisorList(val);
      this.queryParams.nickname = "";
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 督导反馈 */
    handleFeedback(row) {
      this.dialogOpen = true;
      this.isView = false;
      this.currentRow = row;
      this.form = { rectificationFeedback: "", files: null };
      getHospitalSuperviseInfos(row.id).then(res => {
        this.infos = res.data;
      });
    },
    /** 督导详情 */
    handleDetail(row) {
      this.dialogOpen = true;
      this.isView = true;
      getHospitalSuperviseInfos(row.id).then(res => {
        this.infos = res.data;
      });
    },
    submitFeedback() {
      const { id, isNeedRectification } = this.infos;
      const { rectificationFeedback, files } = this.form;
      this.$refs.form.validate(valid => {
        if (valid) {
          updateHospitalSuperviseComprehensive({ id, isNeedRectification, rectificationFeedback, files: JSON.stringify(files) }).then(() => {
            this.$message.success("督导反馈成功！");
            this.cancelFeedback();
            this.getList();
          });
        }
      });
    },
    cancelFeedback() {
      this.$refs.form.clearValidate();
      this.dialogOpen = false;
    },
    /** 点击分数查看评分详情 */
    handleScoreClick(row, isComprehensive) {
      if (isComprehensive) {
        if (row.comprehensiveScore === null) {
          return
        }
      } else {
        if (row.score === null) {
          return
        }
      }
      this.scoreOpen = true;
      this.superviseObject = {
        ...this.currentRow,
        superviseResultId: row.superviseResult,
        isComprehensive
      };
    },
  }
};
</script>
