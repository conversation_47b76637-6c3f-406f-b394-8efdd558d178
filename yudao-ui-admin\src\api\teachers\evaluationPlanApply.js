import request from "@/utils/request";

// 创建师资评优计划申请
export function createEvaluationPlanApply(data) {
  return request({
    url: "/teachers/evaluation-plan-apply/create",
    method: "post",
    data: data,
  });
}

// 更新师资评优计划申请
export function updateEvaluationPlanApply(data) {
  return request({
    url: "/teachers/evaluation-plan-apply/update",
    method: "put",
    data: data,
  });
}

// 删除师资评优计划申请
export function deleteEvaluationPlanApply(id) {
  return request({
    url: "/teachers/evaluation-plan-apply/delete?id=" + id,
    method: "delete",
  });
}

// 获得师资评优计划申请
export function getEvaluationPlanApply(id) {
  return request({
    url: "/teachers/evaluation-plan-apply/get?id=" + id,
    method: "get",
  });
}

// 获得师资评优计划申请分页
export function getEvaluationPlanApplyPage(query) {
  return request({
    url: "/teachers/evaluation-plan-apply/page",
    method: "get",
    params: query,
  });
}

// 导出师资评优计划申请 Excel
export function exportEvaluationPlanApplyExcel(query) {
  return request({
    url: "/teachers/evaluation-plan-apply/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获得师资评优计划
export function getEvaluationPlanDetails(query) {
  return request({
    url: "/teachers/evaluation-plan-apply/get-plan",
    method: "get",
    params: query,
  });
}

// 获得师资评优计划申请分页
export function getEvaluationPlanProcessInfo(query) {
  return request({
    url: "/teachers/evaluation-plan-apply/get-apply-process-info",
    method: "get",
    params: query,
  });
}
