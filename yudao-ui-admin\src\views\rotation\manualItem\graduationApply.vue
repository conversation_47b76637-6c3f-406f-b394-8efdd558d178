<template>
  <div class="graduation-apply">
    <div class="apply-summary">
      <h3>出科小结</h3>
      <el-form inline label-width="90px">
        <el-form-item label="姓名:" class="sw">{{
          studentInfo.nickname
        }}</el-form-item>
        <el-form-item label="年级:" class="sw">{{
          studentInfo.grade
        }}</el-form-item>
        <el-form-item label="学员类型:" class="sw">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="studentInfo.studentType"
          ></dict-tag>
        </el-form-item>
        <el-form-item label="培训专业:" class="sw">{{
          getMajorName(studentInfo.major)
        }}</el-form-item>
        <el-form-item label="个人小结:" required>
          <el-input
            style="width: 850px"
            type="textarea"
            :rows="4"
            :placeholder="
              config.graduationPersonalSummaryTip || '请输入个人小结'
            "
            :minlength="config.graduationPersonalSummaryMinWord"
            show-word-limit
            v-model="form && form.personalSummary"
          ></el-input>
        </el-form-item>
      </el-form>

      <div style="text-align: center">
        <el-button
          type="primary"
          plain
          @click="temporarySave"
          v-if="
            notApply || ['0', '2', '4'].includes(form.graduationAuditStatus)
          "
          >暂存</el-button
        >
        <el-button
          type="primary"
          @click="submitApply"
          v-if="
            notApply || ['0', '2', '4'].includes(form.graduationAuditStatus)
          "
          >提交出科申请</el-button
        >
      </div>
    </div>

    <div class="apply-schedule" v-if="activities.length > 0">
      <div class="apply-schedule-head">
        <span style="margin-bottom: 20px">审批进度：</span>
        <el-button type="text" @click.native="handleAppraiseStatusPreview()"
          >查看出科考核表</el-button
        >
      </div>

      <el-timeline :reverse="false">
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="index"
          placement="top"
          :timestamp="activity.step"
          :type="activity.currentStep ? 'success' : ''"
        >
          <el-tag
            size="small"
            style="margin-top: 5px"
            v-if="activity.auditStatusName"
          >
            {{ activity.auditStatusName }}
          </el-tag>
          <p style="margin-bottom: 6px" v-if="activity.nickName">
            <i class="el-icon-s-custom" style="font-size: 16px"></i>
            {{ activity.nickName }}
          </p>
          <p style="margin-top: 6px" v-if="activity.remark">
            {{ activity.remark }}
          </p>
        </el-timeline-item>
      </el-timeline>
    </div>

    <dept-audit
      :visible.sync="deptAuditVisible"
      is-view
      :row="auditData"
    ></dept-audit>
  </div>
</template>

<script>
import {
  updateGraduationApply,
  createGraduationApply,
  getGraduationApply,
  getGraduationApplySchedule,
  graduationApplyValidate,
} from "@/api/rotation/graduationApply";
import { getUserStudent } from "@/api/system/userStudent";
import { getSimpleMajorList } from "@/api/system/major";
import DeptAudit from "../graduationDeptAudit/deptAudit";

export default {
  name: "graduationApply",
  components: { DeptAudit },
  props: {
    info: Object,
    config: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      notApply: false,
      form: {},
      studentInfo: {},
      activities: [],
      majorList: [],
      deptAuditVisible: false,
      auditData: {},
    };
  },
  methods: {
    temporarySave() {
      updateGraduationApply(this.form).then(() =>
        this.$message.success("暂存成功")
      );
    },
    submitApply() {
      graduationApplyValidate(this.$route.query.id).then((res) => {
        if (!res.data.success) {
          this.$message.warning(res.data.failMsg);
          return;
        }
        createGraduationApply(this.form).then(() => {
          this.$message.success("提交出科申请成功");
          this.queryApply();
        });
      });
    },
    queryApply() {
      const id = this.$route.query.id;
      getGraduationApply(id).then((res) => {
        if (!res.data) {
          this.form = {
            scheduleDetailsId: id,
            studentId: this.info.studentId,
            personalSummary: "",
          };
          this.notApply = true;
        } else {
          this.form = res.data;
          this.notApply = false;
          getGraduationApplySchedule(id).then(
            (res) => (this.activities = res.data)
          );
        }
      });
    },
    getMajorName(major) {
      return this.majorList.find((item) => item.code === major)?.name;
    },
    handleAppraiseStatusPreview() {
      // this.curRow = row;
      this.deptAuditVisible = true;
    },
  },
  created() {
    this.queryApply();
    getUserStudent(this.info.studentId).then((res) => {
      this.studentInfo = res.data;
      this.auditData = {
        ...this.studentInfo,
        graduationApplyId: this.$route.query.id,
      };
      getSimpleMajorList({ studentType: res.data.studentType }).then(
        (res) => (this.majorList = res.data)
      );
    });
  },
};
</script>

<style lang="scss" scoped>
.graduation-apply {
  width: 1000px;
  margin: 0 auto;
}

.apply-summary {
  border-radius: 4px;
  padding: 20px;

  h3 {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
  }

  .sw {
    width: 25%;
    margin-right: 0;
  }
}

.apply-schedule {
  border-radius: 4px;
  padding: 20px;

  .apply-schedule-head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 14px;
      margin: 0 0 0 20px;
      color: #606266;
      font-weight: 700;
    }
  }

  ::v-deep .el-timeline-item__timestamp {
    font-size: 14px;
  }
}
</style>
