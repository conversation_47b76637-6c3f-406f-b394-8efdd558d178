import request from '@/utils/request'

// 创建学员入科
export function createStudentEnrollment(data) {
  return request({
    url: '/rotation/student-enrollment/create',
    method: 'post',
    data: data
  })
}

// 更新学员入科
export function updateStudentEnrollment(data) {
  return request({
    url: '/rotation/student-enrollment/update',
    method: 'put',
    data: data
  })
}

// 删除学员入科
export function deleteStudentEnrollment(id) {
  return request({
    url: '/rotation/student-enrollment/delete?id=' + id,
    method: 'delete'
  })
}

// 获得学员入科
export function getStudentEnrollment(id) {
  return request({
    url: '/rotation/student-enrollment/get?scheduleDetailsId=' + id,
    method: 'get'
  })
}

// 获得学员入科分页
export function getStudentEnrollmentPage(query) {
  return request({
    url: '/rotation/student-enrollment/page',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/studentEnrollment/index'}
  })
}

// 导出学员入科 Excel
export function exportStudentEnrollmentExcel(query) {
  return request({
    url: '/rotation/student-enrollment/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 学员入科
export function saveStudentEnrollment(data) {
  return request({
    url: '/rotation/student-enrollment/save',
    method: 'post',
    data: data
  })
}
