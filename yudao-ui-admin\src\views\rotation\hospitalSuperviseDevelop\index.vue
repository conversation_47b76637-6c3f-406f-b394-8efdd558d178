<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="督导类型" prop="formType">
        <el-select
          v-model="queryParams.formType"
          placeholder="请选择督导类型"
          clearable
          size="small"
          @change="handleTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="督导对象" prop="developObject">
        <el-select
          v-model="queryParams.developObject"
          placeholder="请选择督导对象"
          clearable
          filterable
        >
          <el-option
            v-for="item in queryDevelopObjectList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="督导时间" prop="superviseDates">
        <el-date-picker
          v-model="queryParams.superviseDates"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="hospitalSuperviseStatus">
        <el-input
          v-model="queryParams.hospitalSuperviseName"
          placeholder="请输入督导项目名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="toBeSupervised">
        <el-checkbox
          v-model="queryParams.toBeSupervised"
          label="待督导"
        ></el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="项目名称"
        align="center"
        prop="hospitalSuperviseName"
        width="160px"
      ></el-table-column>
      <el-table-column label="督导类型" align="center" prop="formType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE"
            :value="scope.row.formType"
          />
        </template>
      </el-table-column>
      <el-table-column label="督导对象" align="center" prop="developObjectName">
        <template slot-scope="scope">
          <el-link type="primary" @click="viewObject(scope.row)">{{
            scope.row.developObjectName
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="督导时间"
        align="center"
        width="280"
        prop="beginEndTime"
      />
      <el-table-column
        label="综合表单得分"
        align="center"
        prop="comprehensiveScore"
      >
        <template v-slot="scope">
          <el-link type="primary" @click="handleScoreClick(scope.row, true)">{{
            scope.row.comprehensiveScore === null
              ? "--"
              : scope.row.comprehensiveScore
          }}</el-link>
        </template>
        <!-- <template v-slot="scope">
          {{ scope.row.comprehensiveScore === null ? "--" : scope.row.comprehensiveScore }}
        </template> -->
      </el-table-column>
      <el-table-column label="非综合表单得分" align="center" prop="score">
        <template v-slot="scope">
          <el-link type="primary" @click="handleScoreClick(scope.row, false)">{{
            scope.row.score === null ? "--" : scope.row.score
          }}</el-link>
        </template>
        <!-- <template v-slot="scope">
          {{ scope.row.score === null ? "--" : scope.row.score }}
        </template> -->
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="160px"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleSupervise(scope.row)"
            v-hasPermi="['rotation:hospital-supervise-develop:update']"
            v-if="!scope.row.superviseResultId"
            >督导开展</el-button
          >
          <!-- <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
                     v-hasPermi="['rotation:hospital-supervise-develop:update']" v-if="scope.row.superviseResultId">评分详情</el-button> -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleConfirm(scope.row)"
            v-hasPermi="['rotation:hospital-supervise-develop:update']"
            v-if="scope.row.showConfirm"
            >督导确认</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="督导表单选择"
      :visible.sync="openFormChoice"
      width="980px"
      v-dialogDrag
      append-to-body
      custom-class="develop-supform-choice"
    >
      <el-form label-width="70px">
        <el-form-item
          class="inline-block w-50"
          label="督导类型"
          prop="formType"
        >
          <el-input
            style="width: 220px"
            :value="
              getDictDataLabel(
                DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE,
                formParams.formType
              )
            "
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          class="inline-block w-50"
          label="督导对象"
          prop="developObject"
        >
          <el-input
            class="w-100"
            v-model="formParams.developObjectName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item label="表单名称">
          <el-input
            style="width: 220px; margin-right: 10px"
            v-model="formParams.name"
            placeholder="输入表单名称快速检索表单"
            clearable
          ></el-input>
          <el-button type="primary" @click="querySimpleFormList"
            >表单检索</el-button
          >
        </el-form-item>
        <el-form-item label-width="0px">
          <el-transfer
            v-model="selectedFormList"
            :data="formList"
            :titles="['表单列表', '已选表单']"
          >
            <span slot-scope="{ option }" :title="option.label">{{
              option.label
            }}</span>
          </el-transfer>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleStartSupervise"
          >确 定</el-button
        >
        <el-button @click="cancelFormChoice">取 消</el-button>
      </div>
    </el-dialog>

    <hospital-supervise-evaluate-dialog
      :visible.sync="evaluateDialog"
      :supervise-object="superviseObject"
      :supervise-form-ids="selectedFormList"
      @saved="handleEvaluateSaved"
    ></hospital-supervise-evaluate-dialog>

    <supervise-object-dialog
      :visible.sync="superviseObjectDialog"
      :supervise-object="superviseObject"
    ></supervise-object-dialog>

    <el-dialog
      title="督导确认"
      :visible.sync="confirmOpen"
      width="700px"
      v-dialogDrag
      append-to-body
    >
      <el-form label-width="100px">
        <el-form-item
          class="inline-block"
          style="width: 45%"
          label="督导对象："
          >{{ superviseInfo.developObjectName }}</el-form-item
        >
        <el-form-item class="inline-block" label="督导时间："
          >{{ superviseInfo.startTime }}~{{
            superviseInfo.endTime
          }}</el-form-item
        >
        <el-form-item label="我的督导意见">
          <el-input
            type="textarea"
            disabled
            v-model="superviseInfo.opinions"
          ></el-input>
        </el-form-item>
        <el-form-item label="综合督导意见">
          <el-input
            type="textarea"
            disabled
            v-model="superviseInfo.comprehensiveOpinion"
          ></el-input>
        </el-form-item>
        <el-form-item label="整改要求" v-if="superviseInfo.isNeedRectification">
          <el-input
            type="textarea"
            disabled
            v-model="superviseInfo.rectificationRequire"
          ></el-input>
        </el-form-item>
        <el-form-item label="督导反馈">
          <el-input
            type="textarea"
            disabled
            v-model="superviseInfo.rectificationFeedback"
          ></el-input>
        </el-form-item>
        <el-form-item label="整改附件">
          <template v-if="superviseInfo.files">
            <file-upload
              v-model="superviseInfo.files"
              :limit="9999"
              :file-size="50"
              :fileType="fileType"
              :disabled="true"
              :isShowTip="false"
            >
            </file-upload>
            <!-- <el-link
              style="line-height: 1.2"
              v-for="file in safeJsonParseFiles(superviseInfo.files)"
              :key="file.url"
              :href="file.url"
              type="primary"
              download
            >{{ file.name }}</el-link> -->
          </template>
          <span v-else>暂无</span>
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button type="primary" @click="sureRectifyDone"
          >确认已完成整改工作</el-button
        >
        <el-button @click="cancelConfirm">取消</el-button>
      </span>
    </el-dialog>

    <hospital-supervise-evaluate-dialog
      :visible.sync="scoreOpen"
      :supervise-object="superviseObject"
    ></hospital-supervise-evaluate-dialog>
  </div>
</template>

<script>
import { getDepartmentSimpleList } from "@/api/system/department";
import {
  getHospitalSuperviseDevelopList,
  getHospitalSuperviseInfos,
  updateHospitalSuperviseDevelop,
} from "@/api/rotation/hospitalSuperviseDevelop";
import { getSimpleFormList } from "@/api/rotation/teachingActiveSupervise";
import HospitalSuperviseEvaluateDialog from "./hospital-supervise-evaluate-dialog";
import SuperviseObjectDialog from "./superviseObjectDialog";
import FileUpload from "@/components/FileUploadInfo";

export default {
  name: "HospitalSupervise",
  components: {
    HospitalSuperviseEvaluateDialog,
    SuperviseObjectDialog,
    FileUpload,
  },
  data() {
    return {
      fileType: [
        "doc",
        "xls",
        "ppt",
        "pptx",
        "txt",
        "pdf",
        "png",
        "jpg",
        "jpeg",
      ],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级督导列表
      list: [],
      // 弹出层标题
      title: "",
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        formType: null,
        developObject: null,
        superviseDates: [],
        hospitalSuperviseName: "",
        toBeSupervised: false,
      },
      // 查询条件督导对象列表
      queryDevelopObjectList: [],
      // 专业基地列表
      professionalBaseList: this.getDictDatas(this.DICT_TYPE.PROFESSIONAL_BASE),
      // 科室列表
      departmentOptions: [],
      // 选择表单弹窗
      openFormChoice: false,
      // 当前操作督导
      superviseObject: {},
      // 表单筛选条件
      formParams: {
        superviseType: "2",
        formType: "",
        developObject: "",
        name: "",
      },
      // 带选择表单
      formList: [],
      // 选中表单
      selectedFormList: [],
      // 评价表单弹窗
      evaluateDialog: false,
      // 督导信息
      superviseInfo: {},
      // 督导确认弹窗
      confirmOpen: false,
      superviseObjectDialog: false,
      scoreOpen: false,
    };
  },
  created() {
    this.getList();
    this.getDepartment();
  },
  methods: {
    safeJsonParseFiles(str) {
      try {
        const rs = JSON.parse(str);
        if (typeof rs === "string") {
          return rs
            .split(",")
            .map((url) => ({ url, name: url.split("/").pop() }));
        } else {
          return rs;
        }
      } catch (e) {
        console.log(e);
        return str
          .split(",")
          .map((url) => ({ url, name: url.split("/").pop() }));
      }
    },
    // 获得科室列表
    getDepartment() {
      getDepartmentSimpleList(0).then((res) => {
        const { data = [] } = res;
        // 处理 roleOptions 参数
        const list = [];
        data.forEach((item) => {
          list.push({
            label: item.name,
            value: item.id,
          });
        });
        this.departmentOptions = list;
      });
    },
    /** 查询督导类型改变 */
    handleTypeChange(val) {
      switch (val) {
        case "1":
          this.queryDevelopObjectList = this.professionalBaseList;
          break;
        case "2":
          this.queryDevelopObjectList = this.getDictDatas(
            this.DICT_TYPE.STAFF_ROOM
          );
          break;
        case "3":
          this.queryDevelopObjectList = this.departmentOptions;
          break;
        default:
          this.queryDevelopObjectList = [];
      }
      this.queryParams.developObject = undefined;
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      const params = { ...this.queryParams };
      params.toBeSupervised = params.toBeSupervised || null;
      getHospitalSuperviseDevelopList(params).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 督导开展 */
    handleSupervise(row) {
      this.openFormChoice = true;
      this.superviseObject = row;
      this.formParams.formType = row.formType;
      this.formParams.developObject = row.developObject;
      this.formParams.developObjectName = row.developObjectName;
      if (row.superviseFormIds) {
        this.formParams.superviseFormIds = row.superviseFormIds.split(",");
      }
      this.formParams.name = "";
      this.querySimpleFormList().then(() => {
        this.selectedFormList = [];
        if (this.formList.length === 1) {
          this.selectedFormList.push(this.formList[0].key);
        }
      });
    },
    querySimpleFormList() {
      return getSimpleFormList(this.formParams).then((res) => {
        this.formList = res.data.map((item) => ({
          key: item.id,
          label: item.name,
        }));
      });
    },
    handleStartSupervise() {
      if (this.selectedFormList.length === 0) {
        this.$message.warning("请选择至少一个督导表单");
        return;
      }
      this.evaluateDialog = true;
    },
    cancelFormChoice() {
      this.openFormChoice = false;
      this.superviseObject = {};
      this.formList = [];
      this.selectedFormList = [];
    },
    handleEvaluateSaved() {
      this.getList();
      this.cancelFormChoice();
    },
    /** 查看评分详情 */
    handleView(row) {
      this.superviseObject = row;
      this.selectedFormList = undefined;
      this.evaluateDialog = true;
    },
    /** 督导确认 */
    handleConfirm(row) {
      this.superviseObject = row;
      this.confirmOpen = true;
      getHospitalSuperviseInfos(row.id).then((res) => {
        this.superviseInfo = res.data;
        this.superviseInfo.files = this.safeJsonParseFiles(
          this.superviseInfo.files
        );
      });
    },
    sureRectifyDone() {
      updateHospitalSuperviseDevelop({ id: this.superviseObject.id }).then(
        () => {
          this.$message.success("确认已完成整改工作成功！");
          this.cancelConfirm();
        }
      );
    },
    cancelConfirm() {
      this.superviseObject = {};
      this.superviseInfo = {};
      this.confirmOpen = false;
      this.getList();
    },
    viewObject(row) {
      this.superviseObject = row;
      this.superviseObjectDialog = true;
    },
    /** 点击分数查看评分详情 */
    handleScoreClick(row, isComprehensive) {
      if (isComprehensive) {
        if (row.comprehensiveScore === null) {
          return;
        }
      } else {
        if (row.score === null) {
          return;
        }
      }
      this.scoreOpen = true;
      this.superviseObject = {
        ...row,
        isComprehensive,
      };
    },
  },
};
</script>

<style lang="scss">
.develop-supform-choice {
  .el-transfer-panel {
    width: 43%;
  }

  .el-transfer__buttons {
    display: inline-flex;
    flex-direction: column;

    .el-button + .el-button {
      margin-left: 0;
    }
  }
}
</style>
