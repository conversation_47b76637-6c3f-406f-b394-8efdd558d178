import request from '@/utils/request'

// 创建标准方案
export function createStandardScheme(data) {
  return request({
    url: '/rotation/standard-scheme/create',
    method: 'post',
    data: data
  })
}

// 更新标准方案
export function updateStandardScheme(data) {
  return request({
    url: '/rotation/standard-scheme/update',
    method: 'put',
    data: data
  })
}

// 删除标准方案
export function deleteStandardScheme(id) {
  return request({
    url: '/rotation/standard-scheme/delete?id=' + id,
    method: 'delete'
  })
}

// 获得标准方案
export function getStandardScheme(id) {
  return request({
    url: '/rotation/standard-scheme/get?id=' + id,
    method: 'get'
  })
}

// 获得标准方案分页
export function getStandardSchemePage(query) {
  return request({
    url: '/rotation/standard-scheme/page',
    method: 'get',
    params: query
  })
}

// 导出标准方案 Excel
export function exportStandardSchemeExcel(query) {
  return request({
    url: '/rotation/standard-scheme/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 发布标准方案
export function publishStandardScheme(id) {
  return request({
    url: '/rotation/standard-scheme/publish',
    method: 'put',
    params: { id }
  })
}

// 取消发布标准方案
export function unpublishStrandardScheme(id) {
  return request({
    url: '/rotation/standard-scheme/unpublish',
    method: 'put',
    params: { id }
  })
}

// 获得标准方案配置通过排班详情id
export function getConfigByScheduleDetailsId(scheduleDetailsId) {
  return request({
    url: '/rotation/standard-scheme/get-config-by-schedule-details-id',
    method: 'get',
    params: { scheduleDetailsId }
  })
}
