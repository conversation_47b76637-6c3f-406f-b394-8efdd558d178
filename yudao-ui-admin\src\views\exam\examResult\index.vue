<template>
  <div class="examResult-container">
    <div class="examResult-main" v-if="isAllowView">
      <div class="examResult-header">
        <div class="examResult-title">
          <div class="title">{{ paperInfo.paperName }}</div>
          <div class="examResult-attrs">
            <span>考生姓名：{{ paperInfo.nickname }}</span>
            <span>总分：{{ paperInfo.paperTotalScore }}</span>
            <span>考试用时：{{ paperInfo.answerMinuteTime || 0 }}分钟</span>
          </div>
        </div>
        <div
          class="examResult-score"
          :style="{
            color:
              paperInfo.score >= paperInfo.paperPassScore
                ? '#67C23A'
                : '#F56C6C',
          }"
        >
          <span style="font-size: 1.5em">{{ paperInfo.score }}</span
          >分
        </div>
      </div>

      <div class="examResult-cont">
        <div class="examResult-left">
          <div class="left-header">
            <div class="left-title">答题卡</div>
            <div class="left-title-tips">
              <div class="right">正确</div>
              <div class="wrong">错误</div>
              <div class="yellow">主观判断</div>
            </div>
          </div>
          <div class="left-cont">
            <div
              class="left-group"
              v-for="element in answerQuestionTypes"
              :key="element.questionType"
            >
              <div class="left-group-title">
                <dict-tag
                  :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                  :value="element.questionType"
                />
              </div>
              <div class="left-group-cont">
                <a
                  v-for="(item, index) in element.answerQuestionResults"
                  :class="
                    item.answerResultStatus === '0'
                      ? 'wrong'
                      : item.answerResultStatus === '2'
                      ? 'yellow'
                      : 'right'
                  "
                  :key="index"
                  :href="'#' + element.questionType + '-' + index"
                >
                  {{ index + 1 }}
                </a>
              </div>
            </div>
          </div>
          <div class="left-bottom">
            <el-button type="primary" @click="handleReview">导出预览</el-button>
          </div>
        </div>

        <div class="examResult-right">
          <div>
            <div
              class="right-group"
              v-for="element in answerQuestionTypes"
              :key="element.questionType"
            >
              <div class="left-group-title">
                <dict-tag
                  :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                  :value="element.questionType"
                />
              </div>
              <div
                class="question-item"
                v-for="(item, index) in element.answerQuestionResults"
                :key="index"
                :id="element.questionType + '-' + index"
              >
                <div class="question-item-header">
                  <span class="NO">{{ index + 1 }}、</span>
                  <span class="type"
                    >【<dict-tag
                      :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                      :value="element.questionType"
                    />】</span
                  >
                  <span class="title" v-if="element.questionType !== '3'">
                    <text-to-html
                      :text="item.content.title"
                      :images="getPreviewImages(item.content.titleImages)"
                    ></text-to-html
                    >（ ）
                  </span>
                  <span class="score">({{ element.score }}分)</span>
                  <div
                    class="compat-choice"
                    v-if="element.questionType === '3'"
                  >
                    <div v-for="(choice, key) in item.content.choiceList">
                      {{ key }}、<text-to-html
                        :text="choice"
                        :images="
                          getPreviewImages(
                            (item.content.choiceImages || {})[key]
                          )
                        "
                      ></text-to-html>
                    </div>
                  </div>
                </div>

                <template
                  v-if="['1', '2', '8'].indexOf(element.questionType) > -1"
                >
                  <div class="question-item-cont">
                    <div
                      v-if="
                        element.questionType === '1' ||
                        element.questionType === '8'
                      "
                    >
                      <el-radio-group :value="item.answerResult">
                        <el-radio
                          v-for="(choice, key) in item.content.choiceList"
                          :label="key"
                          :key="key"
                          :class="{
                            'wrong-select':
                              item.answerResultStatus === '0' &&
                              key === item.answerResult,
                          }"
                          @click.native.prevent
                        >
                          {{ key }}、<text-to-html
                            :text="choice"
                            :images="
                              getPreviewImages(
                                (item.content.choiceImages || {})[key]
                              )
                            "
                          ></text-to-html>
                        </el-radio>
                      </el-radio-group>
                    </div>
                    <div v-if="element.questionType === '2'">
                      <el-checkbox-group :value="item.answerResult">
                        <el-checkbox
                          v-for="(choice, key) in item.content.choiceList"
                          :label="key"
                          :key="key"
                          :class="{
                            'wrong-select':
                              item.answerResultStatus === '0' &&
                              item.answerResult.indexOf(key) > -1,
                          }"
                          @click.native.prevent
                        >
                          {{ key }}、<text-to-html
                            :text="choice"
                            :images="
                              getPreviewImages(
                                (item.content.choiceImages || {})[key]
                              )
                            "
                          ></text-to-html>
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </div>
                  <div class="question-item-answer">
                    正确答案：
                    {{
                      element.questionType === "8"
                        ? item.answer === "A"
                          ? "正确"
                          : "错误"
                        : item.answer
                    }}
                  </div>
                  <div class="question-item-analysis" v-if="item.analysis">
                    <strong>试题解析：</strong> {{ item.analysis }}
                  </div>
                </template>
                <template v-if="element.questionType === '3'">
                  <div class="sub-questions">
                    <div
                      v-for="(title, key, index) in item.content.titleList"
                      :key="key"
                      :class="{
                        'wrong-select':
                          item.answerResultStatus === '0' &&
                          (item.answer || '').split(',')[index] !==
                            item.answerResult[index],
                      }"
                    >
                      {{ key }}、<text-to-html
                        :text="title"
                        :images="
                          getPreviewImages(
                            (item.content.titleListImages || {})[key]
                          )
                        "
                      ></text-to-html>
                      (
                      <el-select
                        class="compat-select"
                        size="mini"
                        :value="item.answerResult[index]"
                      >
                        <el-option
                          v-for="(choice, key) in item.content.choiceList"
                          :label="key"
                          :value="key"
                        ></el-option>
                      </el-select>
                      )
                    </div>
                  </div>
                  <div class="question-item-answer">
                    正确答案：
                    <template
                      v-for="item in (item.answer || '')
                        .split(',')
                        .map((val, index) => `${index + 1})、${val}`)"
                    >
                      {{ item }}
                      <span style="display: inline-block; width: 20px"></span>
                    </template>
                  </div>
                  <div class="question-item-analysis" v-if="item.analysis">
                    <strong>试题解析：</strong> {{ item.analysis }}
                  </div>
                </template>
                <template v-if="element.questionType === '4'">
                  <div class="sub-questions">
                    <div
                      v-for="(subsetTitle, index) in item.content
                        .subsetTitles || []"
                      :key="index"
                    >
                      <div class="question-item-header">
                        <span class="NO">{{ index + 1 }}、</span>
                        <span class="title"
                          ><text-to-html
                            :text="subsetTitle.title"
                            :images="getPreviewImages(subsetTitle.titleImages)"
                          ></text-to-html
                          >（）</span
                        >
                      </div>
                      <div class="question-item-cont">
                        <el-checkbox-group
                          :value="item.answerResult[index] || []"
                        >
                          <el-checkbox
                            v-for="(choice, key) in subsetTitle.choiceList"
                            :label="key"
                            :key="key"
                            :class="{
                              'wrong-select': getSubsetTitleChoiceStatus(
                                item.answerResult[index] || [],
                                subsetTitle.answer,
                                key
                              ),
                            }"
                          >
                            {{ key }}、<text-to-html
                              :text="choice"
                              :images="
                                getPreviewImages(
                                  (subsetTitle.choiceImages || {})[key]
                                )
                              "
                            ></text-to-html>
                          </el-checkbox>
                        </el-checkbox-group>
                      </div>
                      <div class="question-item-answer">
                        正确答案： {{ item.answer.split(",")[index] }}
                      </div>
                      <div
                        class="question-item-analysis"
                        v-if="subsetTitle.analysis"
                      >
                        <strong>试题解析：</strong> {{ item.analysis }}
                      </div>
                    </div>
                  </div>
                </template>
                <template v-if="['10'].indexOf(element.questionType) > -1">
                  <div class="question-item-cont">
                    <div
                      v-for="(result, index) in item.answerResult"
                      class="question-tiankong-row"
                    >
                      <span>
                        第
                        {{
                          [
                            "一",
                            "二",
                            "三",
                            "四",
                            "五",
                            "六",
                            "七",
                            "八",
                            "九",
                            "十",
                          ][index]
                        }}
                        空
                      </span>
                      <el-input
                        :key="index"
                        v-model="item.answerResult[index]"
                        placeholder="请输入对应空答案"
                      ></el-input>
                    </div>
                  </div>
                  <div class="question-item-answer">
                    正确答案：
                    {{ item.answer }}
                  </div>
                  <div class="question-item-analysis" v-if="item.analysis">
                    <strong>试题解析：</strong> {{ item.analysis }}
                  </div>
                </template>

                <template v-if="['5', '6'].indexOf(element.questionType) > -1">
                  <div class="question-item-cont">
                    <el-input
                      v-model="item.answerResult"
                      type="textarea"
                      :autosize="{ minRows: 2, maxRows: 6 }"
                      placeholder="请输入试题答案"
                    ></el-input>
                  </div>
                  <div class="question-item-answer">
                    正确答案：
                    {{ item.answer }}
                  </div>
                  <div class="question-item-analysis" v-if="item.analysis">
                    <strong>试题解析：</strong> {{ item.analysis }}
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="fullscreen-wrapper" v-else>
      <el-result
        icon="success"
        :title="resultTitle"
        v-if="resultTitle"
      ></el-result>
    </div>

    <el-dialog
      title="导出预览"
      :visible.sync="open"
      width="1000px"
      v-dialogDrag
      append-to-body
    >
      <div id="view-group-box" style="padding: 30px">
        <div
          class="examResult-header export-item"
          style="
            box-shadow: none;
            margin-bottom: 20px;
            padding-left: 0;
            padding-right: 0;
            padding-top: 0;
          "
        >
          <div class="examResult-title">
            <div class="title">{{ paperInfo.paperName }}</div>
            <div class="examResult-attrs">
              <span>考生姓名：{{ paperInfo.nickname }}</span>
              <span>总分：{{ paperInfo.paperTotalScore }}</span>
              <span>考试用时：{{ paperInfo.answerMinuteTime || 0 }}分钟</span>
            </div>
          </div>
          <div
            class="examResult-score"
            :style="{
              color:
                paperInfo.score >= paperInfo.paperPassScore
                  ? '#67C23A'
                  : '#F56C6C',
            }"
          >
            <span style="font-size: 1.5em">{{ paperInfo.score }}</span
            >分
          </div>
        </div>

        <div
          class="right-group"
          v-for="element in answerQuestionTypes"
          :key="element.questionType"
        >
          <div class="left-group-title export-item">
            <dict-tag
              :type="DICT_TYPE.EXAM_QUESTION_TYPE"
              :value="element.questionType"
            />
          </div>
          <div
            class="question-item export-item"
            v-for="(item, index) in element.answerQuestionResults"
            :key="index"
            :id="element.questionType + '-' + index"
          >
            <div class="question-item-header">
              <span class="NO">{{ index + 1 }}、</span>
              <span class="type"
                >【<dict-tag
                  :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                  :value="element.questionType"
                />】</span
              >
              <span class="title" v-if="element.questionType !== '3'">
                <text-to-html
                  :text="item.content.title"
                  :images="getPreviewImages(item.content.titleImages)"
                ></text-to-html
                >（ ）
              </span>
              <span class="score">({{ element.score }}分)</span>
              <div class="compat-choice" v-if="element.questionType === '3'">
                <div v-for="(choice, key) in item.content.choiceList">
                  {{ key }}、<text-to-html
                    :text="choice"
                    :images="
                      getPreviewImages((item.content.choiceImages || {})[key])
                    "
                  ></text-to-html>
                </div>
              </div>
            </div>

            <template v-if="['1', '2', '8'].indexOf(element.questionType) > -1">
              <div class="question-item-cont">
                <div
                  v-if="
                    element.questionType === '1' || element.questionType === '8'
                  "
                >
                  <el-radio-group :value="item.answerResult">
                    <el-radio
                      v-for="(choice, key) in item.content.choiceList"
                      :label="key"
                      :key="key"
                      :class="{
                        'wrong-select':
                          item.answerResultStatus === '0' &&
                          key === item.answerResult,
                      }"
                      @click.native.prevent
                    >
                      {{ key }}、<text-to-html
                        :text="choice"
                        :images="
                          getPreviewImages(
                            (item.content.choiceImages || {})[key]
                          )
                        "
                      ></text-to-html>
                    </el-radio>
                  </el-radio-group>
                </div>
                <div v-if="element.questionType === '2'">
                  <el-checkbox-group :value="item.answerResult">
                    <el-checkbox
                      v-for="(choice, key) in item.content.choiceList"
                      :label="key"
                      :key="key"
                      :class="{
                        'wrong-select':
                          item.answerResultStatus === '0' &&
                          item.answerResult.indexOf(key) > -1,
                      }"
                      @click.native.prevent
                    >
                      {{ key }}、<text-to-html
                        :text="choice"
                        :images="
                          getPreviewImages(
                            (item.content.choiceImages || {})[key]
                          )
                        "
                      ></text-to-html>
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
              <div class="question-item-answer">
                正确答案：
                {{
                  element.questionType === "8"
                    ? item.answer === "A"
                      ? "正确"
                      : "错误"
                    : item.answer
                }}
              </div>
              <div class="question-item-analysis" v-if="item.analysis">
                <strong>试题解析：</strong> {{ item.analysis }}
              </div>
            </template>
            <template v-if="element.questionType === '3'">
              <div class="sub-questions">
                <div
                  v-for="(title, key, index) in item.content.titleList"
                  :key="key"
                  :class="{
                    'wrong-select':
                      item.answerResultStatus === '0' &&
                      (item.answer || '').split(',')[index] !==
                        item.answerResult[index],
                  }"
                >
                  {{ key }}、<text-to-html
                    :text="title"
                    :images="
                      getPreviewImages(
                        (item.content.titleListImages || {})[key]
                      )
                    "
                  ></text-to-html>
                  (
                  <el-select
                    class="compat-select"
                    size="mini"
                    :value="item.answerResult[index]"
                  >
                    <el-option
                      v-for="(choice, key) in item.content.choiceList"
                      :label="key"
                      :value="key"
                    ></el-option>
                  </el-select>
                  )
                </div>
              </div>
              <div class="question-item-answer">
                正确答案：
                <template
                  v-for="item in (item.answer || '')
                    .split(',')
                    .map((val, index) => `${index + 1})、${val}`)"
                >
                  {{ item }}
                  <span style="display: inline-block; width: 20px"></span>
                </template>
              </div>
              <div class="question-item-analysis" v-if="item.analysis">
                <strong>试题解析：</strong> {{ item.analysis }}
              </div>
            </template>
            <template v-if="element.questionType === '4'">
              <div class="sub-questions">
                <div v-for="(subsetTitle, index) in item.content.subsetTitles">
                  <div class="question-item-header">
                    <span class="NO">{{ index + 1 }}、</span>
                    <span class="title"
                      ><text-to-html
                        :text="subsetTitle.title"
                        :images="getPreviewImages(subsetTitle.titleImages)"
                      ></text-to-html
                      >（ ）</span
                    >
                  </div>
                  <div class="question-item-cont">
                    <el-checkbox-group :value="item.answerResult[index] || []">
                      <el-checkbox
                        v-for="(choice, key) in subsetTitle.choiceList"
                        :label="key"
                        :key="key"
                        :class="{
                          'wrong-select': getSubsetTitleChoiceStatus(
                            item.answerResult[index] || [],
                            subsetTitle.answer,
                            key
                          ),
                        }"
                      >
                        {{ key }}、<text-to-html
                          :text="choice"
                          :images="
                            getPreviewImages(
                              (subsetTitle.choiceImages || {})[key]
                            )
                          "
                        ></text-to-html>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <div class="question-item-answer">
                    正确答案： {{ item.answer.split(",")[index] }}
                  </div>
                  <div
                    class="question-item-analysis"
                    v-if="subsetTitle.analysis"
                  >
                    <strong>试题解析：</strong> {{ item.analysis }}
                  </div>
                </div>
              </div>
            </template>
            <template v-if="['10'].indexOf(element.questionType) > -1">
              <div class="question-item-cont">
                <div
                  v-for="(result, index) in item.answerResult"
                  class="question-tiankong-row"
                >
                  <span>
                    第
                    {{
                      [
                        "一",
                        "二",
                        "三",
                        "四",
                        "五",
                        "六",
                        "七",
                        "八",
                        "九",
                        "十",
                      ][index]
                    }}
                    空
                  </span>
                  <el-input
                    :key="index"
                    v-model="item.answerResult[index]"
                    placeholder="请输入对应空答案"
                  ></el-input>
                </div>
              </div>
              <div class="question-item-answer">
                正确答案：
                {{ item.answer }}
              </div>
              <div class="question-item-analysis" v-if="item.analysis">
                <strong>试题解析：</strong> {{ item.analysis }}
              </div>
            </template>

            <template v-if="['5', '6'].indexOf(element.questionType) > -1">
              <div class="question-item-cont">
                <el-input
                  v-model="item.answerResult"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  placeholder="请输入试题答案"
                ></el-input>
              </div>
              <div class="question-item-answer">
                正确答案：
                {{ item.answer }}
              </div>
              <div class="question-item-analysis" v-if="item.analysis">
                <strong>试题解析：</strong> {{ item.analysis }}
              </div>
            </template>
          </div>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
        style="text-align: right; padding-top: 15px"
      >
        <el-button @click="open = false">取 消</el-button>
        <el-button type="primary" :loading="exportLoading" @click="handleDown"
          >导出</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAnswerResult, getOwnAnswerResult } from "@/api/exam/answerResult";
import TextToHtml from "../components/TextToHtml";
import { getAccessToken } from "@/utils/auth";
import { exportPDF } from "@/utils/exportUtils";

export default {
  name: "ExamResult",
  components: { TextToHtml },
  data() {
    return {
      paperInfo: {},
      answerQuestionTypes: [],
      open: false,
      exportLoading: false,
      resultTitle: "",
      isAllowView: false,
    };
  },
  mounted() {
    const { id, from } = this.$route.query;
    const answerResultApi =
      from === "admin" ? getAnswerResult : getOwnAnswerResult;
    answerResultApi(id).then((res) => {
      if (!res.data) {
        this.$message.warning("试卷没有数据，请联系管理员");
        return;
      }
      this.paperInfo = res.data;
      this.isAllowView = res.data.isAllowView;
      this.resultTitle = res.data.isShowScoreAfterAnswer
        ? `提交成功，本次考试得分${res.data.score}分，考试结束！`
        : "提交成功，本次考试结束！";
      this.answerQuestionTypes = this.paperInfo.answerQuestionTypes;
      this.paperInfo.answerQuestionTypes.forEach((type) => {
        type.answerQuestionResults.forEach((question) => {
          try {
            question.content = JSON.parse(question.content);
          } catch (e) {
            question.content = { title: "", choiceList: [] };
          }
          if (question.questionType === "2") {
            question.answerResult = question.answerResult
              ? question.answerResult.split("")
              : [];
          }
          if (question.questionType === "3" || question.questionType === "10") {
            question.answerResult = question.answerResult
              ? question.answerResult.split(",")
              : [];
          }
          if (question.questionType === "4") {
            question.answerResult = question.answerResult
              ? question.answerResult
                  .split(",")
                  .map((answers) => answers.split(""))
              : [];
          }
          // if (question.questionType === "10") {
          //   // 匹配中文括号对（）或英文括号对()
          //   const regex = /(?:（）|\(\))/g;
          //   const matches = question.content?.title.match(regex);
          //   question.answerResult = matches?.map((item) =>
          //     item.replace(/（|）|\(|\)/g, "")
          //   );
          // }
        });
      });
    });
  },
  methods: {
    getPreviewImages(images) {
      // return (images || []).map(
      //   (item) =>
      //     `${process.env.VUE_APP_BASE_API}${item.url}?token=${getAccessToken()}`
      // );
      return (images || []).map((item) => {
        const url = `${process.env.VUE_APP_BASE_API}${
          item.url
        }?token=${getAccessToken()}`;
        console.log("getPreviewImages====", url);
        return url;
      });
    },
    getSubsetTitleChoiceStatus(answerResult, answer, key) {
      const answerWrong = [...answerResult].sort().join("") !== answer;
      return answerWrong && answerResult.indexOf(key) > -1;
    },
    handleReview() {
      this.open = true;
    },
    handleDown() {
      this.exportLoading = true;
      exportPDF(
        "view-group-box",
        `${this.paperInfo.nickname}+${this.paperInfo.paperName}理论成绩+${this.paperInfo.score}分`,
        () => {
          this.exportLoading = false;
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.examResult-container {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: #fafafa;

  .fullscreen-wrapper {
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center;
  }

  .examResult-main {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .examResult-cont {
      height: calc(100% - 75px);
      padding: 15px;
      display: flex;
      justify-content: space-between;

      .examResult-left {
        width: 25%;
        height: 100%;
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        position: relative;
        display: flex;
        flex-direction: column;

        .left-header {
          padding: 10px 15px;
          border-bottom: 1px #eee solid;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .left-title {
            font-size: 16px;
          }

          .left-title-tips {
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .right,
            .wrong,
            .yellow {
              position: relative;
              padding-left: 15px;
            }
            .right {
              margin-left: 15px;
              &::before {
                content: " ";
                display: inline-block;
                width: 10px;
                height: 10px;
                background: #409eff;
                position: absolute;
                left: 0;
                top: 3px;
              }
            }
            .wrong {
              margin-left: 15px;
              &::before {
                content: " ";
                display: inline-block;
                width: 10px;
                height: 10px;
                background: #f56c6c;
                position: absolute;
                left: 0;
                top: 3px;
              }
            }
            .yellow {
              margin-left: 15px;
              &::before {
                content: " ";
                display: inline-block;
                width: 10px;
                height: 10px;
                background: #f2ae03;
                position: absolute;
                left: 0;
                top: 3px;
              }
            }
          }
        }

        .left-cont {
          height: calc(100% - 93px);
          padding: 15px;
          overflow-y: auto;

          .left-group {
            .left-group-title {
              font-size: 14px;
              font-weight: bold;
              color: #333;
              position: relative;
              padding-left: 12px;

              &::before {
                content: " ";
                display: inline-block;
                width: 5px;
                height: 14px;
                background: #409eff;
                position: absolute;
                left: 0;
                top: 3px;
              }
            }
            .left-group-cont {
              padding-top: 10px;
              padding-bottom: 20px;
              margin-right: -10px;

              a {
                display: inline-block;
                width: 30px;
                height: 30px;
                text-align: center;
                line-height: 30px;
                background: #eee;
                color: #333;
                font-size: 14px;
                margin-right: 10px;
                margin-bottom: 10px;
                cursor: pointer;

                &.right {
                  background: #409eff;
                  color: #fff;
                }

                &.wrong {
                  background: #f56c6c;
                  color: #fff;
                }

                &.yellow {
                  background: #f2ae03;
                  color: #fff;
                }
              }
            }
          }
        }

        .left-bottom {
          height: 50px;
          display: flex;
          align-items: center;
          padding: 0 15px;
          position: absolute;
          width: 100%;
          bottom: 0;

          .el-button {
            width: 100%;
          }
        }
      }

      .examResult-right {
        overflow-y: auto;
        flex: 1;
        // width: calc(100% -15px);
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        margin-left: 15px;
        padding: 15px;
      }
    }
  }
}

.examResult-header {
  background-color: #ffffff;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .examResult-title {
    .title {
      font-size: 18px;
      color: #333;
    }

    .examResult-attrs {
      margin-top: 5px;
      span {
        color: #999;
        font-size: 12px;
        margin-right: 25px;
      }
    }
  }

  .examResult-score {
    font-size: 18px;
    font-weight: bold;
    color: crimson;
  }
}

.right-group {
  .left-group-title {
    background: #fafafa;
    font-size: 14px;
    font-weight: bold;
    color: #333;
    position: relative;
    padding: 12px 15px 12px 28px;
    margin: -15px -15px 15px -15px;
    border-bottom: 1px #eee solid;

    &::before {
      content: " ";
      display: inline-block;
      width: 5px;
      height: 14px;
      background: cornflowerblue;
      position: absolute;
      left: 15px;
      top: 15px;
    }
  }

  .question-item {
    margin-bottom: 30px;
    font-size: 15px;
    .question-item-header {
      .type {
        color: dodgerblue;
      }
      .score {
        color: #999;
      }
    }
    .question-item-cont {
      padding-top: 15px;
      padding-left: 25px;
      .el-radio {
        display: block;
        margin-bottom: 10px;
        width: fit-content;
      }
      .el-checkbox {
        display: block;
        margin-bottom: 10px;
        width: fit-content;
      }
      .wrong-select {
        color: #f56c6c;

        ::v-deep .el-radio__inner,
        ::v-deep .el-checkbox__inner {
          border-color: #f56c6c;
          background: #f56c6c;
        }
        ::v-deep .el-radio__label,
        ::v-deep .el-checkbox__label {
          color: #f56c6c;
        }

        ::v-deep .el-input__inner {
          color: #f56c6c;
          border-color: #f56c6c;
        }

        ::v-deep .el-select__caret {
          color: #f56c6c;
        }
      }
      .question-tiankong-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        span {
          display: inline-block;
          width: 60px;
          margin-right: 10px;
          text-align: right;
        }
      }
    }
    .question-item-answer {
      padding: 10px 0 0 25px;
    }
    .question-item-analysis {
      padding: 10px 0 0 25px;
    }
    .el-radio-group {
      display: block;
    }
  }

  .compat-choice {
    padding-top: 15px;
    padding-left: 25px;
    > div {
      margin-bottom: 10px;
    }
  }

  .compat-select {
    width: 82px;
    ::v-deep .el-input__inner {
      border-radius: 0;
      border-width: 0 0 1px 0;
      text-align: center;
      padding-left: 10px;
    }
  }

  .sub-questions {
    padding-top: 10px;
    padding-left: 15px;
    > div {
      margin-bottom: 15px;
    }
  }
}
</style>
