<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="自主训练名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入自主训练名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="自主训练对象" prop="roleId">
        <el-select
          v-model="queryParams.roleId"
          filterable
          clearable
          placeholder="请选择自主训练对象"
        >
          <el-option
            v-for="item in roleOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="parseInt(item.id)"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['exam:autogenic-training:create']"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="自主训练id" align="center" prop="id" /> -->
      <el-table-column label="自主训练名称" align="center" prop="name" />
      <el-table-column label="自主训练对象" align="center" prop="roleNames" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['exam:autogenic-training:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['exam:autogenic-training:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="600px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="自主训练名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入自主训练名称" />
        </el-form-item>
        <el-form-item label="自主训练对象" prop="roleIds">
          <el-select
            v-model="form.roleIds"
            filterable
            clearable
            multiple
            placeholder="请选择自主训练对象"
            style="width: 100%"
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id.toString()"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开放试题范围选择" prop="pointIds">
          <div class="point-tree">
            <el-tree
              v-if="open"
              ref="pointTree"
              :data="pointTree"
              :props="{
                label: 'name',
                disabled:
                  form.id && form.generationMethod === 'fixed'
                    ? 'name'
                    : 'disabled',
              }"
              show-checkbox
              node-key="id"
              :default-checked-keys="(form.pointIds || '').split(',')"
              @check="handlePointChecked"
            ></el-tree>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createAutogenicTraining,
  updateAutogenicTraining,
  deleteAutogenicTraining,
  getAutogenicTraining,
  getAutogenicTrainingPage,
  exportAutogenicTrainingExcel,
} from "@/api/exam/autogenicTraining";
import { listSimpleRoles } from "@/api/system/role";
import { getKnowledgePointTree } from "@/api/exam/question";

export default {
  name: "AutogenicTraining",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考试自主训练列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        roleId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "自主训练名称不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "是否启用不能为空", trigger: "blur" },
        ],
        pointIds: [
          { required: true, message: "知识点集合不能为空", trigger: "blur" },
        ],
        roleIds: [
          {
            required: true,
            message: "训练角色对象不能为空",
            trigger: "change",
          },
        ],
      },
      roleOptions: [],
      pointTree: [],
    };
  },
  created() {
    // 获得角色列表
    this.roleOptions = [];
    listSimpleRoles().then((response) => {
      const { data } = response;
      let list = [];
      data.forEach((item) => {
        if (item.code !== "super_admin") {
          list.push(item);
        }
      });
      this.roleOptions = list;

      this.getList();
    });
    this.getPointTree();
  },
  methods: {
    /** 查询知识点树 */
    getPointTree() {
      getKnowledgePointTree().then((res) => (this.pointTree = res.data));
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getAutogenicTrainingPage(this.queryParams).then((response) => {
        const list = response.data.list;
        // list.forEach((item) => {
        //   const roleIdArr = item.roleIds.split(",");
        //   const roleNameArr = [];
        //   roleIdArr.forEach((id) => {
        //     this.roleOptions.forEach((role) => {
        //       if (role.id === parseInt(id)) {
        //         roleNameArr.push(role.name);
        //       }
        //     });
        //   });
        //   item.roleNames = roleNameArr.join(",");
        // });
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        status: 1,
        pointIds: undefined,
        roleIds: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加考试自主训练";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getAutogenicTraining(id).then((response) => {
        this.form = response.data;
        this.form.roleIds = this.form.roleIds.split(",");
        this.open = true;
        this.title = "修改考试自主训练";
      });
    },
    /** 知识点选择 */
    handlePointChecked() {
      this.$nextTick(() => {
        const ids = this.$refs.pointTree.getCheckedKeys(true);
        // console.log("handlePointChecked===", ids);
        this.form.pointIds = ids.join(",");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        this.form.roleIds = this.form.roleIds.join(",");
        // 修改的提交
        if (this.form.id != null) {
          updateAutogenicTraining(this.form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createAutogenicTraining(this.form).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const name = row.name;
      this.$modal
        .confirm('是否确认删除考试自主训练"' + name + '"?')
        .then(() => {
          return deleteAutogenicTraining(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.point-tree {
  height: 200px;
  border: 1px solid rgb(220, 223, 230);
  border-radius: 4px;
  overflow: auto;
}
</style>
