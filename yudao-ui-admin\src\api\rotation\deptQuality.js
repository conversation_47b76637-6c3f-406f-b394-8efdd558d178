import request from '@/utils/request'

// 获得统计分页
export function getDeptQualityPage(query) {
  return request({
    url: '/rotation/dept-quality/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/teachingActiveDevelopment/index'}
  })
}

// 导出 Excel
export function exportDeptQuality(query) {
  return request({
    url: '/rotation/dept-quality/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

