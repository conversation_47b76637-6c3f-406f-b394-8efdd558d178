import request from "@/utils/request";

// 获得导师信息统计分页
export function getMentorStatisticPage(query) {
  return request({
    url: "/rotation/mentor-statistic/page",
    method: "get",
    params: query,
    headers: { component: "rotation/mentorstatistic/index" },
  });
}

// 获得导师谈话记录某个学生完成情况分页
export function getCompleteStatusPage(query) {
  return request({
    url: "/rotation/mentor-interview-notes/completeStatusPage",
    method: "get",
    params: query,
  });
}

// 获得导师信息统计-学员评价导师分页
export function getMentorPage(query) {
  return request({
    url: "/rotation/mentor-statistic/mentorPage",
    method: "get",
    params: query,
  });
}

// 获得导师信息统计-学员评价导师分页
export function getStudentPage(query) {
  return request({
    url: "/rotation/mentor-statistic/studentPage",
    method: "get",
    params: query,
  });
}

// 更新导师谈话记录
export function auditMentorNotes(data) {
  return request({
    url: "/rotation/mentor-interview-notes/audit",
    method: "put",
    data: data,
  });
}

// 导出导师信息 Excel
export function exportMentorStatisticExcel(query) {
  return request({
    url: "/rotation/mentor-statistic/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
