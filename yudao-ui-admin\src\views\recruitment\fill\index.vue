<template>
  <div class="app-container">
    <fill-step-residency v-if="studentType === '1'"></fill-step-residency>
    <fill-step v-else></fill-step>
  </div>
</template>

<script>
import FillStep from './fill-step'
import FillStepResidency from './fill-step-residency'

export default {
  name: 'fill',
  components: { FillStep, FillStepResidency },
  data() {
    return {
      studentType: this.$route.query.studentType
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
