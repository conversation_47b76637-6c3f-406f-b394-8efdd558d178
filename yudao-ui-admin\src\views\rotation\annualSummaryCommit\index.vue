<template>
  <div class="app-container annualSummaryCommitn-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ form.releaseYear }}年度总结</span>
        <span v-if="form.summaryEndDate" style="color: crimson">
          （提交截止日期：{{ form.summaryEndDate }}）
        </span>
      </div>

      <div v-if="form.releaseYear" class="cont applyForm">
        <el-alert
          v-if="form.bpmResult != 3"
          :title="
            '年度总结提交要求：完成理论考核（' +
            (form.associateTheoryScore ? '是' : '否') +
            '），完成技能考核（' +
            (form.associateSkillScore ? '是' : '否') +
            '），若无相关成绩请联系专业基地管理员'
          "
          type="info"
          :closable="false"
        >
        </el-alert>
        <el-alert v-else title="审核不通过" type="info" :closable="false">
          <el-button type="primary" @click="handleResubmit">
            重新提交
          </el-button>
        </el-alert>
        <el-form ref="form" :model="form" :rules="rules" label-width="150px">
          <el-form-item label="年度理论考核成绩" prop="graduationSummary">
            <span>
              {{ form.theoryScore === null ? "--" : form.theoryScore }}
            </span>
          </el-form-item>
          <el-form-item label="年度技能考核成绩" prop="graduationSummary">
            <span>
              {{ form.skillScore === null ? "--" : form.skillScore }}
            </span>
          </el-form-item>
          <el-form-item
            label="本人年度工作总结"
            prop="summary"
            v-if="showSubmit"
          >
            <el-input
              v-model="form.summary"
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 10 }"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-form>
        <div class="footer" v-if="showSubmit">
          <el-button
            type="primary"
            :disabled="submitLoading"
            @click="submitForm('save')"
          >
            暂存
          </el-button>
          <el-button
            type="primary"
            :disabled="submitLoading"
            @click="submitForm('submit')"
          >
            提交
          </el-button>
        </div>
      </div>

      <div v-if="!form.releaseYear">
        <el-empty
          description="暂未发布最新的年度总结计划，您可以移步我的年度总结菜单查看历年考评结果"
        ></el-empty>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  getSummaryContent,
  annualSummarySave,
  annualSummaryCommit,
} from "@/api/rotation/annualSummaryCommit";

export default {
  name: "AnnualSummaryCommit",
  data() {
    return {
      form: {},
      rules: {
        summary: [
          {
            required: true,
            message: "本人年度工作总结不能为空",
            trigger: "blur",
          },
        ],
      },
      submitLoading: false,
      resultCode: "",
      showSubmit: false,
    };
  },
  created() {
    this.getData();
  },
  methods: {
    /** 查询列表 */
    getData() {
      this.loading = true;
      // 执行查询
      getSummaryContent().then((response) => {
        console.log("getSummaryContent===", response);
        this.form = response.data;
        this.showSubmit = false;
        if (this.form.associateTheoryScore && this.form.associateSkillScore) {
          if (this.form.skillScore && this.form.theoryScore) {
            this.showSubmit = true;
          } else {
            this.showSubmit = false;
          }
        }

        if (this.form.associateTheoryScore && !this.form.associateSkillScore) {
          if (this.form.theoryScore) {
            this.showSubmit = true;
          } else {
            this.showSubmit = false;
          }
        }

        if (!this.form.associateTheoryScore && this.form.associateSkillScore) {
          if (this.form.skillScore) {
            this.showSubmit = true;
          } else {
            this.showSubmit = false;
          }
        }

        if (!this.form.associateTheoryScore && !this.form.associateSkillScore) {
          this.showSubmit = true;
        }

        this.loading = false;
      });
    },

    handleResubmit() {
      this.form.bpmResult = "";
    },

    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        this.submitLoading = true;
        console.log("submitForm===", this.form);
        const params = {
          ...this.form,
        };

        let server = annualSummarySave;
        let tip = "暂存";
        if (type === "submit") {
          server = annualSummaryCommit;
          tip = "提交";
        }
        server(params)
          .then((res) => {
            this.$modal.msgSuccess(`${tip}成功`);
            this.submitLoading = false;
            this.getData();
          })
          .catch(() => {
            this.submitLoading = false;
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.annualSummaryCommitn-container {
  .box-card {
    margin-bottom: 20px;
  }
  ::v-deep .el-card__header {
    font-size: 16px;
    color: #333;
    font-weight: bold;
  }
  .cont {
    .preview-img {
      max-height: 200px;
    }
  }
  .info {
    div {
      margin-bottom: 12px;
      font-size: 14px;

      span {
        padding: 0 6px;
        font-weight: bold;
      }
    }

    .require-item {
      position: relative;
      padding-left: 20px;

      &::before {
        content: " ";
        display: inline-block;
        position: absolute;
        left: 0;
        top: 6px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #999;
      }
    }
  }

  .applyForm {
    .footer {
      padding-left: 150px;
    }
  }
}
</style>
