<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="试题类型" prop="questionType">
        <el-select
          v-model="queryParams.questionType"
          placeholder="请选择试题类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.EXAM_QUESTION_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['exam:question:create']"
          >新增试题</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="6">
        <div class="tree-wrapper">
          <el-tree
            ref="tree"
            :data="tree"
            :props="{ children: 'children', label: 'name' }"
            highlight-current
            node-key="id"
            :expand-on-click-node="false"
            @node-click="handlePointClick"
          ></el-tree>
        </div>
      </el-col>
      <el-col :span="18">
        <!-- 列表 -->
        <el-table v-loading="loading" :data="list">
          <el-table-column
            label="试题类型"
            align="center"
            prop="questionType"
            width="100px"
          >
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                :value="scope.row.questionType"
              />
            </template>
          </el-table-column>
          <el-table-column label="答案" align="center" prop="answer">
            <template v-slot="scope">
              <div class="overflow-ellipsis-3">
                {{
                  scope.row.questionType === "8"
                    ? { A: "正确", B: "错误" }[scope.row.answer] || ""
                    : scope.row.answer
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="标题" align="center" prop="title">
            <template v-slot="scope">
              <div
                class="question-link overflow-ellipsis-3"
                @click="previewQuestion(scope.row.id)"
              >
                {{ scope.row.title }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="状态"
            align="center"
            prop="status"
            width="100px"
          >
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.COMMON_STATUS"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="120px"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['exam:question:update']"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['exam:question:delete']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="知识点" prop="pointId">
          <el-input
            v-model="form.pointName"
            placeholder="请输入知识点"
            disabled
          />
        </el-form-item>
        <el-form-item label="试题类型" prop="questionType">
          <el-select
            v-model="form.questionType"
            placeholder="请选择试题类型"
            :disabled="!!form.id"
          >
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.EXAM_QUESTION_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="试题难度" prop="difficulty">
          <el-select v-model="form.difficulty" placeholder="请选择试题难度">
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.EXAM_DIFFICULTY_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <!--单项题-->
        <template v-if="form.questionType === '1'">
          <el-form-item label="标题内容" prop="title">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.title"
              placeholder="请输入标题内容"
            />
          </el-form-item>
          <el-form-item label="标题图片" prop="titleImages">
            <file-upload-info
              v-model="form.titleImages"
              :is-show-tip="false"
              :file-type="['png', 'jpg', 'jpeg']"
              :need-domain="false"
              directory="exam"
            >
              <el-button type="primary" size="small">上传图片</el-button>
            </file-upload-info>
          </el-form-item>
          <el-form-item label="试题选项" prop="choiceArr">
            <el-table :data="form.choiceArr" size="small">
              <el-table-column
                label="选项"
                prop="key"
                width="60"
                align="center"
              >
                <template v-slot="scope">{{
                  choiceKeys[scope.$index]
                }}</template>
              </el-table-column>
              <el-table-column label="描述" prop="desc">
                <template v-slot="scope">
                  <el-input
                    v-model="scope.row.desc"
                    type="textarea"
                    :rows="1"
                    autosize
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="正确答案" width="100" align="center">
                <template v-slot="scope">
                  <el-radio
                    class="no-label-radio"
                    v-model="form.answer"
                    :label="choiceKeys[scope.$index]"
                  ></el-radio>
                </template>
              </el-table-column>
              <el-table-column label="选项图片" prop="images" width="180px">
                <template v-slot="scope">
                  <file-upload-info
                    v-model="scope.row.images"
                    :is-show-tip="false"
                    :file-type="['png', 'jpg', 'jpeg']"
                    :need-domain="false"
                    directory="exam"
                  >
                    <el-button type="primary" size="small">上传图片</el-button>
                  </file-upload-info>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="60" align="center">
                <template v-slot="scope">
                  <i
                    class="handle-icon el-icon-plus"
                    @click="addChoice(form.choiceArr, scope.$index)"
                  ></i>
                  <i
                    class="handle-icon el-icon-minus"
                    @click="removeChoice(form.choiceArr, scope.$index)"
                  ></i>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="试题解析" prop="analysis">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.analysis"
            ></el-input>
          </el-form-item>
        </template>

        <!--多项题-->
        <template v-if="form.questionType === '2'">
          <el-form-item label="标题内容" prop="title">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.title"
              placeholder="请输入标题内容"
            />
          </el-form-item>
          <el-form-item label="标题图片" prop="titleImages">
            <file-upload-info
              v-model="form.titleImages"
              :is-show-tip="false"
              :file-type="['png', 'jpg', 'jpeg']"
              :need-domain="false"
              directory="exam"
            >
              <el-button type="primary" size="small">上传图片</el-button>
            </file-upload-info>
          </el-form-item>
          <el-form-item label="试题选项" prop="choiceArr">
            <el-table :data="form.choiceArr" size="small">
              <el-table-column
                label="选项"
                prop="key"
                width="60"
                align="center"
              >
                <template v-slot="scope">{{
                  choiceKeys[scope.$index]
                }}</template>
              </el-table-column>
              <el-table-column label="描述" prop="desc">
                <template v-slot="scope">
                  <el-input
                    v-model="scope.row.desc"
                    type="textarea"
                    autosize
                    :rows="1"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                label="正确答案"
                prop="answer"
                width="100"
                align="center"
              >
                <template v-slot="scope">
                  <el-checkbox v-model="scope.row.answer"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="选项图片" prop="images" width="180px">
                <template v-slot="scope">
                  <file-upload-info
                    v-model="scope.row.images"
                    :is-show-tip="false"
                    :file-type="['png', 'jpg', 'jpeg']"
                    :need-domain="false"
                    directory="exam"
                  >
                    <el-button type="primary" size="small">上传图片</el-button>
                  </file-upload-info>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="60" align="center">
                <template v-slot="scope">
                  <i
                    class="handle-icon el-icon-plus"
                    @click="addChoice(form.choiceArr, scope.$index)"
                  ></i>
                  <i
                    class="handle-icon el-icon-minus"
                    @click="removeChoice(form.choiceArr, scope.$index)"
                  ></i>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="试题解析" prop="analysis">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.analysis"
            ></el-input>
          </el-form-item>
        </template>

        <!--配伍题-->
        <template v-if="form.questionType === '3'">
          <el-form-item label="试题选项" prop="choiceArr">
            <el-table :data="form.choiceArr" size="small">
              <el-table-column
                label="选项"
                prop="key"
                width="60"
                align="center"
              >
                <template v-slot="scope">{{
                  choiceKeys[scope.$index]
                }}</template>
              </el-table-column>
              <el-table-column label="描述" prop="desc">
                <template v-slot="scope">
                  <el-input
                    v-model="scope.row.desc"
                    type="textarea"
                    autosize
                    :rows="1"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="选项图片" prop="images" width="180px">
                <template v-slot="scope">
                  <file-upload-info
                    v-model="scope.row.images"
                    :is-show-tip="false"
                    :file-type="['png', 'jpg', 'jpeg']"
                    :need-domain="false"
                    directory="exam"
                  >
                    <el-button type="primary" size="small">上传图片</el-button>
                  </file-upload-info>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="60">
                <template v-slot="scope">
                  <i
                    class="handle-icon el-icon-plus"
                    @click="addChoice(form.choiceArr, scope.$index)"
                  ></i>
                  <i
                    class="handle-icon el-icon-minus"
                    @click="removeChoice(form.choiceArr, scope.$index)"
                  ></i>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="试题列表" prop="titleArr">
            <el-table :data="form.titleArr" size="small">
              <el-table-column
                label="序号"
                prop="key"
                width="60"
                align="center"
              >
                <template v-slot="scope">{{
                  titleKeys[scope.$index]
                }}</template>
              </el-table-column>
              <el-table-column label="描述" prop="desc">
                <template v-slot="scope">
                  <el-input
                    v-model="scope.row.desc"
                    type="textarea"
                    autosize
                    :rows="1"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="正确答案" width="100" align="center">
                <template v-slot="scope">
                  <el-select v-model="scope.row.answer" placeholder="">
                    <el-option
                      v-for="i in form.choiceArr.length"
                      :key="i"
                      :label="choiceKeys[i - 1]"
                      :value="choiceKeys[i - 1]"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="选项图片" prop="images" width="180px">
                <template v-slot="scope">
                  <file-upload-info
                    v-model="scope.row.images"
                    :is-show-tip="false"
                    :file-type="['png', 'jpg', 'jpeg']"
                    :need-domain="false"
                    directory="exam"
                  >
                    <el-button type="primary" size="small">上传图片</el-button>
                  </file-upload-info>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="60" align="center">
                <template v-slot="scope">
                  <i
                    class="handle-icon el-icon-plus"
                    @click="addTitle(scope.$index)"
                  ></i>
                  <i
                    class="handle-icon el-icon-minus"
                    @click="removeTitle(scope.$index)"
                  ></i>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="试题解析" prop="analysis">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.analysis"
            ></el-input>
          </el-form-item>
        </template>

        <!--病例题-->
        <template v-if="form.questionType === '4'">
          <el-form-item label="题干内容" prop="title">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.title"
              placeholder="请输入标题内容"
            />
          </el-form-item>
          <el-form-item label="题干图片" prop="titleImages">
            <file-upload-info
              v-model="form.titleImages"
              :is-show-tip="false"
              :file-type="['png', 'jpg', 'jpeg']"
              :need-domain="false"
              directory="exam"
            >
              <el-button type="primary" size="small">上传图片</el-button>
            </file-upload-info>
          </el-form-item>
          <el-form-item label="试题列表" prop="subsetTitles">
            <div class="subset-titles">
              <div
                class="subset-title"
                v-for="(subsetTitle, index) in form.subsetTitles"
                :key="index"
              >
                <span class="subset-index">({{ index + 1 }})</span>
                <el-form-item label="标题内容" required>
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 6 }"
                    v-model="subsetTitle.title"
                    placeholder="请输入标题内容"
                  />
                </el-form-item>
                <el-form-item label="标题图片">
                  <file-upload-info
                    v-model="subsetTitle.titleImages"
                    :is-show-tip="false"
                    :file-type="['png', 'jpg', 'jpeg']"
                    :need-domain="false"
                    directory="exam"
                  >
                    <el-button type="primary" size="small">上传图片</el-button>
                  </file-upload-info>
                </el-form-item>
                <el-form-item label="试题选项" required>
                  <el-table :data="subsetTitle.choiceArr" size="small">
                    <el-table-column
                      label="选项"
                      prop="key"
                      width="60"
                      align="center"
                    >
                      <template v-slot="scope">{{
                        choiceKeys[scope.$index]
                      }}</template>
                    </el-table-column>
                    <el-table-column label="描述" prop="desc">
                      <template v-slot="scope">
                        <el-input
                          v-model="scope.row.desc"
                          type="textarea"
                          autosize
                          :rows="1"
                        ></el-input>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="正确答案"
                      prop="answer"
                      width="100"
                      align="center"
                    >
                      <template v-slot="scope">
                        <el-checkbox v-model="scope.row.answer"></el-checkbox>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="选项图片"
                      prop="images"
                      width="180px"
                    >
                      <template v-slot="scope">
                        <file-upload-info
                          v-model="scope.row.images"
                          :is-show-tip="false"
                          :file-type="['png', 'jpg', 'jpeg']"
                          :need-domain="false"
                          directory="exam"
                        >
                          <el-button type="primary" size="small"
                            >上传图片</el-button
                          >
                        </file-upload-info>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="60" align="center">
                      <template v-slot="scope">
                        <i
                          class="handle-icon el-icon-plus"
                          @click="
                            addChoice(subsetTitle.choiceArr, scope.$index)
                          "
                        ></i>
                        <i
                          class="handle-icon el-icon-minus"
                          @click="
                            removeChoice(subsetTitle.choiceArr, scope.$index)
                          "
                        ></i>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
                <el-form-item label="试题解析" required>
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 6 }"
                    v-model="subsetTitle.analysis"
                  ></el-input>
                </el-form-item>
                <div class="subset-handle">
                  <i class="el-icon-plus" @click="addSubsetTitle(index)"></i>
                  <i
                    class="el-icon-minus"
                    @click="removeSubsetTitle(index)"
                  ></i>
                </div>
              </div>
            </div>
          </el-form-item>
        </template>

        <!--是非题-->
        <template v-if="form.questionType === '8'">
          <el-form-item label="标题内容" prop="title">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.title"
              placeholder="请输入标题内容"
            />
          </el-form-item>
          <el-form-item label="标题图片" prop="titleImages">
            <file-upload-info
              v-model="form.titleImages"
              :is-show-tip="false"
              :file-type="['png', 'jpg', 'jpeg']"
              :need-domain="false"
              directory="exam"
            >
              <el-button type="primary" size="small">上传图片</el-button>
            </file-upload-info>
          </el-form-item>
          <el-form-item label="试题答案" prop="answer">
            <el-radio-group v-model="form.answer">
              <el-radio label="A">正确</el-radio>
              <el-radio label="B">错误</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="试题解析" required>
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.analysis"
            ></el-input>
          </el-form-item>
        </template>

        <!--填空题-->
        <template v-if="form.questionType === '10'">
          <el-form-item label="题干内容" prop="title">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.title"
              placeholder="请输入题目名称，每个填空项用一对小括号'()'表示"
            ></el-input>
          </el-form-item>
          <el-form-item label="题干图片" prop="titleImages">
            <file-upload-info
              v-model="form.titleImages"
              :is-show-tip="false"
              :file-type="['png', 'jpg', 'jpeg']"
              :need-domain="false"
              directory="exam"
            >
              <el-button type="primary" size="small">上传图片</el-button>
            </file-upload-info>
          </el-form-item>
          <el-form-item label="试题答案" required>
            <el-form-item
              v-for="(item, index) in form.choiceArr"
              :key="item.key"
              :prop="'choiceArr.' + index + '.answer'"
              :rules="{
                required: true,
                message: '答案不能为空',
                trigger: 'blur',
              }"
              style="margin-bottom: 10px"
            >
              <el-input
                v-model="item.answer"
                style="width: 90%; margin-right: 10px"
              ></el-input>
              <i
                class="handle-icon el-icon-minus"
                @click="removeTiankong(form.choiceArr, index)"
              ></i>
            </el-form-item>
            <i class="handle-icon el-icon-plus" @click="addTiankong()"></i>
          </el-form-item>
          <el-form-item label="试题解析" prop="analysis">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.analysis"
            ></el-input>
          </el-form-item>
        </template>

        <!--名词解释5&简答6-->
        <template v-if="form.questionType === '5' || form.questionType === '6'">
          <el-form-item label="题干内容" prop="title">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.title"
              placeholder="请输入题干内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="题干图片" prop="titleImages">
            <file-upload-info
              v-model="form.titleImages"
              :is-show-tip="false"
              :file-type="['png', 'jpg', 'jpeg']"
              :need-domain="false"
              directory="exam"
            >
              <el-button type="primary" size="small">上传图片</el-button>
            </file-upload-info>
          </el-form-item>
          <el-form-item label="试题答案" required>
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.answer"
              placeholder="请输入试题答案"
            ></el-input>
          </el-form-item>
          <el-form-item label="试题解析" prop="analysis">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              v-model="form.analysis"
            ></el-input>
          </el-form-item>
        </template>

        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 试题预览对话框 -->
    <el-dialog
      title="试题预览"
      :visible.sync="previewOpen"
      width="800px"
      v-dialog-drag
      append-to-body
    >
      <div class="question-wrapper">
        <template
          v-if="
            previewData &&
            ['1', '2', '8'].indexOf(previewData.questionType) > -1
          "
        >
          <div class="question-header">
            <span class="question-index">1、</span>
            <span class="question-type"
              >【<dict-tag
                :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                :value="previewData.questionType"
              />】</span
            >
            <span class="question-title">
              <text-to-html
                :text="previewData.title"
                :images="getPreviewImages(previewData.titleImages)"
              ></text-to-html
              >（）
            </span>
          </div>
          <div class="question-answer">
            <el-radio-group
              v-if="['1', '8'].indexOf(previewData.questionType) > -1"
            >
              <el-radio
                v-for="(choice, key) in previewData.choiceList"
                :label="key"
                :key="key"
              >
                {{ key }}、<text-to-html
                  :text="choice"
                  :images="
                    getPreviewImages((previewData.choiceImages || {})[key])
                  "
                ></text-to-html>
              </el-radio>
            </el-radio-group>
            <el-checkbox-group value="" v-if="previewData.questionType === '2'">
              <el-checkbox
                v-for="(choice, key) in previewData.choiceList"
                :label="key"
                :key="key"
              >
                {{ key }}、<text-to-html
                  :text="choice"
                  :images="
                    getPreviewImages((previewData.choiceImages || {})[key])
                  "
                ></text-to-html>
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div class="question-item-answer">
            正确答案：
            {{
              previewData.questionType === "8"
                ? previewData.answer === "A"
                  ? "正确"
                  : "错误"
                : previewData.answer
            }}
          </div>
          <div class="question-item-analysis" v-if="previewData.analysis">
            <strong>试题解析：</strong> {{ previewData.analysis }}
          </div>
        </template>
        <template v-if="previewData && previewData.questionType === '3'">
          <div class="question-header">
            <span class="question-index">1、</span>
            <span class="question-type"
              >【<dict-tag
                :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                :value="previewData.questionType"
              />】</span
            >
            <div class="question-choice">
              <div v-for="(choice, key) in previewData.choiceList" :key="key">
                {{ key }}、<text-to-html
                  :text="choice"
                  :images="
                    getPreviewImages((previewData.choiceImages || {})[key])
                  "
                ></text-to-html>
              </div>
            </div>
          </div>
          <div class="question-subtitle">
            <div v-for="(title, key) in previewData.titleList" :key="key">
              {{ key }}、<text-to-html
                :text="title"
                :images="
                  getPreviewImages((previewData.titleListImages || {})[key])
                "
              ></text-to-html>
              <span class="question-subtitle-answer">
                (<el-select value="">
                  <el-option
                    v-for="(choice, key) in previewData.choiceList"
                    :label="key"
                    value="key"
                    :key="key"
                  ></el-option> </el-select
                >)
              </span>
            </div>
          </div>
          <div class="question-item-answer">
            正确答案：
            <template
              v-for="item in (previewData.answer || '')
                .split(',')
                .map((val, index) => `${index + 1})、${val}`)"
            >
              {{ item }}
              <span style="display: inline-block; width: 20px"></span>
            </template>
          </div>
          <div class="question-item-analysis" v-if="previewData.analysis">
            <strong>试题解析：</strong> {{ previewData.analysis }}
          </div>
        </template>
        <template v-if="previewData && previewData.questionType === '4'">
          <div class="question-header">
            <span class="question-index">1、</span>
            <span class="question-type"
              >【<dict-tag
                :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                :value="previewData.questionType"
              />】</span
            >
            <span class="question-title">
              <text-to-html
                :text="previewData.title"
                :images="getPreviewImages(previewData.titleImages)"
              ></text-to-html>
            </span>
          </div>
          <div class="question-subset-titles">
            <div
              class="question-subset-title"
              v-for="(subsetTitle, index) in previewData.subsetTitles"
            >
              <div class="question-header">
                <span class="question-index">({{ index + 1 }}). </span>
                <span class="question-type"></span>
                <span class="question-title">
                  <text-to-html
                    :text="subsetTitle.title"
                    :images="getPreviewImages(subsetTitle.titleImages)"
                  ></text-to-html
                  >（）
                </span>
              </div>
              <div class="question-answer">
                <el-checkbox-group value="">
                  <el-checkbox
                    v-for="(choice, key) in subsetTitle.choiceList"
                    :label="key"
                    :key="key"
                  >
                    {{ key }}、<text-to-html
                      :text="choice"
                      :images="
                        getPreviewImages((subsetTitle.choiceImages || {})[key])
                      "
                    ></text-to-html>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
              <div class="question-item-answer">
                正确答案： {{ subsetTitle.answer }}
              </div>
              <div class="question-item-analysis" v-if="subsetTitle.analysis">
                <strong>试题解析：</strong> {{ subsetTitle.analysis }}
              </div>
            </div>
          </div>
        </template>
        <!-- 填空题 -->
        <template v-if="previewData && previewData.questionType === '10'">
          <div class="question-header">
            <span class="question-index">1、</span>
            <span class="question-type"
              >【<dict-tag
                :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                :value="previewData.questionType"
              />】</span
            >
            <span class="question-title">
              <text-to-html
                :text="previewData.title"
                :images="getPreviewImages(previewData.titleImages)"
              ></text-to-html>
            </span>
          </div>
          <div class="question-answer">
            <div
              v-for="(choice, index) in previewData.choiceArr"
              class="question-tiankong-row"
            >
              <span>
                第{{
                  ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"][
                    index
                  ]
                }}空
              </span>
              <el-input :key="index"></el-input>
            </div>
          </div>
          <div class="question-item-answer">
            正确答案：{{ previewData.answer }}
          </div>
          <div class="question-item-analysis" v-if="previewData.analysis">
            <strong>试题解析：</strong> {{ previewData.analysis }}
          </div>
        </template>

        <!-- 填空题 -->
        <template
          v-if="
            previewData &&
            (previewData.questionType === '5' ||
              previewData.questionType === '6')
          "
        >
          <div class="question-header">
            <span class="question-index">1、</span>
            <span class="question-type"
              >【<dict-tag
                :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                :value="previewData.questionType"
              />】</span
            >
            <span class="question-title">
              <text-to-html
                :text="previewData.title"
                :images="getPreviewImages(previewData.titleImages)"
              ></text-to-html>
            </span>
          </div>
          <div class="question-answer">
            <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" />
          </div>
          <div class="question-item-answer">
            正确答案：{{ previewData.answer }}
          </div>
          <div class="question-item-analysis" v-if="previewData.analysis">
            <strong>试题解析：</strong> {{ previewData.analysis }}
          </div>
        </template>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createQuestion,
  updateQuestion,
  deleteQuestion,
  getQuestion,
  getQuestionPage,
  getKnowledgePointTree,
} from "@/api/exam/question";
import Editor from "@/components/Editor";
import FileUploadInfo from "@/components/FileUploadInfo";
import TextToHtml from "../components/TextToHtml.vue";
import { getAccessToken } from "@/utils/auth";

export default {
  name: "Question",
  components: { TextToHtml, Editor, FileUploadInfo },
  data() {
    return {
      // 知识点树
      tree: [],
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考试试题列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        pointId: null,
        questionType: null,
        title: null,
        content: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        pointId: [
          { required: true, message: "知识点id不能为空", trigger: "blur" },
        ],
        questionType: [
          { required: true, message: "试题类型不能为空", trigger: "change" },
        ],
        difficulty: [
          { required: true, message: "试题难度不能为空", trigger: "change" },
        ],
        answer: [{ required: true, message: "答案不能为空", trigger: "blur" }],
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        content: [{ required: true, message: "内容不能为空", trigger: "blur" }],
        analysis: [
          { required: true, message: "试题解析不能为空", trigger: "blur" },
        ],
        status: [
          {
            required: true,
            message: "状态（0正常 1停用）不能为空",
            trigger: "change",
          },
        ],
        choiceArr: [
          { required: true, message: "试题选项不能为空", trigger: "change" },
        ],
        titleArr: [
          { required: true, message: "试题列表不能为空", trigger: "change" },
        ],
        subsetTitles: [
          { required: true, message: "试题列表不能为空", trigger: "change" },
        ],
      },
      // 选项标识列表
      choiceKeys: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"],
      // 配伍题序号
      titleKeys: [
        "(1)",
        "(2)",
        "(3)",
        "(4)",
        "(5)",
        "(6)",
        "(7)",
        "(8)",
        "(9)",
        "(10)",
      ],
      // 试题预览
      previewOpen: false,
      previewData: null,
    };
  },
  created() {
    this.getTree();
  },
  methods: {
    /** 查询知识点树 */
    getTree() {
      return getKnowledgePointTree().then((res) => {
        this.tree = res.data;
      });
    },
    handlePointClick(data, node) {
      if (node && !node.isLeaf) {
        this.$refs.tree.setCurrentKey(this.queryParams.pointId);
        return;
      }
      this.queryParams.pointId = data.id;
      this.getList();
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getQuestionPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 预览试题 */
    previewQuestion(id) {
      this.previewOpen = true;
      this.previewData = null;
      getQuestion(id).then((res) => {
        const { content, ...rest } = res.data;
        const contentObj = content ? JSON.parse(content) : {};
        this.previewData = { ...rest, ...contentObj };

        if (this.previewData.questionType === "10") {
          const choiceArr = (rest.answer || "").split(",").map((key) => ({
            desc: "",
            images: [],
            answer: key,
          }));
          this.previewData.choiceArr = choiceArr;
        }
      });
    },
    getPreviewImages(images) {
      return (images || []).map(
        (item) =>
          `${process.env.VUE_APP_BASE_API}${item.url}?token=${getAccessToken()}`
      );
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        pointId: undefined,
        pointName: undefined,
        questionType: undefined,
        difficulty: undefined,
        answer: undefined,
        title: undefined,
        analysis: undefined,
        status: undefined,
        // content中间转换数据
        titleImages: [],
        choiceArr: [{ desc: "", images: [], answer: "" }],
        titleArr: [{ desc: "", images: [], answer: "" }],
        subsetTitles: [
          {
            title: undefined,
            titleImages: [],
            choiceArr: [{ desc: "", images: [], answer: "" }],
            answer: undefined,
            analysis: undefined,
          },
        ],
      };
      this.resetForm("form");
      const curPoint = this.$refs.tree.getCurrentNode();
      this.form.pointId = curPoint.id;
      this.form.pointName = curPoint.name;
    },
    /** 新增按钮操作 */
    handleAdd() {
      if (!this.queryParams.pointId) {
        this.$message.info("请左侧树选择知识点再新增试题");
        return;
      }
      this.reset();
      this.open = true;
      this.title = "添加考试试题";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getQuestion(id).then((response) => {
        const { content, ...form } = response.data;
        const contentObj = content ? JSON.parse(content) : {};

        if (form.questionType === "1") {
          const choiceArr = Object.keys(contentObj.choiceList || { A: "" }).map(
            (key) => ({
              desc: contentObj.choiceList?.[key] || "",
              images: contentObj.choiceImages?.[key] || [],
            })
          );
          this.form = {
            ...form,
            titleImages: contentObj.titleImages || [],
            choiceArr,
          };
        }

        if (form.questionType === "2") {
          const choiceArr = Object.keys(contentObj.choiceList || { A: "" }).map(
            (key) => ({
              desc: contentObj.choiceList?.[key] || "",
              images: contentObj.choiceImages?.[key] || [],
              answer: (form.answer || "").indexOf(key) > -1,
            })
          );
          this.form = {
            ...form,
            titleImages: contentObj.titleImages || [],
            choiceArr,
          };
        }

        if (form.questionType === "3") {
          const choiceArr = Object.keys(contentObj.choiceList || { A: "" }).map(
            (key) => ({
              desc: contentObj.choiceList?.[key] || "",
              images: contentObj.choiceImages?.[key] || [],
            })
          );
          const titleArr = Object.keys(
            contentObj.titleList || { "(1)": "" }
          ).map((key, index) => ({
            desc: contentObj.titleList?.[key] || "",
            images: contentObj.titleListImages?.[key] || [],
            answer: (form.answer || "").split(",")[index] || "",
          }));
          this.form = {
            ...form,
            titleImages: contentObj.titleImages || [],
            choiceArr,
            titleArr,
          };
        }

        if (form.questionType === "4") {
          const subsetTitles = contentObj.subsetTitles.map(
            (subsetTitle, index) => {
              const choiceArr = Object.keys(
                subsetTitle.choiceList || { A: "" }
              ).map((key) => ({
                desc: subsetTitle.choiceList?.[key] || "",
                images: subsetTitle.choiceImages?.[key] || [],
                answer:
                  ((form.answer || "").split(",")[index] || "").indexOf(key) >
                  -1,
              }));
              return { ...subsetTitle, choiceArr };
            }
          );
          this.form = {
            ...form,
            titleImages: contentObj.titleImages || [],
            subsetTitles,
          };
        }

        if (form.questionType === "8") {
          this.form = { ...form, titleImages: contentObj.titleImages || [] };
        }

        if (form.questionType === "10") {
          const choiceArr = (form.answer || "").split(",").map((key) => ({
            desc: "",
            images: [],
            answer: key,
          }));
          this.form = {
            ...form,
            titleImages: contentObj.titleImages || [],
            choiceArr,
          };
        }

        if (form.questionType === "5" || form.questionType === "6") {
          this.form = {
            ...form,
            titleImages: contentObj.titleImages || [],
          };
        }

        const curPoint = this.$refs.tree.getCurrentNode();
        this.form.pointId = curPoint.id;
        this.form.pointName = curPoint.name;

        this.open = true;
        this.title = "修改考试试题";
      });
    },
    /** 添加删除选项 */
    addChoice(target, index) {
      target.splice(index + 1, 0, { desc: "", images: [], answer: "" });
    },
    removeChoice(target, index) {
      target.splice(index, 1);
    },
    /** 添加删除配伍题子题 */
    addTitle(index) {
      this.form.titleArr.splice(index + 1, 0, {
        desc: "",
        images: [],
        answer: "",
      });
    },
    removeTitle(index) {
      this.form.titleArr.splice(index, 1);
    },
    /** 添加删除病例题试题 */
    addSubsetTitle(index) {
      this.form.subsetTitles.splice(index + 1, 0, {
        title: undefined,
        titleImages: [],
        choiceArr: [{ desc: "", images: [], answer: "" }],
        answer: undefined,
        analysis: undefined,
      });
    },
    removeSubsetTitle(index) {
      this.form.subsetTitles.splice(index, 1);
    },
    /** 添加删除填空题答案 */
    addTiankong() {
      this.form.choiceArr.push({ desc: "", images: [], answer: "" });
    },
    removeTiankong(target, index) {
      target.splice(index, 1);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const { titleImages, choiceArr, titleArr, subsetTitles, ...rest } =
          this.form;
        const form = { ...rest };

        const dealChoiceArr = (choiceArr) => {
          const choiceList = {};
          const choiceImages = {};
          const answerArr = [];
          choiceArr.forEach((choice, index) => {
            const key = this.choiceKeys[index];
            choiceList[key] = choice.desc;
            choiceImages[key] = choice.images;
            if (choice.answer) {
              answerArr.push(key);
            }
          });
          return { choiceList, choiceImages, answerArr };
        };

        if (form.questionType === "1") {
          const { choiceList, choiceImages } = dealChoiceArr(choiceArr);
          form.content = JSON.stringify({
            title: this.form.title,
            titleImages,
            choiceList,
            choiceImages,
          });
        }

        if (form.questionType === "2") {
          const { choiceList, choiceImages, answerArr } =
            dealChoiceArr(choiceArr);
          form.content = JSON.stringify({
            title: this.form.title,
            titleImages,
            choiceList,
            choiceImages,
          });
          form.answer = answerArr.join("");
        }

        if (form.questionType === "3") {
          const { choiceList, choiceImages } = dealChoiceArr(choiceArr);
          const titleList = {};
          const titleListImages = {};
          titleArr.forEach((title, index) => {
            titleList[this.titleKeys[index]] = title.desc;
            titleListImages[this.titleKeys[index]] = title.images;
          });
          form.title = Object.keys(choiceList)
            .map((key) => `${key}. ${choiceList[key]}`)
            .join(" ");
          form.content = JSON.stringify({
            title: this.form.title,
            titleImages,
            choiceList,
            choiceImages,
            titleList,
            titleListImages,
          });
          form.answer = titleArr.map((item) => item.answer).join(",");
        }

        if (form.questionType === "4") {
          const _subsetTitles = subsetTitles.map((item) => {
            const { choiceArr, ...rest } = item;
            const { choiceList, choiceImages, answerArr } =
              dealChoiceArr(choiceArr);
            return {
              ...rest,
              choiceList,
              choiceImages,
              answer: answerArr.join(""),
            };
          });
          form.content = JSON.stringify({
            title: this.form.title,
            titleImages,
            subsetTitles: _subsetTitles,
          });
          form.answer = _subsetTitles.map((item) => item.answer).join(",");
        }

        if (form.questionType === "8") {
          form.content = JSON.stringify({
            title: this.form.title,
            titleImages,
            choiceList: { A: "正确", B: "错误" },
          });
        }

        if (form.questionType === "10") {
          form.content = JSON.stringify({
            title: this.form.title,
            titleImages,
          });
          form.answer = choiceArr.map((item) => item.answer).join(",");
        }

        if (form.questionType === "5" || form.questionType === "6") {
          form.content = JSON.stringify({
            title: this.form.title,
            titleImages,
          });
        }

        // 修改的提交
        if (form.id != null) {
          updateQuestion(form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createQuestion(form).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm("是否确认删除该考试试题?")
        .then(function () {
          return deleteQuestion(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.question-link {
  color: #409eff;
  cursor: pointer;
  &:hover {
    color: #358de3;
  }
}

.overflow-ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.tree-wrapper {
  border: 1px solid #e6ebf2;
  height: calc(100vh - 220px);
  overflow: auto;
  padding: 5px 0;
}

.handle-icon {
  cursor: pointer;
  color: #46a6ff;
  padding: 2px;
}

.handle-icon::before {
  font-size: 14px;
}

.subset-titles {
  margin-left: -70px;
  margin-top: 40px;
  background: #fdfdfd;
}

.subset-title {
  padding: 20px 15px 15px 30px;

  &:not(:last-child) {
    border-bottom: 1px solid #e4e7ed;
  }

  ::v-deep .el-form-item {
    margin-bottom: 22px;
  }
}

.subset-index {
  position: absolute;
  left: -60px;
  font-size: 15px;
  color: #000;
  margin-top: -2px;
}

.subset-handle {
  text-align: right;
  i {
    padding: 4px;
    cursor: pointer;
  }
}

.question-wrapper {
  margin-bottom: 20px;
  min-height: 400px;
  font-size: 15px;

  .question-type {
    color: dodgerblue;
  }
  .question-score {
    color: #999;
  }

  .question-answer {
    padding-top: 15px;
    padding-left: 25px;

    ::v-deep .el-radio,
    ::v-deep .el-checkbox {
      display: block;
      margin-bottom: 10px;
    }

    .question-tiankong-row {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      span {
        display: inline-block;
        width: 60px;
        margin-right: 10px;
        text-align: right;
      }
    }
  }

  .question-choice {
    padding-top: 15px;
    padding-left: 25px;
    margin-bottom: 10px;
    > div {
      margin-bottom: 10px;
    }
  }

  .question-subtitle {
    ::v-deep .el-select {
      width: 92px;
    }
    ::v-deep .el-input__inner {
      border: none;
    }
    .question-subtitle-answer {
      margin-left: 5px;
    }
  }

  .question-subset-titles {
    padding-top: 15px;
    padding-left: 25px;
  }

  .question-subset-title {
    margin-bottom: 20px;
  }
}

.no-label-radio ::v-deep .el-radio__label {
  display: none;
}

.question-item-answer {
  padding: 10px 0 0 25px;
}
.question-item-analysis {
  padding: 10px 0 0 25px;
}
</style>
