<template>
  <div>
    <h3 class="sheet-name">{{ scoreForm.formTitle }}</h3>

    <el-form class="form-info" style="margin-bottom: 20px" inline>
      <el-form-item style="margin-right: 30px" label="姓名：">{{ scoreForm.nickName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="专业：">{{ scoreForm.majorName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="派送单位：">{{ scoreForm.dispatchingUnit }}</el-form-item>
      <el-form-item label="轮转科室：">{{ scoreForm.rotationDepartmentName }}</el-form-item>
      <el-form-item label="轮转时间：">{{ scoreForm.rotationTime }}</el-form-item>
    </el-form>

    <table class="score-table">
      <tr>
        <th style="width: 12%"></th>
        <th style="width: 45%">项目</th>
        <th style="width: 33%">评分标准</th>
        <th style="width: 10%">得分</th>
      </tr>
      <tr>
        <td rowspan="2"><span class="required-tag">*</span>医德医风<br/>（10分）</td>
        <td>医疗作风、廉洁行医、服务态度、医患关系</td>
        <td>
          <el-radio-group v-model="form.medicalEthicsFirstItemScore" :disabled="check" @input="handleChange">
            <el-radio :label="1"></el-radio>
            <el-radio :label="2"></el-radio>
            <el-radio :label="3"></el-radio>
            <el-radio :label="4"></el-radio>
            <el-radio :label="5"></el-radio>
          </el-radio-group>
        </td>
        <td>{{ form.medicalEthicsFirstItemScore }}</td>
      </tr>
      <tr>
        <td>工作负责、主动认真、团结协作、遵守制度</td>
        <td>
          <el-radio-group v-model="form.medicalEthicsSecondItemScore" :disabled="check" @input="handleChange">
            <el-radio :label="1"></el-radio>
            <el-radio :label="2"></el-radio>
            <el-radio :label="3"></el-radio>
            <el-radio :label="4"></el-radio>
            <el-radio :label="5"></el-radio>
          </el-radio-group>
        </td>
        <td>{{ form.medicalEthicsSecondItemScore }}</td>
      </tr>
      <tr>
        <td><span class="required-tag">*</span>出勤状况<br/>（5分）</td>
        <td>
          病假 <span v-if="check">{{ form.attendanceSickLeaveDays }}</span> <el-input v-else class="short-input" size="mini" v-model="form.attendanceSickLeaveDays" /> 天；
          事假 <span v-if="check">{{ form.attendanceLeaveOfAbsenceDays }}</span> <el-input v-else class="short-input" size="mini" v-model="form.attendanceLeaveOfAbsenceDays" /> 天 <br/>
          迟到 <span v-if="check">{{ form.attendanceLateDays }}</span> <el-input v-else class="short-input" size="mini" v-model="form.attendanceLateDays" /> 次；
          脱岗 <span v-if="check">{{ form.attendanceLeaveDays }}</span> <el-input v-else class="short-input" size="mini" v-model="form.attendanceLeaveDays" /> 次
        </td>
        <td>
          <el-radio-group v-model="form.attendanceScore" :disabled="check" @input="handleChange">
            <el-radio :label="1"></el-radio>
            <el-radio :label="2"></el-radio>
            <el-radio :label="3"></el-radio>
            <el-radio :label="4"></el-radio>
            <el-radio :label="5"></el-radio>
          </el-radio-group>
        </td>
        <td>{{ form.attendanceScore }}</td>
      </tr>
      <tr>
        <td rowspan="3">专业考核<br/>（85分）</td>
        <td>病历书写（5分）</td>
        <td>
          <el-radio-group v-model="form.caseWriteScore" :disabled="check" @input="handleChange">
            <el-radio :label="1"></el-radio>
            <el-radio :label="2"></el-radio>
            <el-radio :label="3"></el-radio>
            <el-radio :label="4"></el-radio>
            <el-radio :label="5"></el-radio>
          </el-radio-group>
        </td>
        <td>{{ form.caseWriteScore }}</td>
      </tr>
      <tr>
        <td>理论考试（50分）</td>
        <td>实际分数 <span class="examine-score">{{ form.syncExamResultsActualScore }}</span> * 0.50</td>
        <td>{{ form.syncExamResultsScore || "未参加" }}</td>
      </tr>
      <tr>
        <td>技能考核（30分）</td>
        <td>实际分数 <span class="examine-score">{{ form.graduationAssessmentActualScore }}</span> * 0.30</td>
        <td>{{ form.graduationAssessmentScore || "未参加" }}</td>
      </tr>
      <tr>
        <td colspan="2">考核综合成绩</td>
        <td colspan="2">{{ form.comprehensiveScore }}</td>
      </tr>
      <tr>
        <td colspan="2"><span class="required-tag">*</span>医疗严谨、无差错或事故</td>
        <td colspan="2">
          <span v-if="check">{{ form.existsAccident ? "有" : "无" }}</span>
          <el-radio-group v-else v-model="form.existsAccident">
            <el-radio :label="true">有</el-radio>
            <el-radio :label="false">无</el-radio>
          </el-radio-group>
        </td>
      </tr>
      <tr>
        <td colspan="1"><span class="required-tag">*</span>上传病例书写照片</td>
        <td colspan="3">
          <image-upload class="image-upload" v-model="form.caseWritePhotos" :disabled="check"></image-upload>
        </td>
      </tr>
    </table>
  </div>
</template>

<script>
import ImageUpload from '@/components/ImageUpload'

export default {
  name: 'sheet-szlgrm',
  components: { ImageUpload },
  props: {
    scoreForm: Object,
    check: Boolean,
  },
  data() {
    return {
      form: {}
    }
  },
  methods: {
    handleChange() {
      this.form.comprehensiveScore = (+this.form.medicalEthicsFirstItemScore || 0) +
        (+this.form.medicalEthicsSecondItemScore || 0) +
        (+this.form.attendanceScore || 0) +
        (+this.form.caseWriteScore || 0) +
        (+this.form.syncExamResultsScore || 0) +
        (+this.form.graduationAssessmentScore || 0);
      this.judgeTotalEvaluate();
    },
    judgeTotalEvaluate() {
      const unqualified = this.form.medicalEthicsFirstItemScore === 1 || this.form.medicalEthicsSecondItemScore === 1 ||
        +this.form.graduationAssessmentActualScore < 60 || +this.form.graduationAssessmentActualScore < 60 ||
        +this.form.comprehensiveScore < 60 || this.form.existsAccident;

      this.$emit("evaluate", unqualified ? "0" : "1");
    },
    formValidate() {
      if (!this.form.medicalEthicsFirstItemScore ||
        !this.form.medicalEthicsSecondItemScore ||
        !this.form.attendanceScore
      ) {
        this.$message.warning("请全部完成评分！");
        return false;
      }
      const requiredPhotos = ["logo_kangning", "logo_chancheng"].indexOf(process.env.VUE_APP_PAGE_LOGO) < 0;
      if (requiredPhotos && (this.form.caseWritePhotos || []).length === 0) {
        this.$message.warning("请上传病例书写照片!");
        return false;
      }
      return true;
    },
  },
  created() {
    this.form = this.scoreForm;
    this.judgeTotalEvaluate();
  },
}
</script>

<style lang="scss" scoped>
.sheet-name {
  font-size: 15px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 10px;
}

.form-info {
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}

.score-table {
  line-height: 1.5;
  border-collapse: collapse;
  width: 100%;

  th, td {
    border: 1px solid #DCDFE6;
    text-align: center;
    padding: 7px 5px;
  }

  ::v-deep .el-radio {
    margin-right: 8px;
  }

  ::v-deep .el-input__inner {
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #eee;
  }
}

.required-tag {
  color: red;
  padding-right: 2px;
}

.short-input {
  width: 50px;
}

.examine-score {
  display: inline-block;
  min-width: 30px;
  padding: 5px;
  border-bottom: 1px solid #eee;
}

.image-upload {
  text-align: left;
}
</style>
