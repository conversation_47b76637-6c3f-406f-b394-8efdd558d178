<template>
  <div class="container">
    <div class="logo">{{ loginTitle }}</div>
    <!-- 注册区域 -->
    <div class="content">
      <!-- 配图 -->
      <div class="pic"></div>
      <!-- 表单 -->
      <div class="field">
        <!-- [移动端]标题 -->
        <h2 class="mobile-title">
          <h3 class="title">后台管理系统</h3>
        </h2>

        <!-- 表单 -->
        <div class="form-cont">
          <div class="form-logo">
            <img v-if="formlogo" height="60" :src="require(`../assets/images/${formlogo}.png`)" />
          </div>

          <div class="register-title">用户注册</div>

          <el-form ref="registerForm" label-width="80px" :model="registerForm" :rules="registerRules" class="login-form">
            <!-- 账号密码注册 -->
            <el-form-item prop="nickname" label="真实姓名">
              <el-input v-model="registerForm.nickname" type="text" auto-complete="off" placeholder="真实姓名">
                <svg-icon slot="prefix" icon-class="logininfor" class="el-input__icon input-icon"/>
              </el-input>
            </el-form-item>
            <el-form-item prop="username" label="登陆账号">
              <el-input v-model="registerForm.username" type="text" auto-complete="off" placeholder="登陆账号">
                <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
              </el-input>
            </el-form-item>
            <el-form-item prop="password" label="用户密码">
              <el-tooltip content="密码必须包含大小写字母、特殊符号、数字，长度最少8位" placement="top">
                <el-input v-model="registerForm.password" type="password" auto-complete="off" placeholder="用户密码"
                          @keyup.enter.native="getCode">
                  <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
                </el-input>
              </el-tooltip>
            </el-form-item>
            <el-form-item prop="confirmPassword" label="确认密码">
              <el-input v-model="registerForm.confirmPassword" type="password" auto-complete="off" placeholder="确认密码"
                        @keyup.enter.native="getCode">
                <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button :loading="loading" size="medium" type="primary" @click.native.prevent="getCode">
                <span v-if="!loading">注 册</span>
                <span v-else>注 册 中...</span>
              </el-button>
              <p>已有账号，直接<span class="login-link" @click="$router.push('/login')">登录</span></p>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 图形验证码 -->
    <Verify ref="verify" :captcha-type="'blockPuzzle'" :img-size="{width:'400px',height:'200px'}"
            @success="handleRegister" />

    <!-- footer -->
    <div class="footer" style="color: white;">
      {{ footerText }}
    </div>
  </div>
</template>

<script>
import {registerRecruitmentUser} from "@/api/system/user";
import {getCaptchaEnable} from "@/utils/ruoyi";
import Verify from '@/components/Verifition/Verify';

export default {
  name: "Register",
  components: {
    Verify
  },
  data() {
    return {
      formlogo: process.env.VUE_APP_LOGIN_LOGO,
      loginTitle: process.env.VUE_APP_LOGIN_TITLE,
      footerText: process.env.VUE_APP_FOOTER,
      codeUrl: "",
      captchaEnable: true,
      loading: false,
      registerForm: {
        nickname: "",
        username: "",
        password: "",
        confirmPassword: "",
        captchaVerification: "",
      },
      registerRules: {
        nickname: [
          {required: true, trigger: "blur", message: "真实姓名不能为空"}
        ],
        username: [
          {required: true, trigger: "blur", message: "登陆账号不能为空"}
        ],
        password: [
          {required: true, trigger: "blur", message: "密码不能为空"}
        ],
        confirmPassword: [
          {required: true, trigger: "blur", message: "确认密码不能为空"}
        ],
      },
    };
  },
  created() {
    // 验证码开关
    this.captchaEnable = getCaptchaEnable();
    this.getCookie();
  },
  methods: {
    getCode() {
      // 情况一，未开启：则直接登录
      if (!this.captchaEnable) {
        this.handleLogin({})
        return;
      }

      // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
      // 弹出验证码
      this.$refs.verify.show()
    },
    handleRegister(captchaParams) {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.loading = true;
          this.registerForm.captchaVerification = captchaParams.captchaVerification;
          registerRecruitmentUser(this.registerForm).then(() => {
            this.$message.success("注册成功");
            this.$router.push("/login");
          }).finally(() => {
            this.loading = false;
          });
        }
      }).catch(() => {
        this.loading = false;
      });
    },
  }
};
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/login.scss";

.register-title {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  padding-top: 20px;
}

.login-link {
  color: #409EFF;
  cursor: pointer;
  padding-left: 2px;
  &:hover {
    color: #3b8ce5;
  }
}
</style>
