<template>
  <div>
    <h3 class="sheet-name">{{ scoreForm.formTitle }}</h3>

    <el-form class="form-info" style="margin-bottom: 20px" inline>
      <el-form-item style="margin-right: 30px" label="姓名：">{{ scoreForm.nickName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="专业：">{{ scoreForm.majorName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="派送单位：">{{ scoreForm.dispatchingUnit }}</el-form-item>
      <el-form-item label="轮转科室：">{{ scoreForm.rotationDepartmentName }}</el-form-item>
      <el-form-item label="轮转时间：">{{ scoreForm.rotationTime }}</el-form-item>
    </el-form>

    <table class="score-table">
      <tr>
        <th style="width: 30%"></th>
        <th style="width: 50%">项目</th>
        <th style="width: 10%">满分</th>
        <th style="width: 10%">得分</th>
      </tr>
      <tr>
        <td rowspan="3">多维度评价</td>
        <td>轮转科室评价</td>
        <td>10</td>
        <td>{{ form.rotationDepartmentAppraiseScore }}</td>
      </tr>
      <tr>
        <td>指导医师评价</td>
        <td>10</td>
        <td>{{ form.teacherAppraiseScore }}</td>
      </tr>
      <tr>
        <td>护理人员评价</td>
        <td>5</td>
        <td>{{ form.nurseAppraiseScore }}</td>
      </tr>
      <tr>
        <td>出勤状况</td>
        <td>
          出勤（ {{form.attendanceDays}} ）天、
          休息（ {{form.attendanceRestDays}} ）天、
          旷工（ {{form.attendanceAbsenteeismDays}} ）天、
          迟到（ {{form.attendanceLateDays}} ）天、
          早退（ {{form.attendanceLeaveEarlyDays}} ）次、
          请假（ {{form.attendanceLeaveDays}} ）次
        </td>
        <td>10</td>
        <td>
          <el-input-number class="score-input" v-model="form.attendanceScore" :min="0" :max="10" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td>参加业务学习情况</td>
        <td style="text-align: left">
          <div class="flex-row" v-for="(item, index) in (form.teachingActiveStatisticsRespVOS || []).filter(item => item.activeItem === '2')" :key="index">
            <div>
              <strong>{{ getDictDataLabel(DICT_TYPE.ACTIVE_ITEM, item.activeItem) }}</strong>
              （要求参加：{{ item.cases }}次，总参加：{{ item.joinCases }}次）
            </div>
            <div style="padding-left: 15px;">
              <div class="flex-row" v-for="subItem in item.teachingActiveSubStatisticses" :key="subItem.activeType">
                {{ getDictDataLabel(DICT_TYPE.ROTATION_ACTIVE_TYPE, subItem.activeType) }}{{ subItem.joinCases }}次
              </div>
            </div>
          </div>
          <div v-if="((form.teachingActiveStatisticsRespVOS || []).filter(item => item.activeItem === '2') || []).length === 0">暂无数据</div>
        </td>
        <td>5</td>
        <td>
          <el-input-number class="score-input" v-model="form.businessScore" :min="0" :max="5" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td>培训强度</td>
        <td style="text-align: left;">
          <div class="flex-row">
            <strong>（五选一）</strong>
            <el-radio-group v-model="form.trainingIntensityChoose">
              <el-radio label="manageBed">
                病房管床数（≥4张）; <el-input-number class="short-input-number" v-model="form.trainingIntensityManageBedNumber" size="mini" :min="0" :controls="false"></el-input-number>
              </el-radio>
              <el-radio label="reception">
                门诊日均接诊量（≥15人次） <el-input-number class="short-input-number" v-model="form.trainingIntensityFollowNumber" size="mini" :min="0" :controls="false"></el-input-number>
              </el-radio>
              <el-radio label="imageDiagnose">
                影像诊断报告工作量： 总数 <el-input-number class="short-input-number" v-model="form.trainingIntensityImageDiagnoseSumNumber" size="mini" :min="0" :controls="false"></el-input-number>，
                每个工作日平均 <el-input-number class="short-input-number" v-model="form.trainingIntensityImageDiagnoseAvgNumber" size="mini" :min="0" :controls="false"></el-input-number>
              </el-radio>
              <el-radio label="imageSkill">
                影像技能操作工作量： 总数 <el-input-number class="short-input-number" v-model="form.trainingIntensityImageSkillSumNumber" size="mini" :min="0" :controls="false"></el-input-number>，
                每个工作日平均 <el-input-number class="short-input-number" v-model="form.trainingIntensityImageSkillAvgNumber" size="mini" :min="0" :controls="false"></el-input-number>
              </el-radio>
              <el-radio label="imageInspect">
                影像检查操作工作量： 总数 <el-input-number class="short-input-number" v-model="form.trainingIntensityImageInspectSumNumber" size="mini" :min="0" :controls="false"></el-input-number>，
              </el-radio>
            </el-radio-group>
          </div>
          <div class="flex-row">
            <strong>值班：</strong>
            跟值 <el-input-number class="short-input-number" v-model="form.trainingIntensityReceptionNumber" size="mini" :min="0" :controls="false"></el-input-number> 次，
            独立 <el-input-number class="short-input-number" v-model="form.trainingIntensityIndependentDutyNumber" size="mini" :min="0" :controls="false"></el-input-number>次；
          </div>
          <div class="flex-row">
            <strong>医疗缺陷（含有效投诉，乙级病例等）: </strong>
            <el-input-number class="short-input-number" v-model="form.trainingIntensityMedicalDefectsNumber" size="mini" :min="0" :controls="false"></el-input-number> 次
          </div>
        </td>
        <td>10</td>
        <td>
          <el-input-number class="score-input" v-model="form.trainingIntensityScore" :min="0" :max="10" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td>培训指标完成情况</td>
        <td style="text-align: left;">
          <div class="summary-tips">请务必审核督促数据填写情况，相应赋分</div>
          <div class="flex-row direction-column" v-for="item in (form.manualTotal && form.manualTotal.manualItemSummaryRespVOS || [])" :key="item.rotaionItem">
            <strong style="margin-right: 15px">{{ getDictDataLabel(DICT_TYPE.ROTAION_ITEM, item.rotaionItem) }}</strong>
            <div>
              要求数：<span style="margin-right: 10px;">{{ item.cases }}</span>
              完成数：<span style="margin-right: 10px">{{ item.examineSuccessCases }}</span>
              完成比例：{{ Math.min(Math.round(item.examineSuccessCases * 100 / (item.cases || 1)), 100) }}%
            </div>
          </div>
          <div v-if="(form.manualTotal && form.manualTotal.manualItemSummaryRespVOS || []).length === 0">暂无数据</div>
        </td>
        <td>10</td>
        <td>
          <el-input-number class="score-input" v-model="form.trainingIndicatorsScore" :min="0" :max="10" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td rowspan="2">出科考试</td>
        <td>理论考试(实际分数 <span class="examine-score">{{ form.syncExamResultsActualScore }}</span> * 0.20)</td>
        <td>20</td>
        <td>{{ form.syncExamResultsScore || "未参加" }}</td>
      </tr>
      <tr>
        <td>技能考试(实际分数 <span class="examine-score">{{ form.graduationAssessmentActualScore }}</span> * 0.20)</td>
        <td>20</td>
        <td>{{ form.graduationAssessmentScore || "未参加" }}</td>
      </tr>
      <tr>
        <td colspan="2">考核综合成绩</td>
        <td>100</td>
        <td>{{ form.comprehensiveScore }}</td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  name: 'sheet-fourth',
  components: {},
  props: {
    scoreForm: Object,
    check: Boolean,
  },
  data() {
    return {
      form: {},
    }
  },
  methods: {
    formValidate() {
      const isUndefinedOrNull = (val) => val === undefined || val === null;
      if (!this.form.trainingIntensityChoose) {
        this.$message.warning("培训强度必须五选一");
        return false;
      }
      if (this.form.trainingIntensityChoose === 'manageBed' && isUndefinedOrNull(this.form.trainingIntensityManageBedNumber)) {
        this.$message.warning("病房管床数数量必填");
        return false;
      }
      if (this.form.trainingIntensityChoose === 'reception' && isUndefinedOrNull(this.form.trainingIntensityFollowNumber)) {
        this.$message.warning("门诊日均接诊量数量必填");
        return false;
      }
      if (this.form.trainingIntensityChoose === 'imageDiagnose' && (isUndefinedOrNull(this.form.trainingIntensityImageDiagnoseSumNumber)
        || isUndefinedOrNull(this.form.trainingIntensityImageDiagnoseAvgNumber))) {
        this.$message.warning("影像诊断报告工作量必填");
        return false;
      }
      if (this.form.trainingIntensityChoose === 'imageSkill' && (isUndefinedOrNull(this.form.trainingIntensityImageSkillSumNumber)
        || isUndefinedOrNull(this.form.trainingIntensityImageSkillAvgNumber))) {
        this.$message.warning("影像技能操作工作量必填");
        return false;
      }
      if (this.form.trainingIntensityChoose === 'reception' && isUndefinedOrNull(this.form.trainingIntensityImageInspectSumNumber)) {
        this.$message.warning("影像检查操作工作量必填");
        return false;
      }

      if (isUndefinedOrNull(this.form.trainingIntensityReceptionNumber) ||
        isUndefinedOrNull(this.form.trainingIntensityIndependentDutyNumber) ||
        isUndefinedOrNull(this.form.trainingIntensityMedicalDefectsNumber)) {
        this.$message.warning("值班和医疗缺陷必填");
        return false;
      }
      if (
        isUndefinedOrNull(this.form.attendanceScore) ||
        isUndefinedOrNull(this.form.businessScore) ||
        isUndefinedOrNull(this.form.trainingIntensityScore) ||
        isUndefinedOrNull(this.form.trainingIndicatorsScore)
      ) {
        this.$message.warning("请全部完成评分");
        return false;
      }
      return true;
    },
    handleChange() {
      this.form.comprehensiveScore = (+this.form.rotationDepartmentAppraiseScore || 0) +
        (+this.form.teacherAppraiseScore || 0) +
        (+this.form.nurseAppraiseScore || 0) +
        (+this.form.attendanceScore || 0) +
        (+this.form.businessScore || 0) +
        (+this.form.trainingIntensityScore || 0) +
        (+this.form.trainingIndicatorsScore || 0) +
        (+this.form.syncExamResultsScore || 0) +
        (+this.form.graduationAssessmentScore || 0);
      this.form.comprehensiveScore = this.form.comprehensiveScore.toFixed(1);
    },
  },
  created() {
    this.form = this.scoreForm;
  },
}
</script>

<style lang="scss" scoped>
.sheet-name {
  font-size: 15px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 10px;
}

.form-info {
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}

.score-table {
  line-height: 1.5;
  border-collapse: collapse;
  width: 100%;

  th, td {
    border: 1px solid #DCDFE6;
    text-align: center;
    padding: 7px 5px;
  }
}

.required-tag {
  color: red;
  padding-right: 2px;
}

.score-input {
  width: 80px;
}

.short-input-number {
  width: 50px;
  ::v-deep .el-input__inner {
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #eee;
  }
}

.examine-score {
  display: inline-block;
  min-width: 30px;
  padding: 5px;
  border-bottom: 1px solid #eee;
}

.flex-row {
  display: flex;
  margin-bottom: 4px;
  align-items: center;
  strong {
    flex-shrink: 0;
  }
  ::v-deep .el-radio {
    margin-bottom: 4px;
    margin-right: 36px;
  }
  ::v-deep .el-radio__label {
    font-size: 1rem;
  }
  &.direction-column {
    flex-direction: column;
    align-items: flex-start;
  }
}

.summary-tips {
  color: #ff4d2c;
  margin-bottom: 4px;
}
</style>
