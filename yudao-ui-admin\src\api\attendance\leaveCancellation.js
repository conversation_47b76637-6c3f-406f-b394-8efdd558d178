import request from '@/utils/request'

// 创建销假
export function createLeaveCancellation(data) {
  return request({
    url: '/attendance/leave-cancellation/create',
    method: 'post',
    data: data
  })
}

// 更新销假
export function updateLeaveCancellation(data) {
  return request({
    url: '/attendance/leave-cancellation/update',
    method: 'put',
    data: data
  })
}

// 删除销假
export function deleteLeaveCancellation(id) {
  return request({
    url: '/attendance/leave-cancellation/delete?id=' + id,
    method: 'delete'
  })
}

// 获得销假
export function getLeaveCancellation(id) {
  return request({
    url: '/attendance/leave-cancellation/get?id=' + id,
    method: 'get'
  })
}

// 获得销假分页
export function getLeaveCancellationPage(query) {
  return request({
    url: '/attendance/leave-cancellation/page',
    method: 'get',
    params: query
  })
}

// 导出销假 Excel
export function exportLeaveCancellationExcel(query) {
  return request({
    url: '/attendance/leave-cancellation/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
