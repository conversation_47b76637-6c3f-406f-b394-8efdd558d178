<template>
  <div class="app-container mentorUpdate-page">
    <div class="mentorLibrary-detail-box">
        <el-form ref="form" :model="form" label-width="0px">
          <div class="base-info">
            <userAvatar :user="form" @info="getAvatar" opt="edit" />
            <!-- <img v-if="form.mentorPhoto" class="photo" :src="form.mentorPhoto" />
            <div v-else class="no-photo">
              <i class="el-icon-picture-outline" style="font-size: 28px"></i>
              <span>暂无图片</span>
            </div> -->
            <div class="info-box">
              <div class="time"> 最后更新时间：{{form.updateTime}}</div>
              <el-descriptions class="margin-top" :column="2" border>
                <el-descriptions-item>
                  <template slot="label">
                    姓名
                  </template>
                  {{ form.nickname }}
                  <!-- <el-form-item label="" prop="activeType">
                    <el-input v-model="form.name"></el-input>
                  </el-form-item> -->
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    性别
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_SEX, form.sex) }}
                  <!-- <el-form-item label="" prop="activeType">
                    <el-select v-model="form.activeType" filterable placeholder="请选择性别">
                      <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
                                :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </el-form-item> -->
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    名族
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_NATION, form.nation) }}
                  <!-- <el-form-item label="" prop="activeType">
                    <el-select v-model="form.activeType" filterable placeholder="请选择性别">
                      <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
                                :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </el-form-item> -->
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    导师层次
                  </template>
                  <el-form-item label="" prop="mentorLevel">
                    <el-select v-model="form.mentorLevel" filterable placeholder="请选择导师层次">
                      <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MENTOR_LEVEL)"
                                :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    职称
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES, form.positionalTitles) }}
                  <!-- <el-form-item label="" prop="activeType">
                    <el-select v-model="form.activeType" filterable placeholder="请选择性别">
                      <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
                                :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </el-form-item> -->
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    导师类型
                  </template>
                  <el-form-item label="" prop="mentorType">
                    <el-select v-model="form.mentorType" filterable placeholder="请选择导师类型">
                      <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MENTOR_TYPE)"
                                :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    最后学历
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_EDUCATION, form.education) }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    最后学位
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_DEGREE, form.degree) }}
                  <!-- <el-form-item label="" prop="activeType">
                    <el-select v-model="form.activeType" filterable placeholder="请选择性别">
                      <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
                                :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </el-form-item> -->
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    行政职务
                  </template>
                  <el-form-item label="" prop="jobTitle">
                    <el-select v-model="form.jobTitle" filterable placeholder="请选择行政职务">
                      <el-option v-for="dict in this.getDictDatas(DICT_TYPE.JOB_TITLE)"
                                :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                  </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    个人邮箱
                  </template>
                  {{ form.email }}
                  <!-- <el-form-item label="">
                    <el-input v-model="form.name"></el-input>
                  </el-form-item> -->
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    工作单位
                  </template>
                  <!-- {{ form.organizationName }} -->
                  <el-form-item label="" prop="organizationName">
                    <el-input v-model="form.organizationName"></el-input>
                  </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    邮政编码
                  </template>
                  <el-form-item label="" prop="postalCode">
                    <el-input v-model="form.postalCode"></el-input>
                  </el-form-item>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>

          <div class="desc-box">
            <div class="title">个人简介</div>
            <div class="cont">
              <el-form-item label="" prop="personalProfile">
                <el-input type="textarea" v-model="form.personalProfile" :autosize="{ minRows: 2, maxRows: 5}" placeholder="请输入个人简介"></el-input>
              </el-form-item>
            </div>
          </div>

          <div class="desc-box">
            <div class="title">研究领域</div>
            <div class="cont">
              <el-form-item label="" prop="researchArea">
                <el-input type="textarea" v-model="form.researchArea" :autosize="{ minRows: 2, maxRows: 5}" placeholder="请输入研究领域"></el-input>
              </el-form-item>
            </div>
          </div>

          <div class="desc-box">
            <div class="title">个人成果</div>
            <div class="cont">
              <el-form-item label="" prop="personalAchievements">
                <el-input type="textarea" v-model="form.personalAchievements" :autosize="{ minRows: 2, maxRows: 5}" placeholder="请输入个人成果"></el-input>
              </el-form-item>
            </div>
          </div>

        </el-form>

        <div class="footer">
          <el-button type="primary" @click="handleSave">更新</el-button>
        </div>
      </div>
      
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import { getAccessToken } from "@/utils/auth";
import { getMentorOwn, updateMentorInfo } from "@/api/rotation/mentorLibrary";


export default {
  name: "MentorUpdate",
  components: { userAvatar },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: "",
        sex: null,
        mentorLevel: null,
        mentorType: null,
        positionalTitles: null
      },
      open: false,
      form: {},
    };
  },
  created() {
    getMentorOwn().then((response) => {
      const {data = {}} = response

      this.form = data;
    });
  },
  methods: {
    /** 设置头像*/
    getAvatar(picUrl) {
      this.form.mentorPhoto = picUrl
    },
    handleSave(){
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        let params = {
          ...this.form
        }
        updateMentorInfo(params).then(response => {
          this.$modal.msgSuccess("更新成功");
        });
      });
    }
  }
};
</script>

<style lang="scss">
.mentorUpdate-page{
  .mentorLibrary-detail-box{
    width: 1000px;
    margin: 0 auto;

    .el-form-item{
      margin-bottom: 0;
    }

    .base-info{
      width: 100%;
      display: flex;

      .photo{
        width: 200px;
        height: 280px;
      }

      .no-photo{
        width: 200px;
        height: 280px;
        background: #f8f8f8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 14px;

        span{
          margin-top: 10px;
        }
      }

      .info-box{
        width: calc(100% - 220px);
        padding-left: 20px;

        .time{
          text-align: right;
          margin-bottom: 10px;
        }
      }
    }

    .desc-box{
      margin: 20px 0 30px 0;

      .title{
        position: relative;
        padding-left: 16px;
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;

        &::before{
          content: ' ';
          display: inline-block;
          width: 4px;
          height: 14px;
          background: #1890ff;
          position: absolute;
          left: 0;
          top: 3px;
        }
      }

      .cont{
        border: 1px #eee solid;
        padding: 10px;

        p{
          margin: 0;
          line-height: 25px;
        }
      }
    }

    .footer{
      text-align: right;
    }
  }
}

</style>
