import request from '@/utils/request'

// 获得教学活动分页
export function getTeachingActivePage(query) {
  return request({
    url: '/rotation/teaching-active/statistics/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/teachingActiveDevelopment/index'}
  })
}

// 导出教学活动 Excel
export function exportTeachingActiveExcel(query) {
  return request({
    url: '/rotation/teaching-active/statistics/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得教学活动分页
export function getActivePage(query) {
  return request({
    url: '/rotation/teaching-active-plan/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/teachingActivePlan/index'}
  })
}

