<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div>
            <div class="text-center">
              <userAvatar :user="user" />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <svg-icon icon-class="user" />用户名
                <div class="pull-right">{{ user.username }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="phone" />手机号码
                <div class="pull-right">{{ user.mobile }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="email" />用户邮箱
                <div class="pull-right">{{ user.email }}</div>
              </li>
              <!-- <li class="list-group-item">
                <svg-icon icon-class="tree" />所属部门
                <div class="pull-right" v-if="user.dept">{{ user.dept.name }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="tree" />所属岗位
                <div class="pull-right" v-if="user.posts">{{ user.posts.map(post => post.name).join(',') }}</div>
              </li> -->
              <li class="list-group-item">
                <svg-icon icon-class="peoples" />所属角色
                <div class="pull-right" v-if="user.roles">
                  {{ user.roles.map((role) => role.name).join(",") }}
                </div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="date" />创建日期
                <div class="pull-right">{{ parseTime(user.createTime) }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>基本资料</span>
          </div>
          <el-tabs v-model="activeTab">
            <el-tab-pane
              v-if="user.userType == 0"
              label="基本资料"
              name="userinfo"
            >
              <userInfo :user="user" />
            </el-tab-pane>
            <el-tab-pane
              v-if="user.userType == 1"
              label="基本资料"
              name="userinfo"
            >
              <workUserInfo :user="user" />
            </el-tab-pane>
            <el-tab-pane
              v-if="user.userType === 1"
              label="评聘记录"
              name="teacherRecord"
            >
              <teacherRecord />
            </el-tab-pane>
            <el-tab-pane
              v-if="user.userType == 2"
              label="基本资料"
              name="userinfo"
            >
              <studentUserInfo v-if="activeTab === 'userinfo'" :user="user" />
            </el-tab-pane>
            <el-tab-pane
              v-if="user.userType == 2"
              label="培训信息"
              name="userTrainingInfo"
            >
              <userTrainingInfo
                v-if="activeTab === 'userTrainingInfo'"
                :user="user"
              />
            </el-tab-pane>

            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd v-if="activeTab === 'resetPwd'" :user="user" />
            </el-tab-pane>
            <!-- <el-tab-pane label="社交信息" name="userSocial">
              <userSocial :user="user" :getUser="getUser" :setActiveTab="setActiveTab" />
            </el-tab-pane> -->
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import userTrainingInfo from "./userTrainingInfo";
import studentUserInfo from "./studentUserInfo";
import workUserInfo from "./workUserInfo";
import teacherRecord from "./teacherRecord";
import resetPwd from "./resetPwd";
import userSocial from "./userSocial";
import { getUserProfile } from "@/api/system/user";

export default {
  name: "Profile",
  components: {
    userAvatar,
    userInfo,
    resetPwd,
    userSocial,
    userTrainingInfo,
    studentUserInfo,
    workUserInfo,
    teacherRecord,
  },
  data() {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      activeTab: "userinfo",
    };
  },
  created() {
    this.getUser();
  },
  methods: {
    getUser() {
      getUserProfile().then((response) => {
        this.user = response.data;
      });
    },
    setActiveTab(activeTab) {
      this.activeTab = activeTab;
    },
  },
};
</script>
