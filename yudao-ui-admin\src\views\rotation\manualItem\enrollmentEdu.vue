<template>
  <div class="enrollment-edu">
    <el-form class="edu-doc" inline v-if="(config.enrollmentEduItems || '').indexOf('1') > -1">
      <el-form-item label="入科教育文档：">
        <el-link
          type="primary"
          icon="el-icon-document"
          style="margin-right: 20px;"
          @click="learnEduDoc"
        ></el-link>
        <dict-tag :type="DICT_TYPE.ROTATION_STUDY_STATUS" :value="info.eduDocStatus"></dict-tag>
      </el-form-item>
      <el-form-item label="学习时间：">
        {{ info.eduDocStudyTime ? new Date(info.eduDocStudyTime).toLocaleString() : "--" }}
      </el-form-item>
    </el-form>

    <el-card shadow="never" v-if="(config.enrollmentEduItems || '').indexOf('2') > -1">
      <div slot="header" style="font-size: 15px;">入科教育学习</div>
      <el-table :data="records">
        <el-table-column label="入科教育名称" prop="name" min-width="200px"></el-table-column>
        <el-table-column label="科室名称" prop="departmentName" min-width="100px"></el-table-column>
        <el-table-column label="学员类型" prop="studentTypes">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentTypes"></dict-tag>
          </template>
        </el-table-column>
        <el-table-column label="开始时间" prop="startTime" width="165px"></el-table-column>
        <el-table-column label="结束时间" prop="endTime" width="165px"></el-table-column>
        <el-table-column label="主讲人" prop="speakerUsername"></el-table-column>
        <el-table-column label="扫码时间" prop="scanningTime" width="165px"></el-table-column>
        <el-table-column label="评价" width="130px">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.ROTATION_EVALUATION_STATUS" :value="scope.row.evaluationStatus"></dict-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column label="考核">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.ROTATION_EXAMINE_STATUS" :value="scope.row.examineStatus"></dict-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <el-button v-if="scope.row.evaluationStatus == 0 && scope.row.scanningTime" size="mini" type="text" icon="el-icon-edit" @click="handleEditAppraise(scope.row)">进入评价</el-button>
            <el-button v-if="scope.row.evaluationStatus == 1" size="mini" type="text" icon="el-icon-picture-outline" @click="handleViewAppraise(scope.row)">查看评价</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <AppraiseDialog
      v-if="formData"
      :title="appraiseDialogTitle"
      :open="appraiseDialogOpen"
      :data="formData"
      :curRow="curRow"
      :disabled="appraiseDisabled"
      @setOpen="setAppraiseDialogOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import AppraiseDialog from "./appraiseDialog";
import { getAppraiseResult, getAppraiseForm } from "@/api/rotation/appraiseActive";
import { getEnrollmentEduDoc, getUserEnrollmentEduRecords } from '@/api/rotation/manual'

export default {
  name: 'enrollmentEdu',
  components: {
    AppraiseDialog
  },
  props: {
    info: {
      type: Object,
      default: {}
    },
    config: {
      type: Object,
      default: {}
    },
  },
  data() {
    return {
      scheduleDetailsId: this.$route.query.id,
      eduDocInfo: null,
      records: [],
      formData: null,
      appraiseDialogTitle: '',
      appraiseDialogOpen: false,
      curRow: null,
      appraiseDisabled: false,
    }
  },
  methods: {
    learnEduDoc() {
      if (!this.eduDocInfo) {
        this.$message.info('该科室暂未维护入科教育文档，请联系科室教学秘书')
        return
      }
      this.$router.push({
        path: '/rotation/enrollment-edu-doc-view',
        query: { scheduleDetailsId: this.scheduleDetailsId, id: this.info.id, dept: this.info.rotationDepartmentName  }
      })
    },
    handleViewAppraise(row) {
      this.appraiseDisabled = true
      const params = {
        id: row.appraiseActiveResultId
      }
      getAppraiseResult(params).then(response => {
        this.formData = response.data;
        this.appraiseDialogOpen = true;
        this.appraiseDialogTitle = `查看评价-${row.name}`;
      });
    },
    handleEditAppraise(row) {
      if (!row.scanningTime) {
        this.$message.info('请先参加入科教育再评价')
        return
      }
      this.curRow = row;
      this.appraiseDisabled = false
      const params = {
        appraiseActiveType: '4',
        activeType: row.activeType
      }
      getAppraiseForm(params).then(response => {
        this.formData = response.data
        this.appraiseDialogOpen = true;
        this.appraiseDialogTitle = `正在评价-${row.name}`;
      });
    },
    setAppraiseDialogOpen(flag) {
      this.appraiseDialogOpen = flag;
      this.formData = null
      this.curRow = null
    },
    getList() {
      getUserEnrollmentEduRecords(this.scheduleDetailsId).then(res => this.records = res.data)
    }
  },
  created() {
    getEnrollmentEduDoc(this.scheduleDetailsId).then(res => this.eduDocInfo = res.data)
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
.edu-doc {
  background: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 16px;
  margin-bottom: 20px;

  ::v-deep .el-form-item {
    margin-bottom: 0;
    margin-right: 100px;
  }

  ::v-deep .el-icon-document {
    font-size: 24px;
  }
}
</style>
