<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="600px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="审核结果" prop="professionalBaseExamineResult">
            <el-radio-group v-model="form.professionalBaseExamineResult">
              <el-radio
                v-for="dict in this.getDictDatas(
                  DICT_TYPE.REGISTRATION_PROFESSIONAL_BASE_EXAMINE_RESULT
                )"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item
            label="审核建议"
            prop="professionalBaseExamineRecommend"
          >
            <el-input
              type="textarea"
              v-model="form.professionalBaseExamineRecommend"
              placeholder="请在此输入审核建议"
              :autosize="{ minRows: 2 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm"> 确 认 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { auditProfessionalBaseExam } from "@/api/recruitment/rpregistrationProfessionalBaseExamine";

export default {
  name: "AuditDialog",
  components: {},
  props: {
    title: {
      type: String,
    },
    openAudit: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      open: this.openAudit,

      // 表单参数
      form: {
        professionalBaseExamineResult: "1",
      },
      // 表单校验
      rules: {
        professionalBaseExamineResult: [
          {
            required: true,
            message: "请选择审核结果",
            trigger: "change",
          },
        ],
        professionalBaseExamineRecommend: [
          { required: true, message: "审核建议不能为空", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    openAudit(newVal) {
      this.form = {
        recruitmentRegistrationId: this.formData.recruitmentRegistrationId,
        professionalBaseExamineResult: "1",
      };
      this.open = newVal;
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:openAudit", false);
    },

    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }

        auditProfessionalBaseExam(this.form).then((response) => {
          this.$modal.msgSuccess("审核成功");
          this.$emit("refresh");
          this.cancel();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
