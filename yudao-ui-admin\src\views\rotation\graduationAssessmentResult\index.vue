<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          size="small"
          clearable
        >
          <el-option
            v-for="grade in gradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="轮转状态" prop="rotationStatus">
        <el-select
          v-model="queryParams.rotationStatus"
          placeholder="请选择轮转状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="轮转时间" prop="rotationDates">
        <el-date-picker
          v-model="queryParams.rotationDates"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="轮转科室" prop="rotationDepartmentName">
        <el-input
          v-model="queryParams.rotationDepartmentName"
          placeholder="请输入轮转科室"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="queryParams.isAudit"
          >仅展示待审核数据</el-checkbox
        >
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="人员类型" align="center" prop="personnelType">
        <template v-slot="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE"
            :value="scope.row.personnelType"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="学员姓名" align="center" prop="studentNickName" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column
        label="手机号码"
        align="center"
        prop="mobile"
        width="120px"
      />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template v-slot="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="培训专业" align="center" prop="majorName" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column
        label="轮转科室"
        align="center"
        prop="rotationDepartmentName"
      />
      <el-table-column
        label="轮转时间"
        align="center"
        prop="rotationBeginEndTime"
        width="200px"
      />
      <el-table-column label="轮转状态" align="center" prop="rotationStatus">
        <template v-slot="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_STATUS"
            :value="scope.row.rotationStatus"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="出科操作技能考核"
        align="center"
        class-name="small-padding fixed-width"
        width="130px"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.graduationAssessmentResultresultId"
            size="mini"
            type="text"
            icon="el-icon-eye"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAudit(scope.row)"
            >考核</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="出科技能考核表单选择"
      width="800px"
      :visible.sync="skillVisible"
    >
      <el-form inline>
        <el-form-item label="标准科室">
          <el-select
            v-model="skillQueryParams.standardDepartmentId"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="d in departmentList"
              :key="d.id"
              :label="d.name"
              :value="parseInt(d.id)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="技能名称">
          <el-input
            v-model="skillQueryParams.name"
            clearable
            placeholder="请输入技能名称搜索"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="querySkillList">表单检索</el-button>
          <i
            class="el-icon-info"
            title="此处的检索只针对表单列表"
            style="position: relative; top: -20px"
          ></i>
        </el-form-item>
      </el-form>
      <el-transfer
        class="graduation-assessment-result-transfer"
        v-model="selectSkillList"
        :data="skillList"
        :titles="['表单列表', '已选表单']"
      ></el-transfer>

      <span slot="footer">
        <el-button type="primary" @click="handleStartAudit">开始考核</el-button>
        <el-button @click="skillVisible = false">取消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="出科技能考核详情" :visible.sync="auditVisible">
      <el-tabs>
        <el-tab-pane
          v-for="(item, index) in auditForm.resultFormCreateReqVOS || []"
          :key="index"
          :label="item.graduationAssessmentFormName"
        >
          <el-form inline label-width="90px">
            <el-form-item
              label="学员姓名："
              style="width: 24%; white-space: nowrap"
              >{{ auditRow.studentNickName }}</el-form-item
            >
            <el-form-item label="年级：" style="width: 24%">{{
              auditRow.grade
            }}</el-form-item>
            <el-form-item label="学员类型：" style="width: 24%">
              <dict-tag
                :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                :value="auditRow.studentType"
              ></dict-tag>
            </el-form-item>
            <el-form-item label="培训专业：" style="width: 24%">{{
              auditRow.majorName
            }}</el-form-item>
            <el-form-item
              label="考官姓名："
              style="width: 48%"
              v-if="item.creatorNickname"
              >{{ item.creatorNickname }}</el-form-item
            >
            <el-form-item label="表单名称：" style="width: 100%">{{
              item.graduationAssessmentFormName
            }}</el-form-item>
            <el-form-item
              class="stem-form-item"
              label="题干："
              style="width: 100%"
              >{{ item.graduationAssessmentFormStem || "--" }}</el-form-item
            >
            <el-form-item label="表单总分：" style="margin-right: 20px">{{
              item.graduationAssessmentFormScore
            }}</el-form-item>
            <el-form-item label="项目总分：" style="margin-right: 20px">{{
              item.score
            }}</el-form-item>
            <el-form-item label="考核用时：">
              <el-input-number
                v-model="item.costTime"
                controls-position="right"
                style="width: 100px"
                :min="0"
                :disabled="isView"
              ></el-input-number
              >分钟
            </el-form-item>
          </el-form>

          <div
            v-for="(resultItem, i) in item.resultItems"
            :key="i"
            style="margin-bottom: 30px"
          >
            <header class="table-header">
              <span style="margin-right: 20px"
                >评分项目：{{
                  resultItem.graduationAssessmentFormItemName
                }}</span
              >
              <span style="margin-right: 20px"
                >总分：{{ resultItem.graduationAssessmentFormItemScore }}</span
              >
              <span>考核总分：{{ resultItem.score }}</span>
            </header>
            <el-table :data="resultItem.resultFormSubItems" border>
              <el-table-column
                label="评分要素"
                prop="graduationAssessmentFormSubItemName"
              ></el-table-column>
              <el-table-column
                label="分值"
                prop="graduationAssessmentFormSubItemScore"
                width="100px"
              ></el-table-column>
              <el-table-column label="考核分" width="200px">
                <template v-slot="scope">
                  <el-input-number
                    style="width: 170px"
                    v-model="scope.row.score"
                    controls-position="right"
                    :min="0"
                    :max="scope.row.graduationAssessmentFormSubItemScore"
                    :disabled="isView"
                    @change="handleSubItemScoreChange(resultItem, item)"
                  ></el-input-number>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <el-form inline label-width="90px">
            <el-form-item
              label="现场图片："
              style="width: 24%; white-space: nowrap"
            >
              <imageUpload v-model="item.pictures" :limit="9999" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" v-if="!isView">
        <el-button type="primary" @click="handleSaveSkillForm">确定</el-button>
        <el-button @click="auditVisible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getGraduationAssessmentResultPage,
  getGraduationSkillSimpleList,
  getGraduationSKillForm,
  saveGraduationSkillForm,
  getGraduationSkillData,
} from "@/api/rotation/graduationAssessmentResult";
import { getStudentGradeList } from "@/api/system/userStudent";
import { getStandardDepartmentSimpleList } from "@/api/system/standardDepartment";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "GraduationAssessmentResult",
  components: {
    ImageUpload,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出科技能考核列表
      list: [],
      // 年级列表
      gradeList: [],
      // 查询参数
      queryParams: {
        studentType: null,
        grade: "",
        rotationStatus: "",
        rotationDates: "",
        rotationDepartmentName: "",
        nickname: "",
        isAudit: true,
        pageNo: 1,
        pageSize: 10,
      },
      // 考核
      auditRow: null,
      skillVisible: false,
      departmentList: [],
      skillQueryParams: {
        standardDepartmentId: "",
        name: "",
      },
      skillList: [],
      selectSkillList: [],
      auditForm: {},
      auditVisible: false,
      isView: false,
    };
  },
  created() {
    getStandardDepartmentSimpleList().then(
      (res) => (this.departmentList = res.data)
    );
    getStudentGradeList().then((res) => (this.gradeList = res.data));
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getGraduationAssessmentResultPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 考核操作 */
    handleAudit(row) {
      this.auditRow = row;
      this.skillVisible = true;
      this.selectSkillList = [];
      this.querySkillList();
    },
    querySkillList() {
      getGraduationSkillSimpleList(this.skillQueryParams).then((res) => {
        this.skillList = res.data.map((item) => ({
          key: item.id,
          label: item.name,
        }));
      });
    },
    handleStartAudit() {
      if (this.selectSkillList.length === 0) {
        this.$message.warning("请选择至少一项考核技能");
        return;
      }

      const formNameArr = [];
      this.skillList.forEach((item) => {
        if (this.selectSkillList.indexOf(item.key) > -1) {
          formNameArr.push(item.label);
        }
      });
      const formNames = formNameArr.join("，");

      this.$modal
        .confirm(
          '是否只考核【"' + formNames + '"】，考核完毕后无法添加其他表单'
        )
        .then(() => {
          getGraduationSKillForm({
            graduationAssessmentFormIds: this.selectSkillList.join(","),
            scheduleDetailsId: this.auditRow.scheduleDetailsId,
          }).then((res) => {
            console.log("选择考核表单res", res);
            this.auditForm = res.data;
            this.skillVisible = false;
            this.auditVisible = true;
            this.isView = false;
          });
        })
        .catch(() => {});

      // getGraduationSKillForm({
      //   graduationAssessmentFormIds: this.selectSkillList.join(","),
      //   scheduleDetailsId: this.auditRow.scheduleDetailsId
      // }).then(res => {
      //   this.auditForm = res.data;
      //   this.skillVisible = false;
      //   this.auditVisible = true;
      //   this.isView = false;
      // });
    },
    handleSubItemScoreChange(resultTarget, target) {
      let resultItemScore = 0,
        totalScore = 0;
      resultTarget.resultFormSubItems.forEach((subItem) => {
        resultItemScore += subItem.score;
      });
      resultTarget.score = resultItemScore;
      target.resultItems.forEach((item) => {
        totalScore += item.score;
      });
      target.score = totalScore;
    },
    handleSaveSkillForm() {
      const zeroCostTimeItem = this.auditForm.resultFormCreateReqVOS.find(
        (item) => !item.costTime
      );
      if (zeroCostTimeItem) {
        this.$message.warning(
          `存在表单考核用时为空，需所有表单均完成后才可以提交`
        );
        // this.$message.warning(`${zeroCostTimeItem.graduationAssessmentFormName}的考核用时不能为0或者为空`);
        return;
      }

      const picturesItem = this.auditForm.resultFormCreateReqVOS.find(
        (item) => !item.pictures
      );
      if (picturesItem) {
        this.$message.warning(
          `存在表单现场照片为空，需所有表单均完成后才可以提交`
        );
        // this.$message.warning(`请上传${picturesItem.graduationAssessmentFormName}的现场图片`);
        return;
      }
      saveGraduationSkillForm(this.auditForm).then(() => {
        this.$message.success("出科技能考核提交成功");
        this.auditVisible = false;
        this.getList();
      });
    },
    /** 考核查看 */
    handleView(row) {
      this.isView = true;
      this.auditRow = row;
      getGraduationSkillData(row.graduationAssessmentResultresultId).then(
        (res) => {
          this.auditForm = res.data;
          this.auditVisible = true;
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.table-header {
  padding: 10px;
  border: 1px solid #dfe6ec;
  background: #f8f8f9;
  position: relative;
  top: 1px;
}

.stem-form-item ::v-deep .el-form-item__content {
  width: calc(100% - 90px);
}
</style>

<style>
.graduation-assessment-result-transfer .el-transfer-panel {
  width: 316px;
}

.graduation-assessment-result-transfer .el-transfer-panel__item.el-checkbox {
  height: auto;
  min-height: 30px;
}

.graduation-assessment-result-transfer
  .el-transfer-panel__item.el-checkbox
  .el-checkbox__label {
  overflow: initial;
  text-overflow: initial;
  white-space: initial;
  line-height: 1.2;
  display: inline-block;
}

.graduation-assessment-result-transfer .el-transfer__buttons {
  width: 116px;
}

.graduation-assessment-result-transfer .el-transfer__buttons button {
  margin-left: 0;
}
</style>
