<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="600px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="审核结果" prop="reportExamineResult">
            <el-radio-group
              v-model="form.reportExamineResult"
              @input="handleInput"
            >
              <el-radio
                v-for="dict in resultOptions"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="审核建议" prop="reportExamineRecommend">
            <el-input
              type="textarea"
              v-model="form.reportExamineRecommend"
              placeholder="请在此输入审核建议"
              :autosize="{ minRows: 2 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm"> 确 认 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { auditRpregistrationExam } from "@/api/recruitment/rpregistrationExamine";

export default {
  name: "AuditDialog",
  components: {},
  props: {
    title: {
      type: String,
    },
    openAudit: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      open: this.openAudit,

      // 表单参数
      form: {
        reportExamineResult: "1",
        reportExamineRecommend: "初审通过，等待笔试安排",
      },
      // 表单校验
      rules: {
        reportExamineResult: [
          {
            required: true,
            message: "请选择审核结果",
            trigger: "change",
          },
        ],
        reportExamineRecommend: [
          { required: true, message: "审核建议不能为空", trigger: "blur" },
        ],
      },
      resultOptions: [],
    };
  },
  watch: {
    openAudit(newVal) {
      this.resultOptions = (
        this.getDictDatas(
          this.DICT_TYPE.REGISTRATION_RESIDENTIAL_TRAINING_EXAMINE_RESULT
        ) || []
      ).filter(
        (item) => ["通过", "不通过", "退回修改"].indexOf(item.label) > -1
      );
      this.form = {
        recruitmentRegistrationId: this.formData.recruitmentRegistrationId,
        reportExamineResult: "1",
        reportExamineRecommend: "初审通过，等待笔试安排",
      };
      this.open = newVal;
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:openAudit", false);
    },

    handleInput(val) {
      console.log("handleInput===", val);
      if (val === "1") {
        this.form.reportExamineRecommend = "初审通过，等待笔试安排";
      }
      if (val === "0") {
        this.form.reportExamineRecommend = "初审不通过";
      }
    },

    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }

        auditRpregistrationExam(this.form).then((response) => {
          this.$modal.msgSuccess("审核成功");
          this.$emit("refresh");
          this.cancel();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
