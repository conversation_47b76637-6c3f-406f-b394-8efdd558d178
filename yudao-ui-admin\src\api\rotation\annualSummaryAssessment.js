import request from "@/utils/request";

// 获得年度总结配置完成情况分页
export function getAssessmentPage(query) {
  return request({
    url: "/rotation/annual-summary-content/page-assessment",
    method: "get",
    params: query,
  });
}

// 创建年度总结配置
export function updateAssessmentResult(data) {
  return request({
    url: "/rotation/annual-summary-content/update-assessment-result",
    method: "put",
    data: data,
  });
}

// // 更新年度总结配置
// export function updateAnnualSummaryConfig(data) {
//   return request({
//     url: "/rotation/annual-summary-config/update",
//     method: "put",
//     data: data,
//   });
// }

// // 删除年度总结配置
// export function deleteAnnualSummaryConfig(id) {
//   return request({
//     url: "/rotation/annual-summary-config/delete?id=" + id,
//     method: "delete",
//   });
// }

// // 获得年度总结配置
// export function getAnnualSummaryConfig(id) {
//   return request({
//     url: "/rotation/annual-summary-config/get?id=" + id,
//     method: "get",
//   });
// }

// // 获得年度总结配置分页
// export function getAnnualSummaryConfigPage(query) {
//   return request({
//     url: "/rotation/annual-summary-config/page",
//     method: "get",
//     params: query,
//   });
// }

// // 导出年度总结配置 Excel
// export function exportAnnualSummaryConfigExcel(query) {
//   return request({
//     url: "/rotation/annual-summary-config/export-excel",
//     method: "get",
//     params: query,
//     responseType: "blob",
//   });
// }

// // 获得年级列表
// export function getGradeList(query) {
//   return request({
//     url: "/rotation/annual-summary-config/list-grade",
//     method: "get",
//     params: query,
//   });
// }

// // 获得年度总结配置完成情况分页
// export function getCompletePage(query) {
//   return request({
//     url: "/rotation/annual-summary-config/page-complete",
//     method: "get",
//     params: query,
//   });
// }
