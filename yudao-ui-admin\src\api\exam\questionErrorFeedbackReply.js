import request from "@/utils/request";

// 回复考试错题反馈
export function createQuestionErrorFeedbackReply(data) {
  return request({
    url: "/exam/question-error-feedback-reply/reply",
    method: "post",
    data: data,
  });
}

// 获得考试试题
export function getQuestionAnswer(query) {
  return request({
    url: "/exam/question/get",
    method: "get",
    params: query,
  });
}

// // 更新考试错题反馈
// export function updateQuestionErrorFeedback(data) {
//   return request({
//     url: '/exam/question-error-feedback/update',
//     method: 'put',
//     data: data
//   })
// }

// // 删除考试错题反馈
// export function deleteQuestionErrorFeedback(id) {
//   return request({
//     url: '/exam/question-error-feedback/delete?id=' + id,
//     method: 'delete'
//   })
// }

// // 获得考试错题反馈
// export function getQuestionErrorFeedback(id) {
//   return request({
//     url: '/exam/question-error-feedback/get?id=' + id,
//     method: 'get'
//   })
// }

// 获得考试错题反馈记录分页
export function getQuestionErrorFeedbackReplyPage(query) {
  return request({
    url: "/exam/question-error-feedback-reply/page",
    method: "get",
    params: query,
  });
}

// 导出考试错题反馈 Excel
export function exportQuestionErrorFeedbackExcel(query) {
  return request({
    url: "/exam/question-error-feedback/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
