<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select v-model="queryParams.grade" placeholder="请选择年级" size="small" clearable>
          <el-option v-for="grade in gradeList" :key="grade" :label="grade" :value="grade"/>
        </el-select>
      </el-form-item>
      <el-form-item label="轮转状态" prop="rotationStatus">
        <el-select v-model="queryParams.rotationStatus" placeholder="请选择轮转状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_STATUS)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="轮转时间" prop="rotationDates">
        <el-date-picker v-model="queryParams.rotationDates" style="width: 240px" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item label="轮转科室" prop="rotationDepartmentName">
        <el-input v-model="queryParams.rotationDepartmentName" placeholder="请输入轮转科室" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="学员姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入学员姓名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="queryParams.isAudit">仅展示待审核数据</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="学员姓名" align="center" prop="nickname" />
      <el-table-column label="手机号码" align="center" prop="mobile" width="120px" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType"></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="培训专业" align="center" prop="majorName" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column label="轮转科室" align="center" prop="rotationDepartmentName" />
      <el-table-column label="轮转时间" align="center" prop="rotationBeginEndTime" width="200px" />
      <el-table-column label="出科申请状态" align="center" prop="graduationAuditStatus">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.GRADUATION_AUDIT_STATUS" :value="scope.row.graduationAuditStatus"></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="出科审核" align="center" class-name="small-padding fixed-width" width="130px">
        <template v-slot="scope">
          <el-button
            v-has-permi="['rotation:teacher-graduation-apply:audit']"
            v-if="scope.row.graduationAuditStatus === '1'"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAudit(scope.row)"
          >带教审核</el-button>
          <el-button
            v-if="+scope.row.graduationAuditStatus > 1"
            size="mini"
            type="text"
            icon="el-icon-eye"
            @click="handleView(scope.row)"
          >查看审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog :title="`${isView ? '查看' : ''}带教审核`" :visible.sync="visible">
      <h3 class="summary-title">出科总结</h3>
      <el-form label-width="90px">
        <el-form-item class="quarter-item" label="姓名：">{{ currentRow.nickname }}</el-form-item>
        <el-form-item class="quarter-item" label="年级：">{{ currentRow.grade }}</el-form-item>
        <el-form-item class="quarter-item" label="学员类型：">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="currentRow.studentType"></dict-tag>
        </el-form-item>
        <el-form-item class="quarter-item" label="培训专业：">{{ currentRow.majorName }}</el-form-item>
        <el-form-item class="full-item" label="个人小结：">
          <div class="border-wrapper">{{ applyInfo.personalSummary }}</div>
        </el-form-item>
        <el-form-item class="name-item" label="学员签名：">
          <span class="underline">{{ applyInfo.studentUserNickName }}</span>
        </el-form-item>
        <el-form-item class="date-item" label="日期：">
          <span class="underline">{{ applyInfo.createTime }}</span>
        </el-form-item>
        <el-form-item label-width="0px">
          <template v-if="visible">
            <general-score-sheet
              v-if="formType === 'general_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
              ref="scoreForm"
            ></general-score-sheet>
            <default-score-sheet
              v-if="formType === 'default_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
              ref="scoreForm"
            ></default-score-sheet>
            <second-score-sheet
              v-if="formType === 'second_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
              ref="scoreForm"
            ></second-score-sheet>
            <third-score-sheet
              v-if="formType === 'third_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
              ref="scoreForm"
            ></third-score-sheet>
            <fourth-score-sheet
              v-if="formType === 'fourth_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
              ref="scoreForm"
            ></fourth-score-sheet>
            <fifth-score-sheet
              v-if="formType === 'fifth_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
              ref="scoreForm"
            ></fifth-score-sheet>
          </template>
        </el-form-item>
        <el-form-item class="full-item" label="带教评价：">
          <div class="border-wrapper" v-if="isView">{{ applyInfo.teacherAppraise }}</div>
          <el-input type="textarea" v-model="teacherAppraise" v-else></el-input>
        </el-form-item>
        <el-form-item class="name-item" label="带教签名：" v-if="isView">
          <span class="underline">{{ applyInfo.teacherUserNickName }}</span>
        </el-form-item>
        <el-form-item class="date-item" label="日期：" v-if="isView">
          <span class="underline">{{ formatDate(applyInfo.teacherAuditTime) }}</span>
        </el-form-item>
      </el-form>
      <span slot="footer" v-if="!isView">
        <el-button @click="cancelVisible">取消</el-button>
        <el-popover
          style="margin: 0 10px"
          placement="top"
          width="320"
          v-model="repulseVisible"
        >
          <el-input
            style="margin-bottom: 10px"
            type="textarea"
            placeholder="请输入退回修改的原因！"
            v-model="teacherRepulseReason"
          ></el-input>
          <span >
            <el-button type="primary" size="mini" @click="sureRepulse">确定</el-button>
            <el-button size="mini" @click="cancelRepulse">取消</el-button>
          </span>
          <el-button slot="reference" type="primary">退回修改</el-button>
        </el-popover>
        <el-button type="primary" @click="agreeGraduation">同意出科</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getGraduationTeacherAuditList, teacherAuditSuccess, teacherAuditFail } from "@/api/rotation/graduationAudit";
import { getGraduationApply } from "@/api/rotation/graduationApply";
import { getStudentGradeList } from "@/api/system/userStudent";
import dayjs from "dayjs";
import GeneralScoreSheet from "./sheetGeneral";
import DefaultScoreSheet from "./sheetSzlgrm";
import SecondScoreSheet from "./sheetSecond";
import ThirdScoreSheet from "./sheetThird";
import FourthScoreSheet from "./sheetFourth";
import FifthScoreSheet from "./sheetFifth";

export default {
  name: "GraduationTeacherAudit",
  components: { GeneralScoreSheet, DefaultScoreSheet, SecondScoreSheet, ThirdScoreSheet, FourthScoreSheet, FifthScoreSheet },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出科审核列表
      list: [],
      // 年级列表
      gradeList: [],
      // 查询参数
      queryParams: {
        studentType: null,
        grade: "",
        rotationStatus: "",
        rotationDates: "",
        rotationDepartmentName: "",
        nickname: "",
        isAudit: true,
        pageNo: 1,
        pageSize: 10,
      },
      // 是否查看审核
      isView: false,
      // 弹窗
      visible: false,
      // 操作行
      currentRow: {},
      // 申请信息
      applyInfo: {},
      // 带教评价
      teacherAppraise: "",
      // 退回弹窗
      repulseVisible: false,
      // 退回原因
      teacherRepulseReason: "",
      // 当前表单类型
      formType: "default_reco_score",
    };
  },
  created() {
    getStudentGradeList().then(res => this.gradeList = res.data);
    this.getList();
  },
  methods: {
    /** 格式化日期 */
    formatDate(date) {
      return dayjs(date).format("YYYY-MM-DD");
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getGraduationTeacherAuditList(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看带教审核 */
    handleView(row) {
      this.currentRow = row;
      getGraduationApply(row.graduationApplyId).then(res => {
        this.applyInfo = res.data;
        this.formType = res.data.recoScoreForm?.rotationGraduationFormType || "default_reco_score";
        this.isView = true;
        this.visible = true;
      });
    },
    /** 带教审核 */
    handleAudit(row) {
      this.currentRow = row;
      getGraduationApply(row.graduationApplyId).then(res => {
        this.applyInfo = res.data;
        this.formType = res.data.recoScoreForm?.rotationGraduationFormType || "default_reco_score";
        this.isView = false;
        this.visible = true;
      });
    },
    /** 取消审核 */
    cancelVisible() {
      this.teacherAppraise = "";
      this.visible = false;
      this.cancelRepulse();
    },
    /** 同意出科 */
    agreeGraduation() {
      const valid = this.$refs.scoreForm.formValidate();
      if (!valid) return;
      if (!this.teacherAppraise) {
        this.$message.warning("请输入带教评价!");
        return;
      }
      teacherAuditSuccess({
        id: this.applyInfo.id,
        recoScoreForm: JSON.stringify(this.applyInfo.recoScoreForm),
        teacherAppraise: this.teacherAppraise,
      }).then(() => {
        this.$message.success("同意出科提交成功!");
        this.cancelVisible();
        this.getList();
      });
    },
    /** 退回修改 */
    cancelRepulse() {
      this.teacherRepulseReason = "";
      this.repulseVisible = false;
    },
    sureRepulse() {
      if (!this.teacherRepulseReason) {
        this.$message.warning("请输入退回修改的原因!");
        return;
      }
      teacherAuditFail({
        id: this.applyInfo.id,
        teacherRepulseReason: this.teacherRepulseReason
      }).then(() => {
        this.$message.success("退回修改提交成功!");
        this.cancelVisible();
        this.getList();
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.name-item {
  float: left;
  width: 72%;
}

.date-item {
  float: left;
  width: 28%;
}

.full-item {
  float: left;
  width: 100%;
}

.quarter-item {
  float: left;
  width: 25%;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-align: center;
}

.underline {
  text-decoration: underline;
  text-underline-offset: 5px;
  text-underline-color: #e1e1e1;
}

.border-wrapper {
  border: 1px solid #f1f1f1;
  padding: 3px 10px;
  white-space: pre-wrap;
}
</style>
