<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="姓名">
        <el-input v-model="queryParams.nickname" placeholder="输入姓名查询" clearable></el-input>
      </el-form-item>
      <el-form-item label="性别">
        <el-select v-model="queryParams.sex" placeholder="请选择" clearable>
          <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                     :key ="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否全科师资">
        <el-select v-model="queryParams.isGeneralTeacher" placeholder="请选择" clearable>
          <el-option v-for="dict in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
                     :key ="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否骨干师资">
        <el-select v-model="queryParams.isKeyTeacher" placeholder="请选择" clearable>
          <el-option v-for="dict in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
                     :key ="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="最高学历">
        <el-select v-model="queryParams.education" placeholder="请选择" clearable>
          <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
                     :key ="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="岗位">
        <el-select v-model="queryParams.applyPosition" placeholder="请选择" clearable>
          <el-option v-for="item in roleOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="聘任状态">
        <el-select v-model="queryParams.appointmentStatus" placeholder="请选择" clearable>
          <el-option v-for="dict in getDictDatas(DICT_TYPE.TEACHERS_APPOINTMENT_STATUS)"
                     :key ="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" :loading="exportLoading"
                   @click="handleExport" v-hasPermi="['teachers:archives:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" >
      <el-table-column prop="nickname" label="姓名" align="center" min-width="100" fixed>
        <template slot-scope="scope">
          <el-link type="primary" @click="handleNicknameClick(scope.row)">{{ scope.row.nickname }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="username" label="用户名" align="center" fixed></el-table-column>
      <el-table-column prop="sex" label="性别" align="center">
        <template slot-scope="scope">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_SEX, scope.row.sex) }}</template>
      </el-table-column>
      <el-table-column prop="birthday" label="出生日期" align="center" min-width="100"></el-table-column>
      <el-table-column prop="mobile" label="手机号码" align="center" min-width="120"></el-table-column>
      <el-table-column prop="email" label="用户邮箱" align="center" min-width="200"></el-table-column>
      <el-table-column prop="graduationDate" label="毕业时间" align="center" min-width="120"></el-table-column>
      <el-table-column prop="graduationSchool" label="毕业院校" align="center" min-width="150"></el-table-column>
      <el-table-column prop="education" label="最高学历" align="center" min-width="100">
        <template slot-scope="scope">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_EDUCATION, scope.row.education) }}</template>
      </el-table-column>
      <el-table-column prop="degree" label="最高学位" align="center">
        <template slot-scope="scope">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_DEGREE, scope.row.degree) }}</template>
      </el-table-column>
      <el-table-column prop="workStartDate" label="工作开始时间" align="center" min-width="120"></el-table-column>
      <el-table-column prop="experienceYears" label="从业年限" align="center"></el-table-column>
      <el-table-column prop="chiefPhysicianObtainDate" label="主治职称取得时间" align="center" width="140"></el-table-column>
      <el-table-column prop="chiefPhysicianYears" label="主治年限" align="center" width="80"></el-table-column>
      <el-table-column prop="isGeneralTeacher" label="是否全科师资" align="center" width="100">
        <template slot-scope="scope">{{ getDictDataLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, scope.row.isGeneralTeacher) }}</template>
      </el-table-column>
      <el-table-column prop="isKeyTeacher" label="是否骨干师资" align="center" width="100">
        <template slot-scope="scope">{{ getDictDataLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, scope.row.isKeyTeacher) }}</template>
      </el-table-column>
      <el-table-column prop="appointmentCount" label="师资聘任记录数" align="center" width="120">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleShowRecord(scope.row)">{{ scope.row.appointmentCount }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="appointmentStatusInfo" label="聘任状态" align="center" min-width="220"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="120">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleTableBtn(scope.row, 'rehire')" v-hasPermi="['teachers:archives:rehire']">续聘</el-button>
          <el-button size="mini" type="text" @click="handleTableBtn(scope.row, 'dismissal')" v-hasPermi="['teachers:archives:dismissal']">解聘</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog title="岗位列表" :visible.sync="open">
      <el-row class="mb10">
        <el-col :span="12">姓名：{{ handleRow.nickname }}</el-col>
        <el-col :span="12">用户名：{{ handleRow.username }}</el-col>
      </el-row>
      <el-table :data="positionList">
        <el-table-column label="聘任岗位" align="center" prop="applyPosition">
          <template slot-scope="scope">{{ getMatchedLabel(roleOptions, scope.row.applyPosition, "id", "name") }}</template>
        </el-table-column>
        <el-table-column label="学员类型" align="center" prop="studentTypeNames" min-width="100px"></el-table-column>
        <el-table-column label="科室" align="center" prop="teachDepartmentNames" min-width="100px"></el-table-column>
        <el-table-column label="聘任开始日期" align="center" prop="teachBeginDate"></el-table-column>
        <el-table-column label="聘任截止日期" align="center" prop="teachEndDate"></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="rehireClick(scope.row)" v-if="handleEvent === 'rehire'">续聘</el-button>
            <el-button type="primary" size="mini" @click="dismissalClick(scope.row)" v-if="handleEvent === 'dismissal'">解聘</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="default" @click="open = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog title="师资续聘" :visible.sycn="formOpen" width="500px">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item label="续聘岗位">{{ getMatchedLabel(roleOptions, form.applyPosition, "id", "name") }}</el-form-item>
        <el-form-item label="聘任开始时间" prop="teachBeginDate" required>
          <el-date-picker type="date" v-model="form.teachBeginDate"></el-date-picker>
        </el-form-item>
        <el-form-item label="聘任截止时间" prop="teachEndDate" required>
          <el-date-picker type="date" v-model="form.teachEndDate"></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="rehireConfirm">确认</el-button>
        <el-button type="default" @click="rehireCancel">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="recordDialogTitle" :visible.sync="recordOpen" width="800px">
      <el-form size="small" :inline="true">
        <el-form-item label="岗位">
          <el-select v-model="recordQueryParams.applyPosition" placeholder="请选择" clearable @change="queryRecordList">
            <el-option v-for="item in roleOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="聘任状态">
          <el-select v-model="recordQueryParams.appointmentStatuses" placeholder="请选择" clearable multiple @change="queryRecordList">
            <el-option v-for="dict in getDictDatas(DICT_TYPE.TEACHERS_APPOINTMENT_STATUS)"
                       :key ="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-table :data="recordList">
        <el-table-column label="序号" align="center" type="index"></el-table-column>
        <el-table-column label="聘任岗位" align="center" prop="applyPosition">
          <template slot-scope="scope">{{ getMatchedLabel(roleOptions, scope.row.applyPosition, "id", "name") }}</template>
        </el-table-column>
        <el-table-column label="学员类型" align="center" prop="studentTypeNames"></el-table-column>
        <el-table-column label="科室" align="center" prop="teachDepartmentNames"></el-table-column>
        <el-table-column label="聘任开始日期" align="center" prop="teachBeginDate"></el-table-column>
        <el-table-column label="聘任截止日期" align="center" prop="teachEndDate"></el-table-column>
        <el-table-column label="聘任时长" align="center" prop="appointmentYears"></el-table-column>
        <el-table-column label="解聘时间" align="center" prop="dismissalTime"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
          <el-button type="default" @click="recordOpen = false">关闭</el-button>
        </div>
    </el-dialog>

    <user-worker-dialog
      opt="view"
      dialogTitle="查看职工用户"
      :dialogOpen="archiveOpen"
      :userId="archiveUserId"
      @update:dialogOpen="(value) => (archiveOpen = value)"
    />
  </div>
</template>

<script>
import {
  getArchivesAppointmentPage,
  getArchivesPage,
  archivesRehire,
  exportArchivesAppointmentExcel,
  archivesDismissal
} from '@/api/teachers/archives'
import { listSimpleRoles } from "@/api/system/role";
import userWorkerDialog from "@/views/system/userWorker/useWorderDialog";

export default {
  name: 'archives',
  components: { userWorkerDialog },
  data() {
    const validBeginDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error('聘任开始时间不能为空'));
      } else {
        if (this.form.teachEndDate) {
          this.$refs.form.validateField('teachEndDate')
        }
        callback()
      }
    }

    const validEndDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error('聘任截止时间不能为空'));
      } else if (new Date(value).getTime() < new Date(this.form.teachBeginDate).getTime()) {
        callback(new Error('聘任截止时间不能小于开始日期'));
      } else {
        callback()
      }
    }

    return {
      // 岗位下拉选项
      roleOptions: [],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: '',
        sex: '',
        isGeneralTeacher: '',
        isKeyTeacher: '',
        education: '',
        applyPosition: '',
        appointmentStatus: ''
      },
      // 总条数
      total: 0,
      // 师资聘用申请确认列表
      list: [],
      // 操作行
      handleRow: {},
      // 操作事件
      handleEvent: "",
      // 岗位列表弹窗
      open: false,
      // 岗位列表
      positionList: [],
      // 续聘表单弹窗
      formOpen: false,
      // 续聘表单
      form: {
        id: null,
        applyPosition: "",
        teachBeginDate: "",
        teachEndDate: "",
      },
      // 表单规则
      rules: {
        teachBeginDate: [{ validator: validBeginDate, trigger: ["change", "blur"] }],
        teachEndDate: [{ validator: validEndDate, trigger: ["change", "blur"] }]
      },
      // 记录弹窗
      recordOpen: false,
      // 记录弹窗标题
      recordDialogTitle: "",
      // 记录列表
      recordList: [],
      // 记录查询参数
      recordQueryParams: {},
      // 查看档案弹窗
      archiveOpen: false,
      // 档案用户id
      archiveUserId: "",
    }
  },
  created() {
    this.getList();
    listSimpleRoles().then(response => {
      const excludeRoleCodes = ["super_admin", "admin", "student", "hospital_admin", "recruitment_user"];
      this.roleOptions = response.data.filter(item => !excludeRoleCodes.includes(item.code));
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getArchivesPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 表格按钮操作 */
    queryPositionList() {
      getArchivesAppointmentPage({
        pageNo: 1,
        pageSize: 999,
        applyUserId: this.handleRow.id,
        appointmentStatuses: ['appointment', 'expired'],
      }).then(response => {
        this.positionList = response.data.list;
      });
    },
    handleTableBtn(row, handleEvent) {
      this.handleRow = row;
      this.handleEvent = handleEvent; // "rehire" "dismissal"
      this.open = true;
      this.queryPositionList();
    },
    /* 续聘事件 */
    rehireClick(row) {
      this.formOpen = true;
      this.form = {
        id: row.id,
        applyPosition: row.applyPosition,
        teachBeginDate: row.teachBeginDate,
        teachEndDate: "",
      };
    },
    /* 续聘确认 */
    rehireConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          archivesRehire(this.form).then(response => {
            this.$message.success("续聘成功！");
            this.queryPositionList();
            this.rehireCancel();
          });
        }
      });
    },
    /* 续聘取消 */
    rehireCancel() {
      this.$refs.form.resetFields();
      this.formOpen = false;
    },
    /* 解聘事件 */
    dismissalClick(row) {
      const nickName = this.handleRow.nickname;
      const positionName = this.getMatchedLabel(this.roleOptions, row.applyPosition, "id", "name");
      this.$confirm(`解聘后将同步清除掉用户对应的系统角色，确认继续解聘${nickName}的${positionName}岗位吗？`, "解聘确认").then(() => {
        archivesDismissal(row.id).then(() => {
          this.$message.success("解聘成功！");
          this.queryPositionList();
        });
      });
    },
    /* 查看聘任记录 */
    handleShowRecord(row) {
      this.recordOpen = true;
      this.recordDialogTitle = `师资聘任记录-${row.nickname}（${row.username}）`
      this.recordQueryParams = {
        pageNo: 1,
        pageSize: 999,
        applyUserId: row.id,
        applyPosition: this.queryParams.applyPosition,
        appointmentStatuses: this.queryParams.appointmentStatuses ? [this.queryParams.appointmentStatuses] : [],
      };
      this.queryRecordList();
    },
    queryRecordList() {
      getArchivesAppointmentPage(this.recordQueryParams).then(response => {
        this.recordList = response.data.list;
      });
    },
    /* 导出 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      // 执行导出
      this.$modal.confirm('是否确认导出所有师资档案?').then(() => {
        this.exportLoading = true;
        return exportArchivesAppointmentExcel(params);
      }).then(response => {
        this.$download.excel(response, '师资档案.xlsx');
        this.exportLoading = false;
      }).catch(() => {});
    },
    /** 查看职工档案 */
    handleNicknameClick(row) {
      this.archiveUserId = row.id;
      this.archiveOpen = true;
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
