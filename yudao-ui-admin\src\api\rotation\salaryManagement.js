import request from "@/utils/request";

// 创建薪资管理
export function createSalaryManagement(data) {
  return request({
    url: "/rotation/salary-management/create",
    method: "post",
    data: data,
  });
}

// 更新薪资管理
export function updateSalaryManagement(data) {
  return request({
    url: "/rotation/salary-management/update",
    method: "put",
    data: data,
  });
}

// 删除薪资管理
export function deleteSalaryManagement(id) {
  return request({
    url: "/rotation/salary-management/delete?id=" + id,
    method: "delete",
  });
}

// 获得薪资管理
export function getSalaryManagement(id) {
  return request({
    url: "/rotation/salary-management/get?id=" + id,
    method: "get",
  });
}

// 获得薪资管理分页
export function getSalaryManagementPage(query) {
  return request({
    url: "/rotation/salary-management/page",
    method: "get",
    params: query,
  });
}

// 导出薪资管理 Excel
export function exportSalaryManagementExcel(query) {
  return request({
    url: "/rotation/salary-management/export-owner-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获得学员薪资查询分页
export function getStudentSalaryPage(query) {
  return request({
    url: "/rotation/salary-management/page-owner",
    method: "get",
    params: query,
  });
}

// 获得学员薪资查询列表表头
export function getStudentTableHeads(query) {
  return request({
    url: "/rotation/salary-management/page-owner-heads",
    method: "get",
    params: query,
  });
}

// 导入模版
export function exportSalaryTemplate() {
  return request({
    url: "/rotation/salary-conf/get-import-template",
    method: "get",
    responseType: "blob",
  });
}

// 获得薪资列表表头
export function getTableHeads(query) {
  return request({
    url: "/rotation/salary-management/page-heads",
    method: "get",
    params: query,
  });
}

// 获得学员薪资查询分页
export function getSalaryPage(query) {
  return request({
    url: "/rotation/salary-management/page",
    method: "get",
    params: query,
  });
}

// 导出薪资管理 Excel
export function exportSalaryExcel(query) {
  return request({
    url: "/rotation/salary-management/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
