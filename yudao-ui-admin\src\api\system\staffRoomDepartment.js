import request from '@/utils/request'

// 创建教研室科室关系,教研室配置
export function createStaffRoomDepartment(data) {
  return request({
    url: '/system/staff-room-department/create',
    method: 'post',
    data: data
  })
}

// 更新教研室科室关系,教研室配置
export function updateStaffRoomDepartment(data) {
  return request({
    url: '/system/staff-room-department/update',
    method: 'put',
    data: data
  })
}

// 删除教研室科室关系,教研室配置
export function deleteStaffRoomDepartment(id) {
  return request({
    url: '/system/staff-room-department/delete?id=' + id,
    method: 'delete'
  })
}

// 获得教研室科室关系,教研室配置
export function getStaffRoomDepartment(id) {
  return request({
    url: '/system/staff-room-department/get?id=' + id,
    method: 'get'
  })
}

// 获得教研室科室关系,教研室配置分页
export function getStaffRoomDepartmentPage(query) {
  return request({
    url: '/system/staff-room-department/page',
    method: 'get',
    params: query
  })
}

// 导出教研室科室关系,教研室配置 Excel
export function exportStaffRoomDepartmentExcel(query) {
  return request({
    url: '/system/staff-room-department/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
