<template>
  <el-dialog
    title="申报人员列表"
    :visible="open"
    width="800px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <el-table v-loading="loading" :data="list">
      <el-table-column label="姓名" align="center" prop="applyNickname" />
      <el-table-column label="用户名" align="center" prop="applyUsername" />
      <el-table-column label="申报时间" align="center" prop="applyTime" />
      <el-table-column label="申请状态" align="center" prop="applyResult">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHERS_EVALUATION_APPLY_RESULT"
            :value="scope.row.applyResult"
          />
        </template>
      </el-table-column>
      <el-table-column label="遴选结果" align="center" prop="selectionResult">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHERS_EVALUATION_SELECTION_RESULT"
            :value="scope.row.selectionResult"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script>
import { getEvaUserPage } from "@/api/teachers/evaluationPlan";

export default {
  name: "ApplyPersonDialog",
  components: {},
  props: {
    openApply: {
      type: Boolean,
      default: false,
    },
    curRow: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      open: this.openApply,
      loading: false,
      list: [],
      total: 0,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
      },
    };
  },
  watch: {
    openApply(newVal) {
      this.open = newVal;
      if (newVal) {
        this.getList();
      }
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:openApply", false);
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      this.queryParams.teachersEvaluationPlanId = this.curRow.id;
      getEvaUserPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
