<template>
  <div class="app-container student-appraise-teacher">
    <div v-if="list.length > 0">
      <div class="list-item" v-for="item in list" :key="item.appraiseSourceId">
        <div class="list-item-head">
          <div class="head-item"><label>{{ item.ruleName }}</label></div>
          <div class="head-item">
            <label>轮转科室：</label>
            <span>{{ item.rotationDepartmentName }}</span>
          </div>
          <div class="head-item">
            <label>轮转时间：</label>
            <span>{{ item.rotationBeginEndTime }}</span>
            <span>( {{ item.rotationTime }} <dict-tag :type="DICT_TYPE.ROTATION_CYCLE" :value="item.rotationCycle" /> )</span>
          </div>
          <div class="head-item">
            <span :class="`${item.appraise360ResultId ? 'fontBlur' : 'fontRed'}`">{{ item.appraise360ResultId ? '已评' : '未评' }}</span>
          </div>
        </div>
        <div class="list-item-cont">
          <div class="cont-item">
            <label>带教老师：</label>
            <span>{{ item.teacherNickname }}</span>
          </div>
          <div class="cont-item">
            <label>带教时间：</label>
            <span>{{ item.teacherStartEndDate }}</span>
          </div>
          <div class="cont-item">
            <label>评价得分：</label>
            <span>
              <el-rate
                v-model="item.score"
                disabled
                text-color="#ff9900"
                :max="5"
                score-template="{value}">
              </el-rate>
            </span>
          </div>
          <div class="cont-item">
            <el-button v-if="item.appraise360ResultId" size="mini" type="text" icon="el-icon-document" @click="handleView(item)">
              查看评价
            </el-button>
            <el-button v-else size="mini" type="text" icon="el-icon-edit" @click="handleEdit(item)">
              进入评价
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据"></el-empty>

    <AppraiseDialog
      v-if="formData"
      :title="title"
      :open="open"
      :data="formData"
      :appraiseSourceId="curRow && curRow.appraiseSourceId"
      :appraiseTargetId="curRow && curRow.appraiseTargetId"
      :disabled="appraiseDisabled"
      @setOpen="setOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import AppraiseDialog from "@/views/components/appraiseDialog";
import { getStudentappraiseteacherPage, getAppraiseResult, getAppraiseForm, createAppraise } from "@/api/rotation/studentappraiseteacher";

export default {
  name: "StudentAppraiseTeacher",
  components: {
    AppraiseDialog
  },
  data() {
    return {
      loading: true,
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 99
      },
      formData: null,
      open: false,
      title: '',
      curRow: null,
      appraiseDisabled: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      // 执行查询
      getStudentappraiseteacherPage(this.queryParams).then(response => {
        const list = response.data.list;
        list.forEach(item => {
          const _score = item.score / item.appraise360Score * 5
          item.score = _score
        })
        this.list = list;
      });
    },
    handleView(row) {
      const id = row.appraise360ResultId;
      this.appraiseDisabled = true
      getAppraiseResult(id).then(response => {
        this.formData = response.data;
        this.open = true;
        this.title = `查看评价-${row.teacherNickname}`;
      });
    },
    handleEdit(row) {
      const id = row.scheduleDetailsId;
      this.curRow = row;
      this.appraiseDisabled = false
      getAppraiseForm(id).then(response => {
        this.formData = response.data
        this.open = true;
        this.title = `正在评价-${row.teacherNickname}`;
      });
    },
    setOpen(flag) {
      this.open = flag;
      this.editForm = null
      this.curRow = null
    }
  }
};
</script>

<style lang="scss" scoped>
.student-appraise-teacher{
  font-size: 14px;

  .list-item{
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    margin-bottom: 25px;

    .list-item-head{
      height: 42px;
      display: flex;
      align-items: center;
      background: #f8f8f8;
      border-bottom: 1px #ddd solid;
      padding: 0 15px;


      .head-item{
        margin-right: 25px;

        .fontBlur{
          color: #1890ff;
        }
        .fontRed{
          color: #f56c6c;
        }
      }
    }

    .list-item-cont{
      height: 52px;
      padding: 0 45px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .cont-item{

      }
    }
  }
}
</style>
