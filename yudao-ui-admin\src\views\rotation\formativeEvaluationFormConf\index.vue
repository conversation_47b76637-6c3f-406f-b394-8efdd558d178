<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="14">
        <div class="col-head">
          <span>表单预览</span>
          <el-select v-model="formId" size="small">
            <el-option v-for="item in formList" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </div>

        <component v-if="formId" :is="`form-${formId}`" :form-data="{...formData}"></component>
    </el-col>
      <el-col :span="10">
        <div class="col-head">表单配置</div>
        <el-table class="mb10" v-loading="loading" :data="list">
          <el-table-column label="选择表单" align="center" prop="formId">
            <template slot-scope="scope">
              <el-select v-model="scope.row.formId" placeholder="请选择形成性评价表单类型" clearable size="small">
                <el-option v-for="item in formList" :key="item.id" :label="item.shortName" :value="item.id"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="选择学员类型" align="center" prop="studentType">
            <template slot-scope="scope">
              <el-select v-model="scope.row.studentType" placeholder="请选择学员类型" clearable size="small">
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                           :key="dict.value" :label="dict.label" :value="Number(dict.value)"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="选择科室类型" align="center" prop="departmentType">
            <template slot-scope="scope">
              <el-select v-model="scope.row.departmentType" placeholder="请选择科室类型" clearable size="small" multiple>
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_DEPARTMENT_TYPE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="60">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="mb20">
          <el-link type="primary" icon="el-icon-plus" @click="addRow">新增一行</el-link>
        </div>
        <el-button type="primary" @click="handleSave">保存配置</el-button>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import { getFormativeEvaluationFormList, createGraduationFormConfig, getFormativeEvaluationFormConfList } from '@/api/rotation/formativeEvaluationFormConf'

const requireComponents = require.context("@/views/rotation/common/formativeEvaluationForm", false, /\.vue$/);
const componentsObj = {};
requireComponents.keys().forEach(filePath => {
  const componentName = filePath.split("/")[1].replace(/\.vue$/, "");
  const componentConfig = requireComponents(filePath);
  componentsObj[componentName] = componentConfig.default || componentConfig;
});

export default {
  name: "FormativeEvaluationFormConf",
  components: { ...componentsObj },
  data() {
    return {
      formList: [],
      formId: "",
      loading: false,
      list: [],
      formData: {
        "date_y": "2024",
        "date_m":"03",
        "date_d":"31",
        "medical_name":"--",
        "student_name":"--",
        "student_signature":"--",
        "mentor_signature":"--"
      },
      formComponent: null,
    };
  },
  created() {
    getFormativeEvaluationFormList().then(res => {
      this.formList = res.data;
      this.formId = this.formList[0]?.id
      this.formComponent = this.formId ? this[`form-${this.formId}`] : null
    });
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      getFormativeEvaluationFormConfList().then(response => {
        this.list = response.data.map(row => ({
          ...row,
          departmentType: row.departmentType ? row.departmentType.split(",") : [],
        }));
        this.loading = false;
      });
    },
    addRow() {
      this.list.push({
        formId: "",
        studentType: "",
        departmentType: [],
      });
    },
    handleDelete(index) {
      this.list.splice(index, 1);
    },
    handleSave() {
      const hasUnfilled = this.list.some(row => !row.formId || !row.studentType || !row.departmentType.length);
      if (hasUnfilled) {
        this.$message.warning("存在未选择项，请全部选择后再提交～");
        return;
      }
      this.$confirm("保存配置生效后，后续学员将采用新的形成性表单进行评分，确认保存配置吗？", "提示").then(() => {
        const params = this.list.map(row => ({ ...row, departmentType: row.departmentType.join(",") }));
        createGraduationFormConfig(params).then(() => {
          this.$message.success("表单保存成功！");
        });
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.col-head {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;

  ::v-deep .el-select {
    margin-left: 20px;
  }
}
</style>
