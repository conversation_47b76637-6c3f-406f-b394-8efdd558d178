import request from '@/utils/request'

// 获得病例
export function getCaseUpload(id) {
  return request({
    url: '/rotation/schedule-details/case-upload/get?id=' + id,
    method: 'get'
  })
}

// 获得病例书写上传分页
export function getCaseUploadPage(query) {
  return request({
    url: '/rotation/schedule-details/case-upload/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/studentEnrollment/index'}
  })
}

// 病例上传
export function saveCaseUpload(data) {
  return request({
    url: '/rotation/schedule-details/case-upload/save',
    method: 'post',
    data: data
  })
}
