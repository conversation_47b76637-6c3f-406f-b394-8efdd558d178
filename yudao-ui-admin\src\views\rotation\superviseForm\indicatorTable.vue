<template>
    <el-table
        :data="list"
        class="indicator-list"
        border
        size="small"
        style="width: 100%"
    >
        <el-table-column>
            <template slot="header" slot-scope="scope">
                <div class="action-cell">
                    <span>评分项目：</span>
                    <el-input
                        v-model="formItem.name"
                        size="mini"
                        placeholder="请输入项目名称"
                        @change="(val) => rowProjectNameChange(val)"
                        style="width: 250px;"
                        :disabled="opt === 'view'"
                    />
                    <span style="padding-left: 20px;">总分：</span>
                    <span>{{ projectScore }}</span>
                </div>
            </template>
            <el-table-column label="评分要素" align="center" prop="name">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.name"
                        size="mini"
                        placeholder="请输入评分要素"
                        :disabled="opt === 'view'"
                        @change="(val) => rowCellChange(val, scope.$index, 'name')"
                    />
                </template>
            </el-table-column>
            <el-table-column label="分值" align="center" prop="score" width="160">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.score"
                        size="mini"
                        placeholder="请输入分值"
                        :disabled="opt === 'view'"
                        @change="(val) => rowCellChange(val, scope.$index, 'score')"
                    />
                </template>
            </el-table-column>
            <el-table-column v-if="type == 2" label="是否核心指标" align="center" prop="score" width="160">
                <template slot-scope="scope">
                    <el-select 
                        v-model="scope.row.isCore" 
                        placeholder="请选择" 
                        size="small" 
                        :disabled="opt === 'view'"
                        @change="(val) => rowCellChange(val, scope.$index, 'isCore')"
                    >
                        <el-option label="是" :value="true" />
                        <el-option label="否" :value="false" />
                    </el-select>
                </template>
            </el-table-column>
        </el-table-column>
        <el-table-column v-if="opt === 'edit'">
            <template slot="header" slot-scope="scope">
                <div class="action-cell" style="justify-content: center;">
                    <i class="el-icon-circle-plus-outline" title="添加评分指标" @click="addRowHanler()"></i>
                    <i class="el-icon-delete" title="删除评分项目" @click="delProject()"></i>
                </div>
            </template>
            <el-table-column label="操作" align="center" width="120">
                <template slot-scope="scope">
                    <i class="el-icon-delete" style="cursor: pointer;" title="删除评分指标" @click="delRowHanler(scope.$index)"></i>
                </template>
            </el-table-column>
        </el-table-column>
    </el-table>
</template>

<script>

export default {
    props: {
        formItem: {
            type: Object
        },
        NO: {
            type: Number
        },
        opt: {
            type: String
        },
        type: {
            type: String
        }
    },
    data() {
      return {
        postOptions: this.getDictDatas(this.DICT_TYPE.SYSTEM_USER_POST),
        positionalOptions: this.getDictDatas(this.DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)
      };
    },
    computed: {
      list() {
        const newList = this.formItem.formSubItems
        newList.forEach(item => {
            item.isCore = item.isCore ? item.isCore : false
        })
        return newList
        // return this.formItem.formSubItems
      },
      projectScore() {
        return this.formItem.score
      }
    },
    created() {},
    methods: {
        addRowHanler() {
            const list = this.list || []
            list.push({
                name: '',
                score: 0
            })
            this.$emit("change", list, this.NO, 'score')
        },
        delRowHanler(index) {
            let list = this.list
            list.splice(index, 1)
            this.$emit("change", list, this.NO, 'score')
        },
        rowCellChange(val, index, field) {
            let list = this.list
            list[index][field] = val
            this.$emit("change", list, this.NO, field)
        },
        rowProjectNameChange(val) {
            this.$emit("projectNamechange", val, this.NO)
        },
        delProject() {
            this.$emit("delProject", this.NO)
        }

    }
  };
</script>
<style scoped lang="scss">
  .indicator-list{
    margin-bottom: 15px;
    .action-cell{
        cursor: pointer;
        display: flex;
        align-items: center;

        i{
            font-size: 18px;
            font-weight: bold;
            padding: 0 5px;
        }
    }
  }

</style>
