import request from '@/utils/request'

// 获得学员评价导师分页
export function getStudentMentorPage(query) {
  return request({
    url: '/rotation/appraise-process-result/mentorPage',
    method: 'get',
    params: query
  })
}

// 获得过程评价结果项集合
export function getAppraiseForm(id) {
  return request({
    url: '/rotation/appraise-process-result/itemList?appraiseProcessResultId=' + id,
    method: 'get'
  })
}

// 保存过程评价结果
export function updateAppraiseResult(data) {
    return request({
      url: '/rotation/appraise-process-result/update',
      method: 'put',
      data: data
    })
}

// 获得导师评价学员分页
export function getMentorStudentPage(query) {
    return request({
      url: '/rotation/appraise-process-result/studentPage',
      method: 'get',
      params: query
    })
}
