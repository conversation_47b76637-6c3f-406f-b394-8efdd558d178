<template>
  <div>
    <h3 class="form-name">SOAP病例汇报评估表</h3>

    <el-form class="form-info" size="mini" inline>
      <el-form-item class="quarter-item" label="时间:">
        {{ formData.date_m }}月{{ formData.date_d }}日
      </el-form-item>
      <el-form-item class="majority-item" label="地点:">
        <el-radio-group v-model="formData.address" :disabled="check || feedback">
          <el-radio label="病房"></el-radio>
          <el-radio label="门诊"></el-radio>
          <el-radio label="急诊"></el-radio>
          <el-radio label="ICU"></el-radio>
          <el-radio label="其他"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="quarter-item" label="指导医师:">{{ formData.medical_name }}</el-form-item>
      <el-form-item class="majority-item" label-width="45px">
        <el-radio-group v-model="formData.medical_title" :disabled="check || feedback">
          <el-radio label="主任医师"></el-radio>
          <el-radio label="副主任医师"></el-radio>
          <el-radio label="主治医师"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="quarter-item" label="学生姓名:">{{ formData.student_name }}</el-form-item>
      <el-form-item class="majority-item" label-width="45px">
        <el-radio-group v-model="formData.student_year" :disabled="check || feedback">
          <el-radio label="第一年"></el-radio>
          <el-radio label="第二年"></el-radio>
          <el-radio label="第三年"></el-radio>
          <el-radio label="其他"></el-radio>
        </el-radio-group>
        <el-input
          style="width: 80px"
          v-if="formData.student_year === '其他'"
          v-model="formData.student_year_other"
          :disabled="check || feedback"
        ></el-input>
      </el-form-item>
      <el-form-item class="quarter-item" label="患者:">
        <el-input style="width: 100px" v-model="formData.patient_name" :disabled="check || feedback"></el-input>
      </el-form-item>
      <el-form-item class="quarter-item" label="年龄:">
        <el-input-number
          style="width: 90px;"
          :min="0"
          controls-position="right"
          v-model="formData.patient_age"
          :disabled="check || feedback"
        ></el-input-number>
      </el-form-item>
      <el-form-item class="quarter-item" label="性别:">
        <el-radio-group v-model="formData.patient_gender" :disabled="check || feedback">
          <el-radio label="男"></el-radio>
          <el-radio label="女"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="quarter-item">
        <el-radio-group v-model="formData.patient_diagnose" :disabled="check || feedback">
          <el-radio label="初诊"></el-radio>
          <el-radio label="复诊"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="half-item" label="诊断:">
        <el-input style="width: 278px;" v-model="formData.diagnose" :disabled="check || feedback"></el-input>
      </el-form-item>
      <el-form-item class="half-item" label="病情复杂程度:">
        <el-radio-group v-model="formData.disease_complexity" :disabled="check || feedback">
          <el-radio label="低"></el-radio>
          <el-radio label="中"></el-radio>
          <el-radio label="高"></el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <h3 class="part-title">第一部分：SOAP分项内容的完整性</h3>
    <table class="evaluate-table">
      <tr>
        <th>评估指标</th>
        <th>不适用评价</th>
        <th>内容完全遗漏</th>
        <th>内容部分遗漏</th>
        <th>内容完整</th>
      </tr>
      <tr v-for="(item, index) in partA" :key="index">
        <template v-if="item.prop">
          <td>{{ item.name }}</td>
          <td v-for="i in 4" :key="i">
            <el-radio v-model="formData[item.prop]" :label="i" :disabled="check || feedback"></el-radio>
          </td>
        </template>
        <template v-else>
          <td colspan="5" class="table-inner-title">{{ item.name }}</td>
        </template>
      </tr>
    </table>

    <h3 class="part-title">第二部分：总体评估</h3>
    <table class="evaluate-table">
      <tr>
        <th rowspan="2">评估指标</th>
        <th colspan="2">未达要求</th>
        <th colspan="1">符合要求</th>
        <th colspan="2">表现优秀</th>
      </tr>
      <tr>
        <th style="width: 15%">1</th>
        <th style="width: 15%">2</th>
        <th style="width: 15%">3</th>
        <th style="width: 15%">4</th>
        <th style="width: 15%">5</th>
      </tr>
      <tr v-for="item in partB" :key="item.prop">
        <td>{{ item.name }}</td>
        <td v-for="i in 5" :key="i">
          <el-radio v-model="formData[item.prop]" :label="i" :disabled="check || feedback"></el-radio>
        </td>
      </tr>
    </table>

    <el-form class="form-info" inline size="mini">
      <el-form-item class="half-item" label="直接观察时间:">
        <el-input-number v-model="formData.observation_time" :min="0" :disabled="check || feedback" controls-position="right"></el-input-number>
        分钟
      </el-form-item>
      <el-form-item class="half-item" label="反馈时间:">
        <el-input-number v-model="formData.feedback_time" :min="0" :disabled="check || feedback" controls-position="right"></el-input-number>
        分钟
      </el-form-item>
      <el-form-item class="full-item" label="指导医师对评估的满意程度:">
        低
        <el-radio-group v-model="formData.mentor_satisfaction" :disabled="check || feedback">
          <el-radio v-for="i in 9" :key="i" :value="i" :label="i"></el-radio>
        </el-radio-group>
        高
      </el-form-item>
      <el-form-item class="full-item" label="考评学员对评估的满意程度:">
        低
        <el-radio-group v-model="formData.student_satisfaction" :disabled="check || evaluate">
          <el-radio v-for="i in 9" :key="i" :value="i" :label="i"></el-radio>
        </el-radio-group>
        高
      </el-form-item>
      <el-form-item class="comment-item" label="指导医师的评语:">
        <el-input type="textarea" :rows="3" v-model="formData.comment"  :disabled="check || feedback"></el-input>
      </el-form-item>
    </el-form>

    <div class="signature-bar">
      <div>学员签字:{{ formData.student_signature }}</div>
      <div>指导医师签字:{{ formData.mentor_signature }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'formFirst',
  props: {
    formData: Object,
    evaluate: Boolean, // 指导医师评价
    feedback: Boolean, // 学员反馈
    check: Boolean, // 仅查看
  },
  data() {
    return {
      partA: [
        { name: "主观资料" },
        { name: "1. 基本情况（年龄、性别等）", prop: "basic_info" },
        { name: "2. 主要症状描述", prop: "main_symptom_description" },
        { name: "3. 主要伴随阳性/阴性症状", prop: "associated_symptoms" },
        { name: "4. 相关处理及反应", prop: "related_treatment_response" },
        { name: "5. 其他相关情况说明", prop: "other_related_info" },
        { name: "客观资料" },
        { name: "1. 生命体征", prop: "vital_signs" },
        { name: "2. 心肺基本查体", prop: "cardiopulmonary_exam" },
        { name: "3. 重要阳性和阴性体征", prop: "important_signs" },
        { name: "4. 重要辅助检查", prop: "auxiliary_exams" },
        { name: "5. 其他相关情况说明", prop: "other_exam_info" },
        { name: "评价" },
        { name: "1. 简单总结", prop: "summary" },
        { name: "2. 列举问题", prop: "issues_listed" },
        { name: "3. 分析问题的原因/依据", prop: "cause_analysis" },
        { name: "计划" },
        { name: "1. 安排辅助检查", prop: "arrange_auxiliary_exams" },
        { name: "2. 安排治疗和健康指导", prop: "arrange_treatment_advice" },
        { name: "3. 随访时间与项目", prop: "follow_up_schedule" },
      ],
      partB: [
        { name: "1.资料收集", prop: "overall_data_collection" },
        { name: "2.列举问题", prop: "overall_issues_listed" },
        { name: "3.诊疗计划", prop: "overall_diagnosis_plan" },
        { name: "4.组织效能", prop: "overall_org_effectiveness" },
        { name: "5.沟通表达", prop: "overall_communication_skills" },
        { name: "6.职业素养", prop: "overall_professional_qualities" },
        { name: "7.整体表现", prop: "overall_performance" },
      ],
    }
  },
  methods: {
    validData() {
      if (!this.formData.address ||
        !this.formData.medical_title ||
        !this.formData.student_year ||
        (this.formData.student_year === '其他' && !this.formData.student_year_other) ||
        !this.formData.patient_name ||
        !this.formData.patient_age ||
        !this.formData.patient_gender ||
        !this.formData.patient_diagnose ||
        !this.formData.diagnose ||
        !this.formData.disease_complexity ||
        this.formData.observation_time === null ||
        this.formData.feedback_time === null ||
        !this.formData.mentor_satisfaction ||
        (this.feedback && !this.formData.student_satisfaction) ||
        !this.formData.comment ||
        this.partA.some(item => item.prop && !this.formData[item.prop]) ||
        this.partB.some(item => item.prop && !this.formData[item.prop])
      ) {
        this.$message.warning(this.feedback ? "请选择考评学员对评估的满意程度" : "请填写完所有考评项目～");
        return false;
      }
      return true;
    },
  }
}
</script>

<style scoped lang="scss">
.form-name {
  font-size: 16px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 16px;
}

.form-info {
  margin-bottom: 10px;

  ::v-deep .el-form-item {
    margin: 0 0 10px 0;
  }

  ::v-deep .el-radio-group {
    margin-left: 10px;
  }

  ::v-deep .el-radio {
    margin-right: 16px;
  }

  .full-item {
    width: 100%;
  }

  .half-item {
    width: 50%;
  }

  .quarter-item {
    width: 25%;
  }

  .majority-item {
    width: 75%;
  }

  .comment-item {
    width: 100%;
    ::v-deep .el-form-item__content {
      display: block;
    }
  }
}

.part-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 10px;
}

.evaluate-table {
  width: 100%;
  border: 1px solid #dfe6ec;
  border-collapse: collapse;
  margin-bottom: 10px;

  td, th {
    border: 1px solid #dfe6ec;
    padding: 8px 0;
    text-align: center;
  }

  ::v-deep .el-radio__label {
    display: none;
  }

  ::v-deep .el-radio__inner {
    vertical-align: middle;
  }
}

td.table-inner-title {
  text-align: left;
  padding-left: 16px;
  font-size: 13px;
}

.signature-bar {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  margin-bottom: 20px;
  padding-top: 40px;
}
</style>
