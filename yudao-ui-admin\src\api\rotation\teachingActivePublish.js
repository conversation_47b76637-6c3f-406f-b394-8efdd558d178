import request from '@/utils/request'

//保存发布教学活动
export function updatePublishTeachingActive(data) {
  return request({
    url: '/rotation/teacher-teaching-active-plan/update-publish',
    method: 'put',
    data: data
  })
}

// 保存教学活动
export function updateTeachingActive(data) {
  return request({
    url: '/rotation/teacher-teaching-active-plan/update',
    method: 'put',
    data: data
  })
}


// 更新教学活动照片
export function updateTeachingActivePictures(data) {
  return request({
    url: "/rotation/teacher-teaching-active-plan/update-pictures",
    method: "put",
    data: data,
  });
}

// 更新教学活动课件
export function updateTeachingActiveCoursewares(data) {
  return request({
    url: "/rotation/teacher-teaching-active-plan/update-coursewares",
    method: "put",
    data: data,
  });
}

// 更新教学活动附件
export function updateTeachingActiveFiles(data) {
  return request({
    url: "/rotation/teacher-teaching-active-plan/update-files",
    method: "put",
    data: data,
  });
}

// 发布教学活动
export function publishTeachingActive(id) {
  return request({
    url: `/rotation/teacher-teaching-active-plan/publish?id=${id}`,
    method: 'put'
  })
}

// 撤销教学活动
export function revokeTeachingActive(id) {
  return request({
    url: `/rotation/teacher-teaching-active-plan/revoke?id=${id}`,
    method: 'put'
  })
}

// 删除教学活动
export function deleteTeachingActive(id) {
  return request({
    url: '/rotation/teacher-teaching-active-plan/delete?id=' + id,
    method: 'delete'
  })
}

// 获得教学活动
export function getTeachingActive(id) {
  return request({
    url: '/rotation/teacher-teaching-active-plan/get?id=' + id,
    method: 'get'
  })
}

// 获得教学活动分页
export function getTeachingActivePage(query) {
  return request({
    url: '/rotation/teacher-teaching-active-plan/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/teachingActiveDevelopment/index'}
  })
}

// 导出教学活动 Excel
export function exportTeachingActiveExcel(query) {
  return request({
    url: '/rotation/teacher-teaching-active-plan/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得参加学员列表
export function getStudentsList(query) {
  return request({
    url: '/rotation/teaching-active-student-plan/list-students',
    method: 'get',
    params: query
  })
}

// 确认参加
export function confirmJoin(data) {
  return request({
    url: '/rotation/teaching-active-student-plan/confirm-join',
    method: 'put',
    data: data
  })
}

// 撤销参加
export function revokeJoin(data) {
  return request({
    url: '/rotation/teaching-active-student-plan/revoke-join',
    method: 'put',
    data: data
  })
}

// 添加教学活动用户
export function addStudentUsers(data) {
  return request({
    url: `/rotation/teaching-active-student-plan/addUsers`,
    method: 'put',
    data: data
  })
}

// 删除教学活动用户
export function deleteStudentUsers(data) {
  return request({
    url: `/rotation/teaching-active-student-plan/deleteUser?teachingActiveId=${data.teachingActiveId}&userId=${data.userId}`,
    method: 'delete'
  })
}

// 发布教学活动自评
export function updateSelfAssessment(data) {
  return request({
    url: '/rotation/teacher-teaching-active-plan/update-self-assessment',
    method: 'put',
    data
  })
}
