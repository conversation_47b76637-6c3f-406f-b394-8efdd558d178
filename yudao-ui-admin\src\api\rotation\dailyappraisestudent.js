import request from '@/utils/request'

// 获得日常考核分页
export function getDailyAppraiseStudent(query) {
    return request({
      url: '/rotation/dailyappraisestudent/page',
      method: 'get',
      params: query,
      headers: {'component': 'rotation/dailyappraisestudent/index'}
    })
}

// 获得科日常考核表单
export function getAppraiseForm(id) {
    return request({
        url: '/rotation/dailyappraisestudent/get-form?scheduleDetailsId=' + id,
        method: 'get'
    })
}
