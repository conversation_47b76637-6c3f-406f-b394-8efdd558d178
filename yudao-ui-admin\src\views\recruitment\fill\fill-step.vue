<template>
  <div class="fill-step-default">
    <el-steps class="fill-step" :active="active" finish-status="success" simple>
      <el-step title="申请信息填写" ></el-step>
      <el-step title="提交申请表" ></el-step>
      <el-step title="审核结果" ></el-step>
    </el-steps>

    <div class="plan-name">名称：{{ $route.query.planName }}</div>

    <div class="step-first" v-if="active === 1">
      <recruitment-form
        ref="recruitmentForm"
        :plan-id="$route.query.planId"
        :recruitment-registration-id="$route.query.recruitmentRegistrationId"
      ></recruitment-form>

      <div class="bottom-bar">
        <el-checkbox v-model="realPromise">本人承诺以上信息真实可靠，如有不实之处,愿意承担相应责任。</el-checkbox>

        <div class="bottom-buttons">
          <el-button type="primary" @click="handleStaging">暂存</el-button>
          <el-button type="primary" :disabled="!realPromise" @click="handleNext">下一步</el-button>
          <el-button type="default" @click="handleClose">关闭</el-button>
        </div>
      </div>
    </div>

    <div class="step-second" v-if="active === 2">
      <el-alert type="warning" show-icon title="注意：本人保证所填写信息和提供材料均真实有效，无任何虚假情况。如不真实本人愿意永久放弃申请资格，并承担由此带来的法律后果!"></el-alert>

      <div class="download-upload-sheet">
        下载申请表：
        <el-button style="margin-right: 50px" size="mini" type="primary" @click="downloadApplicationForm">下载</el-button>
        上传申请表：
        <file-upload
          style="display: inline-block; vertical-align: top;"
          v-model="applicationFormAddress"
          :file-size="500"
          :limit="1"
          :file-type="['pdf']"
        ></file-upload>
      </div>

      <div class="bottom-bar">
        <el-button @click="active = 1">上一步</el-button>
        <el-button type="primary" @click="handleToLast">提交报名申请</el-button>
      </div>
    </div>

    <div class="step-last" v-if="active === 3">
      <el-steps direction="vertical" :active="stepActive">
        <el-step :title="`申请材料提交：${reportInfo.reported ? '已提交' : '未提交'}`">
          <template slot="description">
            <div class="audit-content">
              <ul>
                <li>
                  <span>报名申请：{{ reportInfo.reported ? "已提交" : "未提交" }}</span>
                  <span>{{ reportInfo.commitTime }}</span>
                </li>
              </ul>
            </div>
          </template>
        </el-step>
        <el-step :title="`报名审核：${ getDictDataLabel(DICT_TYPE.RECRUITMENT_RESULT, reportExameInfo.result) }`">
          <template slot="description">
            <div class="audit-content">
              <ul>
                <li v-for="(item, index) in reportExameInfo.subItemRespVOList || []" :key="index">
                  <span>{{ item.name }}：{{ item.reason || getDictDataLabel(DICT_TYPE.RECRUITMENT_RESULT, item.result) }}</span>
                  <span>{{ item.processTime }}</span>
                </li>
                <li v-if="reportExameInfo.result === 4">
                  <el-button type="primary" size="mini" @click="reApplication">重新提交报名申请</el-button>
                </li>
              </ul>
            </div>
          </template>
        </el-step>
        <el-step :title="`录取审核：${ enrollExameInfo.isEnable ? getDictDataLabel(DICT_TYPE.RECRUITMENT_RESULT, enrollExameInfo.result) : '未进行' }`">
          <template slot="description">
            <div class="audit-content">
              <ul>
                <li v-for="(item, index) in enrollExameInfo.subItemRespVOList || []" :key="index">
                  <span>{{ item.name }}：{{ item.reason || getDictDataLabel(DICT_TYPE.RECRUITMENT_RESULT, item.result) }}</span>
                  <span>{{ item.processTime }}</span>
                </li>
                <li v-if="enrollExameInfo.noticeSendStatus === 'issued'">
                  <span>您已通过录取审核，请携带录取通知书前来我院报到。</span>
                  <el-button type="primary" size="mini" @click="checkNoticeSend(enrollExameInfo.noticeContent)">立刻查看录取通知书</el-button>
                </li>
                <li v-if="enrollExameInfo.result === 4">
                  <el-button type="primary" size="mini" @click="reApplication">重新提交报名申请</el-button>
                </li>
              </ul>
            </div>
          </template>
        </el-step>
        <el-step :title="`学员报到：${ checkInfo.isEnable ? getDictDataLabel(DICT_TYPE.REGISTRATION_CHECK_STATUS, checkInfo.checkStatus) : '未进行' }`" finish-status="success">
          <template slot="description">
            <div class="audit-content">
              <ul>
                <li>
                  <span>报到确认：{{ getDictDataLabel(DICT_TYPE.REGISTRATION_CHECK_STATUS, checkInfo.checkStatus) }}</span>
                  <span v-if="checkInfo.processTime">{{ checkInfo.processTime }}</span>
                </li>
              </ul>
            </div>
          </template>
        </el-step>
        <el-step class="end-step" title="结束"></el-step>
      </el-steps>
    </div>

    <el-dialog title="" :visible.sync="noticeOpen" width="1000px" v-dialogDrag append-to-body>
      <div ref="noticeContent" id="noticeContent-box" v-html="noticeContent" style="padding: 0 50px;"></div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleDown">下载</el-button>
        <el-button @click="noticeOpen = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  stagingApplicationInfo,
  nextStepApplicationInfo,
  getRegistration,
  saveApplicationForm,
  getExamineInfo,
} from '@/api/recruitment/registration'
import { getTemplateByType, previewDocTemplateUrl } from '@/api/system/template'
import FileUpload from "@/components/FileUpload";
import RecruitmentForm from "@/views/recruitment/fill/recruitment-form";
import RecruitmentFormResidency from '@/views/recruitment/fill/recruitment-form-residency'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export default {
  name: "Fill",
  components: { FileUpload, RecruitmentForm, RecruitmentFormResidency },
  data() {
    return {
      recruitmentRegistrationId: this.$route.query.recruitmentRegistrationId,
      active: 0,
      // 是否承诺真实性
      realPromise: false,
      // 下载申请表地址
      applicationTemplateUrl: "",
      // 申请表上传地址
      applicationFormAddress: "",
      // 审核结果
      planName: "",
      reportInfo: {},
      reportExameInfo: {},
      enrollExameInfo: {},
      checkInfo: {},
      stepActive: 0,
      // 录取通知书
      noticeOpen: false,
      noticeContent: "",
    }
  },
  methods: {
    handleStaging() {
      this.$refs.recruitmentForm.validForm().then(data => {
        stagingApplicationInfo(data).then((res) => {
          this.$message.success("暂存成功！");
          this.$router.push({
            path: this.$route.path,
            query: { ...this.$route.query, recruitmentRegistrationId: res.data },
          });
        });
      });
    },
    handleNext() {
      this.$refs.recruitmentForm.validForm().then(data => {
        nextStepApplicationInfo(data).then((res) => {
          this.$message.success("保存成功！");
          this.recruitmentRegistrationId = res.data;
          this.active = 2;
        });
      });
    },
    handleClose() {
      this.$tab.closePage();
    },
    downloadApplicationForm() {
      getTemplateByType({ templateType: 'registration_application_form', studentType: '4' }).then(res => {
        const url = previewDocTemplateUrl(res.data.id, this.recruitmentRegistrationId, "申请表");
        window.open(url);
      });
      // downloadApplicationTemplate().then(res => {
      //   this.$download.download0(res, "申请表.docx");
      // });
    },
    handleToLast() {
      if (!this.applicationFormAddress) {
        this.$message.warning("请上传申请表!");
        return;
      }

      this.$confirm("提交申请后不可修改，确认提交吗？", "提示").then(() => {
        saveApplicationForm({
          id: this.recruitmentRegistrationId,
          applicationFormAddress: this.applicationFormAddress
        }).then(() => {
          this.active = 3;
          this.queryExamineInfo();
        });
      });
    },
    queryExamineInfo() {
      getExamineInfo(this.recruitmentRegistrationId).then(res => {
        this.reportInfo = res.data.reportInfo || {};
        this.reportExameInfo = res.data.reportExameInfo || {};
        this.enrollExameInfo = res.data.enrollExameInfo || {};
        this.checkInfo = res.data.checkInfo || {};
        if (res.data.isEnd) {
          this.stepActive = 5;
        } else if (res.data.checkInfo.isEnable) {
          this.stepActive = 3;
        } else if (res.data.enrollExameInfo.isEnable) {
          this.stepActive = 2;
        } else if (res.data.reportExameInfo.isEnable) {
          this.stepActive = 1;
        } else {
          this.stepActive = 0;
        }
      });
    },
    reApplication() {
      this.active = 1;
    },
    checkNoticeSend(html) {
      this.noticeOpen = true;
      this.noticeContent = html;
      this.$nextTick(() => {
        const editableElements = this.$refs.noticeContent.querySelectorAll("[contenteditable='true']");
        editableElements.forEach(element => {
          element.contentEditable = "false";
        });
      });
    },
    handleDown(){
      this.exportPDF('noticeContent-box', '进修人员报道通知书')
    },
    exportPDF(tableId, fileName){
      const table = document.getElementById(tableId);
      html2canvas(table).then(canvas => {
        const contentWidth = canvas .width;
        const contentHeight = canvas.height;
        const pageHeight = contentWidth / 592.28 * 841.89;
        let leftHeight = contentHeight;
        let position = 0;
        const imgWidth = 595.28;
        const imgHeight = 592.28 / contentWidth * contentHeight;
        const pageData = canvas .toDataURL('image/jpeg', 1.0);
        const pdf = new jsPDF( '', 'pt','a4');
        if (leftHeight < pageHeight) {
          pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;

            if (leftHeight > 0) {
              pdf.addPage();
            }
          }
        }
        pdf.save(`${fileName}.pdf`);
      })
    },
    queryActive() {
      const recruitmentRegistrationId = this.recruitmentRegistrationId;
      if (!recruitmentRegistrationId) {
        this.active = 1;
        return;
      }
      getRegistration(recruitmentRegistrationId).then(res => {
        this.applicationFormAddress = res.data.applicationFormAddress;
        switch (res.data.step) {
          case "application_form":
            this.active = 2;
            break;
          case "finding_of_audit":
            this.active = 3;
            this.queryExamineInfo();
            break;
          default:
            this.active = 1;
        }
      });
    },
  },
  created() {
    this.queryActive();
  },
  activated() {
    const queryId = this.$route.query.recruitmentRegistrationId;
    if (queryId !== this.recruitmentRegistrationId) {
      this.recruitmentRegistrationId = queryId;
      this.queryActive();
    }
  }
}
</script>

<style lang="scss" scoped>
.fill-step {
  margin-bottom: 20px;
}

.plan-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

.bottom-bar {
  padding: 30px 0 20px 0;
  text-align: center;

  .bottom-buttons {
    display: flex;
    padding-top: 10px;
    justify-content: center;
  }
}

.full-item {
  width: 100%;
}

.download-upload-sheet {
  padding: 20px 0;
  font-size: 14px;
  i {
    margin-right: 50px;
    font-size: 18px;
    color: #2b85e4;
    cursor: pointer;
  }
}

.step-last  {
  padding-top: 20px;

  ::v-deep .el-step__description {
    position: relative;
    left: 300px;
    top: -26px;
    width: 600px;
    min-height: 80px;
    border: 1px solid;
    font-size: 14px;
    padding-right: 20px;
    color: #333;
  }

  ::v-deep .el-step__description.is-wait {
    display: none;
  }

  .audit-content ul {
    margin-left: 20px;
    padding-left: 0;
  }

  .audit-content li {
    list-style-type: none;
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    margin-left: 0;
    padding-left: 0;
  }
}

.end-step {
  ::v-deep .el-step__description {
    display: none;
  }
}
</style>
