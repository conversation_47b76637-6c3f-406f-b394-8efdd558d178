<template>
  <div class="app-container talkRecord-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="交流方式" prop="communicationWay">
        <el-select
          v-model="queryParams.communicationWay"
          placeholder="请选择交流方式"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.MENTOR_COMMUNICATION_WAY
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="交流日期" prop="communicationDate">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.communicationDate"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          placeholder="请选择审核状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:mentor-interview-notes:create']"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <div class="tips-box">
      您需要在本院规培期间，登记并完成审核与导师的谈话记录共<span
        class="total"
        >{{ totalAndCompletedInfo.total }}</span
      >篇，当前已完成<span class="finish">{{
        totalAndCompletedInfo.completed
      }}</span>
      篇，请继续努力！
    </div>
    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="编号" align="center" prop="id" /> -->
      <el-table-column
        label="交流日期"
        align="center"
        prop="communicationDate"
      />
      <el-table-column label="交流方式" align="center" prop="communicationWay">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.MENTOR_COMMUNICATION_WAY"
            :value="scope.row.communicationWay"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="交流地点"
        align="center"
        prop="communicationAddress"
      />
      <el-table-column label="审核状态" align="center" prop="auditStatus">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_AUDIT_STATUS"
            :value="scope.row.auditStatus"
          />
        </template>
      </el-table-column>
      <el-table-column label="填写时间" align="center" prop="createTime" />
      <el-table-column label="审核时间" align="auditTime" prop="name" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <!-- // 审核不通过 2 -->
          <el-button
            v-if="scope.row.auditStatus == 2"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row, 'edit')"
            v-hasPermi="['rotation:mentor-interview-notes:update']"
            >更正</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handleUpdate(scope.row, 'view')"
            v-hasPermi="['rotation:mentor-interview-notes:query']"
            >查看详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="680px"
      v-dialogDrag
      append-to-body
      custom-class="talkRecord-dialog"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="180px"
        :disabled="opt === 'view'"
      >
        <el-form-item label="交流方式" prop="communicationWay">
          <el-select
            v-model="form.communicationWay"
            placeholder="请选择交流方式"
            style="width: 100%"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.MENTOR_COMMUNICATION_WAY
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="交流日期" prop="communicationDate">
          <el-date-picker
            clearable
            v-model="form.communicationDate"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择开展时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="交流地点" prop="communicationAddress">
          <el-input
            type="textarea"
            v-model="form.communicationAddress"
            placeholder="请输入交流地点"
          />
        </el-form-item>

        <el-form-item label="主要交流内容" prop="communicationContent">
          <el-input
            type="textarea"
            v-model="form.communicationContent"
            :minlength="mixContentLength"
            maxlength="1000"
            show-word-limit
            autosize
            placeholder="（填写提纲：医师当月轮转情况(管床数、培训细则要求完成情况等)、医师近期考试情况、医师登记手册或系统填写情况、科研与教学能力培养、学术活动等参加情况、医师思想、心理、生活等方面情况）"
          />
        </el-form-item>

        <el-form-item
          label="导师评语"
          prop="mentorComment"
          v-if="opt === 'view'"
        >
          <el-input
            type="textarea"
            v-model="form.mentorComment"
            :minlength="mixContentLength"
            maxlength="500"
            show-word-limit
            :autosize="{ minRows: 2 }"
            placeholder="导师评语"
            disabled
          />
        </el-form-item>

        <el-form-item label="图片上传" prop="photo">
          <imageUpload
            v-model="form.photo"
            :limit="9999"
            activeTypeName=""
            :disabled="opt === 'view'"
          />
        </el-form-item>

        <el-form-item label="附件上传" prop="file">
          <FileUpload
            v-model="form.file"
            :limit="999"
            :fileSize="50"
            :disabled="opt === 'view'"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('save')">提交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";
import {
  getMentorNotesPage,
  getTotalAndCompleted,
  createMentorNotes,
  updateMentorNotes,
  getMentorNote,
  exportMentorNotes,
} from "@/api/rotation/talkRecord";
import { getConfigKey } from "@/api/infra/config";

export default {
  name: "TalkRecord",
  components: {
    FileUpload,
    ImageUpload,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级培训列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        auditStatus: null,
        communicationWay: null,
        communicationDate: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communicationAddress: [
          { required: true, message: "交流地点不能为空", trigger: "blur" },
        ],
        communicationWay: [
          { required: true, message: "交流方式不能为空", trigger: "change" },
        ],
        communicationDate: [
          { required: true, message: "交流日期不能为空", trigger: "change" },
        ],
        communicationContent: [
          { required: true, message: "主要交流内容不能为空", trigger: "blur" },
          {
            min: 200,
            max: 1000,
            message: "长度在 200 到 1000 个字符",
            trigger: "blur",
          },
        ],
        mentorComment: [
          { required: true, message: "导师评语不能为空", trigger: "blur" },
          {
            min: 50,
            max: 500,
            message: "长度在 50 到 500 个字符",
            trigger: "blur",
          },
        ],
        photo: [{ required: true, message: "请上传图片", trigger: "change" }],
      },
      curActive: {},
      opt: "",
      totalAndCompletedInfo: {},
      mixContentLength: 200,
    };
  },
  created() {
    getConfigKey("communication.content.min.size").then((res) => {
      if (res.code === 0) {
        this.mixContentLength = res.data ? Number(res.data) : 200;
        const rule = [
          { required: true, message: "主要交流内容不能为空", trigger: "blur" },
          {
            min: this.mixContentLength,
            max: 1000,
            message: `长度在 ${this.mixContentLength} 到 1000 个字符`,
            trigger: "blur",
          },
        ];
        this.$set(this.rules, "communicationContent", rule);
      }
    });
    this.getList();

    getTotalAndCompleted().then((res) => {
      this.totalAndCompletedInfo = res.data;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getMentorNotesPage(this.queryParams).then((response) => {
        const list = response.data.list;
        this.list = list;
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        communicationWay: undefined,
        communicationDate: undefined,
        communicationAddress: undefined,
        communicationContent: undefined,
        mentorComment: undefined,
        file: undefined,
        photo: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.opt = "add";
      this.open = true;
      this.title = "新增谈话记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row, optType) {
      this.reset();
      const id = row.id;
      this.curActive = row;
      this.opt = optType;
      getMentorNote(id).then((response) => {
        const { data } = response;
        this.form = data;
        this.form.file = JSON.parse(this.form.file);
        this.open = true;
        this.title = opt === "edti" ? "更正谈话记录" : "查看谈话记录";
      });
    },
    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = {
          communicationWay: this.form.communicationWay,
          communicationDate: this.form.communicationDate,
          communicationAddress: this.form.communicationAddress,
          communicationContent: this.form.communicationContent,
          mentorComment: this.form.mentorComment,
          file: JSON.stringify(this.form.file),
          photo: this.form.photo,
        };
        // 修改的提交
        if (this.form.id != null) {
          params.id = this.form.id;
          updateMentorNotes(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createMentorNotes(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportMentorNotes(params);
        })
        .then((response) => {
          this.$download.excel(response, "谈话记录.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss">
.talkRecord-container {
  .tips-box {
    .total {
      color: rgb(250, 82, 82);
      padding: 0 10px;
      font-weight: bold;
    }

    .finish {
      color: rgb(25, 116, 219);
      padding: 0 10px;
      font-weight: bold;
    }
  }
}

.talkRecord-dialog {
  .el-dialog__body {
    padding-right: 100px;

    .el-textarea .el-input__count {
      position: relative;
      display: block;
      text-align: right;
      height: 14px;
      background: none;
    }
  }
}
</style>
