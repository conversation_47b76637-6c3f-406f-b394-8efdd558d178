<template>
  <div class="app-container student-appraise-mentor">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="70px">
      <el-form-item label="评价状态" prop="appraised">
        <el-select v-model="queryParams.appraised" placeholder="请选择评价状态" filterable clearable size="small" @change="getList">
          <el-option label="全部" value=""></el-option>
          <el-option label="待评价" value="false"></el-option>
          <el-option label="已评价" value="true"></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <div v-if="list.length > 0">
      <div class="list-item" v-for="item in list" :key="item.appraiseSourceId">
        <div class="list-item-cont">
          <div class="cont-item">
            <label>评价对象：</label>
            <span>{{ item.mentorUserName }}</span>
          </div>
          <div class="cont-item">
            <label>评价年月：</label>
            <span>{{ item.appraiseBeginDate }}</span>
          </div>
          <div class="cont-item">
            <label>评价得分：</label>
            <span>
              <el-rate
                v-model="item.score"
                disabled
                text-color="#ff9900"
                :max="5"
                score-template="{value}">
              </el-rate>
            </span>
          </div>
          <div class="cont-item">
            <el-button v-if="item.appraised" size="mini" type="text" icon="el-icon-document" @click="handleView(item)">
              查看评价
            </el-button>
            <el-button v-else size="mini" type="text" icon="el-icon-edit" @click="handleEdit(item)">
              进入评价
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据"></el-empty>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <AppraiseDialog 
      v-if="formData"
      :title="title"
      :open="open"
      :data="formData"
      :appraiseSourceId="curRow && curRow.id"
      :disabled="appraiseDisabled"
      @setOpen="setOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import AppraiseDialog from "./appraiseDialog";
import { getStudentMentorPage, getAppraiseForm } from "@/api/rotation/appraiseMentor";

export default {
  name: "AppraiseMentor",
  components: {
    AppraiseDialog
  },
  data() {
    return {
      loading: true,
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        appraised: '',
      },
      formData: null,
      open: false,
      title: '',
      curRow: null,
      appraiseDisabled: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      // 执行查询
      getStudentMentorPage(this.queryParams).then(response => {
        const list = response.data.list;
        this.list = list;
        this.total = response.data.total
      });
    },
    handleView(row) {
      const id = row.id;
      this.appraiseDisabled = true
      getAppraiseForm(id).then(response => {
        this.formData = response.data;
        this.open = true;
        this.title = `查看评价-${row.mentorUserName}`;
      });
    },
    handleEdit(row) {
      const id = row.id;
      this.curRow = row;
      this.appraiseDisabled = false
      getAppraiseForm(id).then(response => {
        this.formData = response.data
        this.open = true;
        this.title = `正在评价-${row.mentorUserName}`;
      });
    },
    setOpen(flag) {
      this.open = flag;
      this.editForm = null
      this.curRow = null
    }
    
  }
};
</script>

<style lang="scss" scoped>
.student-appraise-mentor{
  font-size: 14px;

  .list-item{
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    margin-bottom: 25px;
    
    .list-item-head{
      height: 42px;
      display: flex;
      align-items: center;
      background: #f8f8f8;
      border-bottom: 1px #ddd solid;
      padding: 0 15px;
      

      .head-item{
        margin-right: 25px;

        .fontBlur{
          color: #1890ff;
        }
        .fontRed{
          color: #f56c6c;
        }
      }
    }

    .list-item-cont{
      height: 52px;
      padding: 0 45px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .cont-item{
        
      }
    }
  }
}
</style>
