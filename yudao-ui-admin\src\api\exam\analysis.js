import request from '@/utils/request'

// 获得基本信息
export function getPaperBaseInfo(id) {
  return request({
    url: '/exam/paper/analysis/get-base-info',
    method: 'get',
    params: { id }
  })
}

// 根据试卷id获得考卷难度统计
export function getDifficultyStatistics(id) {
  return request({
    url: '/exam/paper/analysis/get-difficulty-statistics',
    method: 'get',
    params: { id }
  })
}

// 根据试卷id获得参考用户统计
export function getPaperUserStatistics(id) {
  return request({
    url: '/exam/paper/analysis/get-paper-user-statistics',
    method: 'get',
    params: { id }
  })
}

// 根据试卷id获得考卷知识点统计
export function getPointStatistics(id) {
  return request({
    url: '/exam/paper/analysis/get-point-statistics',
    method: 'get',
    params: { id }
  })
}

// 根据试卷id获得考卷试题类型统计
export function getQuestionTypeStatistics(id) {
  return request({
    url: '/exam/paper/analysis/get-question-type-statistics',
    method: 'get',
    params: { id }
  })
}

// 根据试卷id获得考卷成绩统计
export function getResultStatistics(id) {
  return request({
    url: '/exam/paper/analysis/get-result-statistics',
    method: 'get',
    params: { id }
  })
}

// 根据试卷id获得考卷得分区间统计
export function getScoreDistributionStatistics(id) {
  return request({
    url: '/exam/paper/analysis/get-score-distribution-statistics',
    method: 'get',
    params: { id }
  })
}
