import request from '@/utils/request'

// 获得带教出科审核分页
export function getGraduationTeacherAuditList(query) {
  return request({
    url: '/rotation/teacher-graduation-apply/page',
    method: 'get',
    params: query
  })
}

// 带教审核通过
export function teacherAuditSuccess(data) {
  return request({
    url: '/rotation/teacher-graduation-apply/teacher-audit-success',
    method: 'put',
    data
  })
}

// 带教审核不通过
export function teacherAuditFail(data) {
  return request({
    url: '/rotation/teacher-graduation-apply/teacher-audit-fail',
    method: 'put',
    data
  })
}

// 获得科室出科审核分页
export function getGraduationDeptAuditList(query) {
  return request({
    url: '/rotation/dept-graduation-apply/page',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/graduationDeptAudit/index'}
  })
}

// 科室审核通过
export function deptAuditSuccess(data) {
  return request({
    url: '/rotation/dept-graduation-apply/dept-audit-success',
    method: 'put',
    data
  })
}

// 科室审核不通过
export function deptAuditFail(data) {
  return request({
    url: '/rotation/dept-graduation-apply/dept-audit-fail',
    method: 'put',
    data
  })
}
