<template>
  <el-dialog :title="superviseObject.developObjectName + '督导记录'" :visible="visible" width="900px" @close="handleCancel">
    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="督导项目名称" align="center" prop="hospitalSuperviseName" ></el-table-column>
      <el-table-column label="督导得分" align="center" prop="score" ></el-table-column>
      <el-table-column label="综合督导意见" align="center" prop="comprehensiveOpinion" ></el-table-column>
      <el-table-column label="整改要求" align="center" prop="rectificationRequire" ></el-table-column>
      <el-table-column label="督导反馈" align="center" prop="rectificationFeedback" ></el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
  </el-dialog>
</template>

<script>
import { getSuperviseHistoryPage } from "@/api/rotation/hospitalSupervise";

export default {
  name: "SuperviseObjectDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    superviseObject: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 院级督导列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10
      },
    };
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      const params = { 
        ...this.queryParams,
        id: this.superviseObject.id,
        formType: this.superviseObject.formType,
        developObject: this.superviseObject.developObject
      };
      getSuperviseHistoryPage(params).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    handleCancel() {
      this.$emit("update:visible", false);
    },
    
  },
  watch: {
    visible(val) {
      if (!val) return;

      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>

