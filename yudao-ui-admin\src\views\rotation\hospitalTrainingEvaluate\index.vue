<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="培训名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入培训名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评价状态" prop="evaluationStatus">
        <el-select
          v-model="queryParams.evaluationStatus"
          placeholder="请选择评价状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.ROTATION_EVALUATION_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训类型" prop="trainingType">
        <el-select
          v-model="queryParams.trainingType"
          placeholder="请选择培训类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE)"
            v-if="dict.value == 3 || dict.value == 5"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="培训对象" prop="trainingType">
        <el-select v-model="queryParams.trainingType" placeholder="请选择培训类型" clearable size="small">
          <el-option v-if="dict.value != 0" v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="培训人" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入培训人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开展时间" prop="developDates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.developDates"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="" prop="isHadEvaluation">
        <el-checkbox v-model="queryParams.isHadEvaluation"
          >完成评价</el-checkbox
        >
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:hospital-training:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:hospital-training:export']">导出</el-button>
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="编号" align="center" prop="id" /> -->
      <el-table-column
        label="培训名称"
        align="center"
        prop="name"
        fixed
        width="180"
      />
      <el-table-column
        label="培训级别"
        align="center"
        prop="trainingLevel"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_TRAINING_LEVEL"
            :value="scope.row.trainingLevel"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="培训类型"
        align="center"
        prop="trainingType"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_TRAINING_TYPE"
            :value="scope.row.trainingType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="开展时间"
        align="center"
        prop="startTime"
        width="280"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }} ~ {{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="培训人"
        align="center"
        prop="nickname"
        width="100"
      />
      <el-table-column label="培训地点" align="center" prop="trainingAddress" />
      <el-table-column label="培训课件" align="center" prop="attendance">
        <template slot-scope="scope">
          <div style="text-align: left">
            <el-link
              v-for="(file, index) in scope.row.coursewaresArr"
              :key="index"
              type="primary"
              :underline="false"
              :href="file.url"
              >{{ file.name || "--" }}
            </el-link>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="200"
      >
        <template slot-scope="scope">
          <el-button
            v-if="!scope.row.evaluateId"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEvaluate(scope.row)"
            v-hasPermi="['rotation:hospital-training-evaluate:create']"
            >开始评课</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['rotation:hospital-training-evaluate-details:query']"
            >我的评课</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleRecords(scope.row)"
            v-hasPermi="['rotation:hospital-training-evaluate-log:query']"
            >评课记录查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="听课评课记录"
      width="1100px"
      :visible.sync="evaluateVisible"
      destroy-on-close
    >
      <div id="pdf-main-cont" class="pdf-main-cont" style="padding: 80px">
        <h2 style="text-align: center; margin: 0 0 30px 0" class="export-item">
          深圳市龙岗区人民医院听课记录表
        </h2>

        <el-form inline label-width="90px" class="export-item">
          <el-form-item
            label="培训名称："
            style="width: 60%; white-space: nowrap; margin-bottom: 0"
            >{{ curActive.name }}</el-form-item
          >
          <el-form-item label="培训人：" style="width: 30%; margin-bottom: 0">{{
            curActive.nickname
          }}</el-form-item>
          <el-form-item label="开展时间：" style="width: 100%; margin-bottom: 0"
            >{{ curActive.startTime }} ~ {{ curActive.endTime }}</el-form-item
          >
          <el-form-item
            label="培训地点："
            style="width: 100%; margin-bottom: 0"
            >{{ curActive.trainingAddress }}</el-form-item
          >
        </el-form>

        <p class="export-item">
          请您根据评价标准和听课情况进行评价，并在得分栏打分，谢谢您的合作！
        </p>

        <div style="margin-bottom: 10px" class="export-item">
          <el-table :data="evaluateData" border :span-method="objectSpanMethod">
            <el-table-column label="评价项目" prop="projectName" width="100px">
              <template v-slot="scope">
                <div>
                  {{
                    scope.row.projectName ||
                    scope.row.sumText ||
                    scope.row.evlTitle
                  }}
                </div>
                <div v-if="scope.row.projectName">
                  {{ scope.row.totalScore }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="序号" prop="id" width="60px">
              <template v-slot="scope">
                <div v-if="scope.row.projectName">{{ scope.row.id }}</div>
                <div v-if="scope.row.sumText">
                  {{ scope.row.evaluateTotal }}
                </div>
                <div v-if="scope.row.evlTitle">
                  <el-input
                    v-if="!isView"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入总体评价、意见及建议"
                    :disabled="isView"
                    :maxlength="200"
                    show-word-limit
                    v-model="scope.row.evaluateComments"
                  >
                  </el-input>
                  <div v-else>{{ scope.row.evaluateComments }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="评价标准" prop="item"></el-table-column>
            <el-table-column
              label="分值"
              prop="itemScore"
              width="60px"
            ></el-table-column>
            <el-table-column label="得分" width="150px">
              <template v-slot="scope">
                <el-input-number
                  style="width: 120px"
                  v-model="scope.row.evaluateItem"
                  controls-position="right"
                  :min="0"
                  :max="scope.row.itemScore"
                  :disabled="isView"
                  @change="handleSubItemScoreChange"
                ></el-input-number>
              </template>
            </el-table-column>
          </el-table>
          <div style="text-align: right; margin: 10px 0 20px 0">
            听课专家签名：{{ curUserName }}
          </div>
        </div>

        <el-form inline label-width="140px" class="export-item">
          <el-form-item
            label="现场图片："
            style="width: 24%; white-space: nowrap"
          >
            <imageUpload
              v-model="livePhotos"
              :limit="9999"
              activeTypeName=""
              :disabled="isView"
            />
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" v-if="!isView">
        <el-button type="primary" @click="handleSave">确定</el-button>
        <el-button @click="evaluateVisible = false">取消</el-button>
      </span>
      <span slot="footer" v-if="isView">
        <el-button type="primary" :loading="exportloading" @click="handleDown"
          >下载表单</el-button
        >
      </span>
    </el-dialog>

    <el-dialog
      title="听课评课列表"
      width="600px"
      :visible.sync="recordsVisible"
    >
      <el-form inline label-width="90px">
        <el-form-item
          label="培训名称："
          style="width: 60%; white-space: nowrap; margin-bottom: 0"
          >{{ curActive.name }}</el-form-item
        >
        <el-form-item label="培训人：" style="width: 30%; margin-bottom: 0">{{
          curActive.nickname
        }}</el-form-item>
      </el-form>

      <el-table :data="evaRecordslist">
        <el-table-column
          label="评价人"
          align="center"
          prop="evaluateUserName"
          width="100"
        />
        <el-table-column
          label="评价得分"
          align="center"
          prop="evaluateTotal"
        ></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="100"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleRecordView(scope.row)"
              >查看评价</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer">
        <el-button @click="recordsVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import store from "@/store";
import { parseTime } from "@/utils/ruoyi";
import { getAccessToken } from "@/utils/auth";
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";
import AppraiseScoreDialog from "@/views/components/appraiseScoreDialog";
import SuperviseRecordDialog from "../hospitalTrainingSupervise/supervise-record-dialog";
import {
  getHospitalTrainingPage,
  createHospitalTrainingEvaluate,
  getHospitalTrainingEvaluate,
  getHospitalTrainingLogPage,
} from "@/api/rotation/hospitalTrainingEvaluate";
import { exportPDF } from "@/utils/exportUtils";

const evaluateListData = [
  {
    id: 1,
    projectName: "教学态度",
    totalScore: 10,
    itemScore: 5,
    evaluateItem: 5,
    item: "仪表端庄、举止得体，精神饱满，富有激情。",
  },
  {
    id: 2,
    projectName: "教学态度",
    totalScore: 10,
    itemScore: 5,
    evaluateItem: 5,
    item: "教案撰写规范;严格进行课堂管理，遵守课息时间。",
  },

  {
    id: 3,
    projectName: "教学内容",
    totalScore: 40,
    itemScore: 6,
    evaluateItem: 6,
    item: "教学目标明确，符合教学大纲要求。",
  },
  {
    id: 4,
    projectName: "教学内容",
    totalScore: 40,
    itemScore: 6,
    evaluateItem: 6,
    item: "内容严谨充实，无科学性、政治性错误，无不当言论。",
  },
  {
    id: 5,
    projectName: "教学内容",
    totalScore: 40,
    itemScore: 12,
    evaluateItem: 12,
    item: "课堂时间分配合理；层次清晰，逻辑性强，重点突出难占进透。",
  },
  {
    id: 6,
    projectName: "教学内容",
    totalScore: 40,
    itemScore: 8,
    evaluateItem: 8,
    item: "理论联系实际，范例合理，适度介绍社会和学科发展。",
  },
  {
    id: 7,
    projectName: "教学内容",
    totalScore: 40,
    itemScore: 4,
    evaluateItem: 4,
    item: "适当运用本学科专业外语词汇(读、写)。",
  },
  {
    id: 8,
    projectName: "教学内容",
    totalScore: 40,
    itemScore: 4,
    evaluateItem: 4,
    item: "在专业教学中融入课程思政元素。",
  },

  {
    id: 9,
    projectName: "教学方法",
    totalScore: 30,
    itemScore: 8,
    evaluateItem: 8,
    item: "根据教学需求选用适当的教学方法，注重师生互动，有效调动学生 学习积极性。",
  },
  {
    id: 10,
    projectName: "教学方法",
    totalScore: 30,
    itemScore: 8,
    evaluateItem: 8,
    item: "突出学生主体地位，重视学生创新意识、批判性思维自主学习能 力的培养。",
  },
  {
    id: 11,
    projectName: "教学方法",
    totalScore: 30,
    itemScore: 8,
    evaluateItem: 8,
    item: "合理运用信息技术手段开展教学: 课件显示学校名称LOG0，内容 简明，素材丰富，运行流畅: 适当板书，利于知识传授。",
  },
  {
    id: 12,
    projectName: "教学方法",
    totalScore: 30,
    itemScore: 6,
    evaluateItem: 6,
    item: "普通话授课，教学语言准确、规范、简洁、生动，语速适中。",
  },

  {
    id: 13,
    projectName: "教学效果",
    totalScore: 10,
    itemScore: 6,
    evaluateItem: 6,
    item: "完成设定教学目标，促进学生思维能力、分析解决问题和学习能力 的提高。",
  },
  {
    id: 14,
    projectName: "教学效果",
    totalScore: 10,
    itemScore: 4,
    evaluateItem: 4,
    item: "学生参与程度高、学习兴趣浓，课堂氛围活跃。",
  },

  {
    id: 15,
    projectName: "教书育人",
    totalScore: 10,
    itemScore: 5,
    evaluateItem: 5,
    item: "为人师表，治学严谨，无不当言论;引导学生端正学习态度。",
  },
  {
    id: 16,
    projectName: "教书育人",
    totalScore: 10,
    itemScore: 5,
    evaluateItem: 5,
    item: "注重思想品德、职业道德教育，培养学生严谨求实的科学态度和作风。",
  },

  { id: 17, sumText: "合计", evaluateTotal: 100 },
  { id: 18, evlTitle: "总体评价、意见及建议", evaluateComments: "" },
];

export default {
  name: "HospitalTrainingEvaluate",
  components: {
    FileUpload,
    ImageUpload,
    AppraiseScoreDialog,
    SuperviseRecordDialog,
  },
  data() {
    return {
      curUserName: store.getters.nickname,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级培训列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      evaluateVisible: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        evaluationStatus: null,
        trainingType: null,
        nickname: null,
        developDates: [
          parseTime(new Date(), "{y}-{m}-{d}"),
          parseTime(new Date(), "{y}-{m}-{d}"),
        ],
        isHadEvaluation: false,
      },

      isView: false,
      curActive: {},
      livePhotos: "",
      evaluateData: evaluateListData,
      recordsVisible: false,
      evaRecordslist: [],
      exportloading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getHospitalTrainingPage(this.queryParams).then((response) => {
        const list = response.data.list;
        list.forEach((item) => {
          let coursewaresArr = [];
          if (item.coursewares && item.coursewares !== "null") {
            coursewaresArr = JSON.parse(item.coursewares);

            if (coursewaresArr.length > 0) {
              coursewaresArr.forEach((ele) => {
                let url = ele.url;
                if (url.indexOf("?") > -1) {
                  url = url.split("?")[0];
                }
                url = `${url}?token=${getAccessToken()}`;
                ele.url = url;
              });
            }
          }
          item.coursewaresArr = coursewaresArr;
        });
        this.list = list;
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: 2,
            colspan: 1,
          };
        } else if (rowIndex === 2) {
          return {
            rowspan: 6,
            colspan: 1,
          };
        } else if (rowIndex === 8) {
          return {
            rowspan: 4,
            colspan: 1,
          };
        } else if (rowIndex === 12) {
          return {
            rowspan: 2,
            colspan: 1,
          };
        } else if (rowIndex === 14) {
          return {
            rowspan: 2,
            colspan: 1,
          };
        } else if (rowIndex === 16) {
          return {
            rowspan: 1,
            colspan: 4,
          };
        } else if (rowIndex === 17) {
          return {
            rowspan: 1,
            colspan: 2,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
      if (columnIndex === 1) {
        if (rowIndex === 17) {
          return {
            rowspan: 1,
            colspan: 3,
          };
        }
      }
    },

    handleEvaluate(row) {
      this.curActive = row;
      this.isView = false;
      this.evaluateData = evaluateListData;
      this.evaluateVisible = true;
    },

    handleView(row) {
      this.curActive = row;
      this.isView = true;
      this.evaluateVisible = true;
      const { evaluateId = "" } = row;
      if (evaluateId) {
        getHospitalTrainingEvaluate(evaluateId).then((response) => {
          const { data = {} } = response;
          const list = JSON.parse(JSON.stringify(this.evaluateData));
          list.forEach((item) => {
            if (item.projectName) {
              item.evaluateItem = data["evaluateItem" + item.id];
            }
            if (item.sumText) {
              item.evaluateTotal = data.evaluateTotal;
            }
            if (item.evlTitle) {
              item.evaluateComments = data.evaluateComments;
            }
          });
          this.livePhotos = data.livePhotos;
          this.curUserName = data.evaluateUserName;

          this.evaluateData = list;
        });
      }
    },

    handleRecordView(row) {
      this.isView = true;
      this.evaluateVisible = true;
      const { evaluateId = "" } = row;
      if (evaluateId) {
        getHospitalTrainingEvaluate(evaluateId).then((response) => {
          const { data = {} } = response;
          const list = JSON.parse(JSON.stringify(this.evaluateData));
          list.forEach((item) => {
            if (item.projectName) {
              item.evaluateItem = data["evaluateItem" + item.id];
            }
            if (item.sumText) {
              item.evaluateTotal = data.evaluateTotal;
            }
            if (item.evlTitle) {
              item.evaluateComments = data.evaluateComments;
            }
          });
          this.livePhotos = data.livePhotos;
          this.curUserName = data.evaluateUserName;

          this.evaluateData = list;
        });
      }
    },

    handleSubItemScoreChange() {
      const list = JSON.parse(JSON.stringify(this.evaluateData));
      let totalScore = 0;
      list.forEach((item) => {
        if (item.projectName) {
          totalScore = totalScore + item.evaluateItem;
        }
      });
      list[16].evaluateTotal = totalScore;
      console.log("handleSubItemScoreChange====", list);

      this.evaluateData = list;
    },

    handleSave() {
      let evaluateItemObj = {};
      let evaluateComments = "";
      let evaTotal = 0;
      console.log("handleSave", this.evaluateData);
      this.evaluateData.forEach((item) => {
        if (item.projectName) {
          evaluateItemObj["evaluateItem" + item.id] = item.evaluateItem;
        }
        if (item.sumText) {
          evaTotal = item.evaluateTotal;
        }
        if (item.evlTitle) {
          evaluateComments = item.evaluateComments;
        }
      });
      const params = {
        evaluateComments,
        trainingId: this.curActive.trainingId,
        livePhotos: this.livePhotos,
        evaluateTotal: evaTotal,
        ...evaluateItemObj,
      };
      this.$modal
        .confirm(`提交后不可修改，确认提交吗？`)
        .then(() => {
          createHospitalTrainingEvaluate(params).then((res) => {
            this.evaluateVisible = false;
            this.getList();
            this.$modal.msgSuccess(`创建成功`);
          });
        })
        .catch(() => {});
    },

    handleRecords(row) {
      this.curActive = row;
      const params = {
        pageNo: 1,
        pageSize: 999,
        trainingId: row.trainingId,
      };
      getHospitalTrainingLogPage(params).then((response) => {
        this.evaRecordslist = response.data.list;
        this.recordsVisible = true;
      });
    },

    handleDown() {
      this.exportloading = true;
      exportPDF("pdf-main-cont", "听课记录表", () => {
        this.exportloading = false;
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有院级培训数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportHospitalTrainingExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "院级培训.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss"></style>

<style lang="scss" scoped></style>
