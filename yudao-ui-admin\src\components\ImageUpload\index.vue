<template>
  <div class="component-upload-image">
    <el-upload
        multiple
        :action="`${uploadFileUrl}?watermarkContent=${activeTypeName}`"
        list-type="picture-card"
        :on-success="handleUploadSuccess"
        :before-upload="handleBeforeUpload"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        name="file"
        :on-remove="handleRemove"
        :show-file-list="true"
        :headers="headers"
        :file-list="fileList"
        :on-preview="handlePictureCardPreview"
        :class="{hide: this.fileList.length >= this.limit || disabled}"
        :disabled="disabled"
    >
      <i class="el-icon-plus"></i>
    </el-upload>

    <!-- 上传提示 -->
    <div class="el-upload__tip" slot="tip" v-if="showTip && !disabled">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
      的文件
    </div>

    <el-dialog
        :visible.sync="dialogVisible"
        title="预览"
        width="1180"
        append-to-body
        custom-class="preview-img-dialog"
    > 
      <div class="rotate-btns-box">
        <el-button icon="el-icon-refresh-left" size="small" @click="rotateLeft()"></el-button>
        <el-button icon="el-icon-refresh-right" size="small" @click="rotateRight()"></el-button>
      </div>

      <el-button class="arrow-left" icon="el-icon-arrow-left" @click="handlePre()"></el-button>
      <el-button class="arrow-right" icon="el-icon-arrow-right" @click="handleNext()"></el-button>
      
      <div class="preview-img-box">
        <img
            :src="dialogImageUrl"
            class="preview-img"
            :style="imageStyle"
        />
        
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAccessToken } from "@/utils/auth";
import { Message } from "element-ui";

export default {
  props: {
    value: [String, Object, Array],
    // 图片数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["png", "jpg", "jpeg"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    activeTypeName: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      dialogImageUrl: "",
      dialogVisible: false,
      hideUpload: false,
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/admin-api/infra/file/upload", // 请求地址
      headers: { Authorization: "Bearer " + getAccessToken() }, // 设置上传的请求头部
      fileList: [],
      rotation: 0, // 旋转的角度
      previewIndex: 0,
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 首先将值转为数组, 当只穿了一个图片时，会报map方法错误
          const list = Array.isArray(val) ? val :  Array.isArray(this.value.split(',')) ? this.value.split(','): Array.of(this.value);
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (typeof item === "string") {
              // edit by 芋道源码
              if (item.indexOf('?') > -1) {
                item = item.split('?')[0]
              }
              item = { name: item, url: `${item}?token=${getAccessToken()}` };
            }
            // console.log('item=====', item)
            return item;
          });
          // console.log('this.fileList=====', this.fileList)
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
    imageStyle() {
      return {
        transform: `rotate(${this.rotation}deg)`,
      };
    },
  },
  methods: {
    // 删除图片
    handleRemove(file, fileList) {
      const findex = this.fileList.map(f => f.name).indexOf(file.name);
      if(findex > -1) {
        this.fileList.splice(findex, 1);
        this.$emit("input", this.listToString(this.fileList));
      }
    },
    // 上传成功回调
    handleUploadSuccess(res) {
      if (res.code != 0) {
        this.$modal.closeLoading();
        this.$modal.msgError(res.msg);
        return false
      }
      if (res.code == 0) {
        // edit by 芋道源码
        this.uploadList.push({ name: res.data, url: res.data });
        if (this.uploadList.length === this.number) {
          this.fileList = this.fileList.concat(this.uploadList);
          this.uploadList = [];
          this.number = 0;
          this.$emit("input", this.listToString(this.fileList));
          this.$modal.closeLoading();
        }
      }
    },
    // 上传前loading加载
    handleBeforeUpload(file) {
      let isImg = false;
      if (this.fileType.length) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        isImg = this.fileType.some(type => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
      } else {
        isImg = file.type.indexOf("image") > -1;
      }

      if (!isImg) {
        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join("/")}图片格式文件!`);
        return false;
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      this.$modal.loading("正在上传图片，请稍候...");
      this.number++;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError("上传图片失败，请重试");
      this.$modal.closeLoading();
    },
    // 预览
    handlePictureCardPreview(file) {
      console.log('handlePictureCardPreview===', this.fileList, file)
      this.dialogImageUrl = file.url;
      this.previewIndex = this.fileList.findIndex(item => item.url === file.url)
      this.dialogVisible = true;
    },
    handlePre(){
      if(this.previewIndex === 0){
        this.previewIndex = this.fileList.length - 1;
      }else{
        this.previewIndex--;
      }
      this.dialogImageUrl = this.fileList[this.previewIndex].url;
    },
    handleNext(){
      if(this.previewIndex === this.fileList.length - 1){
        this.previewIndex = 0;
      }else{
        this.previewIndex++;
      }
      this.dialogImageUrl = this.fileList[this.previewIndex].url;
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        strs += list[i].url.replace(this.baseUrl, "") + separator;
      }
      return strs !== '' ? strs.substr(0, strs.length - 1) : '';
    },
    rotateLeft() {
      this.rotation += 90; // 每次旋转90度
    },
    rotateRight(){
      this.rotation -= 90;
    }
  }
};
</script>
<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
::v-deep.hide .el-upload--picture-card {
  display: none;
}
// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter, .el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}

::v-deep .el-dialog__body{
  position: relative;
  padding-top: 0;
}

::v-deep .preview-img-dialog{
  position: relative;

  .el-dialog__body{
    padding: 0 60px;

    .el-button--medium{
      padding: 8px;
    }
  }

  .arrow-left{
    position: absolute;
    left: 10px; 
    top: 50%;
    transform: translateY(-50%);
  }

  .arrow-right{
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  .preview-img-box{
    height: calc(100vh - 220px);
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f8f8;
    position: relative;
  }
  
  .preview-img{
    display: block; 
    max-width: calc(100vh - 220px);
    max-height: calc(100vh - 220px);
    margin: 0 auto;
  }
  .rotate-btns-box{
    width: 150px;
    margin: 0 auto;
    padding-bottom: 10px;
  }

}

</style>

