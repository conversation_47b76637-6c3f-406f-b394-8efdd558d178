import request from '@/utils/request'

// 创建招生计划
export function createPlan(data) {
  return request({
    url: '/recruitment/plan/create',
    method: 'post',
    data: data
  })
}

// 更新招生计划
export function updatePlan(data) {
  return request({
    url: '/recruitment/plan/update',
    method: 'put',
    data: data
  })
}

// 删除招生计划
export function deletePlan(id) {
  return request({
    url: '/recruitment/plan/delete?id=' + id,
    method: 'delete'
  })
}

// 获得招生计划
export function getPlan(id) {
  return request({
    url: '/recruitment/plan/get?id=' + id,
    method: 'get'
  })
}

// 获得招生计划(隐藏信息)
export function getPlanHide(id) {
  return request({
    url: '/recruitment/plan/get-hide-info?id=' + id,
    method: 'get'
  })
}

// 获得招生计划分页
export function getPlanPage(query) {
  return request({
    url: '/recruitment/plan/page',
    method: 'get',
    params: query
  })
}

// 导出招生计划 Excel
export function exportPlanExcel(query) {
  return request({
    url: '/recruitment/plan/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 发布招生计划
export function publishPlan(id) {
  return request({
    url: '/recruitment/plan/publish',
    method: 'put',
    params: { id },
  })
}

// 撤销发布招生计划
export function revokePlan(id) {
  return request({
    url: '/recruitment/plan/revoke',
    method: 'put',
    params: { id },
  })
}

// 复制招生计划
export function copyPlan(id) {
  return request({
    url: '/recruitment/plan/copy',
    method: 'put',
    data: { id }
  })
}

// 获得招生计划专业列表
export function getPlanMajorList(id) {
  return request({
    url: '/recruitment/plan/list-major',
    method: 'get',
    params: { id }
  })
}
