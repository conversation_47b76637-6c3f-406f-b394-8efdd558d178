<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="招生计划名称" prop="planName" label-width="100px">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入招生计划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in studentTypeList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态" prop="publishStatus">
        <el-select
          v-model="queryParams.publishStatus"
          placeholder="请选择发布状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_PUBLISH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['recruitment:plan:create']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['recruitment:plan:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="招生计划名称"
        align="center"
        prop="planName"
        min-width="200px"
      />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          />
        </template>
      </el-table-column>
      <el-table-column label="报名时间" align="center" width="280">
        <template slot-scope="scope">
          <span
            >{{ scope.row.applicationStartTime }}-{{
              scope.row.applicationEndTime
            }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="报到时间" align="center" width="280">
        <template slot-scope="scope">
          <span
            >{{ scope.row.reportBeginTime }}-{{ scope.row.reportEndTime }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center" prop="publishStatus">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_PUBLISH_STATUS"
            :value="scope.row.publishStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        class-name="small-padding fixed-width"
        width="220px"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-eye"
            @click="handleCheck(scope.row)"
            v-hasPermi="['recruitment:plan:query']"
            v-if="scope.row.publishStatus"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['recruitment:plan:update']"
            v-if="!scope.row.publishStatus"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-position"
            @click="handlePublish(scope.row)"
            v-hasPermi="['recruitment:plan:update']"
            v-if="!scope.row.publishStatus"
            >发布</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh-left"
            @click="handleRevoke(scope.row)"
            v-hasPermi="['recruitment:plan:update']"
            v-if="scope.row.publishStatus"
            >撤销</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['recruitment:plan:delete']"
            v-if="!scope.row.publishStatus"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-copy"
            v-hasPermi="['recruitment:plan:update']"
            @click="handleCopy(scope.row)"
            >复制</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <plan-dialog
      :open.sync="open"
      :type="type"
      :id="planId"
      @submitted="getList"
    ></plan-dialog>
  </div>
</template>

<script>
import {
  deletePlan,
  getPlanPage,
  exportPlanExcel,
  publishPlan,
  revokePlan,
  copyPlan,
} from "@/api/recruitment/plan";
import PlanDialog from "./plan-dialog";

export default {
  name: "Plan",
  components: { PlanDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 招生计划列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        planName: null,
        studentType: null,
        publishStatus: null,
      },
      // 学员列表
      studentTypeList: [],
      // 弹窗显示
      open: false,
      // 弹窗类型
      type: "create",
      // 计划id
      planId: undefined,
    };
  },
  created() {
    this.studentTypeList = (
      this.getDictDatas(this.DICT_TYPE.SYSTEM_STUDENT_TYPE) || []
    ).filter(
      (item) => ["进修生", "短期培训", "住院医师"].indexOf(item.label) > -1
    );
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getPlanPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        planName: undefined,
        studentType: undefined,
        applicationStartEndTime: [],
        reportBeginEndTime: [],
        brochure: undefined,
        files: undefined,
        brochurePreviewTime: undefined,
        settingsSaveReqVOList: [],
        publishStatus: 0,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true;
      this.type = "create";
      this.planId = undefined;
    },
    /** 查看招生计划 */
    handleCheck(row) {
      this.open = true;
      this.type = "get";
      this.planId = row.id;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true;
      this.type = "update";
      this.planId = row.id;
    },
    /** 发布计划 */
    handlePublish(row) {
      publishPlan(row.id).then(() => {
        this.$modal.msgSuccess("发布计划成功！");
        this.getList();
      });
    },
    /** 撤销计划 */
    handleRevoke(row) {
      revokePlan(row.id).then(() => {
        this.$modal.msgSuccess("撤销计划成功！");
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除招生计划编号为"' + id + '"的数据项?')
        .then(function () {
          return deletePlan(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 复制操作 */
    handleCopy(row) {
      copyPlan(row.id).then(() => {
        this.getList();
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有招生计划数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportPlanExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "招生计划.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss">
.tox-tinymce-aux {
  z-index: 9999 !important;
}
</style>
