<template>
  <el-dialog
    title=""
    :visible="open"
    width="1000px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <div
      ref="noticeContent"
      id="noticeContent-box"
      v-html="noticeContent"
      style="padding: 0 50px"
    ></div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">关闭</el-button>
      <el-button type="primary" @click="handleNoticeDown">下载</el-button>
      <el-button
        v-if="noticeOpt === 'noticeSend'"
        type="primary"
        @click="submitNoticeForm"
      >
        提交
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import {
  getNoticeContent,
  sendNotice,
} from "@/api/recruitment/rpregistrationEnrollExamine";

export default {
  name: "NoticeDialog",
  components: {},
  props: {
    noticeOpen: {
      type: <PERSON>olean,
      default: false,
    },
    curRow: {
      type: Object,
      default: () => {},
    },
    noticeOpt: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      open: this.noticeOpen,
      noticeContent: "",
    };
  },
  watch: {
    noticeOpen(newVal) {
      if (newVal) {
        getNoticeContent({
          recruitmentRegistrationId: this.curRow.recruitmentRegistrationId,
        }).then((res) => {
          this.noticeContent = res.data.noticeContent;
          this.open = newVal;

          this.$nextTick(() => {
            const contentElement = this.$refs.noticeContent;
            if (this.noticeOpt === "noticeSend") {
              const editableElements = contentElement.querySelectorAll(
                '[contenteditable="false"]'
              );
              editableElements.forEach((element) => {
                element.contentEditable = true;
              });
            } else {
              const editableElements = contentElement.querySelectorAll(
                '[contenteditable="true"]'
              );
              editableElements.forEach((element) => {
                element.contentEditable = false;
              });
            }
          });
        });
      } else {
        this.open = newVal;
      }
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:noticeOpen", false);
      this.noticeContent = "";
    },

    handleNoticeDown() {
      this.exportPDF("noticeContent-box", "住培录取通知书");
    },

    exportPDF(tableId, fileName) {
      const table = document.getElementById(tableId);
      html2canvas(table).then((canvas) => {
        // debugger
        const contentWidth = canvas.width;
        const contentHeight = canvas.height;
        const pageHeight = (contentWidth / 592.28) * 841.89;
        let leftHeight = contentHeight;
        let position = 0;
        const imgWidth = 595.28;
        const imgHeight = (592.28 / contentWidth) * contentHeight;
        const pageData = canvas.toDataURL("image/jpeg", 1.0);
        const pdf = new jsPDF("", "pt", "a4");
        if (leftHeight < pageHeight) {
          pdf.addImage(pageData, "JPEG", 0, 0, imgWidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            pdf.addImage(pageData, "JPEG", 0, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;

            if (leftHeight > 0) {
              pdf.addPage();
            }
          }
        }
        pdf.save(`${fileName}.pdf`);
      });
    },

    /** 提交按钮 */
    submitNoticeForm() {
      const htmlContent = this.$refs.noticeContent.innerHTML;
      console.log("htmlContent===", htmlContent);
      const params = {
        noticeContent: htmlContent,
        recruitmentRegistrationId: this.curRow.recruitmentRegistrationId,
      };
      //提交
      sendNotice(params).then((response) => {
        this.$modal.msgSuccess("操作成功");
        this.cancel();
        this.$emit("refresh");
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
