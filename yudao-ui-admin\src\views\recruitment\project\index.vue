<template>
  <div class="app-container">
    <el-radio-group class="mb10" v-model="queryParams.studentType" size="mini" @change="handleStudentTypeChange">
      <el-radio-button
        v-for="dict in studentTypeList"
        :key="dict.value"
        :label="dict.value"
      >{{ dict.label }}</el-radio-button>
    </el-radio-group>

    <el-row :gutter="10">
      <el-col :span="5">
        <div class="department-wrapper">
          <el-input
            class="mb5"
            v-model="departmentFilterKey"
            placeholder="输入科室名过滤"
            clearable
            size="small"
            @change="$refs.departmentTree.filter(departmentFilterKey)"
          ></el-input>
          <el-tree
            ref="departmentTree"
            highlight-current
            node-key="id"
            :data="departmentTree"
            :props="{ label: 'name' }"
            :filter-node-method="filterDepartmentMethod"
            @node-click="handleDepartmentNodeClick"
          ></el-tree>
        </div>
      </el-col>
      <el-col :span="19">
        <!-- 项目列表 -->
        <div class="table-title">
          <span>项目列表</span>
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleProjectAdd"
                     v-hasPermi="['recruitment:project:create']">新增项目</el-button>
        </div>

        <el-table
          v-loading="loading"
          ref="projectTable"
          class="mb20"
          :data="projectList"
          highlight-current-row
          @row-click="handleProjectClick">
          <el-table-column label="项目名称" align="center" prop="projectName" />
          <el-table-column label="项目编码" align="center" prop="code" />
          <el-table-column label="招收人数" align="center" prop="recruitNumber" />
          <el-table-column label="状态" align="center" prop="status">
            <template v-slot="scope">
              {{ getDictDataLabel(DICT_TYPE.COMMON_STATUS, scope.row.status) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleProjectUpdate(scope.row)"
                         v-hasPermi="['recruitment:project:update']">修改</el-button>
<!--              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleProjectDelete(scope.row)"
                         v-hasPermi="['recruitment:project:delete']">删除</el-button>-->
              <el-button size="mini" type="text" :icon="scope.row.status ? 'el-icon-unlock' : 'el-icon-lock'" @click="handleSwitchProject(scope.row)"
                         v-hasPermi="['recruitment:project:update']">{{ scope.row.status ? "关闭" : "启用" }}</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 列表 -->
        <div class="table-title">
          <span>项目明细</span>
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleDetailAdd"
                     v-hasPermi="['recruitment:project-detailed:create']">新增明细</el-button>
        </div>

        <el-table v-loading="detailLoading" :data="detailList">
          <el-table-column label="招收时长（月）" align="center" prop="recruitMonths" />
          <el-table-column label="学费（元）" align="center" prop="tuition" />
          <el-table-column label="备注" align="center" prop="remarks" />
          <el-table-column label="培训方案" align="center" prop="trainingProgramName">
            <template v-slot="scope">
              <el-link href="scope.row.trainingProgramUrl">{{ scope.row.trainingProgramName }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleDetailUpdate(scope.row)"
                         v-hasPermi="['recruitment:project-detailed:update']">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDetailDelete(scope.row)"
                         v-hasPermi="['recruitment:project-detailed:delete']">删除</el-button>
              <file-upload-info
                class="upload-program"
                :limit="1"
                :file-size="500"
                :is-show-tip="false"
                @input="handleUploadProgram(scope.row, $event)"
              >
                <el-button size="mini" type="text" icon="el-icon-upload2"
                           v-hasPermi="['recruitment:project-detailed:update']">上传培训方案</el-button>
              </file-upload-info>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <!-- 项目对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="projectOpen" width="500px" v-dialogDrag append-to-body>
      <el-form ref="projectForm" :model="projectForm" :rules="projectRules" label-width="80px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="projectForm.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目编码" prop="code">
          <el-input v-model="projectForm.code" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="招收人数" prop="recruitNumber">
          <el-input-number v-model="projectForm.recruitNumber" placeholder="请输入招收人数" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="projectForm.status" placeholder="请选择状态">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                       :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="projectSubmitForm">确 定</el-button>
        <el-button @click="projectCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 明细对话框(添加 / 修改) -->
    <el-dialog :title="detailTitle" :visible.sync="detailOpen" width="500px" v-dialogDrag append-to-body>
      <el-form ref="detailForm" :model="detailForm" :rules="detailRules" label-width="130px">
        <el-form-item label="项目名称" prop="projectId">
          <el-input :value="selectProject && selectProject.projectName" placeholder="请输入项目名称" disabled />
        </el-form-item>
        <el-form-item label="招收时长（月）" prop="recruitMonths">
          <el-input-number v-model="detailForm.recruitMonths" placeholder="请输入招收时长" :min="0" />
        </el-form-item>
        <el-form-item label="学费（元）" prop="tuition">
          <el-input-number v-model="detailForm.tuition" placeholder="请输入学费" :min="0" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input type="textarea" v-model="detailForm.remarks" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="detailSubmitForm">确 定</el-button>
        <el-button @click="detailCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createProject, updateProject, deleteProject, getProject, getProjectPage } from "@/api/recruitment/project";
import { createProjectDetailed, updateProjectDetailed, deleteProjectDetailed, getProjectDetailed, getProjectDetailedPage, exportProjectDetailedExcel } from "@/api/recruitment/projectDetailed";
import { getDepartmentSimpleTree } from "@/api/system/department";
import FileUploadInfo from "@/components/FileUploadInfo";

export default {
  name: "Project",
  components: {
    FileUploadInfo
  },
  data() {
    return {
      // 学员类型
      studentTypeList: [],
      // 医院科室列表
      departmentTree: [],
      // 科室过滤名称
      departmentFilterKey: "",
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 99,
        rotationDepartmentId: null,
        studentType: null,
      },
      // 遮罩层
      loading: false,
      // 招录项目列表
      projectList: [],
      // 项目弹窗
      projectOpen: false,
      // 项目弹出层标题
      title: "",
      // 项目表单参数
      projectForm: {},
      // 项目表单校验
      projectRules: {
        projectName: [{ required: true, message: "项目名称不能为空", trigger: "blur" }],
        code: [{ required: true, message: "项目编码不能为空", trigger: "blur" }],
        recruitNumber: [{ required: true, message: "招收人数不能为空", trigger: "blur" }],
        status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
      },
      // 选中项目
      selectProject: null,
      // 明细遮罩层
      detailLoading: false,
      // 详情查询参数
      detailQueryParams: {
        pageNo: 1,
        pageSize: 99,
        projectId: null,
      },
      // 明细列表
      detailList: [],
      // 明细弹窗
      detailOpen: false,
      // 明细弹出层标题
      detailTitle: "",
      // 明细表单参数
      detailForm: {},
      // 明细表单校验
      detailRules: {
        projectId: [{ required: true, message: "项目不能为空" }],
        recruitMonths: [{ required: true, message: "招收时长不能为空", trigger: "blur" }],
        tuition: [{ required: true, message: "学费不能为空", trigger: "blur" }],
      },
    };
  },
  mounted() {
    this.studentTypeList = (this.getDictDatas(this.DICT_TYPE.SYSTEM_STUDENT_TYPE) || [])
      .filter(item => ['进修生', '短期培训'].indexOf(item.label) > -1);
    this.queryParams.studentType = this.studentTypeList[0]?.value;
    getDepartmentSimpleTree().then(res => {
      this.departmentTree = res.data || [];
    });
  },
  methods: {
    handleStudentTypeChange() {
      this.detailList = [];
      this.getProjectList();
    },
    /** 过滤科室 */
    filterDepartmentMethod(value, data) {
      return data.name.indexOf(this.departmentFilterKey) > -1;
    },
    /** 点击科室节点 */
    handleDepartmentNodeClick(data, node) {
      if (node.level === 1) {
        this.$refs.departmentTree.setCurrentKey(this.queryParams.rotationDepartmentId);
      } else {
        this.queryParams.rotationDepartmentId = data.id;
        this.detailList = [];
        this.getProjectList();
      }
    },
    /** 查询列表 */
    getProjectList() {
      this.loading = true;
      // 执行查询
      getProjectPage(this.queryParams).then(response => {
        this.projectList = response.data.list;
        this.loading = false;
      });
    },
    /** 项目取消按钮 */
    projectCancel() {
      this.projectOpen = false;
      this.projectReset();
    },
    /** 项目表单重置 */
    projectReset() {
      this.projectForm = {
        id: undefined,
        studentType: this.queryParams.studentType,
        rotationDepartmentId: this.queryParams.rotationDepartmentId,
        projectName: undefined,
        code: undefined,
        recruitNumber: undefined,
        status: undefined,
      };
      this.resetForm("projectForm");
    },
    /** 新增按钮操作 */
    handleProjectAdd() {
      if (!this.queryParams.rotationDepartmentId) {
        this.$message.warning("请先选择轮转科室～");
        return;
      }
      this.projectReset();
      this.projectOpen = true;
      this.title = "添加招录项目";
    },
    /** 修改按钮操作 */
    handleProjectUpdate(row) {
      this.projectReset();
      getProject(row.id).then(response => {
        this.projectForm = response.data;
        this.projectForm.studentType = this.queryParams.studentType;
        this.projectForm.rotationDepartmentId = this.queryParams.rotationDepartmentId;
        this.projectOpen = true;
        this.title = "修改招录项目";
      });
    },
    /** 提交按钮 */
    projectSubmitForm() {
      this.$refs["projectForm"].validate(valid => {
        if (!valid) return;
        // 修改的提交
        if (this.projectForm.id) {
          updateProject(this.projectForm).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.projectOpen = false;
            this.getProjectList();
          });
          return;
        }
        // 添加的提交
        createProject(this.projectForm).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.projectOpen = false;
          this.getProjectList();
        });
      });
    },
    /** 删除按钮操作 */
    handleProjectDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除招录项目编号为"' + id + '"的数据项?').then(function() {
          return deleteProject(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 启用关闭招录项目 */
    handleSwitchProject(row) {
      const params = { ...row };
      params.status = row.status ? 0 : 1;
      params.studentType = this.queryParams.studentType;
      params.rotationDepartmentId = this.queryParams.rotationDepartmentId;
      updateProject(params).then(() => {
        this.$modal.msgSuccess(row.status ? "项目关闭成功" : "项目启用成功");
        this.getProjectList();
      });
    },
    /** 点击项目查询明细  */
    handleProjectClick(row) {
      this.selectProject = row;
      this.detailQueryParams.projectId = row?.id;
      this.getDetailList();
    },
    /** 查询详情列表 */
    getDetailList() {
      if (!this.detailQueryParams.projectId) {
        this.detailList = [];
        return;
      }
      this.detailLoading = true;
      // 执行查询
      getProjectDetailedPage(this.detailQueryParams).then(response => {
        this.detailList = response.data.list;
        this.detailLoading = false;
      });
    },
    /** 详情取消按钮 */
    detailCancel() {
      this.detailOpen = false;
      this.detailReset();
    },
    /** 详情表单重置 */
    detailReset() {
      this.detailForm = {
        id: undefined,
        projectId: undefined,
        recruitMonths: undefined,
        tuition: undefined,
        trainingProgramName: undefined,
        trainingProgramUrl: undefined,
        remarks: undefined,
      };
      this.resetForm("detailForm");
    },
    /** 新增按钮操作 */
    handleDetailAdd() {
      if (!this.detailQueryParams.projectId) {
        this.$message.warning("请先选择一个项目～");
        return;
      }
      this.detailReset();
      this.detailForm.projectId = this.detailQueryParams.projectId;
      this.detailOpen = true;
      this.detailTitle = "添加招录项目明细";
    },
    /** 修改按钮操作 */
    handleDetailUpdate(row) {
      this.detailReset();
      getProjectDetailed(row.id).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
        this.detailTitle = "修改招录项目明细";
      });
    },
    /** 提交按钮 */
    detailSubmitForm() {
      this.$refs["detailForm"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.detailForm.id != null) {
          updateProjectDetailed(this.detailForm).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.detailOpen = false;
            this.getDetailList();
          });
          return;
        }
        // 添加的提交
        createProjectDetailed(this.detailForm).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.detailOpen = false;
          this.getDetailList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDetailDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除该招录项目明细?').then(function() {
        return deleteProjectDetailed(id);
      }).then(() => {
        this.getDetailList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 上传培训方案 */
    handleUploadProgram(row, fileList) {
      const { name, url } = fileList[0];
      const params = { ...row, trainingProgramName: name, trainingProgramUrl: url };
      updateProjectDetailed(params).then(response => {
        this.$modal.msgSuccess("上传培训方案成功");
        this.getDetailList();
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.department-wrapper {
  width: 100%;
  height: calc(100vh - 160px);
  border: 1px solid #eee;
  padding: 10px 5px;
  overflow: auto;

  ::v-deep .el-radio {
    width: 100%;
    margin-right: 0;
    margin-bottom: 10px;
  }

  ::v-deep .el-radio__input {
    display: none;
  }
}

.detail-table-wrapper {
  padding: 10px 20px;

  ::v-deep .el-table .el-table__header-wrapper th {
    background-color: #FFFFFF;
    border-right: none;
  }
}

.table-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 17px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: "";
    display: block;
    height: 17px;
    width: 5px;
    border-radius: 2px;
    background-color: #1890ff;
    position: absolute;
    left: 0;
  }
}

.upload-program {
  display: inline-block;
  margin-left: 10px;
}
</style>
