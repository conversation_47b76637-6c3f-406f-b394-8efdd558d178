import request from '@/utils/request'

// 创建科室培训
export function createDeptTraining(data) {
  return request({
    url: '/rotation/dept-training/create',
    method: 'post',
    data: data
  })
}

// 更新科室培训
export function updateDeptTraining(data) {
  return request({
    url: '/rotation/dept-training/update',
    method: 'put',
    data: data
  })
}

// 删除科室培训
export function deleteDeptTraining(id) {
  return request({
    url: '/rotation/dept-training/delete?id=' + id,
    method: 'delete'
  })
}

// 获得科室培训
export function getDeptTraining(id) {
  return request({
    url: '/rotation/dept-training/get?id=' + id,
    method: 'get'
  })
}

// 获得科室培训分页
export function getDeptTrainingPage(query) {
  return request({
    url: '/rotation/dept-training/page',
    method: 'get',
    params: query
  })
}

// 导出科室培训 Excel
export function exportDeptTrainingExcel(query) {
  return request({
    url: '/rotation/dept-training/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
