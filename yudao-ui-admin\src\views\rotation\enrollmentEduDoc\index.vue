<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="科室" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入科室简介" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="信息状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择信息状态" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ENROLLMENT_EDU_DOC_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:enrollment-edu-doc:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:enrollment-edu-doc:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="科室" align="center" prop="name" />
      <el-table-column label="入科教育信息状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.ROTATION_ENROLLMENT_EDU_DOC_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleMaintain(scope.row)"
                     v-hasPermi="['rotation:enrollment-edu-doc:save']">维护</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="科室简介" prop="departmentIntro">
          <el-input type="textarea" autosize v-model="form.departmentIntro" placeholder="介绍科室的学术地位、技术专长等"></el-input>
        </el-form-item>
        <el-form-item label="科室人员列表" prop="departmentWorkers">
          <personTable :formData="form" @change="setDepartmentWorkers" />
        </el-form-item>
        <el-form-item label="人员简介" prop="userIntro">
          <el-input type="textarea" autosize v-model="form.userIntro" placeholder="可填写科室架构信息" />
        </el-form-item>
        <el-form-item label="工作环境介绍" prop="workEnv">
          <el-input type="textarea" autosize v-model="form.workEnv" placeholder="介绍科室位置、床位数、处置室、示教室、随访室数等信息" />
        </el-form-item>
        <el-form-item label="诊治范围" prop="treatScope">
          <el-input type="textarea" autosize v-model="form.treatScope" placeholder="介绍科室主要诊治疾病范围" />
        </el-form-item>
        <el-form-item label="科室周程列表" prop="departmentJourney">
          <weekEventList :formData="form" @change="setDepartmentJourney" />
        </el-form-item>
        <el-form-item label="科室考勤制度" prop="attendanceSystem">
          <el-input type="textarea" autosize v-model="form.attendanceSystem" placeholder="介绍科室考勤制度" />
        </el-form-item>
        <el-form-item label="入科教育文档" prop="files">
          <!-- <el-input v-model="form.files" placeholder="请输入附件列表" /> -->
          <FileUpload
            v-model="form.files"
            :limit="999"
            :fileSize="50"
          />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import personTable from "./personTable";
import weekEventList from "./weekEventList";
import FileUpload from '@/components/FileUploadInfo';
import { saveEnrollmentEduDoc, deleteEnrollmentEduDoc, getEnrollmentEduDoc, getEnrollmentEduDocPage, exportEnrollmentEduDocExcel } from "@/api/rotation/enrollmentEduDoc";

export default {
  name: "EnrollmentEduDoc",
  components: {
    personTable,
    weekEventList,
    FileUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 入科教育文档列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getEnrollmentEduDocPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        departmentIntro: undefined,
        userIntro: undefined,
        workEnv: undefined,
        treatScope: undefined,
        attendanceSystem: undefined,
        departmentJourney: [
          {time: '周一', addr: '', cont: ''},
          {time: '周二', addr: '', cont: ''},
          {time: '周三', addr: '', cont: ''},
          {time: '周四', addr: '', cont: ''},
          {time: '周五', addr: '', cont: ''},
          {time: '周六', addr: '', cont: ''},
          {time: '周日', addr: '', cont: ''}
        ],
        files: undefined,
        departmentWorkers: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加入科教育文档";
    },
    /** 维护按钮操作 */
    handleMaintain(row) {
      this.reset();
      const id = row.id;
      getEnrollmentEduDoc(id).then(response => {
        this.open = true;
        this.title = "入科教育文档";
        this.form.id = row.id
        if (response.data) {
          this.form = response.data;
          this.form.departmentWorkers = JSON.parse(this.form.departmentWorkers)
          this.form.departmentJourney = JSON.parse(this.form.departmentJourney)
          this.form.files = JSON.parse(this.form.files)
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        this.form.departmentWorkers = JSON.stringify(this.form.departmentWorkers)
        this.form.departmentJourney = JSON.stringify(this.form.departmentJourney)
        this.form.files = JSON.stringify(this.form.files)
        saveEnrollmentEduDoc(this.form).then(response => {
          this.$modal.msgSuccess("保存成功");
          this.open = false;
          this.getList();
        })
        // 修改的提交
        // if (this.form.id != null) {
        //   updateEnrollmentEduDoc(this.form).then(response => {
        //     this.$modal.msgSuccess("修改成功");
        //     this.open = false;
        //     this.getList();
        //   });
        //   return;
        // }
        // 添加的提交
        // createEnrollmentEduDoc(this.form).then(response => {
        //   this.$modal.msgSuccess("新增成功");
        //   this.open = false;
        //   this.getList();
        // });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除入科教育文档编号为"' + id + '"的数据项?').then(function() {
          return deleteEnrollmentEduDoc(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有入科教育文档数据项?').then(() => {
          this.exportLoading = true;
          return exportEnrollmentEduDocExcel(params);
        }).then(response => {
          this.$download.excel(response, '入科教育文档.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    },
    setDepartmentWorkers(list) {
      this.form.departmentWorkers = list
      console.log('setDepartmentWorkers===',this.form)
    },
    setDepartmentJourney(list) {
      this.form.departmentJourney = list
      console.log('setDepartmentWorkers===',this.form)
    }
  }
};
</script>
