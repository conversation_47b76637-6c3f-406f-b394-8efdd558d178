import request from '@/utils/request'

// 获得学员绩点统计分页
export function getStudentGpaStatisticPage(query) {
  return request({
    url: '/rotation/studentgpastatistic/page',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/studentgpastatistic/index'}
  })
}

// 导出学员绩点统计数据
export function exportStudentGpaStatisticPage(query) {
  return request({
    url: '/rotation/studentgpastatistic/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得教学活动有效评价分页
export function getStudentEffectiveAppraiseActivePage(query) {
  return request({
    url: '/rotation/studentgpastatistic/page-student-effective-appraise-active',
    method: 'get',
    params: query,
  })
}

// 获得对科室有效评价分页
export function getStudentEffectiveAppraiseDeptPage(query) {
  return request({
    url: '/rotation/studentgpastatistic/page-student-effective-appraise-dept',
    method: 'get',
    params: query,
  })
}

// 获得对带教有效评价分页
export function getStudentEffectiveAppraiseTeacherPage(query) {
  return request({
    url: '/rotation/studentgpastatistic/page-student-effective-appraise-teacher',
    method: 'get',
    params: query,
  })
}
