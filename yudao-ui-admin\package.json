{"name": "yudao-ui-admin", "version": "1.6.4-snapshot", "description": "芋道管理系统", "author": "芋道", "license": "MIT", "scripts": {"local": "vue-cli-service serve --mode local", "dev": "vue-cli-service serve --mode dev", "show": "vue-cli-service serve --mode show", "longgang": "vue-cli-service serve --mode longgang", "chancheng": "vue-cli-service serve --mode chancheng", "kangning": "vue-cli-service serve --mode kangning", "yfzyy": "vue-cli-service serve --mode yfzyy", "jmfy": "vue-cli-service serve --mode jmfy", "gzsy": "vue-cli-service serve --mode gzsy", "hzly": "vue-cli-service serve --mode hzly", "hzlyhl": "vue-cli-service serve --mode hzlyhl", "szsrmyy": "vue-cli-service serve --mode szsrmyy", "gdsfy": "vue-cli-service serve --mode gdsfy", "mmsrmyy": "vue-cli-service serve --mode mmsrmyy", "szsfybjy": "vue-cli-service serve --mode szsfybjy", "gzsyyw": "vue-cli-service serve --mode gzsyyw", "smey": "vue-cli-service serve --mode smey", "nhly": "vue-cli-service serve --mode nhly", "nhlynw": "vue-cli-service serve --mode nhlynw", "smeya": "vue-cli-service serve --mode smeya", "smeyb": "vue-cli-service serve --mode smeyb", "smeyc": "vue-cli-service serve --mode smeyc", "smeyd": "vue-cli-service serve --mode smeyd", "xaszxyy": "vue-cli-service serve --mode xaszxyy", "dgszyy": "vue-cli-service serve --mode dgszyy", "psfy": "vue-cli-service serve --mode psfy", "psfyyw": "vue-cli-service serve --mode psfyyw", "myszyy": "vue-cli-service serve --mode myszyy", "xaszxyyqkpx": "vue-cli-service serve --mode xaszxyyqkpx", "build:prod": "vue-cli-service build --mode prod", "build:stage": "vue-cli-service build --mode stage", "build:dev": "vue-cli-service build --mode dev", "build:show": "vue-cli-service build --mode show", "build:demo1024": "vue-cli-service build --mode demo1024", "build:longgang": "vue-cli-service build --mode longgang", "build:chancheng": "vue-cli-service build --mode chancheng", "build:kangning": "vue-cli-service build --mode kangning", "build:yfzyy": "vue-cli-service build --mode yfzyy", "build:jmfy": "vue-cli-service build --mode jmfy", "build:hzly": "vue-cli-service build --mode hzly", "build:hzlyhl": "vue-cli-service build --mode hzlyhl", "build:szsrmyy": "vue-cli-service build --mode szsrmyy", "build:gzsy": "vue-cli-service build --mode gzsy", "build:gdsfy": "vue-cli-service build --mode gdsfy", "build:mmsrmyy": "vue-cli-service build --mode mmsrmyy", "build:szsfybjy": "vue-cli-service build --mode szsfybjy", "build:gzsyyw": "vue-cli-service build --mode gzsyyw", "build:smey": "vue-cli-service build --mode smey", "build:nhly": "vue-cli-service build --mode nhly", "build:nhlynw": "vue-cli-service build --mode nhlynw", "build:smeya": "vue-cli-service build --mode smeya", "build:smeyb": "vue-cli-service build --mode smeyb", "build:smeyc": "vue-cli-service build --mode smeyc", "build:smeyd": "vue-cli-service build --mode smeyd", "build:xaszxyy": "vue-cli-service build --mode xaszxyy", "build:dgszyy": "vue-cli-service build --mode dgszyy", "build:psfy": "vue-cli-service build --mode psfy", "build:psfyyw": "vue-cli-service build --mode psfyyw", "build:xaszxyyqkpx": "vue-cli-service build --mode xaszxyyqkpx", "build:myszyy": "vue-cli-service build --mode myszyy", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "clean": "rimraf node_modules"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://github.com/YunaiV/ruoyi-vue-pro"}, "dependencies": {"@babel/parser": "7.7.4", "@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "bpmn-js-token-simulation": "0.10.0", "clipboard": "2.0.8", "core-js": "^3.21.1", "crypto-js": "^4.0.0", "dayjs": "^1.11.7", "echarts": "4.9.0", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "js-beautify": "1.13.0", "jsencrypt": "3.0.0-rc.1", "jspdf": "^2.5.1", "min-dash": "3.5.2", "nprogress": "0.2.0", "qrcodejs2": "0.0.2", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "throttle-debounce": "2.1.0", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "^2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xml-js": "1.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "@vue/compiler-sfc": "^3.0.1", "@vue/eslint-config-prettier": "^5.0.0", "babel-eslint": "10.1.0", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.20.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "7.2.0", "fs-extra": "^8.1.0", "javascript-obfuscator": "^2.5.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "terser-webpack-plugin": "^4.2.3", "uglifyjs-webpack-plugin": "^2.2.0", "vue-template-compiler": "2.6.12", "vue2-ace-editor": "^0.0.15", "webpack-bundle-analyzer": "^3.9.0", "webpack-obfuscator": "^2.6.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}