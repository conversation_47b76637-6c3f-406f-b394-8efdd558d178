<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布年度" prop="releaseYear">
        <el-date-picker
          v-model="queryParams.releaseYear"
          type="year"
          placeholder="选择年"
          value-format="yyyy"
          format="yyyy"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          @change="handleStudentType"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          multiple
          v-model="queryParams.grade"
          placeholder="请选择年级"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="grade in studentGradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          multiple
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          clearable
          size="small"
        >
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="考评结果" prop="result">
        <el-select v-model="queryParams.result" placeholder="请选择考评结果">
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ASSESSMENT_RESULT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="学员姓名" align="center" prop="nickname" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column label="专业" align="center" prop="major" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="学员类别" align="center" prop="personnelType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE"
            :value="scope.row.personnelType"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="发布年度" align="center" prop="releaseYear" />
      <el-table-column label="理论成绩" align="center" prop="theoryScore" />
      <el-table-column label="技能成绩" align="center" prop="skillScore" />
      <el-table-column label="考评结果" align="center" prop="result">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ASSESSMENT_RESULT"
            :value="scope.row.result"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="!scope.row.result"
            size="mini"
            type="text"
            @click="handleView(scope.row, 'edit')"
          >
            进入考评
          </el-button>
          <el-button
            v-else
            size="mini"
            type="text"
            @click="handleView(scope.row, 'view')"
          >
            查看考评
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <ViewDialog
      :visible.sync="viewVisible"
      :curRow="curRow"
      :opt="opt"
      @refresh="getList"
    />
  </div>
</template>

<script>
import { getAssessmentPage } from "@/api/rotation/annualSummaryAssessment";
import { getGradeByStudentType } from "@/api/system/userStudent";
import { getSimpleMajorList } from "@/api/system/major";
import ViewDialog from "./components/viewDialog";

export default {
  name: "AnnualSummaryAssessment",
  components: { ViewDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 年度总结内容列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: null,
        username: null,
        releaseYear: null,
        studentType: null,
        grade: null,
        major: null,
        result: null,
      },
      yearList: [],
      studentGradeList: [],
      queryMajorList: [],
      viewVisible: false,
      opt: "",
      curRow: {},
    };
  },
  created() {
    getSimpleMajorList().then((res) => {
      this.queryMajorList = res.data;
    });
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getAssessmentPage(this.queryParams).then((response) => {
        const list = response.data.list;
        let yearList = [];
        list.forEach((element) => {
          for (const key in element) {
            if (key.indexOf("result_") > -1) {
              const keyArr = key.split("_");
              const year = keyArr[1];
              if (yearList.indexOf(year) < 0) {
                yearList.push(keyArr[1]);
              }
            }
          }
        });
        this.yearList = yearList;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getGradeList(params) {
      getGradeByStudentType(params).then((res) => {
        this.studentGradeList = res.data;
      });
    },
    handleStudentType() {
      const params = {
        studentType: this.queryParams.studentType,
      };
      this.getGradeList(params);
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleView(row, type) {
      this.opt = type;
      this.curRow = row;
      this.viewVisible = true;
    },
  },
};
</script>
