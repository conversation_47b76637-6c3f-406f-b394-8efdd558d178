import request from "@/utils/request";

// 创建教研室活动记录
export function createRoomActivityRecord(data) {
  return request({
    url: "/rotation/room-activity-record/create",
    method: "post",
    data: data,
  });
}

// 更新教研室活动记录
export function updateRoomActivityRecord(data) {
  return request({
    url: "/rotation/room-activity-record/update",
    method: "put",
    data: data,
  });
}

// 删除教研室活动记录
export function deleteRoomActivityRecord(id) {
  return request({
    url: "/rotation/room-activity-record/delete?id=" + id,
    method: "delete",
  });
}

// 获得教研室活动记录
export function getRoomActivityRecord(id) {
  return request({
    url: "/rotation/room-activity-record/get?id=" + id,
    method: "get",
  });
}

// 获得教研室活动记录分页
export function getRoomActivityRecordPage(query) {
  return request({
    url: "/rotation/room-activity-record/page",
    method: "get",
    params: query,
    headers: { component: "rotation/roomActivityRecord/index" },
  });
}

// 导出教研室活动记录 Excel
export function exportRoomActivityRecordExcel(query) {
  return request({
    url: "/rotation/room-activity-record/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
