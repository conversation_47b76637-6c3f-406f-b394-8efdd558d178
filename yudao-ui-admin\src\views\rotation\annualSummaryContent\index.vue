<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布年度" prop="releaseYears">
        <el-date-picker
          v-model="queryParams.releaseYears"
          type="years"
          placeholder="选择年"
          value-format="yyyy"
          format="yyyy"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          @change="handleStudentType"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grades">
        <el-select
          multiple
          v-model="queryParams.grades"
          placeholder="请选择年级"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="grade in studentGradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="majors">
        <el-select
          multiple
          v-model="queryParams.majors"
          placeholder="请选择培训专业"
          clearable
          size="small"
        >
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          >成绩导入</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="学员姓名" align="center" prop="nickname" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column label="专业" align="center" prop="major" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          ></dict-tag>
        </template>
      </el-table-column>

      <el-table-column
        v-for="column in yearList"
        :key="column"
        :label="column + '年度'"
        align="center"
      >
        <el-table-column
          :prop="'theory_score_' + column"
          label="理论成绩"
          align="center"
        >
          <template slot-scope="scope">
            <span> {{ scope.row["theory_score_" + column] }} </span>
            <span v-if="scope.row['theory_num_' + column]">
              ( {{ scope.row["theory_num_" + column] }} )
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :prop="'skill_score_' + column"
          label="技能成绩"
          align="center"
        >
          <template slot-scope="scope">
            <span> {{ scope.row["skill_score_" + column] }} </span>
            <span v-if="scope.row['skill_num_' + column]">
              ( {{ scope.row["skill_num_" + column] }} )
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :prop="'result_' + column"
          label="考评结果"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="viewTemplate(scope.row, column)">
              <dict-tag
                :type="DICT_TYPE.ASSESSMENT_RESULT"
                :value="scope.row['result_' + column]"
              ></dict-tag>
            </el-button>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <!-- <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的数据
          </div> -->
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAnnualSummaryContentPage,
  importTemplate,
} from "@/api/rotation/annualSummaryContent";
import { getGradeByStudentType } from "@/api/system/userStudent";
import { getSimpleMajorList } from "@/api/system/major";
import { getBaseHeader } from "@/utils/request";
import {
  getTemplateByType,
  previewDocTemplateUrl,
} from "@/api/system/template";

export default {
  name: "AnnualSummaryContent",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 年度总结内容列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: null,
        username: null,
        releaseYears: null,
        studentType: null,
        grades: null,
        majors: null,
      },
      yearList: [],
      studentGradeList: [],
      queryMajorList: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/admin-api/rotation/annual-summary-content/import",
      },
    };
  },
  created() {
    getSimpleMajorList().then((res) => {
      this.queryMajorList = res.data;
    });
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getAnnualSummaryContentPage(this.queryParams).then((response) => {
        const list = response.data.list;
        let yearList = [];
        list.forEach((element) => {
          for (const key in element) {
            if (key.indexOf("result_") > -1) {
              const keyArr = key.split("_");
              const year = keyArr[1];
              if (yearList.indexOf(year) < 0) {
                yearList.push(keyArr[1]);
              }
            }
          }
        });
        yearList.sort((a, b) => parseInt(a) - parseInt(b));
        this.yearList = yearList;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getGradeList(params) {
      getGradeByStudentType(params).then((res) => {
        this.studentGradeList = res.data;
      });
    },
    handleStudentType() {
      const params = {
        studentType: this.queryParams.studentType,
      };
      this.getGradeList(params);
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    viewTemplate(row, column) {
      getTemplateByType({
        templateType: "annual_summary",
        studentType: row.studentType,
      }).then((res) => {
        const id = row[`id_${column}`];
        const url = previewDocTemplateUrl(
          res.data.id,
          id,
          `annualSummary${id}`
        );
        window.open(url);
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "成绩导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.$download.excel(response, "成绩导入模板.xlsx");
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      let text = "创建成功数量：" + data.successAnnualSummaryContent.length;
      for (const items of data.failAnnualSummaryContent) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + items;
      }
      text +=
        "<br />创建失败数量：" +
        Object.keys(data.failAnnualSummaryContent).length;
      for (const items in data.failAnnualSummaryContent) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + items;
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
