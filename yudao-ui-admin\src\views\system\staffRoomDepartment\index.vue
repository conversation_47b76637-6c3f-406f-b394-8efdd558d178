<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="教研室" prop="staffRoomName">
        <el-input v-model="queryParams.staffRoomName" placeholder="请输入教研室" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['system:staff-room-department:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['system:staff-room-department:export']">导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="教研室" align="left" width="200" prop="staffRoomName"></el-table-column>
      <el-table-column label="科室配置" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)">配置</el-button>
        </template>
      </el-table-column>
      <el-table-column label="管理科室" align="center" prop="departmentNames" />
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="720px" v-dialogDrag append-to-body>
      <div style="">
        <el-checkbox-group v-model="departmentIds" size="small">
          <el-checkbox v-for="item in departmentOptions" :key="parseInt(item.id)" :label="item.id.toString()" border
            style="margin-bottom: 10px; width: 140px; margin-left: 0">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDepartmentSimpleList } from "@/api/system/department";
import { updateStaffRoomDepartment, deleteStaffRoomDepartment, getStaffRoomDepartmentPage, exportStaffRoomDepartmentExcel } from "@/api/system/staffRoomDepartment";

export default {
  name: "StaffRoomDepartment",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 教研室科室关系,教研室配置列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        staffRoomName: null,
      },
      departmentOptions: [],
      departmentIds: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
    this.getDepartment();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getStaffRoomDepartmentPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then(res => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        staffRoomValue: undefined,
        departmentId: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加教研室科室关系,教研室配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form = {...row}
      this.open = true;
      this.departmentIds = row.departmentIds ? row.departmentIds.split(',') : [];
      // console.log('this.departmentIds======', this.departmentIds)
      this.title = `${row.staffRoomName}配置`;
    },
    /** 提交按钮 */
    submitForm() {
      // 修改的提交
      if (this.form.staffRoomValue != null) {
        const params = {
          departmentIds: this.departmentIds,
          staffRoomValue: this.form.staffRoomValue
        }
        updateStaffRoomDepartment(params).then(response => {
          this.$modal.msgSuccess("配置成功");
          this.open = false;
          this.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const staffRoomValue = row.staffRoomValue;
      this.$modal.confirm('是否确认删除教研室科室关系,教研室配置编号为"' + staffRoomValue + '"的数据项?').then(function() {
          return deleteStaffRoomDepartment(staffRoomValue);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有教研室科室关系,教研室配置数据项?').then(() => {
          this.exportLoading = true;
          return exportStaffRoomDepartmentExcel(params);
        }).then(response => {
          this.$download.excel(response, '教研室科室关系,教研室配置.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
