import request from "@/utils/request";

// 获得用户拥有的轮转科室列表
export function getRotationDepartmentPermissionList(query) {
  return request({
    url: "/system/permission/list-current-user-rotation-depts",
    method: "get",
    params: query,
  });
}

// 获得月度科室评价学员分页
export function getDeptStudentPage(query) {
  return request({
    url: "/rotation/appraise-process-result/deptStudentPage",
    method: "get",
    params: query,
  });
}

// 获得过程评价结果项集合
export function getAppraiseItemList(id) {
  return request({
    url:
      "/rotation/appraise-process-result/itemList?appraiseProcessResultId=" +
      id,
    method: "get",
  });
}

// 保存过程评价结果
export function updateAppraiseResult(data) {
  return request({
    url: "/rotation/appraise-process-result/update",
    method: "put",
    data: data,
  });
}

// 导出教学活动 Excel
export function exportDeptStudentExcel(query) {
  return request({
    url: "/rotation/appraise-process-result/deptStudent-export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
