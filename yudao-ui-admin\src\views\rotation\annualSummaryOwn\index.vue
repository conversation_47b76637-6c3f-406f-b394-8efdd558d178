<template>
  <div class="app-container annualSummaryOwn-container">
    <el-row v-if="list.length > 0">
      <el-col :span="4" v-for="item in list" :key="item.id">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>{{ item.releaseYear }}年度总结</span>
          </div>
          <div class="cont applyForm">
            <div class="item">
              <span>年度理论考核成绩：</span>
              <span>{{ item.theoryScore }}</span>
            </div>
            <div class="item">
              <span>年度技能考核成绩：</span>
              <span>{{ item.skillScore }}</span>
            </div>
            <div class="item">
              <span>审批进度：</span>
              <span @click="handleDetail(item)" style="cursor: pointer">
                <dict-tag
                  :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT"
                  :value="item.bpmResult"
                />
              </span>
            </div>
            <div class="item">
              <span>考评结果：</span>
              <el-button type="text" @click="viewTemplate(item)">
                <dict-tag
                  :type="DICT_TYPE.ASSESSMENT_RESULT"
                  :value="item.result || '0'"
                />
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-empty v-else description="暂无数据"></el-empty>
  </div>
</template>

<script>
import { getOwnSummarys } from "@/api/rotation/annualSummaryOwn";
import {
  getTemplateByType,
  previewDocTemplateUrl,
} from "@/api/system/template";

export default {
  name: "AnnualSummaryCommit",
  data() {
    return {
      list: [],
      loading: false,
    };
  },
  created() {
    this.getData();
  },
  methods: {
    /** 查询列表 */
    getData() {
      this.loading = true;
      // 执行查询
      getOwnSummarys().then((response) => {
        this.list = response.data;
        this.loading = false;
      });
    },

    handleDetail(row) {
      this.$router.push({
        path: "/bpm/process-instance/detail",
        query: { id: row.processInstanceId },
      });
    },

    viewTemplate(row) {
      getTemplateByType({
        templateType: "annual_summary",
        studentType: row.studentType,
      }).then((res) => {
        const url = previewDocTemplateUrl(
          res.data.id,
          row.id,
          `annualSummary${row.id}`
        );
        window.open(url);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.annualSummaryOwn-container {
  .box-card {
    margin-bottom: 20px;
    cursor: default;
  }
  ::v-deep .el-card__header {
    font-size: 16px;
    color: #333;
    font-weight: bold;
  }
  .cont {
    .preview-img {
      max-height: 200px;
    }
  }

  .applyForm {
    .item {
      font-size: 14px;
      line-height: 32px;
    }
    .footer {
      padding-left: 150px;
    }
  }
}
</style>
