<template>
  <div>
    <el-dialog
      :title="title"
      :visible="open"
      width="1000px"
      v-dialogDrag
      append-to-body
      custom-class="plan-dialog"
      @close="cancel"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="110px"
        :disabled="type === 'get'"
      >
        <el-form-item label="招生计划名称" prop="planName">
          <el-input v-model="form.planName" placeholder="请输入招生计划名称" />
        </el-form-item>
        <el-form-item label="学员类型" prop="studentType">
          <el-select v-model="form.studentType" placeholder="请选择学员类型">
            <el-option
              v-for="dict in studentTypeList"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="报名时间" prop="applicationStartEndTime">
          <el-date-picker
            v-model="form.applicationStartEndTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择报名时间"
          />
        </el-form-item>
        <el-form-item label="报到时间" prop="reportBeginEndTime">
          <el-date-picker
            v-model="form.reportBeginEndTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择报到时间"
          />
        </el-form-item>
        <el-form-item label="招生简章" prop="brochure">
          <div v-if="type === 'get'" v-html="form.brochure"></div>
          <tinymce
            v-else-if="open"
            v-model="form.brochure"
            placeholder="请输入招生简章"
            min-height="300"
          ></tinymce>
        </el-form-item>
        <el-form-item label=" 附件上传" prop="file">
          <file-upload-info
            v-model="form.files"
            :limit="99"
            :file-size="500"
          ></file-upload-info>
        </el-form-item>
        <el-form-item label="简章预览时间" prop="brochurePreviewTime">
          <el-select v-model="form.brochurePreviewTime">
            <el-option label="5" :value="5"></el-option>
            <el-option label="10" :value="10"></el-option>
            <el-option label="20" :value="20"></el-option>
            <el-option label="30" :value="30"></el-option>
          </el-select>
          秒
        </el-form-item>

        <el-form-item
          label="项目"
          prop="settingsSaveReqVOList"
          v-if="form.studentType !== 1"
        >
          <el-button
            class="mb10"
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="addProjectDetail"
            v-if="type !== 'get'"
            >添加项目明细</el-button
          >
          <el-table :data="form.settingsSaveReqVOList">
            <el-table-column
              label="项目名称"
              prop="projectName"
            ></el-table-column>
            <el-table-column
              label="科室名称"
              prop="rotationDepartmentName"
            ></el-table-column>
            <el-table-column
              label="招收时长（月）"
              prop="recruitMonths"
            ></el-table-column>
            <el-table-column label="学费（元）" prop="tuition">
              <template v-slot="scope">{{
                scope.row.tuition || scope.row.tuitionHide
              }}</template>
            </el-table-column>
            <el-table-column label="备注" prop="remarks"></el-table-column>
            <el-table-column
              label="培训方案"
              align="center"
              prop="trainingProgramName"
            >
              <template v-slot="scope">
                <el-link
                  :href="`${
                    scope.row.trainingProgramUrl
                  }?token=${getAccessToken()}`"
                  target="_blank"
                  >{{ scope.row.trainingProgramName }}</el-link
                >
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              class-name="small-padding fixed-width"
              width="60"
              v-if="type !== 'get'"
            >
              <template v-slot="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDetailDelete(scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item
          label="专业"
          prop="majorSettingsSaveReqVOList"
          v-if="form.studentType === 1"
        >
          <el-button
            class="mb10"
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAddMajorCode"
            v-if="type !== 'get'"
          >
            新增招生专业
          </el-button>
          <el-table :data="form.majorSettingsSaveReqVOList">
            <el-table-column width="50" label="序号">
              <template v-slot="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column label="专业名称" prop="majorCode">
              <template v-slot="scope">
                {{ getMajorCodeName(scope.row.majorCode) }}
              </template>
            </el-table-column>
            <el-table-column
              label="招生数量"
              prop="recruitmentNum"
            ></el-table-column>
            <el-table-column label="备注" prop="remarks">
              <template v-slot="scope">
                {{ scope.row.remarks || "" }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              class-name="small-padding fixed-width"
              width="60"
              v-if="type !== 'get'"
            >
              <template v-slot="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleMajorDelete(scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="type !== 'get'">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="添加项目明细"
      :visible.sync="detailAddOpen"
      append-to-body
    >
      <el-form inline>
        <el-form-item label="轮转科室">
          <el-select
            v-model="detailAddForm.deptId"
            filterable
            @change="handleDeptChange"
          >
            <el-option
              v-for="item in deptList"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="招录项目">
          <el-select
            v-model="detailAddForm.projectId"
            filterable
            @change="handleProjectChange"
          >
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :value="item.id"
              :label="item.projectName"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-table :data="detailList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          label="招收时长（月）"
          align="center"
          prop="recruitMonths"
        />
        <el-table-column label="学费（元）" align="center" prop="tuition" />
        <el-table-column label="备注" align="center" prop="remarks" />
        <el-table-column
          label="培训方案"
          align="center"
          prop="trainingProgramName"
        >
          <template v-slot="scope">
            <el-link :href="scope.row.trainingProgramUrl">{{
              scope.row.trainingProgramName
            }}</el-link>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer">
        <el-button type="primary" @click="handleSureDetail">确定</el-button>
        <el-button @click="handleCancelDetail">取消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="新增招生专业" :visible.sync="majorAddOpen" append-to-body>
      <el-form
        ref="majorForm"
        :model="majorForm"
        :rules="majorRules"
        label-width="110px"
        :disabled="type === 'get'"
      >
        <el-form-item label="专业名称" prop="majorCode">
          <el-select
            v-model="majorForm.majorCode"
            placeholder="请选择专业"
            filterable
            clearable
          >
            <el-option
              v-for="item in queryMajorList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="招生人数" prop="recruitmentNum">
          <el-input-number
            v-model="majorForm.recruitmentNum"
            :min="1"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="majorForm.remarks"
            type="textarea"
            :rows="2"
            placeholder="备注"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="handleMajorSubmit">确定</el-button>
        <el-button @click="handleCancelMajorSubmit">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAccessToken } from "@/utils/auth";
import { createPlan, getPlanHide, updatePlan } from "@/api/recruitment/plan";
import { getProjectList } from "@/api/recruitment/project";
import { getProjectDetailedList } from "@/api/recruitment/projectDetailed";
import { getRotationDepartmentSimpleList } from "@/api/system/department";
import Tinymce from "@/components/tinymce";
import FileUploadInfo from "@/components/FileUploadInfo";
import { getSimpleMajorList } from "@/api/system/major";

export default {
  name: "plan-dialog",
  components: { Tinymce, FileUploadInfo },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "create", // create update get
    },
    id: Number,
  },
  data() {
    return {
      // 弹出层标题
      title: "",
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        planName: [
          { required: true, message: "招生计划名称不能为空", trigger: "blur" },
        ],
        studentType: [
          { required: true, message: "学员类型不能为空", trigger: "change" },
        ],
        applicationStartEndTime: [
          { required: true, message: "报名时间不能为空", trigger: "blur" },
        ],
        reportBeginEndTime: [
          { required: true, message: "报到时间不能为空", trigger: "blur" },
        ],
        brochure: [
          { required: true, message: "招生简章不能为空", trigger: "blur" },
        ],
        brochurePreviewTime: [
          {
            required: true,
            message: "招生简章预览时间(秒)不能为空",
            trigger: "blur",
          },
        ],
        settingsSaveReqVOList: [
          { required: true, message: "项目不能为空", trigger: "change" },
        ],
        majorSettingsSaveReqVOList: [
          { required: true, message: "专业不能为空", trigger: "change" },
        ],
      },
      // 学员列表
      studentTypeList: [],
      // 详情添加弹窗
      detailAddOpen: false,
      // 轮转科室列表
      deptList: [],
      // 招录项目列表
      projectList: [],
      // 项目明细列表
      detailList: [],
      // 明细选中项
      multipleSelection: [],
      // 明细添加表单
      detailAddForm: {
        deptId: "",
        projectId: "",
        detailedIds: [],
      },
      majorAddOpen: false,
      majorForm: {
        majorCode: "",
        recruitmentNum: "",
        remarks: "",
      },
      queryMajorList: [],
      majorRules: {
        majorCode: [
          { required: true, message: "请选择专业", trigger: "change" },
        ],
        recruitmentNum: [
          { required: true, message: "请输入招生人数", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    getAccessToken,
    /** 取消按钮 */
    cancel() {
      this.$emit("update:open", false);
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        planName: undefined,
        studentType: undefined,
        applicationStartEndTime: [],
        reportBeginEndTime: [],
        brochure: undefined,
        files: undefined,
        brochurePreviewTime: undefined,
        settingsSaveReqVOList: [],
        majorSettingsSaveReqVOList: [],
        publishStatus: 0,
      };
      this.resetForm("form");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getPlanHide(id).then((response) => {
        const {
          applicationStartTime,
          applicationEndTime,
          reportBeginTime,
          reportEndTime,
        } = response.data;
        const form = response.data;
        form.applicationStartEndTime = [
          applicationStartTime,
          applicationEndTime,
        ];
        form.reportBeginEndTime = [reportBeginTime, reportEndTime];
        this.form = form;
        this.open = true;
        this.title = "修改招生计划";
      });
    },
    // 添加项目详情
    addProjectDetail() {
      if (!this.form.studentType) {
        this.$modal.msgError("请先选择学员类型～");
        return;
      }
      this.detailAddOpen = true;
      this.projectList = [];
      this.detailList = [];
      this.detailAddForm = {
        deptId: "",
        projectId: "",
        projectDetailedId: "",
      };
    },
    getMajorCodeName(code) {
      const major = this.queryMajorList.find((item) => item.code === code);
      return major?.name || "";
    },
    // 添加招生专业
    handleAddMajorCode() {
      if (!this.form.studentType) {
        this.$modal.msgError("请先选择学员类型～");
        return;
      }
      this.majorAddOpen = true;
      this.majorForm = {
        majorCode: "",
        recruitmentNum: "",
        remarks: "",
      };
    },
    handleMajorSubmit() {
      this.$refs["majorForm"].validate((valid) => {
        this.form.majorSettingsSaveReqVOList.push(this.majorForm);
        console.log(
          "this.form.majorSettingsSaveReqVOList======",
          this.form.majorSettingsSaveReqVOList
        );
        this.majorAddOpen = false;
      });
    },
    // 取消明细选择
    handleCancelMajorSubmit() {
      this.majorAddOpen = false;
    },
    // 切换轮转科室
    handleDeptChange(value) {
      this.projectList = [];
      this.detailList = [];
      this.multipleSelection = [];
      getProjectList({
        rotationDepartmentId: value,
        studentType: this.form.studentType,
        status: 1,
      }).then((response) => {
        this.projectList = response.data;
        this.detailAddForm.projectId = "";
        this.detailAddForm.detailedIds = [];
        this.detailList = [];
        this.multipleSelection = [];
      });
    },
    // 切换招录项目
    handleProjectChange(value) {
      this.detailList = [];
      this.multipleSelection = [];
      getProjectDetailedList({ projectId: value }).then((response) => {
        this.detailList = response.data;
      });
    },
    // 选择招录明细
    handleSelectionChange(value) {
      this.multipleSelection = value;
    },
    // 删除选择的专业
    handleMajorDelete(index) {
      this.form.majorSettingsSaveReqVOList.splice(index, 1);
    },
    // 确定项目明细
    handleSureDetail() {
      if (this.multipleSelection.length === 0) {
        this.$modal.msgError("请选择项目明细～");
        return;
      }
      const projectDetails = this.multipleSelection.map((item) => {
        const {
          id,
          recruitMonths,
          tuition,
          remarks,
          trainingProgramName,
          trainingProgramUrl,
        } = item;
        const { deptId, projectId } = this.detailAddForm;
        const rotationDepartmentName =
          this.deptList.find((dept) => dept.id === deptId)?.name || "";
        const projectName =
          this.projectList.find((project) => project.id === projectId)
            ?.projectName || "";
        return {
          rotationDepartmentId: deptId,
          rotationDepartmentName,
          projectId,
          projectName,
          projectDetailedId: id,
          recruitMonths,
          tuition,
          remarks,
          trainingProgramName,
          trainingProgramUrl,
        };
      });
      this.form.settingsSaveReqVOList.push(...projectDetails);
      this.handleCancelDetail();
    },
    // 取消明细选择
    handleCancelDetail() {
      this.detailAddOpen = false;
    },
    // 删除选择的项目明细
    handleDetailDelete(index) {
      this.form.settingsSaveReqVOList.splice(index, 1);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const { applicationStartEndTime, reportBeginEndTime, files, ...rest } =
          this.form;
        const form = {
          ...rest,
          applicationStartTime: applicationStartEndTime[0],
          applicationEndTime: applicationStartEndTime[1],
          reportBeginTime: reportBeginEndTime[0],
          reportEndTime: reportBeginEndTime[1],
          files: files ? JSON.stringify(files) : "",
        };
        // 修改的提交
        if (form.id != null) {
          updatePlan(form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.cancel();
            this.$emit("submitted");
          });
          return;
        }
        // 添加的提交
        createPlan(form).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.cancel();
          this.$emit("submitted");
        });
      });
    },
  },
  created() {
    this.studentTypeList = (
      this.getDictDatas(this.DICT_TYPE.SYSTEM_STUDENT_TYPE) || []
    ).filter(
      (item) => ["进修生", "短期培训", "住院医师"].indexOf(item.label) > -1
    );
    getRotationDepartmentSimpleList().then(
      (res) => (this.deptList = res.data || [])
    );
  },
  watch: {
    open: {
      immediate: true,
      async handler(val) {
        if (val) {
          this.queryMajorList = [];
          const res = await getSimpleMajorList();
          this.queryMajorList = res.data;

          if (this.type === "create") {
            this.reset();
            this.title = "添加招生计划";
          } else {
            getPlanHide(this.id).then((response) => {
              const {
                applicationStartTime,
                applicationEndTime,
                reportBeginTime,
                reportEndTime,
                files,
              } = response.data;
              const form = response.data;
              form.applicationStartEndTime = [
                applicationStartTime,
                applicationEndTime,
              ];
              form.reportBeginEndTime = [reportBeginTime, reportEndTime];
              this.form = form;
              this.form.majorSettingsSaveReqVOList =
                form.majorSettingsRespVOList;
              console.log("form======", form);
              try {
                this.form.files = JSON.parse(files);
              } catch (e) {
                this.form.files = undefined;
              }
              this.title =
                this.type === "update" ? "更新招生计划" : "查看招生计划";
            });
          }
        }
      },
    },
  },
};
</script>

<style lang="scss" scoped></style>
