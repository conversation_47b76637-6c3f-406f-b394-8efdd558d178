<template>
  <div>
    <h3 class="form-name">临床文献研读评分表</h3>

    <el-form class="form-info" size="mini" inline>
      <el-form-item class="quarter-item" label="时间:">
        {{ formData.date_y }}年{{ formData.date_m }}月{{ formData.date_d }}日
      </el-form-item>
      <el-form-item class="majority-item" label="地点:">
        <el-radio-group v-model="formData.address" :disabled="check || feedback">
          <el-radio label="病房"></el-radio>
          <el-radio label="门诊"></el-radio>
          <el-radio label="急诊"></el-radio>
          <el-radio label="ICU"></el-radio>
          <el-radio label="其他"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="quarter-item" label="指导医师:">{{ formData.medical_name }}</el-form-item>
      <el-form-item class="majority-item" label-width="45px">
        <el-radio-group v-model="formData.medical_title" :disabled="check || feedback">
          <el-radio label="主任医师"></el-radio>
          <el-radio label="副主任医师"></el-radio>
          <el-radio label="主治医师"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="quarter-item" label="住院医师:">{{ formData.student_name }}</el-form-item>
      <el-form-item class="majority-item" label-width="45px">
        <el-radio-group v-model="formData.student_year" :disabled="check || feedback">
          <el-radio label="第一年"></el-radio>
          <el-radio label="第二年"></el-radio>
          <el-radio label="第三年"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="full-item" label="文献主题:">
        <el-input v-model="formData.literature_topic" :disabled="check || feedback"></el-input>
      </el-form-item>
      <el-form-item label="参加人数:">
        <el-input-number controls-position="right" v-model="formData.participation_count" :disabled="check || feedback"></el-input-number>
      </el-form-item>
    </el-form>

    <table class="evaluate-table">
      <tr>
        <th>评估指标</th>
        <th style="width: 72px;">5分<br/>（非常好）</th>
        <th style="width: 72px;">4分<br/>（好）</th>
        <th style="width: 72px;">3分<br/>（一般）</th>
        <th style="width: 72px;">2分<br/>（差）</th>
        <th style="width: 72px;">1分<br/>（非常差）</th>
        <th>备注</th>
      </tr>
      <tr v-for="item in items" :key="item.prop">
        <td>{{ item.name }}</td>
        <td v-for="i in 5" :key="i">
          <el-radio v-model="formData[item.prop]" :label="i-1" :disabled="check || feedback"></el-radio>
        </td>
        <td style="padding: 5px;"><el-input v-model="formData[`${item.prop}_remarks`]" size="small" :disabled="check || feedback"></el-input></td>
      </tr>
    </table>

    <el-form class="form-info" inline size="mini">
      <el-form-item class="full-item" label="住院医师对评估的满意程度:">
        低
        <el-radio-group v-model="formData.student_satisfaction" :disabled="check || evaluate">
          <el-radio v-for="i in 9" :key="i" :value="i" :label="i"></el-radio>
        </el-radio-group>
        高
      </el-form-item>
      <el-form-item class="comment-item" label="指导医师的评语:">
        <el-input type="textarea" :rows="3" v-model="formData.comment" :disabled="check || feedback"></el-input>
      </el-form-item>
    </el-form>

    <div class="signature-bar">
      <div>住院医师签字:{{ formData.student_signature }}</div>
      <div>指导医师签字:{{ formData.mentor_signature }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'formSeventh',
  props: {
    formData: Object,
    evaluate: Boolean, // 指导医师评价
    feedback: Boolean, // 学员反馈
    check: Boolean, // 仅查看
  },
  data() {
    return {
      items: [
        { name: "主讲医师准备程度", prop: "lecturer_preparation_level" },
        { name: "文献掌握程度", prop: "literature_mastery_degree" },
        { name: "文献报告表现", prop: "literature_presentation_performance" },
        { name: "参与讨论表现", prop: "discussion_participation_performance" },
        { name: "教学目标完成情况", prop: "teaching_objective_achievement" },
        { name: "总体评价", prop: "overall_evaluation" },
      ]
    }
  },
  methods: {
    validData() {
      if (!this.formData.address ||
        !this.formData.medical_title ||
        !this.formData.student_year ||
        !this.formData.literature_topic ||
        !this.formData.participation_count ||
        (this.feedback && !this.formData.student_satisfaction) ||
        !this.formData.comment ||
        this.items.some(item => !this.formData[item.prop] && this.formData[item.prop] !== 0)
      ) {
        this.$message.warning(this.feedback ? "请选择住院医师对评估的满意程度" : "请填写完所有考评项目～");
        return false;
      }
      return true;
    },
  }
}
</script>

<style scoped lang="scss">
.form-name {
  font-size: 16px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 16px;
}

.form-info {
  margin-bottom: 10px;

  ::v-deep .el-form-item {
    margin: 0 0 10px 0;
  }

  ::v-deep .el-radio-group {
    margin-left: 10px;
  }

  ::v-deep .el-radio {
    margin-right: 16px;
  }

  .full-item {
    width: 100%;
    display: flex;
    ::v-deep .el-form-item__content {
      flex-grow: 1;
    }
  }

  .half-item {
    width: 50%;
  }

  .quarter-item {
    width: 25%;
  }

  .majority-item {
    width: 75%;
  }

  .comment-item {
    width: 100%;
    ::v-deep .el-form-item__content {
      display: block;
    }
  }
}

.evaluate-table {
  width: 100%;
  border: 1px solid #dfe6ec;
  border-collapse: collapse;
  margin-bottom: 10px;

  td, th {
    border: 1px solid #dfe6ec;
    padding: 8px 0;
    text-align: center;
  }

  ::v-deep .el-radio__label {
    display: none;
  }

  ::v-deep .el-radio__inner {
    vertical-align: middle;
  }
}

.signature-bar {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  margin-bottom: 20px;
  padding-top: 40px;
}
</style>
