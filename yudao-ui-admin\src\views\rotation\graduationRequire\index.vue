<template>
  <div class="app-container">
    <el-radio-group style="margin-bottom: 20px;" size="medium" v-model="activeType" @change="handleStudentTypeChange">
      <el-radio-button v-for="(item, index) in studentTypeList" :key="index" :label="item.value">{{ item.label }}</el-radio-button>
    </el-radio-group>

    <div class="items-box">
      <div class="item">
        <el-checkbox v-model="form.admissionEducationSwitch">入院教育要求完成次数</el-checkbox>
        <el-input 
          placeholder="请输入次数" 
          v-model="form.admissionEducation" 
          type="number"
          @input="(val) => handleInput(val, 'admissionEducation')" 
          style="width: 150px;"
        >
          <template slot="suffix">次</template>
        </el-input>
      </div>
      <div class="item">
        <el-checkbox v-model="form.professionalBaseEducationSwitch">入专业基地教育要求参与次数</el-checkbox>
        <el-input 
          placeholder="请输入次数" 
          v-model="form.professionalBaseEducation" 
          type="number"
          @input="(val) => handleInput(val, 'professionalBaseEducation')" 
          style="width: 150px;"
        >
          <template slot="suffix">次</template>
        </el-input>
      </div>
      <div class="item">
        <el-checkbox v-model="form.rotationDepartmentSwitch">轮转科室完成率要求</el-checkbox>
        <el-input 
          placeholder="请输入完成率" 
          v-model="form.rotationDepartment" 
          type="number"  
          @input="(val) => handlePercentInput(val, 'rotationDepartment')" 
          style="width: 150px;"
        >
          <template slot="suffix">%</template>
        </el-input>
      </div>
      <div class="item">
        <el-checkbox v-model="form.rotationDataSwitch">轮转数据完成率要求</el-checkbox>
        <el-input 
          placeholder="请输入完成率" 
          v-model="form.rotationData" 
          type="number"
          @input="(val) => handlePercentInput(val, 'rotationData')" 
          style="width: 150px;"
        >
          <template slot="suffix">%</template>
        </el-input>
      </div>
      <div class="item">
        <el-checkbox v-model="form.teachingActiveSwitch">教学活动参与次数要求</el-checkbox>
        <el-input 
          placeholder="请输入次数" 
          v-model="form.teachingActive" 
          type="number"
          @input="(val) => handleInput(val, 'teachingActive')" 
          style="width: 150px;"
        >
          <template slot="suffix">次</template>
        </el-input>
      </div>
      <div class="item">
        <el-checkbox v-model="form.hospitalTrainingSwitch">院级培训参与次数要求</el-checkbox>
        <el-input 
          placeholder="请输入次数" 
          v-model="form.hospitalTraining" 
          type="number"
          @input="(val) => handleInput(val, 'hospitalTraining')" 
          style="width: 150px;"
        >
          <template slot="suffix">次</template>
        </el-input>
      </div>
      <div class="item">
        <el-checkbox v-model="form.annualAssessmentSwitch">年度考核合格次数要求</el-checkbox>
        <el-input 
          placeholder="请输入次数" 
          v-model="form.annualAssessment" 
          type="number"
          @input="(val) => handleInput(val, 'annualAssessment')" 
          style="width: 150px;"
        >
          <template slot="suffix">次</template>
        </el-input>
      </div>
      <div class="item">
        <el-checkbox v-model="form.qualificationCertificateSwitch">要求取得执业医师资格证书：</el-checkbox>
        <!-- <span style="font-size: 14px;">要求取得执业医师资格证书：</span> -->
        <el-radio-group v-model="form.qualificationCertificate">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </div>

      <div class="btns">
        <el-button type="primary" @click="submitForm">保存后生效</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  updateGraduationRequire,
  getGraduationRequireList
} from "@/api/rotation/graduationRequire";

export default {
  name: "GraduationRequire",
  components: {
  },
  data() {
    return {
      // 学员类型
      studentTypeList: [],
      activeType: "1",
      form: {},
      
    };
  },
  created() {
    this.studentTypeList = this.getDictDatas(this.DICT_TYPE.SYSTEM_STUDENT_TYPE);
    console.log('学员类型===', this.studentTypeList)
    this.activeType = this.studentTypeList[0]?.value;
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getGraduationRequireList().then(response => {
        this.list = response.data;
        this.form = this.list.find(item => item.studentType == this.activeType)
        this.loading = false;
      });
    },

    /** 指标类型改变 */
    handleStudentTypeChange(val) {
      this.form = this.list.find(item => item.studentType == val)
    },

    handleInput(val, field) {
      // 将用户输入的内容强制转换为整数
      this.form[field] = val.replace(/[^\d]/g, '');
    },

    handlePercentInput(val, field){
      // 使用正则表达式限制输入
      const regex = /^\d+(\.\d{1,2})?$/;
      if (!regex.test(val) || val > 100) {
        // 如果输入不符合规则，则设置为上一个合法的值
        this.form[field] = val.substring(0, val.length - 1);
      }
    },
    
    /** 提交按钮 */
    submitForm() {
      updateGraduationRequire(this.form).then(response => {
        if (response.code == 0) {
          this.$message({
            message: '保存成功',
            type: 'success'
          });
          this.getList()
        }
        
      });
    }
    
  }
};
</script>

<style lang="scss" scoped>
.items-box{
  padding: 20px;

  .item{
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .el-input{
      margin-left: 10px;
    }

    
  }

  ::v-deep .el-input__suffix{
    top: 8px;
  }

  .btns{
    margin-top: 50px;
  }
}
</style>
