import request from '@/utils/request'

// 创建标准科室
export function createStandardDepartment(data) {
  return request({
    url: '/system/standard-department/create',
    method: 'post',
    data: data
  })
}

// 更新标准科室
export function updateStandardDepartment(data) {
  return request({
    url: '/system/standard-department/update',
    method: 'put',
    data: data
  })
}

// 删除标准科室
export function deleteStandardDepartment(id) {
  return request({
    url: '/system/standard-department/delete?id=' + id,
    method: 'delete'
  })
}

// 获得标准科室
export function getStandardDepartment(id) {
  return request({
    url: '/system/standard-department/get?id=' + id,
    method: 'get'
  })
}

// 获得标准科室分页
export function getStandardDepartmentPage(query) {
  return request({
    url: '/system/standard-department/page',
    method: 'get',
    params: query
  })
}

// 导出标准科室 Excel
export function exportStandardDepartmentExcel(query) {
  return request({
    url: '/system/standard-department/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取标准科室精简信息列表
export function getStandardDepartmentSimpleList(parentId) {
  return request({
    url: '/system/standard-department/list-all-simple',
    method: 'get',
    params: { parentId },
  })
}
