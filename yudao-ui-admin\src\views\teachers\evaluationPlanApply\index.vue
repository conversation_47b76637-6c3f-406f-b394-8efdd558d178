<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评优项目" prop="teachersEvaluationProject">
        <el-select
          v-model="queryParams.teachersEvaluationProject"
          placeholder="请选择评优项目"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.TEACHERS_EVALUATION_PROJECT
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请状态" prop="applyResult">
        <el-select
          v-model="queryParams.applyResult"
          placeholder="请选择申请状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.TEACHERS_EVALUATION_APPLY_RESULT
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['teachers:evaluation-plan-apply:create']"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['teachers:evaluation-plan-apply:export']"
        >
          导出
        </el-button>
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="项目名称" align="center" prop="name" />
      <el-table-column label="评优年度" align="center" prop="year" />
      <el-table-column
        label="评优项目"
        align="center"
        prop="teachersEvaluationProject"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHERS_EVALUATION_PROJECT"
            :value="scope.row.teachersEvaluationProject"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="申报开始日期"
        align="center"
        prop="declareStartDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.declareStartDate }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="申报截止日期"
        align="center"
        prop="declareEndDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.declareEndDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请状态" align="center" prop="applyResult">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHERS_EVALUATION_APPLY_RESULT"
            :value="scope.row.applyResult"
          />
        </template>
      </el-table-column>
      <el-table-column label="遴选结果" align="center" prop="selectionResult">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHERS_EVALUATION_SELECTION_RESULT"
            :value="scope.row.selectionResult"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.applyResult === '0'"
            size="mini"
            type="text"
            @click="handleViewRemarks(scope.row)"
            v-hasPermi="['teachers:evaluation-plan-apply:create']"
          >
            发起申请
          </el-button>
          <el-button
            v-if="scope.row.applyResult !== '0'"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row, 'view')"
            v-hasPermi="['teachers:evaluation-plan-apply:query']"
          >
            查看申请
          </el-button>
          <el-button
            v-if="scope.row.applyResult !== '0'"
            size="mini"
            type="text"
            @click="viewAudit(scope.row)"
            v-hasPermi="['teachers:evaluation-plan-apply:create']"
          >
            审批进度
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      title="遴选说明"
      :visible.sync="openRemarks"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <div>{{ planDetails.remarks }}</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleApply">
          我已知悉，继续申请
        </el-button>
        <el-button @click="cancelApply"> 我再想想 </el-button>
      </div>
    </el-dialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="项目名称" prop="name">
              <span>{{ form.name }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="评选年度" prop="year">
              <span>{{ form.year }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="评优项目" prop="teachersEvaluationProject">
              <dict-tag
                :type="DICT_TYPE.TEACHERS_EVALUATION_PROJECT"
                :value="form.teachersEvaluationProject"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="申报开始日期" prop="declareDate">
              <span>{{ form.declareStartDate }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="申报截止日期" prop="declareDate">
              <span>{{ form.declareEndDate }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="申请表模板"
              prop="applicationDocumentTemplates"
            >
              <FileUpload
                v-model="form.applicationDocumentTemplates"
                :limit="1"
                :fileSize="50"
                :fileType="['doc', 'pdf']"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="评优内容" prop="content">
              <el-input
                v-model="form.content"
                type="textarea"
                placeholder="请输入评优内容"
                :disabled="opt === 'view'"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="遴选申请材料" prop="applicationDocument">
              <FileUpload
                v-model="form.applicationDocument"
                :limit="1"
                :fileSize="50"
                :fileType="['pdf']"
                :disabled="opt === 'view'"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="submitForm"
          v-if="opt !== 'view'"
        >
          确 定
        </el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import {
  createEvaluationPlanApply,
  updateEvaluationPlanApply,
  deleteEvaluationPlanApply,
  getEvaluationPlanApply,
  getEvaluationPlanApplyPage,
  exportEvaluationPlanApplyExcel,
  getEvaluationPlanDetails,
} from "@/api/teachers/evaluationPlanApply";

export default {
  name: "EvaluationPlanApply",
  components: { FileUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 师资评优计划申请列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openRemarks: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        teachersEvaluationProject: null,
        applyResult: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        content: [
          { required: true, message: "评优内容不能为空", trigger: "blur" },
        ],
        applicationDocument: [
          {
            required: true,
            message: "遴选申请材料不能为空",
            trigger: "change",
          },
        ],
      },
      curRow: {},
      planDetails: {},
      opt: "",
      submitLoading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getEvaluationPlanApplyPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        teachersEvaluationPlanId: undefined,
        applyUserId: undefined,
        applyTime: undefined,
        applicationDocument: undefined,
        applyResult: undefined,
        processInstanceId: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加师资评优计划申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row, opt) {
      this.reset();
      const id = row.id;
      this.opt = opt;
      getEvaluationPlanApply(id).then((response) => {
        const details = response.data || {};
        const { planApplyRespVO, planRespVO } = details;
        if (planApplyRespVO.applicationDocument) {
          try {
            planApplyRespVO.applicationDocument = JSON.parse(
              planApplyRespVO.applicationDocument
            );
          } catch (error) {
            planApplyRespVO.applicationDocument = [];
          }
        }
        if (planRespVO.applicationDocumentTemplates) {
          try {
            planRespVO.applicationDocumentTemplates = JSON.parse(
              planRespVO.applicationDocumentTemplates
            );
          } catch (error) {
            planRespVO.applicationDocumentTemplates = [];
          }
        }
        const data = Object.assign({}, planRespVO, planApplyRespVO);
        this.form = data;
        console.log("查看评优申请====", this.form);
        this.open = true;
        this.title = `查看评优申请`;
      });
    },
    viewAudit(row) {
      this.$router.push({
        path: "/bpm/process-instance/detail",
        query: { id: row.processInstanceId },
      });
    },
    handleViewRemarks(row) {
      this.curRow = row;
      this.opt = "";
      this.title = "评优申请";
      getEvaluationPlanDetails({
        teachersEvaluationPlanId: row.teachersEvaluationPlanId,
      }).then((response) => {
        this.planDetails = response.data;
        this.form = response.data;
        if (this.form.applicationDocumentTemplates) {
          try {
            this.form.applicationDocumentTemplates = JSON.parse(
              this.form.applicationDocumentTemplates
            );
          } catch (error) {
            this.form.applicationDocumentTemplates = [];
          }
        }
        this.openRemarks = true;
      });
    },
    handleApply(row) {
      this.cancelApply();
      this.open = true;
    },
    cancelApply() {
      this.openRemarks = false;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        this.submitLoading = true;
        const params = {
          teachersEvaluationPlanId: this.curRow.teachersEvaluationPlanId,
          content: this.form.content,
          applicationDocument: this.form.applicationDocument
            ? JSON.stringify(this.form.applicationDocument)
            : "",
        };
        // 添加的提交
        createEvaluationPlanApply(params)
          .then((response) => {
            this.submitLoading = false;
            this.$modal.msgSuccess("申请成功");
            this.open = false;
            this.getList();
          })
          .catch(() => {
            this.submitLoading = false;
          });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除师资评优计划申请编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteEvaluationPlanApply(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有师资评优计划申请数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportEvaluationPlanApplyExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "师资评优计划申请.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>
