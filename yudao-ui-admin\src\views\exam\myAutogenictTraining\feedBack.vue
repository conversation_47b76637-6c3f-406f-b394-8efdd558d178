<template>
  <el-dialog
    title="错题反馈"
    :visible="open"
    width="800px"
    v-dialogDrag
    append-to-body
    @close="cancel"
  >
    <div class="feedback-item">
      <span class="label">试题目录：</span>
      <span class="val">{{ pointPath }}</span>
    </div>
    <div class="feedback-item">
      <span class="label">试题内容：</span>
      <question-view :curQuestion="curQuestion" />
    </div>
    <el-form
      :model="feedbackForm"
      :rules="feedbackRules"
      ref="feedbackForm"
      label-width="100px"
    >
      <el-form-item label="点评反馈" prop="replyContent">
        <el-input
          type="textarea"
          autosize
          v-model="feedbackForm.replyContent"
          placeholder="请在此处输入您的点评/反馈内容"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitFeedback">提交</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import TextToHtml from "../components/TextToHtml";
import QuestionView from "../components/QuestionView";
import { createFeedback } from "@/api/exam/myAutogenictTraining";

export default {
  name: "FeedBackDialog",
  components: { TextToHtml, QuestionView },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    curQuestion: {
      type: Object,
      default: null,
    },
    current: {
      type: Number,
    },
    pointPath: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      feedbackForm: {
        replyContent: "",
      },
      feedbackRules: {
        replyContent: [
          { required: true, message: "请输入点评/反馈内容", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    open(val) {
      if (val) {
        this.feedbackForm.replyContent = "";
      }
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("setOpen", false);
    },
    submitFeedback() {
      this.$refs["feedbackForm"].validate((valid) => {
        if (!valid) {
          return;
        }

        const params = {
          questionId: this.curQuestion.id,
          feedbackContent: this.feedbackForm.replyContent,
        };

        createFeedback(params).then((res) => {
          this.$message.success("提交成功");
          this.$emit("setOpen", false);
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.feedback-item {
  margin-bottom: 10px;
  .label {
    font-weight: bold;
  }
}

.question-item {
  margin-bottom: 30px;
  font-size: 15px;
  position: relative;
  padding-right: 35px;
  margin-top: 10px;

  .question-item-header {
    .type {
      color: dodgerblue;
    }
    .score {
      color: #999;
    }
  }
  .question-item-cont {
    padding-top: 15px;
    padding-left: 25px;
    .el-radio {
      display: block;
      margin-bottom: 10px;
      width: fit-content;
    }
    .el-checkbox {
      display: block;
      margin-bottom: 10px;
      width: fit-content;
    }
    .wrong-select {
      color: #f56c6c;

      ::v-deep .el-radio__inner,
      ::v-deep .el-checkbox__inner {
        border-color: #f56c6c;
        background: #f56c6c;
      }
      ::v-deep .el-radio__label,
      ::v-deep .el-checkbox__label {
        color: #f56c6c;
      }

      ::v-deep .el-input__inner {
        color: #f56c6c;
        border-color: #f56c6c;
      }

      ::v-deep .el-select__caret {
        color: #f56c6c;
      }
    }
  }
  .question-item-answer {
    padding: 10px 0 0 25px;
  }
  .question-item-analysis {
    padding: 10px 0 0 25px;
  }
  .el-radio-group {
    display: block;
  }

  .collect-box {
    position: absolute;
    display: inline-block;
    right: 10px;
    top: 10px;

    i {
      color: #409eff;
      font-size: 20px;
    }
  }
}
</style>
