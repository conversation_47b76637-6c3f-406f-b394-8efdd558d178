<template>
  <div class="notice-foreign">
    <div class="header-bar">
      <img v-if="logo" class="header-logo" :src="require(`../../../assets/logo/${logo}.png`)" />
      <h1 class="header-title">{{ title }} </h1>
    </div>
    <div class="content-wrapper" v-if="$route.query.id">
      <div class="notice-detail">
        <notice-view />
      </div>
    </div>
    <div class="content-wrapper" v-else>
      <h2 class="content-title">通知公告</h2>
      <div class="notice-wrapper">
        <el-table :data="noticeList">
          <el-table-column label="标题" prop="title" min-width="400">
            <template v-slot="scope">
              <el-link class="notice-title" @click="handleToView(scope.row.id)">{{ scope.row.title }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="发布日期" prop="createTime" align="center"></el-table-column>
          <el-table-column label="浏览次数" prop="readCount" align="center"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                    @pagination="queryNoticeData"/>
      </div>
    </div>
    <div class="footer-text">{{ footerText }}</div>
  </div>
</template>

<script>
import { getForeignNoticeInfoPage } from '@/api/rotation/noticeInfo';
import NoticeView from "@/views/rotation/noticeView";

export default {
  name: 'notice-foreign',
  components: { NoticeView },
  data() {
    return {
      logo: process.env.VUE_APP_PAGE_LOGO,
      title: process.env.VUE_APP_PAGE_TITLE,
      footerText: process.env.VUE_APP_FOOTER,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
      },
      total: 0,
      noticeList: [],
    }
  },
  methods: {
    queryNoticeData() {
      getForeignNoticeInfoPage(this.queryParams).then(res => {
        this.noticeList = res.data.list;
        this.total = res.data.total;
      });
    },
    handleToView(id) {
      const { href } = this.$router.resolve(`/notices?id=${id}`);
      window.open(href, "_blank");
    },
  },
  created() {
    this.queryNoticeData();
  },
}
</script>

<style lang="scss" scoped>
.notice-foreign {
  background: #F6F8F9;
  height: 100vh;
  overflow: auto;
}

.header-bar {
  height: 70px;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 20px;
  .header-logo {
    height: 80%;
  }
}

.content-wrapper {
  width: 90%;
  max-width: 1600px;
  margin: 0 auto;

  .content-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .notice-wrapper {
    padding: 40px;
    background: #fff;
  }

  .notice-detail {
    background: #fff;
    margin-top: 40px;
    min-height: calc(100vh - 180px);
  }
}

.footer-text {
  height: 16px;
  line-height: 16px;
  font-size: 12px;
  color: #8c8c8c;
  width: 100%;
  text-align: center;
  padding: 30px 0 40px 0;
  a,
  a:hover,
  a:active {
    color: inherit;
    text-decoration: none;
  }
}

.notice-title {
  display: initial;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
