import request from '@/utils/request'

// 获得招录报名待审核分页
export function getRegistrationexaminePage(query) {
    return request({
      url: '/recruitment/enrollexamine/page',
      method: 'get',
      params: query
    })
}

// 获得招录报名审核通过、不通过分页
export function getRegistrationexaminePageDone(query) {
    return request({
      url: '/recruitment/enrollexamine/page-done',
      method: 'get',
      params: query
    })
}

// 通过任务
export function approve(data) {
    return request({
      url: '/recruitment/enrollexamine/approve',
      method: 'put',
      data: data
    })
}

// 不通过任务
export function reject(data) {
    return request({
      url: '/recruitment/enrollexamine/reject',
      method: 'put',
      data: data
    })
}

// 退回修改，取消流程实例
export function backRegistration(data) {
    return request({
      url: '/recruitment/enrollexamine/back',
      // url: '/recruitment/enrollexamine/cancel',
      method: 'put',
      data: data
    })
  }

// 批量审批
export function batchExamine(data) {
  return request({
    url: '/recruitment/enrollexamine/batch-examine',
    method: 'put',
    data: data
  })
}
