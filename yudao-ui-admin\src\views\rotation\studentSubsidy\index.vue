<template>
  <div class="app-container studentSubsidy">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="轮转科室" prop="rotationDepartmentId">
        <el-select
          v-model="queryParams.rotationDepartmentId"
          placeholder="请选择轮转科室"
          clearable
          filterable
        >
          <el-option
            v-for="item in rotationDepartmentList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          clearable
          filterable
          @change="handleStudentTypeChange"
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select v-model="queryParams.major" clearable filterable>
          <el-option
            v-for="item in majorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="grade in studentGradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="派送单位" prop="dispatchingUnit">
        <el-input
          v-model="queryParams.dispatchingUnit"
          placeholder="请输入派送单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="补贴月份" prop="generateDate">
        <el-date-picker
          v-model="queryParams.generateDate"
          type="month"
          value-format="yyyy-MM"
          format="yyyy-MM"
          placeholder="选择月"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:student-subsidy:export']"
        >
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-warning-outline"
          size="mini"
          @click="handleAttention"
        >
          注意事项
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        fixed
        label="月份"
        align="center"
        width="100"
        prop="generateDate"
      />
      <el-table-column
        fixed
        label="轮转科室"
        align="center"
        width="120"
        prop="rotationDeptNames"
      />
      <el-table-column
        fixed
        label="姓名"
        align="center"
        width="100"
        prop="nickname"
      />
      <el-table-column
        fixed
        label="用户名"
        align="center"
        width="100"
        prop="username"
      />
      <el-table-column label="学员类型" width="120" prop="studentType">
        <template slot-scope="scope">
          {{
            getDictDataLabel(
              DICT_TYPE.SYSTEM_STUDENT_TYPE,
              scope.row.studentType
            )
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="培训专业"
        align="center"
        width="120"
        prop="majorName"
      />
      <el-table-column label="年级" align="center" width="100" prop="grade" />
      <el-table-column
        label="带教老师"
        align="center"
        width="120"
        prop="techerNames"
      />
      <el-table-column
        label="请假天数"
        align="center"
        width="100"
        prop="leaveDay"
      />
      <el-table-column
        label="住宅补贴"
        align="center"
        width="160"
        prop="housingSubsidy"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            type="number"
            v-model="scope.row.housingSubsidy"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
            @input="handleInput(scope.row, 'housingSubsidy')"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="生活补助"
        align="center"
        width="160"
        prop="livingAllowance"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            type="number"
            v-model="scope.row.livingAllowance"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
            @input="handleInput(scope.row, 'livingAllowance')"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="独立夜班费"
        align="center"
        width="180"
        prop="independentNightShiftFee"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            type="number"
            v-model="scope.row.independentNightShiftFee"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
            @input="handleInput(scope.row, 'independentNightShiftFee')"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="考勤奖"
        align="center"
        width="160"
        prop="attendanceAward"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            type="number"
            v-model="scope.row.attendanceAward"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
            @input="handleInput(scope.row, 'attendanceAward')"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="工作业绩及工作表现奖"
        align="center"
        width="200"
        prop="workPerformanceAward"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            type="number"
            v-model="scope.row.workPerformanceAward"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
            @input="handleInput(scope.row, 'workPerformanceAward')"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="应发金额"
        align="center"
        width="120"
        prop="grossAmount"
      >
        <template slot-scope="scope">{{ scope.row.grossAmount }}</template>
      </el-table-column>
      <el-table-column
        label="扣发金额"
        align="center"
        width="160"
        prop="deductionAmount"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            type="number"
            v-model="scope.row.deductionAmount"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
            @input="handleInput(scope.row, 'deductionAmount')"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="实发金额"
        align="center"
        width="120"
        prop="actualAmount"
      >
        <template slot-scope="scope">{{ scope.row.actualAmount }}</template>
      </el-table-column>
      <el-table-column
        label="税前金额"
        align="center"
        width="160"
        prop="preTaxAmount"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            type="number"
            v-model="scope.row.preTaxAmount"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
            @input="handleInput(scope.row, 'preTaxAmount')"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="未全奖原因"
        align="center"
        width="180"
        prop="notFullAwardReason"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.notFullAwardReason"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="扣发原因"
        align="center"
        width="180"
        prop="deductionReason"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.deductionReason"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        align="center"
        width="180"
        prop="remarks"
        class="scroll-columm"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.remarks"
            placeholder="请输入"
            clearable
            @blur="handleUpdate(scope.row)"
            @keyup.enter.native="handleUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="220"
        prop="file"
        class="scroll-columm"
      >
        <template slot="header" slot-scope="scope">
          <div>
            <span>附件</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="请上传大小不超过10M的PDF文件"
              placement="top-end"
            >
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </div>
        </template>
        <template slot-scope="scope">
          <FileUpload
            v-if="!scope.row.file || scope.row.file.length === 0"
            ref="fileUpload"
            v-model="scope.row.file"
            :limit="1"
            :fileSize="10"
            :fileType="['pdf']"
            :isShowTip="false"
            @input="handleUpdate(scope.row)"
            :showBtn="false"
          />
          <div v-else class="file-cell">
            <el-button
              type="text"
              v-for="item in scope.row.file"
              @click="viewPdf(item.url, item.name)"
            >
              {{ item.name }}
            </el-button>
            <i class="el-icon-delete" @click="delFile(scope.row)"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="登记人"
        align="center"
        width="120"
        prop="registeredNickname"
      />
      <!-- <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rotation:student-subsidy:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:student-subsidy:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <el-input
        type="textarea"
        v-model="form.precautions"
        placeholder="请输入注意事项"
        :autosize="{ minRows: 10, maxRows: 20 }"
        :readOnly="!hasPermission(['rotation:student-subsidy-config:save'])"
      />
      <div slot="footer" class="dialog-footer">
        <el-button
          v-hasPermi="['rotation:student-subsidy-config:save']"
          type="primary"
          @click="submitForm"
        >
          保 存
        </el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  updateStudentSubsidy,
  deleteStudentSubsidy,
  getStudentSubsidyPage,
  exportStudentSubsidyExcel,
  updateStudentSubsidyConfig,
  getStudentSubsidyPageConfig,
} from "@/api/rotation/studentSubsidy";
import { getRotationDepartmentSimpleList } from "@/api/system/department";
import { getSimpleMajorList } from "@/api/system/major";
import { getGradeByStudentType } from "@/api/system/userStudent";
import { getAccessToken } from "@/utils/auth";
import FileUpload from "@/components/FileUploadInfo";
import dayjs from "dayjs";

export default {
  name: "StudentSubsidy",
  components: { FileUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员轮转补助列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        rotationDepartmentId: null,
        nickname: "",
        username: "",
        studentType: null,
        major: "",
        grade: "",
        dispatchingUnit: "",
        generateDate: dayjs().format("YYYY-MM"),
      },
      // 表单参数
      form: {},
      rotationDepartmentList: [],
      majorList: [],
      studentGradeList: [],
    };
  },
  created() {
    this.getList();
    getRotationDepartmentSimpleList().then((res) => {
      this.rotationDepartmentList = res.data;
    });
  },
  methods: {
    hasPermission(perms) {
      const userPermissions = this.$store.getters.permissions;
      return perms.some((perm) => userPermissions.includes(perm));
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getStudentSubsidyPage(this.queryParams).then((response) => {
        const list = response.data.list || [];
        list.forEach((item) => {
          if (item.file) {
            item.file = JSON.parse(item.file);
          }
        });
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        precautions: undefined,
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 切换学员类型 */
    handleStudentTypeChange(val) {
      getSimpleMajorList({ studentTypes: val }).then((res) => {
        this.majorList = res.data;
        this.queryParams.major = "";
      });

      getGradeByStudentType({ studentType: val }).then((res) => {
        this.studentGradeList = res.data;
        this.queryParams.grade = "";
      });
    },
    handleInput(row, field) {
      let value = row[field];
      if (typeof value === "number") {
        value = value.toString();
      }
      // 处理输入，确保最多两位小数
      value = value.replace(/[^\d.]/g, ""); // 去除非数字和小数点的字符
      value = value.replace(/\.{2,}/g, "."); // 处理多个连续的小数点
      value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); // 确保只有一个小数点
      value = value.match(/^\d*(\.?\d{0,2})/)[0]; // 限制最多两位小数
      row[field] = value ? parseFloat(value) : null;
    },
    handleUpdate(row) {
      row.grossAmount =
        Number(row.housingSubsidy) +
        Number(row.livingAllowance) +
        Number(row.independentNightShiftFee) +
        Number(row.attendanceAward) +
        Number(row.workPerformanceAward);

      row.actualAmount = Number(row.grossAmount) - Number(row.deductionAmount);

      if (row.file) {
        row.file = JSON.stringify(row.file);
      }

      updateStudentSubsidy({
        ...row,
      }).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.getList();
      });
    },

    delFile(row) {
      row.file = [];
      updateStudentSubsidy({
        ...row,
        file: "",
      }).then((response) => {
        this.$modal.msgSuccess("删除成功");
        this.getList();
      });
    },

    viewPdf(url, name) {
      const token = getAccessToken();
      const path = url + "?token=" + token;
      window.open(path, "_blank");
    },
    handleAttention() {
      getStudentSubsidyPageConfig().then((response) => {
        this.form = response.data || {};
        this.open = true;
        this.title = "注意事项";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 注意事项提交
      updateStudentSubsidyConfig(this.form).then((response) => {
        this.$modal.msgSuccess("保存成功");
        this.open = false;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除学员轮转补助编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteStudentSubsidy(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有学员轮转补助数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportStudentSubsidyExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "学员轮转补助.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.studentSubsidy {
  .el-table__header-wrapper {
    th.el-table__cell > .cell {
      color: #064c8d;
    }
  }

  .el-table__body-wrapper {
    .cell {
      color: #064c8d;
    }
  }

  .file-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    .el-icon-delete {
      color: #f56c6c;
    }
  }
}
</style>
