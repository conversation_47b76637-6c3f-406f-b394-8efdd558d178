import request from '@/utils/request'

// 根据当前登录用户获得轮转手册
export function getUserManual() {
  return request({
    url: '/rotation/manual/list-by-currentuser',
    method: 'get'
  })
}

// 获得轮转手册步骤
export function getScheduleSteps(scheduleDetailsId) {
  return request({
    url: '/rotation/manual/get-by-scheduleDetailsId',
    method: 'get',
    params: { scheduleDetailsId }
  })
}

// 导出轮转手册
export function exportManualPdf() {
  return request({
    url: '/rotation/manual/get-manual-pdf',
    method: 'get',
    responseType: 'blob'
  })
}

// 获得入科教育文档
export function getEnrollmentEduDoc(scheduleDetailsId) {
  return request({
    url: '/rotation/manual/get-enrollment-edu-doc',
    method: 'get',
    params: { scheduleDetailsId }
  })
}

// 根据当前登录用户获得入科教育学习记录
export function getUserEnrollmentEduRecords(scheduleDetailsId) {
  return request({
    url: '/rotation/manual/list-enrollment-edu',
    method: 'get',
    params: { scheduleDetailsId }
  })
}

// 学习入科文档
export function studyEnrollmentEduDoc(scheduleDetailsId) {
  return request({
    url: '/rotation/manual/update-enrollment-edu-doc-study-status',
    method: 'put',
    params: { id: scheduleDetailsId }
  })
}

// 获得轮转项统计数据
export function getRotationItems(scheduleDetailsId) {
  return request({
    url: '/rotation/manual/get-total-rotation-items',
    method: 'get',
    params: { scheduleDetailsId }
  })
}

// 获得轮转子项项统计数据
export function getRotationSubItems(scheduleDetailsId, rotationItem) {
  return request({
    url: '/rotation/manual/get-total-rotation-sub-items',
    method: 'get',
    params: { scheduleDetailsId, rotationItem }
  })
}

// 创建登记数据
export function createRecordData(data) {
  return request({
    url: '/rotation/manual/create-daily-data',
    method: 'post',
    data
  })
}

// 获取登记数据列表
export function getRecordDataList(params) {
  return request({
    url: '/rotation/manual/get-daily-datas',
    method: 'get',
    params
  })
}

// 获取登记数据
export function getRecordData(id) {
  return request({
    url: '/rotation/manual/get-daily-data',
    method: 'get',
    params: { id }
  })
}

// 获得活动统计
export function getActiveStatistics(scheduleDetailsId) {
  return request({
    url: '/rotation/manual/get-active-statistics',
    method: 'get',
    params: { scheduleDetailsId }
  })
}

// 获得360评价统计
export function getAppraise360Statistics(scheduleDetailsId) {
  return request({
    url: '/rotation/manual/get-appraise-360-statistics',
    method: 'get',
    params: { scheduleDetailsId }
  })
}

// 获得出科技能考核
export function getGraduationAssessment(scheduleDetailsId) {
  return request({
    url: '/rotation/manual/get-graduation-assessment',
    method: 'get',
    params: { scheduleDetailsId }
  })
}

// 根据轮转手册查找拥有权限的教学秘书
export function getRotationTeachingSecretary(scheduleDetailsId) {
  return request({
    url: '/rotation/manual/get-rotation-teaching_secretary',
    method: 'get',
    params: { scheduleDetailsId }
  })
}
