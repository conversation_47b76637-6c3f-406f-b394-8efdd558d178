import request from "@/utils/request";

// 创建师资评优计划
export function createEvaluationPlan(data) {
  return request({
    url: "/teachers/evaluation-plan/create",
    method: "post",
    data: data,
  });
}

// 更新师资评优计划
export function updateEvaluationPlan(data) {
  return request({
    url: "/teachers/evaluation-plan/update",
    method: "put",
    data: data,
  });
}

// 删除师资评优计划
export function deleteEvaluationPlan(id) {
  return request({
    url: "/teachers/evaluation-plan/delete?id=" + id,
    method: "delete",
  });
}

// 获得师资评优计划
export function getEvaluationPlan(id) {
  return request({
    url: "/teachers/evaluation-plan/get?id=" + id,
    method: "get",
  });
}

// 获得师资评优计划分页
export function getEvaluationPlanPage(query) {
  return request({
    url: "/teachers/evaluation-plan/page",
    method: "get",
    params: query,
  });
}

// 导出师资评优计划 Excel
export function exportEvaluationPlanExcel(query) {
  return request({
    url: "/teachers/evaluation-plan/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 更新师资评优计划配置
export function updateConfig(data) {
  return request({
    url: "/teachers/evaluation-plan/update-config",
    method: "put",
    data: data,
  });
}

// 获得师资评优申请人分页
export function getEvaUserPage(query) {
  return request({
    url: "/teachers/evaluation-plan/page-user",
    method: "get",
    params: query,
  });
}
