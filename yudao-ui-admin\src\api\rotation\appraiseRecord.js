import request from "@/utils/request";

// 创建见习评价记录
export function createAppraiseRecord(data) {
  return request({
    url: "/rotation/appraise-record/create",
    method: "post",
    data: data,
  });
}

// 更新见习评价记录
export function updateAppraiseRecord(data) {
  return request({
    url: "/rotation/appraise-record/update",
    method: "put",
    data: data,
  });
}

// 删除见习评价记录
export function deleteAppraiseRecord(id) {
  return request({
    url: "/rotation/appraise-record/delete?id=" + id,
    method: "delete",
  });
}

// 获得见习评价记录
export function getAppraiseRecord(id) {
  return request({
    url: "/rotation/appraise-record/get?id=" + id,
    method: "get",
  });
}

// 获得见习评价记录分页
export function getAppraiseRecordPage(query) {
  return request({
    url: "/rotation/appraise-record/page",
    method: "get",
    params: query,
    headers: { component: "rotation/appraiseRecord/index" },
  });
}

// 导出见习评价记录 Excel
export function exportAppraiseRecordExcel(query) {
  return request({
    url: "/rotation/appraise-record/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
