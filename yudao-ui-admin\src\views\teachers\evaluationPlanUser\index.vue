<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请结果" prop="applyResult">
        <el-input v-model="queryParams.applyResult" placeholder="请输入申请结果" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['teachers:evaluation-plan-user:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['teachers:evaluation-plan-user:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="申请人员id" align="center" prop="userId" />
      <el-table-column label="申请时间" align="center" prop="applyTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applyTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请材料" align="center" prop="applicationDocument" />
      <el-table-column label="申请结果" align="center" prop="applyResult" />
      <el-table-column label="流程实例的编号" align="center" prop="processInstanceId" />
      <el-table-column label="遴选结果" align="center" prop="selectionResult" />
      <el-table-column label="遴选得分" align="center" prop="selectionScore" />
      <el-table-column label="遴选人员id" align="center" prop="selectionUserId" />
      <el-table-column label="遴选时间" align="center" prop="selectionTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.selectionTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['teachers:evaluation-plan-user:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['teachers:evaluation-plan-user:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请人员id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入申请人员id" />
        </el-form-item>
        <el-form-item label="申请时间" prop="applyTime">
          <el-date-picker clearable v-model="form.applyTime" type="date" value-format="timestamp" placeholder="选择申请时间" />
        </el-form-item>
        <el-form-item label="申请材料" prop="applicationDocument">
          <el-input v-model="form.applicationDocument" placeholder="请输入申请材料" />
        </el-form-item>
        <el-form-item label="申请结果" prop="applyResult">
          <el-input v-model="form.applyResult" placeholder="请输入申请结果" />
        </el-form-item>
        <el-form-item label="流程实例的编号" prop="processInstanceId">
          <el-input v-model="form.processInstanceId" placeholder="请输入流程实例的编号" />
        </el-form-item>
        <el-form-item label="遴选结果" prop="selectionResult">
          <el-input v-model="form.selectionResult" placeholder="请输入遴选结果" />
        </el-form-item>
        <el-form-item label="遴选得分" prop="selectionScore">
          <el-input v-model="form.selectionScore" placeholder="请输入遴选得分" />
        </el-form-item>
        <el-form-item label="遴选人员id" prop="selectionUserId">
          <el-input v-model="form.selectionUserId" placeholder="请输入遴选人员id" />
        </el-form-item>
        <el-form-item label="遴选时间" prop="selectionTime">
          <el-date-picker clearable v-model="form.selectionTime" type="date" value-format="timestamp" placeholder="选择遴选时间" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createEvaluationPlanUser, updateEvaluationPlanUser, deleteEvaluationPlanUser, getEvaluationPlanUser, getEvaluationPlanUserPage, exportEvaluationPlanUserExcel } from "@/api/teachers/evaluationPlanUser";

export default {
  name: "EvaluationPlanUser",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 师资评优计划申请人员列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        applyResult: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [{ required: true, message: "申请人员id不能为空", trigger: "blur" }],
        applyTime: [{ required: true, message: "申请时间不能为空", trigger: "blur" }],
        applicationDocument: [{ required: true, message: "申请材料不能为空", trigger: "blur" }],
        applyResult: [{ required: true, message: "申请结果不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getEvaluationPlanUserPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        userId: undefined,
        applyTime: undefined,
        applicationDocument: undefined,
        applyResult: undefined,
        processInstanceId: undefined,
        selectionResult: undefined,
        selectionScore: undefined,
        selectionUserId: undefined,
        selectionTime: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加师资评优计划申请人员";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getEvaluationPlanUser(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改师资评优计划申请人员";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateEvaluationPlanUser(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createEvaluationPlanUser(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除师资评优计划申请人员编号为"' + id + '"的数据项?').then(function() {
          return deleteEvaluationPlanUser(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有师资评优计划申请人员数据项?').then(() => {
          this.exportLoading = true;
          return exportEvaluationPlanUserExcel(params);
        }).then(response => {
          this.$download.excel(response, '师资评优计划申请人员.xls');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
