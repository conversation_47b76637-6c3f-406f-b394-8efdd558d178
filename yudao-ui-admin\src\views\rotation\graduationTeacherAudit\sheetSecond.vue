<template>
  <div>
    <h3 class="sheet-name">{{ scoreForm.formTitle }}</h3>

    <el-form class="form-info" style="margin-bottom: 20px" inline>
      <el-form-item style="margin-right: 30px" label="姓名：">{{ scoreForm.nickName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="专业：">{{ scoreForm.majorName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="派送单位：">{{ scoreForm.dispatchingUnit }}</el-form-item>
      <el-form-item label="轮转科室：">{{ scoreForm.rotationDepartmentName }}</el-form-item>
      <el-form-item label="轮转时间：">{{ scoreForm.rotationTime }}</el-form-item>
    </el-form>

    <table class="score-table">
      <tr>
        <th style="width: 10%"></th>
        <th style="width: 25%">考核内容</th>
        <th style="width: 45%">考核要求</th>
        <th style="width: 10%">满分</th>
        <th style="width: 10%">得分</th>
      </tr>
      <tr>
        <td rowspan="8">临床工作能力</td>
        <td>1.文书书写</td>
        <td>抽查病历、大病历、处方、医嘱等。</td>
        <td>5</td>
        <td>
          <el-input-number class="score-input" v-model="form.workAbilityFirstItemScore" :min="0" :max="5" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td>2.管理病种或操作种类</td>
        <td>培训内容细则求的病种或操作种类。每缺少一个扣2分(批准更换的不扣分)。</td>
        <td>20</td>
        <td>
          <el-input-number class="score-input" v-model="form.workAbilitySecondItemScore" :min="0" :max="20" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td>3.例数要求</td>
        <td>培训内容细则要求的例数。每缺少一例扣0.2分(批准更换的不扣分)。</td>
        <td>20</td>
        <td>
          <el-input-number class="score-input" v-model="form.workAbilityThirdItemScore" :min="0" :max="20" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td>4.应急处置</td>
        <td>急、危重病人的处置或抢救。</td>
        <td>5</td>
        <td>
          <el-input-number class="score-input" v-model="form.workAbilityFourthItemScore" :min="0" :max="5" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td>5.诊治能力</td>
        <td>常见病诊断和鉴别，每出现一个不掌握、不熟悉、不了解，各扣1分。</td>
        <td>5</td>
        <td>
          <el-input-number class="score-input" v-model="form.workAbilityFifthItemScore" :min="0" :max="5" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td>6.查房</td>
        <td>询问病情，检查病人，汇报病情及疑难问题，归纳上级医师的意见等能力。</td>
        <td>5</td>
        <td>
          <el-input-number class="score-input" v-model="form.workAbilitySixthItemScore" :min="0" :max="5" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
          <td>7.病例讨论</td>
        <td>掌握病例特点，分析深入，语言表达准确精练，推理逻辑性强。</td>
        <td>2</td>
        <td>
          <el-input-number class="score-input" v-model="form.workAbilitySeventhItemScore" :min="0" :max="2" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td>8.医疗事故</td>
        <td>发生医疗差错事故为0分</td>
        <td>8</td>
        <td>
          <el-input-number class="score-input" v-model="form.workAbilityEighthItemScore" :min="0" :max="8" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td colspan="2">培训登记考核手册</td>
        <td>记录完整、整洁</td>
        <td>2</td>
        <td>
          <el-input-number class="score-input" v-model="form.assessmentManualScore" :min="0" :max="2" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td colspan="2">阅读专业文献能力</td>
        <td>结合临床工作提交文献综述、论文发表、读书报告</td>
        <td>2</td>
        <td>
          <el-input-number class="score-input" v-model="form.readLiteratureScore" :min="0" :max="2" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td colspan="2">专业外语水平</td>
        <td>能阅读和笔译外文专业文献</td>
        <td>1</td>
        <td>
          <el-input-number class="score-input" v-model="form.languageAbilityScore" :min="0" :max="1" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td colspan="2">医德医风</td>
        <td>服务态度、工作责任、医疗作风、廉洁行医</td>
        <td>5</td>
        <td>
          <el-input-number class="score-input" v-model="form.medicalEthicsScore" :min="0" :max="5" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td colspan="2">工作表现和态度</td>
        <td>热爱本职工作，对工作认真负责，服务规范，团结同志，遵守医院和科室规章制度，服从领导</td>
        <td>5</td>
        <td>
          <el-input-number class="score-input" v-model="form.jobPerformanceScore" :min="0" :max="5" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td colspan="2">考勤</td>
        <td>组织纪律、有无旷工、迟到、早退、脱岗</td>
        <td>10</td>
        <td>
          <el-input-number class="score-input" v-model="form.attendanceScore" :min="0" :max="10" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td colspan="2">学术活动</td>
        <td>每参加科室及以上学术活动记0.1分，最高5分</td>
        <td>5</td>
        <td>
          <el-input-number class="score-input" v-model="form.academicActivityScore" :min="0" :max="5" :controls="false" :disabled="check" @change="handleChange"></el-input-number>
        </td>
      </tr>
      <tr>
        <td colspan="3">考核综合成绩</td>
        <td>100</td>
        <td>{{ form.comprehensiveScore }}</td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  name: 'sheet-second',
  components: {},
  props: {
    scoreForm: Object,
    check: Boolean,
  },
  data() {
    return {
      form: {}
    }
  },
  methods: {
    formValidate() {
      const isUndefinedOrNull = (val) => val === undefined || val === null;
      if (
        isUndefinedOrNull(this.form.workAbilityFirstItemScore) ||
        isUndefinedOrNull(this.form.workAbilitySecondItemScore) ||
        isUndefinedOrNull(this.form.workAbilityThirdItemScore) ||
        isUndefinedOrNull(this.form.workAbilityFourthItemScore) ||
        isUndefinedOrNull(this.form.workAbilityFifthItemScore) ||
        isUndefinedOrNull(this.form.workAbilitySixthItemScore) ||
        isUndefinedOrNull(this.form.workAbilitySeventhItemScore) ||
        isUndefinedOrNull(this.form.workAbilityEighthItemScore) ||
        isUndefinedOrNull(this.form.assessmentManualScore) ||
        isUndefinedOrNull(this.form.readLiteratureScore) ||
        isUndefinedOrNull(this.form.languageAbilityScore) ||
        isUndefinedOrNull(this.form.medicalEthicsScore) ||
        isUndefinedOrNull(this.form.jobPerformanceScore) ||
        isUndefinedOrNull(this.form.attendanceScore) ||
        isUndefinedOrNull(this.form.academicActivityScore)
      ) {
        this.$message.warning("请全部完成评分");
        return false;
      }
      return true;
    },
    handleChange() {
      this.form.comprehensiveScore = (+this.form.workAbilityFirstItemScore || 0) +
        (+this.form.workAbilitySecondItemScore || 0) +
        (+this.form.workAbilityThirdItemScore || 0) +
        (+this.form.workAbilityFourthItemScore || 0) +
        (+this.form.workAbilityFifthItemScore || 0) +
        (+this.form.workAbilitySixthItemScore || 0) +
        (+this.form.workAbilitySeventhItemScore || 0) +
        (+this.form.workAbilityEighthItemScore || 0) +
        (+this.form.assessmentManualScore || 0) +
        (+this.form.readLiteratureScore || 0) +
        (+this.form.languageAbilityScore || 0) +
        (+this.form.medicalEthicsScore || 0) +
        (+this.form.jobPerformanceScore || 0) +
        (+this.form.attendanceScore || 0) +
        (+this.form.academicActivityScore || 0);
      this.form.comprehensiveScore = this.form.comprehensiveScore.toFixed(1);
    },
  },
  created() {
    this.form = this.scoreForm;
  },
}
</script>

<style lang="scss" scoped>
.sheet-name {
  font-size: 15px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 10px;
}

.form-info {
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}

.score-table {
  line-height: 1.5;
  border-collapse: collapse;
  width: 100%;

  th, td {
    border: 1px solid #DCDFE6;
    text-align: center;
    padding: 7px 5px;
  }
}

.required-tag {
  color: red;
  padding-right: 2px;
}

.score-input {
  width: 80px;
}
</style>
