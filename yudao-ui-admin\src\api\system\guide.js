import request from '@/utils/request'

// 创建操作指引
export function createGuide(data) {
  return request({
    url: '/system/guide/create',
    method: 'post',
    data: data
  })
}

// 更新操作指引
export function updateGuide(data) {
  return request({
    url: '/system/guide/update',
    method: 'put',
    data: data
  })
}

// 删除操作指引
export function deleteGuide(id) {
  return request({
    url: '/system/guide/delete?id=' + id,
    method: 'delete'
  })
}

// 获得操作指引
export function getGuide(id) {
  return request({
    url: '/system/guide/get?id=' + id,
    method: 'get'
  })
}

// 获得操作指引分页
export function getGuidePage(query) {
  return request({
    url: '/system/guide/page',
    method: 'get',
    params: query
  })
}

// 导出操作指引 Excel
export function exportGuideExcel(query) {
  return request({
    url: '/system/guide/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
