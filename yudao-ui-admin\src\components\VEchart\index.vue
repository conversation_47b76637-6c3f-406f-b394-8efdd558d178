<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import { debounce } from '@/utils'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    options: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      sidebarElm: null,
      resizeHandler: null,
      chart: null
    }
  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.resizeHandler()
      }
    },
    initListener() {
      this.resizeHandler = debounce(() => {
        this.resize()
      }, 100)
      window.addEventListener('resize', this.resizeHandler)

      this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
      this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
    },
    destroyListener() {
      window.removeEventListener('resize', this.resizeHandler)
      this.resizeHandler = null

      this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)
    },
    resize() {
      const { chart } = this
      chart && chart.resize()
    },
    initChart() {
      this.chart = echarts.init(this.$el)
      this.chart.setOption(Object.assign({
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
      }, this.options))
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.initListener()
    })
  },
  beforeDestroy() {
    this.destroyListener()
    if (!this.chart) return
    this.chart.dispose()
    this.chart = null
  },
  activated() {
    if (!this.resizeHandler) {
      // avoid duplication init
      this.initListener()
    }

    // when keep-alive chart activated, auto resize
    this.resize()
  },
  deactivated() {
    this.destroyListener()
  },
}
</script>

<style lang="scss" scoped></style>
