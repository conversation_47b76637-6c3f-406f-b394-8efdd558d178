<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="14">
        <div class="col-head">
          <span>表单预览</span>
          <el-select v-model="formType" size="small">
            <el-option v-for="dict in formList" :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </div>

        <general-score-sheet :score-form="{ ...scoreForm }" v-if="formType === 'general_reco_score'"></general-score-sheet>
        <default-score-sheet :score-form="{ ...scoreForm }" v-if="formType === 'default_reco_score'"></default-score-sheet>
        <second-score-sheet :score-form="{ ...scoreForm }" v-if="formType === 'second_reco_score'"></second-score-sheet>
        <third-score-sheet :score-form="{ ...scoreForm }" v-if="formType === 'third_reco_score'"></third-score-sheet>
        <fourth-score-sheet :score-form="{ ...scoreForm }" v-if="formType === 'fourth_reco_score'"></fourth-score-sheet>
        <fifth-score-sheet :score-form="{ ...scoreForm }" v-if="formType === 'fifth_reco_score'"></fifth-score-sheet>
      </el-col>
      <el-col :span="10">
        <div class="col-head">表单配置</div>
        <el-table class="mb10" v-loading="loading" :data="list">
          <el-table-column label="选择表单" align="center" prop="rotationGraduationFormType">
            <template slot-scope="scope">
              <el-select v-model="scope.row.rotationGraduationFormType" placeholder="请选择出科考核评分表类型" clearable size="small">
                <el-option v-for="dict in formList" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="选择学员类型" align="center" prop="studentType">
            <template slot-scope="scope">
              <el-select v-model="scope.row.studentType" placeholder="请选择学员类型" clearable size="small">
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                           :key="dict.value" :label="dict.label" :value="Number(dict.value)"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="选择科室类型" align="center" prop="departmentTypes">
            <template slot-scope="scope">
              <el-select v-model="scope.row.departmentTypes" placeholder="请选择科室类型" clearable size="small" multiple>
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_DEPARTMENT_TYPE)"
                           :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="60">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="mb20">
          <el-link type="primary" icon="el-icon-plus" @click="addRow">新增一行</el-link>
        </div>
        <el-button type="primary" @click="handleSave">保存配置</el-button>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import { getAllGraduationFormConfig, saveGraduationFormConfig } from '@/api/rotation/graduationFormConfig'
import GeneralScoreSheet from "../graduationTeacherAudit/sheetGeneral";
import DefaultScoreSheet from "../graduationTeacherAudit/sheetSzlgrm";
import SecondScoreSheet from "../graduationTeacherAudit/sheetSecond";
import ThirdScoreSheet from "../graduationTeacherAudit/sheetThird";
import FourthScoreSheet from "../graduationTeacherAudit/sheetFourth";
import FifthScoreSheet from "../graduationTeacherAudit/sheetFifth";

export default {
  name: "GraduationFormConfig",
  components: {
    GeneralScoreSheet,
    DefaultScoreSheet,
    SecondScoreSheet,
    ThirdScoreSheet,
    FourthScoreSheet,
    FifthScoreSheet,
  },
  data() {
    return {
      formList: [],
      formType: "",
      loading: false,
      list: [],
      scoreForm: {
        formTitle: "出科考核表单",
        nickName: "张三",
        studentTypeName: "住院医师",
        majorName: "内科",
        dispatchingUnit: "市第三医院",
        rotationDepartmentName: "心血管内科",
        rotationTime: "2022-05-12",
      },
    };
  },
  created() {
    this.formList = this.getDictDatas(this.DICT_TYPE.ROTATION_GRADUATION_FORM_TYPE);
    this.formType = this.formList[0]?.value
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      getAllGraduationFormConfig().then(response => {
        this.list = response.data.map(row => ({
          ...row,
          departmentTypes: (row.departmentTypes || "").split(",")
        }));
        this.loading = false;
      });
    },
    addRow() {
      this.list.push({
        rotationGraduationFormType: "",
        studentType: "",
        departmentTypes: [],
      });
    },
    handleDelete(index) {
      this.list.splice(index, 1);
    },
    handleSave() {
      const hasUnfilled = this.list.some(row => !row.rotationGraduationFormType || !row.studentType || !row.departmentTypes.length);
      if (hasUnfilled) {
        this.$message.warning("存在未选择项，请全部选择后再提交～");
        return;
      }
      this.$confirm("保存配置生效后，后续学员将采用新的考核表单进行考核，确认保存配置吗？", "提示").then(() => {
        const params = this.list.map(row => ({ ...row, departmentTypes: row.departmentTypes.join(",") }));
        saveGraduationFormConfig(params).then(() => {
          this.$message.success("表单保存成功！");
        });
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.col-head {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;

  ::v-deep .el-select {
    margin-left: 20px;
  }
}
</style>
