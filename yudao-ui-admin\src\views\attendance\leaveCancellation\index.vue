<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学员姓名">
        <el-input v-model="queryParams.nickname" placeholder="请输入学员姓名"></el-input>
      </el-form-item>
      <el-form-item label="请假类型">
        <el-select v-model="queryParams.type" clearable>
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.BPM_OA_LEAVE_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="请假时长">
        <el-input-number
          class="input-number"
          v-model="queryParams.day"
          :min="0"
          :controls="false"
          placeholder="请输入"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="学员类型">
        <el-select v-model="queryParams.studentType" clearable>
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业">
        <el-select v-model="queryParams.major" clearable filterable>
          <el-option v-for="dict in majorList" :key="dict.code" :label="dict.name" :value="dict.code"/>
        </el-select>
      </el-form-item>
      <el-form-item label="年级">
        <el-input v-model="queryParams.grade" clearable placeholder="请输入年级"></el-input>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="queryParams.isToCancel">待销假</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="学员姓名" align="center" prop="nickname" width="120" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType"></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="专业" align="center" prop="majorName" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column label="请假类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.BPM_OA_LEAVE_TYPE" :value="scope.row.type"></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="120" />
      <el-table-column label="结束时间" align="center" prop="endTime" width="120" />
      <el-table-column label="请假时长（天）" align="center" prop="day" width="120" />
      <el-table-column label="轮转科室" align="center" prop="rotationDepartmentName" width="150" />
      <el-table-column label="返岗日期" align="center" prop="returnedDate" width="180"></el-table-column>
      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleCancellation(scope.row)"
                     v-hasPermi="['attendance:leave-cancellation:create']" v-if="!scope.row.id">销假</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleCancellation(scope.row, true)"
                     v-hasPermi="['attendance:leave-cancellation:query']" v-if="scope.row.id">销假详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="请假申请id" prop="leaveId" v-show="false">
          <el-input v-model="form.leaveId" placeholder="请输入请假申请id" />
        </el-form-item>
        <el-form-item label="学员姓名">
          <el-input :value="curRow.nickname" disabled></el-input>
        </el-form-item>
        <el-form-item label="请假时间">
          <el-date-picker type="daterange" :value="[curRow.startTime, curRow.endTime]" disabled></el-date-picker>
        </el-form-item>
        <el-form-item label="请假原因">
          <el-input type="textarea" :value="curRow.reason" disabled autosize></el-input>
        </el-form-item>
        <el-form-item label="是否正常返岗" prop="returned">
          <el-radio-group v-model="form.returned" :disabled="!!curRow.id">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="返岗日期" prop="returnedDate">
          <el-date-picker
            clearable
            v-model="form.returnedDate"
            type="date"
            value-format="timestamp"
            placeholder="选择返岗日期"
            :disabled="!!curRow.id"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!curRow.id">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createLeaveCancellation, updateLeaveCancellation, deleteLeaveCancellation, getLeaveCancellation, getLeaveCancellationPage, exportLeaveCancellationExcel } from "@/api/attendance/leaveCancellation";
import { getSimpleMajorList } from '@/api/system/major'

export default {
  name: "LeaveCancellation",
  components: {
  },
  data() {
    return {
      // 专业列表
      majorList: [],
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 销假列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        component: "attendance/leaveCancellation/index",
        nickname: "",
        type: undefined,
        day: undefined,
        isToCancel: true,
        studentType: undefined,
        grade: undefined,
        major: undefined,
      },
      // 操作行
      curRow: {},
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        leaveId: [{ required: true, message: "请假申请id不能为空", trigger: "blur" }],
        returnedDate: [{ required: true, message: "返岗日期不能为空", trigger: "blur" }],
        returned: [{ required: true, message: "是否正常返岗不能为空", trigger: "change" }],
      }
    };
  },
  created() {
    getSimpleMajorList().then(res => this.majorList = res.data);
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getLeaveCancellationPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 销假按钮操作 */
    handleCancellation(row) {
      this.curRow = row;
      this.form = {
        leaveId: row.leaveId,
        returned: row.returned === false ? 0 : 1,
        returnedDate: row.returnedDate ? new Date(row.returnedDate) : "",
      };
      this.open = true;
      this.title = row.id ? "销假详情" : "销假确认";
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        const data = { ...this.form, returned: this.form.returned === 1 };
        createLeaveCancellation(data).then(response => {
          this.$modal.msgSuccess("销假成功");
          this.open = false;
          this.getList();
        });
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.input-number ::v-deep .el-input__inner {
  text-align: left;
}
</style>
