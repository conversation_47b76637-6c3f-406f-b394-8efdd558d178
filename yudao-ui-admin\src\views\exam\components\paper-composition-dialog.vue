<template>
  <!-- 添加/修改考试试卷 -->
  <el-dialog :title="title" :visible.sync="open" width="800px" v-dialogDrag append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" v-if="open">
      <el-form-item label="试卷名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入试卷名称" />
      </el-form-item>
      <el-form-item class="half-item" label="组卷方式" prop="generationMethod">
        <el-radio-group v-model="form.generationMethod" :disabled="!!form.id">
          <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.EXAM_GENERATION_METHOD)"
                    :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.generationMethod === 'fixed'" class="half-item padding-left" label="是否乱序" prop="isFixedOutOrder">
        <el-radio-group v-model="form.isFixedOutOrder">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <div></div>
      <el-form-item class="half-item" label="及格分" prop="passScore">
        <el-input-number v-model="form.passScore" placeholder="请输入总分" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item class="half-item padding-left" label="答题时间" prop="answerMinuteTime">
        <el-input-number v-model="form.answerMinuteTime" placeholder="请输入答题时间" :min="0" controls-position="right" /> 分钟
      </el-form-item>
      <el-form-item class="half-item" label="开始时间" prop="beginTime">
        <el-date-picker style="width: 100%" clearable v-model="form.beginTime" type="datetime" value-format="timestamp" placeholder="选择开始时间" />
      </el-form-item>
      <el-form-item class="half-item padding-left" label="结束时间" prop="endTime">
        <el-date-picker style="width: 100%" clearable v-model="form.endTime" type="datetime" value-format="timestamp" placeholder="选择结束时间" />
      </el-form-item>

      <!-- 轮转、活动、模拟、院内考试表单差异部分 -->
      <template v-if="type === 'rotation'">
        <el-form-item class="half-item" label="考试类型" prop="examePaperType">
          <el-select style="width: 100%" v-model="form.examePaperType" placeholder="请选择考试类型">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_EXAM_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="half-item padding-left" label="学员类型" prop="studentTypes">
          <el-select
            style="width: 100%"
            :value="form.studentTypes ? form.studentTypes.split(',') : []"
            placeholder="请选择学员类型"
            filterable clearable multiple collapse-tags
            @input="val => form.studentTypes = val.join(',')"
            @change="handleStudentTypesChange"
          >
            <el-option v-for="dict in studentTypeList" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="half-item" label="专业名称" prop="majorIds">
          <el-select
            style="width: 100%"
            :value="form.majorIds ? form.majorIds.split(',') : []"
            placeholder="请选择专业名称"
            filterable clearable multiple collapse-tags
            @input="val => form.majorIds = val.join(',')"
          >
            <el-option v-for="dict in majorList" :key="dict.code" :label="dict.name" :value="dict.code" />
          </el-select>
        </el-form-item>
        <el-form-item class="half-item padding-left" label="年级" prop="grades">
          <el-select
            style="width: 100%"
            :value="form.grades ? form.grades.split(',') : []"
            placeholder="请选择年级"
            filterable multiple collapse-tags
            @input="val => form.grades = val.join(',')"
          >
            <el-option v-for="item in gradeList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item class="half-item" label="医院科室" prop="departmentIds" v-if="form.examePaperType === '1'">
          <el-select
            style="width: 100%"
            :value="form.departmentIds ? form.departmentIds.split(',') : []"
            placeholder="请选择医院科室"
            filterable clearable multiple collapse-tags
            @input="val => form.departmentIds = val.join(',')"
            @change="handleDepartmentChange"
          >
            <el-option v-for="item in departmentList" :key="item.id" :label="item.name" :value="String(item.id)" />
          </el-select>
        </el-form-item>
        <el-form-item class="half-item padding-left" label="轮转科室" prop="rotationDepartmentIds" v-if="form.examePaperType === '1'">
          <el-select
            style="width: 100%"
            :value="form.rotationDepartmentIds ? form.rotationDepartmentIds.split(',') : []"
            placeholder="请选择轮转科室"
            filterable clearable multiple collapse-tags
            @input="val => form.rotationDepartmentIds = val.join(',')"
          >
            <el-option v-for="item in rotationDepartmentList" :key="item.id" :label="item.name" :value="String(item.id)" />
          </el-select>
        </el-form-item>
      </template>

      <template v-if="type === 'active'">
        <el-form-item class="half-item" label="活动分类" prop="activeType">
          <el-select style="width: 100%" v-model="form.activeType" placeholder="请选择活动分类" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.EXAM_ACTIVE_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.activeType === 'hospital_training'" class="half-item padding-left"
                      label="培训类型" prop="hospitalTrainingType">
          <el-select style="width: 100%" v-model="form.hospitalTrainingType" placeholder="请选择培训类型" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-else class="half-item padding-left"
                      label="活动类型" prop="teachingActiveType">
          <el-select style="width: 100%" v-model="form.teachingActiveType" placeholder="请选择培训类型" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="half-item" label="医院科室" prop="departmentIds">
          <el-select
            style="width: 100%"
            :value="form.departmentIds ? form.departmentIds.split(',') : []"
            placeholder="请选择医院科室"
            filterable clearable multiple collapse-tags
            @input="val => form.departmentIds = val.join(',')"
          >
            <el-option v-for="item in departmentList" :key="item.id" :label="item.name" :value="String(item.id)" />
          </el-select>
        </el-form-item>
      </template>

      <template v-if="type === 'imitate'">
        <el-form-item class="half-item" label="考试类型" prop="examePaperType">
          <el-select style="width: 100%" v-model="form.examePaperType" placeholder="请选择考试类型" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.IMITATE_EXAM_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </template>

      <template v-if="type === 'hospital'">
        <el-form-item class="half-item" label="考试类型" prop="examePaperType">
          <el-select style="width: 100%" v-model="form.examePaperType" placeholder="请选择考试类型" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.HOSPITAL_EXAM_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </template>
      <!-- 差异结束 -->

      <el-form-item
        class="half-item"
        :style="type === 'rotation' ? { marginRight: '50%' } : { paddingLeft: '20px', paddingRight: 0 }"
        label="限制次数"
        prop="limitCount">
        <el-input-number v-model="form.limitCount" placeholder="请输入次数" :min="0" controls-position="right" />
        <el-tooltip class="ml10" content="不配置或配置为0，则不限制次数" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
      </el-form-item>
      <el-form-item class="half-item" label="作答后显示成绩" label-width="120px" prop="isShowScoreAfterAnswer">
        <el-switch v-model="form.isShowScoreAfterAnswer" @change="handleShowScoreChange"></el-switch>
      </el-form-item>
      <el-form-item class="half-item" label="允许试卷查看" label-width="120px" prop="isAllowView">
        <el-switch v-model="form.isAllowView" @change="handleAllowViewChange"></el-switch>
      </el-form-item>

      <el-form-item label="知识点" prop="pointIds">
        <div class="point-tree">
          <el-tree
            v-if="open"
            ref="pointTree"
            :data="pointTree"
            :props="{ label: 'name', disabled: form.id && form.generationMethod === 'fixed' ? 'name' : 'disabled' }"
            show-checkbox
            node-key="id"
            :default-checked-keys="(form.pointIds || '').split(',')"
            @check="handlePointChecked"
          ></el-tree>
        </div>
      </el-form-item>
      <template v-if="!(form.id && form.generationMethod === 'fixed')">
        <el-form-item label="试题类型" prop="questionTypes">
          <el-checkbox-group
            placeholder="请选择试题类型"
            :value="(form.questionTypes || []).map(item => item.questionType)"
          >
            <el-checkbox
              v-for="dict in this.getDictDatas(DICT_TYPE.EXAM_QUESTION_TYPE)"
              :key="dict.value"
              :label="dict.value"
              @change="handleQuestionTypeChange(dict.value, $event)"
            >{{ dict.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="出题类型" prop="questionSettingMethod">
          <el-radio-group v-model="form.questionSettingMethod" @change="handleQuestionMethodChange">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.EXAM_QUESTION_SETTING_METHOD)"
                      :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="questionTypes">
          <el-table v-if="form.questionSettingMethod === 'question_type'" :data="form.questionTypes" show-summary
                    :summary-method="getSummaries">
            <el-table-column label="题目类型" prop="questionType" align="center">
              <template v-slot="scope">
                {{ getDictDataLabel(DICT_TYPE.EXAM_QUESTION_TYPE, scope.row.questionType) }}
              </template>
            </el-table-column>
            <el-table-column label="可选题数" prop="count" align="center"></el-table-column>
            <el-table-column label="选择数" prop="questionNum" align="center">
              <template v-slot="scope">
                <el-input-number
                  style="width: 90px"
                  v-model="scope.row.questionNum"
                  :controls="false"
                  :min="0"
                  :max="scope.row.count"
                  @change="computeQuestionTotalScore(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="每题分值" prop="score" align="center">
              <template v-slot="scope">
                <el-input-number
                  style="width: 90px"
                  v-model="scope.row.score"
                  :controls="false"
                  :min="0"
                  @change="computeQuestionTotalScore(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="总分" prop="totalScore" align="center"></el-table-column>
          </el-table>

          <ul class="question-ul" :key="form.questionSettingMethod" v-else>
            <li v-for="item in form.questionTypes" :key="item.questionType">
              <div class="type-header">
                <strong class="mr5">{{ getDictDataLabel(DICT_TYPE.EXAM_QUESTION_TYPE, item.questionType) }}</strong>
                <span class="mr10">[共{{ item.count }}题]</span>
                每题分值
                <el-input-number v-model="item.score" :controls="false" :min="0"
                                 @change="handleTypeScoreChange(item, $event)"></el-input-number>
              </div>
              <el-table :data="form.questionSettingMethod === 'knowledge_point' ? item.questionTypeKnowledgePoints : item.questionTypeDifficulties">
                <el-table-column :label="form.questionSettingMethod === 'knowledge_point' ? '知识点' : '难度'">
                  <template #default="scope">
                    {{ form.questionSettingMethod === "knowledge_point" ? scope.row.knowledgePoint :
                    getDictDataLabel(DICT_TYPE.EXAM_DIFFICULTY_TYPE, scope.row.difficulty) }}
                  </template>
                </el-table-column>
                <el-table-column label="可选题数" prop="count" align="center"></el-table-column>
                <el-table-column label="选择数" prop="questionNum" align="center">
                  <template v-slot="scope">
                    <el-input-number
                      style="width: 90px"
                      v-model="scope.row.questionNum"
                      :controls="false"
                      :min="0"
                      :max="scope.row.count"
                      @change="handleQuestionNumChange(scope.row, item, $event)"
                    ></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="所占比例" prop="proportion" align="center" v-if="form.questionSettingMethod === 'knowledge_point'"></el-table-column>
                <el-table-column label="每题分值" prop="score" align="center"></el-table-column>
                <el-table-column label="总分" prop="totalScore" align="center"></el-table-column>
              </el-table>
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="生成试卷套数" label-width="100px">
          <el-input-number v-model="form.paperSize" controls-position="right" :min="1" :max="100" :step="1" step-strictly></el-input-number>
        </el-form-item>
      </template>

      <paper-question-list
        v-if="form.id && form.questionTypes.length > 0 && form.generationMethod === 'fixed'"
        :question-types="form.questionTypes"
        :is-view="isView"
      ></paper-question-list>

      <template v-if="form.paperConfig">
        <el-form-item label="试卷开放设置" label-width="100px">
          <el-checkbox-group
            :value="form.paperConfig.openingSettings ? form.paperConfig.openingSettings.split(',') : []"
            @input="val => form.paperConfig.openingSettings = val.join(',')"
          >
            <el-checkbox
              v-for="dict in this.getDictDatas(DICT_TYPE.EXAM_PAPER_OPEN_SETTING)"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label  }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="开启考前认证" label-width="100px">
          <el-radio-group v-model="form.paperConfig.isAuthentication">
            <el-radio :label="false">否</el-radio>
            <el-radio :label="true">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开启防作弊" label-width="100px">
          <el-radio-group v-model="form.paperConfig.isPreventionCheat">
            <el-radio :label="false">否</el-radio>
            <el-radio :label="true">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label-width="20px" v-if="form.paperConfig.isPreventionCheat">
          <div class="prevention-cheat-config">
            当系统检测到小程序切屏
            <el-input-number v-model="form.paperConfig.alarmScreenNum" :min="1" :step="1"
                             step-strictly controls-position="right"></el-input-number>
            次，提示内容
            <el-input v-model="form.paperConfig.alarmContent" placeholder="请在此处输入告警内容"></el-input>
          </div>
        </el-form-item>
        <el-form-item label-width="20px" v-if="form.paperConfig.isPreventionCheat">
          <div class="prevention-cheat-config">
            当系统检测到小程序切屏
            <el-input-number v-model="form.paperConfig.lockScreenNum" :min="1" :step="1"
                             step-strictly controls-position="right"></el-input-number>
            次，锁定屏幕并提示
            <el-input v-model="form.paperConfig.lockHint" placeholder="请在此处输入锁定提示"></el-input>
          </div>
        </el-form-item>
      </template>
    </el-form>
    <div slot="footer" class="dialog-footer" v-if="!isView">
      <el-button type="primary" :loading="submitting" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import PaperQuestionList from "./paper-question-list";
import { getKnowledgePointTree, getQuestionCount } from '@/api/exam/question'
import { createPaper, getPaper, updatePaper } from '@/api/exam/paper'
import { getDepartmentSimpleList } from '@/api/system/department'
import { getSimpleMajorList } from '@/api/system/major'
import { getCurrentUserDepts, getCurrentUserStudentTypes } from '@/api/system/permission'
import { getStudentGradeList } from '@/api/system/userStudent'

const typeMap = { rotation: "轮转", active: "活动", imitate: "模拟", hospital: "院内" };

export default {
  name: "paper-form-dialog",
  components: { PaperQuestionList },
  props: {
    type: {
      validator(value) {
        return ["rotation", "active", "imitate", "hospital"].includes(value);
      }
    }
  },
  data() {
    return {
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "试卷名称不能为空", trigger: "blur" }],
        generationMethod: [{ required: true, message: "组卷方式不能为空", trigger: "change" }],
        isFixedOutOrder: [{ required: true, message: "是否乱序不能为空", trigger: "change" }],
        passScore: [{ required: true, message: "及格分数不能为空", trigger: "blur" }],
        answerMinuteTime: [{ required: true, message: "答题时间不能为空", trigger: "blur" }],
        beginTime: [{ required: true, message: "开始时间不能为空", trigger: "blur" }],
        endTime: [{ required: true, message: "结束时间不能为空", trigger: "blur" }],
        examePaperType: [{ required: true, message: "试卷类型不能为空", trigger: "change" }],
        studentTypes: [{ required: true, message: "学员类型不能为空", trigger: "change" }],
        majorIds: [{ required: true, message: "专业名称不能为空", trigger: "change" }],
        departmentIds: [{ required: true, message: "医院科室不能为空", trigger: "change" }],
        activeType: [{ required: true, message: "活动分类不能为空", trigger: "change" }],
        hospitalTrainingType: [{ required: true, message: "培训类型不能为空", trigger: "change" }],
        teachingActiveType: [{ required: true, message: "活动类型不能为空", trigger: "change" }],
        pointIds: [{ required: true, message: "知识点不能为空", trigger: "blur" }],
        questionTypes: [{ required: true, message: "试题类型不能为空", trigger: "change" }],
        questionSettingMethod: [{ required: true, message: "出题类型不能为空", trigger: "change" }],
        totalScore: [{ required: true, message: "总分不能为空", trigger: "blur" }],
      },
      // 知识点树
      pointTree: [],
      // 学员类型列表
      studentTypeList: [],
      // 医院科室列表
      departmentList: [],
      // 轮转科室列表
      rotationDepartmentList: [],
      // 专业列表
      majorList: [],
      // 年级列表
      gradeList: [],
      // 查看试卷
      isView: false,
      // 提交中
      submitting: false,
    }
  },
  methods: {
    /** 查询知识点树 */
    getPointTree() {
      getKnowledgePointTree().then(res => this.pointTree = res.data);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = `添加${typeMap[this.type]}考卷`;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.title = `修改${typeMap[this.type]}考卷`;
      const id = row.id;
      getPaper(id, this.type).then(response => {
        const { paperQuestionTypes, ...rest } = response.data;
        this.form =  { ...this.form, ...rest, questionTypes: paperQuestionTypes };
        this.handleStudentTypesChange((this.form.studentTypes || "").split(','));
        getDepartmentSimpleList(this.form.departmentIds).then(res => {
          this.rotationDepartmentList = res.data
          this.getQuestionTypeCount();
          this.open = true;
        });
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.handleUpdate(row);
      this.title = `查看${typeMap[this.type]}考卷`;
      this.isView = true;
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        generationMethod: "random",
        isFixedOutOrder: true,
        passScore: undefined,
        answerMinuteTime: undefined,
        beginTime: undefined,
        endTime: undefined,
        examePaperType: undefined,
        studentTypes: undefined,
        majorIds: undefined,
        grades: undefined,
        departmentIds: undefined,
        rotationDepartmentIds: undefined,
        // 活动管理试卷配置
        activeType: undefined,  // 活动分类
        hospitalTrainingType: undefined, //  培训类型
        teachingActiveType: undefined, //  活动类型
        // 活动管理试卷配置
        limitCount: undefined,
        isShowScoreAfterAnswer: undefined,
        isAllowView: undefined,
        pointIds: undefined,
        questionTypes: [],
        questionSettingMethod: "question_type",
        paperSize: 1,
        totalScore: undefined,
        paperConfig: {
          openingSettings: undefined,
          isAuthentication: false,
          isPreventionCheat: false,
          alarmScreenNum: undefined,
          alarmContent: undefined,
          lockScreenNum: undefined,
          lockHint: undefined,
        },
      };
      this.isView = false;
      try {
        this.resetForm("form");
      } catch (e) {}
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        this.submitting = true;
        // 修改的提交
        if (this.form.id != null) {
          updatePaper(this.form, this.type).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.$emit("saved");
          }).finally(() => this.submitting = false);
          return;
        }
        // 添加的提交
        createPaper(this.form, this.type).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.$emit("saved");
        }).finally(() => this.submitting = false);
      });
    },
    /** 获取可选题数 */
    getQuestionTypeCount() {
      const { pointIds, questionTypes, questionSettingMethod } = this.form;
      if (!pointIds || questionTypes.length === 0 || !questionSettingMethod) return;
      getQuestionCount({
        pointIds: pointIds.split(","),
        questionTypes: questionTypes.map(item => item.questionType),
        questionSettingMethod,
      }).then(res => {
        if (questionSettingMethod === "question_type") {
          questionTypes.forEach(q => {
            const matched = res.data.find(item => item.questionType === q.questionType);
            this.$set(q, "count", matched?.count || 0);
          });
        } else if (questionSettingMethod === "knowledge_point") {
          questionTypes.forEach(q => {
            const matched = res.data.find(item => item.questionType === q.questionType);
            if (q.questionTypeKnowledgePoints && q.questionTypeKnowledgePoints.length > 0) {
              q.questionTypeKnowledgePoints.forEach(item => {
                const point = matched.knowledgePointCounts.find(_item => _item.id === item.knowledgePointId);
                this.$set(item, "count", point?.count || 0);
                this.$set(item, "knowledgePoint", point?.name || 0);
              })
            } else {
              this.$set(q, "questionTypeKnowledgePoints", matched.knowledgePointCounts.map(item => ({
                knowledgePointId: item.id,
                knowledgePoint: item.name,
                paperId: undefined,
                proportion: 0,
                questionNum: undefined,
                count: item.count,
                questionTypeId: undefined,
                score: q.score || 0,
                totalScore: 0,
              })));
            }
          })
        } else {
          questionTypes.forEach(q => {
            const matched = res.data.find(item => item.questionType === q.questionType);
            if (q.questionTypeDifficulties && q.questionTypeDifficulties.length > 0) {
              q.questionTypeDifficulties.forEach(item => {
                const difficulty = matched.difficultyCounts.find(_item => Number(_item.difficulty) === item.difficulty);
                this.$set(item, "count", difficulty?.count || 0);
                this.$set(item, "difficulty", difficulty?.difficulty ? Number(difficulty?.difficulty) : 0);
              });
            } else {
              this.$set(q, "questionTypeDifficulties", matched.difficultyCounts.map(item => ({
                difficulty: item.difficulty,
                paperId: undefined,
                proportion: 0,
                questionNum: undefined,
                count: item.count,
                questionTypeId: undefined,
                score: q.score || 0,
                totalScore: 0,
              })));
            }
          })
        }
      });
    },
    /** 知识点选择 */
    handlePointChecked() {
      this.$nextTick(() => {
        const ids = this.$refs.pointTree.getCheckedKeys(true);
        this.form.pointIds = ids.join(",");
        this.getQuestionTypeCount();
      });
    },
    /** 选择试题类型 */
    handleQuestionTypeChange(value, selected) {
      if (selected) {
        this.form.questionTypes.push({
          "paperId": undefined,
          "questionNum": 0,
          "questionType": value,
          "score": 0,
          "totalScore": 0,
          "count": 0,
        });
      } else {
        const index = this.form.questionTypes.findIndex(item => item.questionType === value);
        this.form.questionTypes.splice(index, 1);
      }
      this.getQuestionTypeCount();
    },
    /** 计算试题总分 */
    computeQuestionTotalScore(row) {
      row.totalScore = row.score * row.questionNum;
      let totalScore = 0;
      this.form.questionTypes.forEach(q => totalScore += q.totalScore);
      this.form.totalScore = totalScore;
    },
    /** 总计方法 */
    getSummaries({ columns, data }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "总计";
          return;
        }
        if (index === 3) {
          sums[index] = "-";
          return;
        }
        const values = data.map(item => item[column.property] ? Number(item[column.property]) : 0);
        sums[index] = values.reduce((prev, curr) => prev + curr, 0);
      });
      return sums;
    },
    /** 改变医院科室 */
    handleDepartmentChange() {
      this.form.rotationDepartmentIds = "";
      getDepartmentSimpleList(this.form.departmentIds).then(res => {
        this.rotationDepartmentList = res.data;
      });
    },
    /** 切换学员类型 */
    handleStudentTypesChange(val) {
      getSimpleMajorList({ studentTypes: val.join(',') }).then(res => {
        this.majorList = res.data;
        const allMajorIds = this.majorList.map(item => item.code);
        const majorIds = (this.form.majorIds || '').split(',');
        const matchedIds = majorIds.filter(id => allMajorIds.indexOf(id) > -1);
        this.form.majorIds = matchedIds.join(',');
      });
    },
    /** 改变作答后显示成绩状态 */
    handleShowScoreChange(val) {
      if (!val) {
        this.form.isAllowView = false;
      }
    },
    /** 改变允许试卷查看状态 */
    handleAllowViewChange(val) {
      if (val) {
        this.form.isShowScoreAfterAnswer = true;
      }
    },
    /** 出题类型切换 */
    handleQuestionMethodChange() {
      this.form.questionTypes.forEach(q => {
        this.$set(q, "questionTypeKnowledgePoints", []);
        this.$set(q, "questionTypeDifficulties", []);
      });
      this.getQuestionTypeCount();
    },
    /** 切换试题类型每题分数 */
    handleTypeScoreChange(item, value) {
      const list = this.form.questionSettingMethod === 'knowledge_point' ? item.questionTypeKnowledgePoints : item.questionTypeDifficulties;
      list.forEach(item => {
        item.score = value;
        item.totalScore = (item.questionNum || 0) * (value || 0);
      });
      item.totalScore = list.reduce((acc, cur) => acc + (cur.totalScore || 0), 0);
      this.form.totalScore = this.form.questionTypes.reduce((acc, cur) => acc + (cur.totalScore || 0), 0);
    },
    /** 每项选择数改变计算总分 */
    handleQuestionNumChange(row, item, value) {
      row.totalScore = (row.score || 0) * (value || 0);
      const list = this.form.questionSettingMethod === 'knowledge_point' ? item.questionTypeKnowledgePoints : item.questionTypeDifficulties;
      item.totalScore = list.reduce((acc, cur) => acc + (cur.totalScore || 0), 0);
      item.questionNum = list.reduce((acc, cur) => acc + (cur.questionNum || 0), 0);
      this.form.totalScore = this.form.questionTypes.reduce((acc, cur) => acc + (cur.totalScore || 0), 0);
      const total = list.reduce((acc, cur) => acc + (cur.questionNum || 0), 0);
      list.forEach(e => {
        e.proportion = total > 0 ? +((e.questionNum || 0) * 100 / total).toFixed(1) : 0;
      });
    },
  },
  created() {
    this.getPointTree();
    getCurrentUserStudentTypes({ 'component': `exam/${this.type}Paper/index` })
      .then(res => this.studentTypeList = res.data);
    getCurrentUserDepts({ 'component': `exam/${this.type}Paper/index` })
      .then(res => this.departmentList = res.data);
    getStudentGradeList().then(res => this.gradeList = res.data);
  }
}
</script>

<style lang="scss" scoped>
.half-item {
  display: inline-block;
  width: 50%;
  padding-right: 20px;
}

.half-item.padding-left {
  padding-right: 0;
  padding-left: 20px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
}

.point-tree {
  height: 200px;
  border: 1px solid rgb(220, 223, 230);
  border-radius: 4px;
  overflow: auto;
}

.prevention-cheat-config {
  display: flex;

  ::v-deep > .el-input-number {
    width: 120px;
    margin: 0 5px;
  }

  ::v-deep > .el-input {
    width: 300px;
    margin: 0 5px;
  }
}

.question-ul {
  padding: 0;
  margin: 0;
  li {
    list-style-type: none;
    &:not(:last-child) {
      margin-bottom: 20px;
    }
  }
  .type-header {
    margin-bottom: 10px;
    ::v-deep .el-input-number {
      width: 80px;
    }
  }
}
</style>
