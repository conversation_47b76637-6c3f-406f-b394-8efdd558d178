<template>
  <el-table
    :data="list"
    class="indicator-list"
    border
    size="small"
    style="width: 100%"
  >
    <el-table-column label="证书级别" align="center" prop="certificateLevel">
      <template slot-scope="scope">
        <el-select
          v-model="scope.row.certificateLevel"
          placeholder="请选择"
          @change="
            (val) => rowCellChange(val, scope.$index, 'certificateLevel')
          "
          :disabled="opt === 'view'"
        >
          <el-option
            v-for="dict in certificateLevelList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="证书类型" align="center" prop="certificateType">
      <template slot-scope="scope">
        <el-select
          v-model="scope.row.certificateType"
          placeholder="请选择"
          @change="(val) => rowCellChange(val, scope.$index, 'certificateType')"
          :disabled="opt === 'view'"
        >
          <el-option
            v-for="dict in certificateTypeList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="证书编码" align="center" prop="certificateCode">
      <template slot-scope="scope">
        <el-input
          v-model="scope.row.certificateCode"
          size="mini"
          placeholder="请输入证书编码"
          :disabled="opt === 'view'"
          @change="(val) => rowCellChange(val, scope.$index, 'certificateCode')"
        />
      </template>
    </el-table-column>
    <el-table-column label="专业名称" align="center" prop="majorCode">
      <template slot-scope="scope">
        <el-select
          v-model="scope.row.majorCode"
          placeholder="请选择"
          @change="(val) => rowCellChange(val, scope.$index, 'majorCode')"
          :disabled="opt === 'view'"
        >
          <el-option
            v-for="dict in majorList"
            :key="dict.code"
            :label="dict.name"
            :value="dict.code"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="证书获得日期" align="center" prop="obtainDate">
      <template slot-scope="scope">
        <el-date-picker
          v-model="scope.row.obtainDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          @change="(val) => rowCellChange(val, scope.$index, 'obtainDate')"
          :disabled="opt === 'view'"
        ></el-date-picker>
      </template>
    </el-table-column>
    <el-table-column label="证书失效日期" align="center" prop="expirationDate">
      <template slot-scope="scope">
        <el-date-picker
          v-model="scope.row.expirationDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          @change="(val) => rowCellChange(val, scope.$index, 'expirationDate')"
          :disabled="opt === 'view'"
        ></el-date-picker>
      </template>
    </el-table-column>
    <el-table-column label="师资级别" align="center" prop="teacherLevel">
      <template slot-scope="scope">
        <el-select
          v-model="scope.row.teacherLevel"
          placeholder="请选择"
          @change="(val) => rowCellChange(val, scope.$index, 'teacherLevel')"
          :disabled="opt === 'view'"
        >
          <el-option
            v-for="dict in teacherTypeList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value.toString()"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="证书照片" align="center" prop="certificatePhoto">
      <template slot-scope="scope">
        <imageUpload
          v-model="scope.row.certificatePhoto"
          :limit="1"
          :disabled="opt === 'view' ? true : false"
          :isShowTip="false"
        />
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" width="80">
      <template slot="header" v-if="opt !== 'view'">
        <div class="action-cell" style="justify-content: center">
          <span>操作</span>
          <i
            class="el-icon-circle-plus-outline"
            title="添加"
            @click="addRowHanler()"
          ></i>
        </div>
      </template>
      <template slot-scope="scope" v-if="opt !== 'view'">
        <i
          class="el-icon-delete"
          style="cursor: pointer"
          title="删除"
          @click="delRowHanler(scope.$index)"
        ></i>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import ImageUpload from "@/components/ImageUpload";
import { DICT_TYPE, getDictDatas } from "@/utils/dict";

export default {
  props: {
    formItem: {
      type: Array,
    },
    NO: {
      type: Number,
    },
    opt: {
      type: String,
    },
    majorList: {
      type: Array,
    },
  },
  components: { ImageUpload },
  data() {
    return {
      certificateLevelList: getDictDatas(DICT_TYPE.SYSTEM_CERTIFICATE_LEVEL),
      certificateTypeList: getDictDatas(DICT_TYPE.SYSTEM_CERTIFICATE_TYPE),
      teacherTypeList: getDictDatas(DICT_TYPE.SYSTEM_TEACHER_LEVEL),
    };
  },
  computed: {
    list() {
      return this.formItem;
    },
    projectScore() {
      return this.formItem.score;
    },
  },
  created() {},
  methods: {
    addRowHanler() {
      const list = this.list || [];
      list.push({
        certificateCode: "",
        certificateLevel: "",
        certificatePhoto: "",
        certificateType: "",
        expirationDate: "",
        majorCode: "",
        obtainDate: "",
        teacherLevel: "",
      });
      this.$emit("change", list);
    },
    delRowHanler(index) {
      let list = this.list;
      list.splice(index, 1);
      this.$emit("change", list);
    },
    rowCellChange(val, index, field) {
      let list = this.list;
      list[index][field] = val;
      this.$emit("change", list);
    },
  },
};
</script>
<style scoped lang="scss">
.indicator-list {
  margin-bottom: 15px;
  .action-cell {
    cursor: pointer;
    display: flex;
    align-items: center;

    i {
      font-size: 18px;
      font-weight: bold;
      padding: 0 5px;
    }
  }
}
</style>
