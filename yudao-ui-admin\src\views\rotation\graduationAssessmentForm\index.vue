<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="表单名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入表单名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="标准科室" prop="standardDepartmentId">
        <el-select v-model="queryParams.standardDepartmentId" filterable style="width: 100%">
          <el-option v-for="d in departmentList" :key="d.id" :label="d.name" :value="parseInt(d.id)"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:graduation-assessment-form:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:graduation-assessment-form:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="表单名称" align="center" prop="name" />
      <el-table-column label="标准科室" align="center" prop="standardDepartmentName" />
      <el-table-column label="总分" align="center" prop="score" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['rotation:graduation-assessment-form:update']">修改</el-button>
          <el-button v-if="scope.row.status == 0" size="mini" type="text" icon="el-icon-edit" @click="handleMaintain(scope.row, 'edit')"
                     v-hasPermi="['rotation:graduation-assessment-form:update']">维护指标</el-button>
          <el-button v-if="scope.row.status == 1" size="mini" type="text" icon="el-icon-document" @click="handleMaintain(scope.row, 'view')"
                     v-hasPermi="['rotation:graduation-assessment-form:update']">查看指标</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['rotation:graduation-assessment-form:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="表单名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入表单名称" />
        </el-form-item>
        <el-form-item label="标准科室" prop="standardDepartmentId">
          <el-select v-model="form.standardDepartmentId" filterable clearable style="width: 100%">
            <el-option v-for="d in departmentList" :key="d.id" :label="d.name" :value="parseInt(d.id)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题干" prop="stem">
          <el-input v-model="form.stem" type="textarea" placeholder="请输入题干" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" filterable placeholder="请选择状态" style="width: 100%">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                       :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog custom-class="assessmentform-indicator-dialog" :title="indicatorTitle" :visible.sync="openIndicator" width="900px" v-dialogDrag append-to-body>
      <div class="indicators-wapper">
        <div class="indicators-wapper-head">
          <div>
            <el-button v-if="opt === 'edit'" type="primary" @click="addProject">添加评分项目</el-button>
          </div>
          <span>表单总分：{{ score }}</span>
        </div>
        <div v-if="formItems.length > 0" class="indicators-wapper-tables">
          <indicatorTable
            v-for="(formItem, index) in formItems"
            :key="index"
            :NO="index"
            :formItem="formItem"
            :opt="opt"
            @change="setFormItems"
            @delProject="delProject"
            @projectNamechange="projectNamechange"
          />
        </div>
        <div v-else class="indicators-wapper-empty">
          <el-empty description="暂无数据"></el-empty>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitIndicators">确 定</el-button>
        <el-button @click="cancelIndicators">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import indicatorTable from "./indicatorTable";
import { getStandardDepartmentSimpleList } from '@/api/system/standardDepartment'
import { createGraduationAssessmentForm, updateGraduationAssessmentForm, deleteGraduationAssessmentForm,
  getGraduationAssessmentForm, getGraduationAssessmentFormPage, exportGraduationAssessmentFormExcel,
  getGraduationAssessmentFormItems, createFormItems } from "@/api/rotation/graduationAssessmentForm";

export default {
  name: "GraduationAssessmentForm",
  components: {
    indicatorTable
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出科技能考核列表
      list: [],
      // 弹出层标题
      title: "",
      indicatorTitle: '',
      // 是否显示弹出层
      open: false,
      openIndicator: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        status: null,
        standardDepartmentId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "表单名称不能为空", trigger: "blur" }],
        status: [{ required: true, message: "状态不能为空", trigger: "change" }],
      },
      formId: null,
      formItems: [],
      score: 0,
      opt: '',
      departmentList: []
    };
  },
  created() {
    this.getList();
    this.getDepartmentList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getGraduationAssessmentFormPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 获取科室列表 */
    getDepartmentList() {
      getStandardDepartmentSimpleList(0).then(res => {
        this.departmentList = res.data;
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        stem: undefined,
        status: 0,
        // score: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加出科技能考核";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getGraduationAssessmentForm(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改出科技能考核";
      });
    },
    handleMaintain(row, opt) {
      const id = row.id;
      getGraduationAssessmentFormItems(id).then(response => {
        this.formItems = response.data.formItems
        this.score =  response.data.score
        this.formId = response.data.id
        this.opt = opt
        this.openIndicator = true
        this.indicatorTitle = opt === 'edit' ? "表单指标维护" : "查看表单指标";
      });
    },
    cancelIndicators() {
      this.openIndicator = false;
      // this.reset();
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateGraduationAssessmentForm(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createGraduationAssessmentForm(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除出科技能考核编号为"' + id + '"的数据项?').then(function() {
          return deleteGraduationAssessmentForm(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有出科技能考核数据项?').then(() => {
          this.exportLoading = true;
          return exportGraduationAssessmentFormExcel(params);
        }).then(response => {
          this.$download.excel(response, '出科技能考核.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    },
    addProject() {
      const list = JSON.parse(JSON.stringify(this.formItems || []))
      list.push({
        formSubItems: [
          {
            id: undefined,
            name: "",
            score: 0
          }
        ],
        id: undefined,
        name: "",
        score: 0
      })
      this.formItems = list
    },
    submitIndicators() {
      // console.log('submitIndicators===', this.formItems, this.score, this.formId)
      if (this.formItems.length === 0) {
        return this.$modal.msgError("请添加评分项目");
      }
      let proNameFlag = true
      let formSubItemsFlag = true
      let subItemNameFlag = true
      this.formItems.forEach(item => {
        if (!item.name) {
          proNameFlag = false
        }
        if (!item.formSubItems || item.formSubItems.length === 0) {
          formSubItemsFlag = false
        } else {
          item.formSubItems.forEach(ele => {
            if (!ele.name) {
              subItemNameFlag = false
            }
          })
        }
      })
      if (!proNameFlag) {
        return this.$modal.msgError("还有评分项目名称没有填写");
      }
      if (!formSubItemsFlag) {
        return this.$modal.msgError("还有评分项目没有添加评分指标");
      }
      if (!subItemNameFlag) {
        return this.$modal.msgError("还有评分要素没有填写");
      }
      const params = {
        id: this.formId,
        formItems: this.formItems,
        score: this.score
      }
      createFormItems(params).then(response => {
        this.$modal.msgSuccess("新增成功");
        this.openIndicator = false;
        this.getList();
      })
    },
    computeScore(formItems) {
      let total = 0
      formItems.forEach(item => {
        let subtotal = 0
        if (item.formSubItems && item.formSubItems.length > 0) {
          item.formSubItems.forEach(ele => {
            subtotal = subtotal + parseInt(ele.score)
          })
        }
        item.score = subtotal
        total = total + parseInt(item.score)
      })
      this.score = total
    },
    setFormItems(list, index, field) {
      let formItems = JSON.parse(JSON.stringify(this.formItems || []))
      formItems[index].formSubItems = list
      if (field && field === 'score') {
        this.computeScore(formItems)
      }
      this.formItems = formItems
    },
    delProject(index) {
      let list = JSON.parse(JSON.stringify(this.formItems || []))
      list.splice(index, 1)
      this.formItems = list
      this.computeScore(list)
    },
    projectNamechange(val, index) {
      let list = JSON.parse(JSON.stringify(this.formItems || []))
      list[index].name = val
      this.formItems = list
    }
  }
};
</script>

<style lang="scss">
.assessmentform-indicator-dialog{

  .el-dialog__body{
    padding-top: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .indicators-wapper{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .indicators-wapper-head{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 8px;
      border-bottom: 1px #ddd solid;
    }

    .indicators-wapper-tables{
      max-height: calc(100vh - 240px);
      flex: auto;
      padding-top: 13px;
      overflow-y: auto;
    }
  }
}

</style>
