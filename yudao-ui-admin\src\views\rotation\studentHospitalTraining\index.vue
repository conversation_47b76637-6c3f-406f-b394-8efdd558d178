<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="培训名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入培训名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训级别" prop="trainingLevel">
        <el-select
          v-model="queryParams.trainingLevel"
          placeholder="请选择培训级别"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训类型" prop="trainingType">
        <el-select
          v-model="queryParams.trainingType"
          placeholder="请选择培训类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训人" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入培训人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="当前状态" prop="hospitalTrainingStatus">
        <el-select
          v-model="queryParams.hospitalTrainingStatus"
          placeholder="请选择当前状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.HOSPITAL_TRAINING_JOIN_STATE
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开展时间" prop="developTimes">
        <el-date-picker
          clearable
          v-model="queryParams.developTimes"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="选择开展时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:teaching-active:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="培训名称"
        align="center"
        prop="name"
        fixed
        width="180"
      />
      <el-table-column
        label="培训级别"
        align="center"
        prop="trainingLevel"
        width="130"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_TRAINING_LEVEL"
            :value="scope.row.trainingLevel"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="培训类型"
        align="center"
        prop="trainingType"
        width="120"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_TRAINING_TYPE"
            :value="scope.row.trainingType"
          />
        </template>
      </el-table-column>
      <el-table-column label="培训人" align="center" prop="nickname" />
      <el-table-column
        label="开展时间"
        align="center"
        prop="startTime"
        width="300"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }} ~ {{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="培训地点" align="center" prop="trainingAddress" />
      <el-table-column
        label="签到时间"
        align="center"
        prop="scanningTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.scanningTime
              ? parseTime(scope.row.scanningTime)
              : "暂未签到"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="综合评价"
        align="center"
        prop="evaluationScore"
        width="140"
      >
        <template slot-scope="scope">
          <el-rate
            v-if="scope.row.evaluationScore"
            v-model="scope.row.evaluationScore"
            disabled
            show-score
            text-color="#ff9900"
            :max="5"
            score-template="{value}"
          >
          </el-rate>
          <span v-else>暂未评价</span>
        </template>
      </el-table-column>
      <el-table-column
        label="当前状态"
        align="center"
        prop="hospitalTrainingStatus"
        width="130"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.HOSPITAL_TRAINING_JOIN_STATE"
            :value="scope.row.hospitalTrainingStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row)"
          >
            查看详情
          </el-button>
          <el-button
            v-if="scope.row.hospitalTrainingStatus == 2"
            size="mini"
            type="text"
            icon="el-icon-s-claim"
            @click="joinClick(scope.row)"
          >
            报名参加
          </el-button>
          <el-button
            v-if="
              scope.row.hospitalTrainingStatus == 1 && scope.row.joinType == 2
            "
            size="mini"
            type="text"
            icon="el-icon-refresh-left"
            @click="handleRevoke(scope.row)"
          >
            撤销报名
          </el-button>
          <el-button
            v-if="scope.row.scanningTime && !scope.row.evaluationScore"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEditAppraise(scope.row)"
          >
            进入评价
          </el-button>
          <el-button
            v-if="scope.row.scanningTime && scope.row.evaluationScore"
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handleViewAppraise(scope.row)"
          >
            查看评价
          </el-button>
          <el-button
            v-if="
              scope.row.scanningTime &&
              scope.row.paperId &&
              !scope.row.answerResultId
            "
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleExam(scope.row)"
          >
            进入考试
          </el-button>
          <el-button
            v-if="scope.row.scanningTime && scope.row.answerResultId"
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleExamView(scope.row)"
          >
            查看考试
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="680px"
      v-dialogDrag
      append-to-body
      custom-class="hospitalTraining-dialog"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="180px"
        disabled
      >
        <el-form-item label="培训名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入培训名称" />
        </el-form-item>
        <el-form-item label="培训级别" prop="trainingLevel">
          <el-select
            v-model="form.trainingLevel"
            placeholder="请选择培训级别"
            style="width: 100%"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.TEACHING_TRAINING_LEVEL
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="培训类型" prop="trainingType">
          <el-select
            v-model="form.trainingType"
            placeholder="请选择培训类型"
            style="width: 100%"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.TEACHING_TRAINING_TYPE
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.trainingType == 4"
          label="专业名称"
          prop="majorCode"
        >
          <el-select
            v-model="form.majorCode"
            placeholder="请选择专业"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in queryMajorList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开展时间" prop="timeArr">
          <el-date-picker
            v-model="form.timeArr"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="培训人类型" prop="trainingUserType">
          <el-select
            v-model="form.trainingUserType"
            placeholder="请选择培训人类型"
            style="width: 100%"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.TEACHING_TRAINING_USER_TYPE
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.trainingUserType == 1"
          label="培训人"
          prop="courtTrainingUserId"
        >
          <el-select
            v-model="form.courtTrainingUserId"
            filterable
            placeholder="请选择本院培训人"
            style="width: 100%"
          >
            <el-option
              v-for="user in userWorkerOptions"
              :key="user.id"
              :label="user.nickname"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.trainingUserType == 2"
          label="培训人"
          prop="outerCourtyardTrainingUserName"
        >
          <el-input
            v-model="form.outerCourtyardTrainingUserName"
            placeholder="请输入外院培训人姓名"
          />
        </el-form-item>
        <el-form-item label="培训地点" prop="trainingAddress">
          <el-input
            type="textarea"
            v-model="form.trainingAddress"
            placeholder="请输入培训地点"
          />
        </el-form-item>
        <el-form-item label="是否手动选择培训对象" prop="isManualSelection">
          <el-radio-group v-model="form.isManualSelection">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="form.isManualSelection == true"
          label="培训对象"
          prop="trainingObjects"
        >
          <el-select
            multiple
            v-model="form.trainingObjects"
            placeholder="请选择培训对象"
            style="width: 100%"
          >
            <el-option
              v-if="dict.value != 0"
              v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.trainingObjects && form.trainingObjects.indexOf('1') > -1"
          label="角色选择"
          prop="roleIds"
        >
          <el-select
            v-model="form.roleIds"
            multiple
            filterable
            clearable
            placeholder="请选择角色"
            style="width: 100%"
            @change="getSelectedNumber"
          >
            <el-option
              v-for="item in roleOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="item.id.toString()"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.trainingObjects && form.trainingObjects.indexOf('2') > -1"
          label="学员类型"
          prop="studentTypes"
        >
          <el-select
            v-model="form.studentTypes"
            multiple
            filterable
            placeholder="请选择学员类型"
            style="width: 100%"
            @change="getSelectedNumber"
          >
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.trainingObjects && form.trainingObjects.indexOf('2') > -1"
          label="学员专业"
          prop="studentMajors"
        >
          <el-select
            v-model="form.studentMajors"
            multiple
            filterable
            placeholder="请选择学员专业"
            style="width: 100%"
            @change="getSelectedNumber"
          >
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_MAJOR)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.trainingObjects && form.trainingObjects.indexOf('2') > -1"
          label="学员年级"
          prop="grades"
        >
          <el-select
            v-model="form.grades"
            multiple
            filterable
            placeholder="请选择年级"
            style="width: 100%"
            @change="getSelectedNumber"
          >
            <el-option
              v-for="grade in studentGradeList"
              :key="grade"
              :label="grade"
              :value="grade"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.isManualSelection == true"
          label="已选择培训人数"
          prop="trainingSelectedNumber"
        >
          <el-input v-model="form.trainingSelectedNumber" disabled />
        </el-form-item>
        <el-form-item label="是否允许自主报名参加" prop="isSelfRegistration">
          <el-radio-group v-model="form.isSelfRegistration">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="报名人数限制" prop="registrationLimitNumber">
          <el-input-number
            v-model="form.registrationLimitNumber"
            :min="0"
            :max="9999"
            label="请输入报名人数限制"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="培训课件" prop="coursewares">
          <FileUpload v-model="form.coursewares" :limit="999" :fileSize="50" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="submitForm('save')">保存</el-button>
        <el-button type="primary" @click="submitForm('publish')">直接发布</el-button> -->
        <el-button @click="cancel">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="提示"
      :visible.sync="joinDialogVisible"
      width="300px"
      custom-class="join-dialog"
    >
      <span>是否确认报名参加该培训?</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="joinDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleJoin">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="提示"
      :visible.sync="joinConfirmVisible"
      width="450px"
      custom-class="join-dialog"
    >
      <span>您在该培训时间段内已经参与了其它活动，确认继续报名？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="joinConfirmVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleJoinConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <AppraiseDialog
      v-if="formData"
      :title="appraiseDialogTitle"
      :open="appraiseDialogOpen"
      :data="formData"
      :curRow="curRow"
      :disabled="appraiseDisabled"
      @setOpen="setAppraiseDialogOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import AppraiseDialog from "./appraiseDialog";
import { getDepartmentSimpleList } from "@/api/system/department";
import { getUserWorkerSimpleList } from "@/api/system/userWorker";
import {
  getAppraiseResult,
  getAppraiseForm,
} from "@/api/rotation/appraiseActive";
import {
  getTrainingJoinPage,
  confirmJoin,
  revokeJoin,
  getHospitalTraining,
} from "@/api/rotation/studentHospitalTraining";
import {
  validateAuthenticationCode,
  getPaperPureConfig,
} from "@/api/exam/paperConfig";
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "TeachingActive",
  components: {
    FileUpload,
    ImageUpload,
    AppraiseDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      studentDetailLoading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 教学活动列表
      list: [],
      studentDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openPics: false,
      openStudentDetail: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        trainingLevel: null,
        trainingType: null,
        nickname: null,
        hospitalTrainingStatus: null,
        developTimes: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "培训名称不能为空", trigger: "blur" },
        ],
        trainingLevel: [
          { required: true, message: "培训级别不能为空", trigger: "change" },
        ],
        trainingType: [
          { required: true, message: "培训类型不能为空", trigger: "change" },
        ],
        majorCode: [
          { required: true, message: "培训专业不能为空", trigger: "change" },
        ],
        startTime: [
          { required: true, message: "开展开始时间不能为空", trigger: "blur" },
        ],
        endTime: [
          { required: true, message: "开展结束时间不能为空", trigger: "blur" },
        ],
        trainingUserType: [
          { required: true, message: "培训人类型不能为空", trigger: "change" },
        ],
        courtTrainingUserId: [
          { required: true, message: "培训人不能为空", trigger: "change" },
        ],
        outerCourtyardTrainingUserName: [
          { required: true, message: "培训人不能为空", trigger: "change" },
        ],
        trainingAddress: [
          { required: true, message: "培训地点不能为空", trigger: "blur" },
        ],
        isManualSelection: [
          {
            required: true,
            message: "是否手动选择培训对象不能为空",
            trigger: "blur",
          },
        ],
        trainingObjects: [
          { required: true, message: "培训对象不能为空", trigger: "change" },
        ],
        roleIds: [
          {
            type: "array",
            required: true,
            message: "角色不能为空",
            trigger: "change",
          },
        ],
        studentTypes: [
          {
            type: "array",
            required: true,
            message: "学员类型不能为空",
            trigger: "change",
          },
        ],
        trainingSelectedNumber: [
          { required: true, message: "选择培训人数不能为空", trigger: "blur" },
        ],
        isSelfRegistration: [
          {
            required: true,
            message: "是否允许自主报名参加不能为空",
            trigger: "blur",
          },
        ],
        registrationLimitNumber: [
          { required: true, message: "报名人数限制不能为空", trigger: "blur" },
        ],
        publishStatus: [
          { required: true, message: "发布状态不能为空", trigger: "change" },
        ],
      },
      departmentOptions: [],
      userWorkerOptions: [],
      formData: null,
      appraiseDialogTitle: "",
      appraiseDialogOpen: false,
      curRow: null,
      appraiseDisabled: false,
      //是否第二次报名确认
      confirmed: false,
      joinDialogVisible: false,

      joinConfirmVisible: false,
    };
  },
  created() {
    if (this.$route.query.trainingType) {
      this.queryParams.trainingType = this.$route.query.trainingType;
    }
    this.getList();
    this.getDepartment();
    this.getUserworkData();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getTrainingJoinPage(this.queryParams).then((response) => {
        const list = response.data.list;
        list.forEach((item) => {
          if (item.score) {
            const _score = (item.evaluationScore / item.score) * 5;
            item.evaluationScore = _score;
          }
        });
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    getUserworkData() {
      getUserWorkerSimpleList().then((res) => {
        this.userWorkerOptions = [];
        this.userWorkerOptions.push(...res.data);
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelSubmitPics() {
      this.openPics = false;
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        trainingLevel: undefined,
        trainingType: undefined,
        majorCode: undefined,
        timeArr: [],
        startTime: undefined,
        endTime: undefined,
        trainingUserType: undefined,
        courtTrainingUserId: undefined,
        outerCourtyardTrainingUserName: undefined,
        trainingAddress: undefined,
        isManualSelection: false,
        trainingObjects: [],
        roleIds: [],
        studentTypes: [],
        studentMajors: [],
        grades: [],
        trainingSelectedNumber: 0,
        isSelfRegistration: true,
        registrationLimitNumber: 0,
        coursewares: undefined,
        pictures: undefined,
        publishStatus: undefined,
      };
      this.resetForm("form");
    },
    timeValueChange(values) {
      this.form.startTime = undefined;
      this.form.endTime = undefined;
      if (values) {
        this.form.startTime = values[0];
        this.form.endTime = values[1];
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getHospitalTraining(id).then((response) => {
        const { data } = response;
        const { startTime, endTime } = data;
        this.form = data;
        this.form.timeArr = [startTime, endTime];
        this.form.trainingObjects = data.trainingObjects
          ? data.trainingObjects.split(",")
          : [];
        this.form.roleIds = data.roleIds ? data.roleIds.split(",") : [];
        this.form.studentTypes = data.studentTypes
          ? data.studentTypes.split(",")
          : [];
        this.form.studentMajors = data.studentMajors
          ? data.studentMajors.split(",")
          : [];
        this.form.grades = data.grades ? data.grades.split(",") : [];
        this.form.coursewares = this.form.coursewares
          ? JSON.parse(this.form.coursewares)
          : undefined;
        this.open = true;
        this.title = "查看院级培训";
        console.log("this.form====", this.form);
      });
    },
    handleViewAppraise(row) {
      this.appraiseDisabled = true;
      const params = {
        id: row.appraiseActiveResultId,
      };
      getAppraiseResult(params).then((response) => {
        this.formData = response.data;
        this.appraiseDialogOpen = true;
        this.appraiseDialogTitle = `查看评价-${row.name}`;
      });
    },
    handleEditAppraise(row) {
      this.curRow = row;
      this.appraiseDisabled = false;
      const params = {
        appraiseActiveType: "2",
        activeType: row.trainingType,
      };
      getAppraiseForm(params).then((response) => {
        this.formData = response.data;
        this.appraiseDialogOpen = true;
        this.appraiseDialogTitle = `正在评价-${row.name}`;
      });
    },
    setAppraiseDialogOpen(flag) {
      this.appraiseDialogOpen = flag;
      this.formData = null;
      this.curRow = null;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = { ...this.form };
        delete params.timeValue;
        params.studentTypes = params.studentTypes.join(",");
        params.coursewares = JSON.stringify(params.coursewares);
        // 修改的提交
        if (this.form.id != null) {
          updateTeachingActive(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createTeachingActive(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    submitPics() {
      const params = { ...this.form };
      delete params.timeValue;
      updateTeachingActive(params).then((response) => {
        this.$modal.msgSuccess("保存成功");
        this.openPics = false;
        this.getList();
      });
    },
    handleExam(row) {
      getPaperPureConfig(row.paperId).then((res) => {
        const jump = (code = "") => {
          const url = this.$router.resolve({
            path: "/onlineExam",
            query: {
              examObjectId: row.hospitalTrainingUserId,
              paperId: row.paperId,
              code,
            },
          }).href;
          window.open(url, "_blank");
        };
        if (res.data.isAuthentication) {
          this.$prompt("请输入考试码", "校验确认", {
            inputPattern: /^\d{6}$/,
            inputErrorMessage: "考试码为6位数字",
          }).then(({ value }) => {
            validateAuthenticationCode({
              paperId: row.paperId,
              code: value,
            }).then((res) => {
              if (res.data) {
                jump(value);
              }
            });
          });
        } else {
          jump();
        }
      });
    },
    handleExamView(row) {
      const url = this.$router.resolve({
        path: "/examResult",
        query: { id: row.answerResultId },
      }).href;
      window.open(url, "_blank");
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除教学活动编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteTeachingActive(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有教学活动数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportTeachingActiveExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "教学活动.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    viewStudentDetail(row) {
      if (!row.attendance) {
        return;
      }
      this.getStudentList(row.id, () => {
        this.openStudentDetail = true;
      });
    },
    getStudentList(id, callback) {
      const params = {
        teachingActiveId: id,
      };
      this.studentDetailLoading = true;
      getStudentsList(params).then((response) => {
        this.studentDetailList = response.data || [];
        if (callback) {
          callback();
        }
        this.studentDetailLoading = false;
      });
    },
    handleRevoke(row) {
      const params = {
        hospitalTrainingUserId: row.hospitalTrainingUserId,
      };
      this.$modal
        .confirm(`是否确认撤销该次报名?`)
        .then(function () {
          return revokeJoin(params);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(`撤销成功`);
        })
        .catch(() => {});
    },

    joinClick(row) {
      this.curRow = row;
      this.confirmed = false;
      this.joinTip = "是否确认报名参加该培训?";
      this.joinDialogVisible = true;
    },

    handleJoin() {
      const params = {
        hospitalTrainingId: this.curRow.id,
        confirmed: false,
      };

      confirmJoin(params)
        .then((res) => {
          this.$modal.msgSuccess(`报名成功`);
          this.joinDialogVisible = false;
          this.getList();
        })
        .catch((code) => {
          console.log("err", code);
          if (code === 1003017005) {
            this.joinDialogVisible = false;
            this.joinConfirmVisible = true;
          }
        });
    },

    handleJoinConfirm() {
      const params = {
        hospitalTrainingId: this.curRow.id,
        confirmed: true,
      };

      confirmJoin(params).then((res) => {
        this.$modal.msgSuccess(`报名成功`);
        this.joinConfirmVisible = false;
        this.getList();
      });
    },
    // handleJoin(row) {
    //   const params = {
    //     hospitalTrainingId: row.id,
    //   }
    //   confirmJoin(params).then(res => {
    //     this.$modal.msgSuccess("操作成功");
    //     this.getList();
    //   })
    // },
    // handleRevoke(row) {
    //   const params = {
    //     hospitalTrainingId: row.id,
    //   }
    //   revokeJoin(params).then(res => {
    //     this.$modal.msgSuccess("操作成功");
    //     this.getList();
    //   })
    // }
  },
};
</script>

<style lang="scss">
.hospitalTraining-dialog {
  .el-dialog__body {
    padding-right: 100px;
  }
}

.join-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
}
</style>
