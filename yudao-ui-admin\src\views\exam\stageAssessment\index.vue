<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" size="small" label-width="70px" :model="queryParams" :inline="true" ref="queryForm">
      <el-form-item label="试卷名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入试卷名称或关键字" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="考试类型" v-if="['imitate', 'hospital'].indexOf(type) > -1">
        <el-select v-model="queryParams.examePaperType" placeholder="请选择考试类型" clearable>
          <el-option v-for="dict in getDictDatas(type === 'imitate' ? DICT_TYPE.IMITATE_EXAM_TYPE : DICT_TYPE.HOSPITAL_EXAM_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="试卷名称" align="center" prop="name" min-width="150px" show-overflow-tooltip fixed="left" />
      <el-table-column label="考试类型" align="center" prop="examePaperType" v-if="['imitate', 'hospital'].indexOf(type) > -1">
        <template slot-scope="scope">
          {{ getDictDataLabel(type === "imitate" ? DICT_TYPE.IMITATE_EXAM_TYPE : DICT_TYPE.HOSPITAL_EXAM_TYPE, scope.row.examePaperType) }}
        </template>
      </el-table-column>
      <el-table-column label="总题数" align="center" prop="questionNum" />
      <el-table-column label="总分" align="center" prop="totalScore" />
      <el-table-column label="及格分" align="center" prop="passScore" />
      <el-table-column label="答题时间" align="center" prop="answerMinuteTime" />
      <el-table-column label="考试次数" align="center" prop="limitCount">
        <template v-slot="{ row }">{{ row.limitCount || "不限" }}</template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="beginTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.beginTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180px" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEnter(scope.row.id)" v-if="!scope.row.isAnswerTimeExpired">进入考试</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleResults(scope.row)">查看试卷</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog title="试卷查看" :visible.sync="resultOpen" width="60%">
      <el-table :data="resultList" border>
        <el-table-column label="试卷名称" prop="paperName" align="center"></el-table-column>
        <el-table-column label="考试时间" prop="startTime" width="170px" align="center"></el-table-column>
        <el-table-column label="交卷时间" prop="endTime" width="170px" align="center"></el-table-column>
        <el-table-column label="试卷得分" prop="score" width="80px" align="center"></el-table-column>
        <el-table-column label="操作" width="90px" align="center">
          <template v-slot="scope">
            <el-link
              v-if="scope.row.isAllowView"
              style="margin-right: 5px"
              type="primary"
              @click="handleResultPreview(scope.row.id)"
            >查看</el-link>
            <el-link
              v-if="scope.row.download"
              type="primary"
              @click="handleResultDownload(scope.row.download)"
            >下载</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { getRotationAssessmentPage } from '@/api/exam/paper'
import { validateAuthenticationCode, getPaperPureConfig } from '@/api/exam/paperConfig'
import { getOwnAnswerResultList } from '@/api/exam/answerResult'

export default {
  name: 'stageAssessment',
  data() {
    return {
      examObjectId: this.$store.state.user.id,
      type: "stage",
      queryParams: {
        openingSetting: "1",
        name: "",
        examePaperType: undefined,
        pageNo: 1,
        pageSize: 10,
      },
      showSearch: true,
      loading: false,
      list: [],
      total: 0,
      resultOpen: false,
      resultList: [],
    }
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getRotationAssessmentPage(this.queryParams, this.type).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 进入考试 */
    handleEnter(id) {
      getPaperPureConfig(id).then(res => {
        const jump = (code = '') => {
          const url = this.$router.resolve({
            path: '/onlineExam',
            query: { examObjectId: this.examObjectId, paperId: id, code }
          }).href
          window.open(url, '_blank')
        }
        if (res.data.isAuthentication) {
          this.$prompt('请输入考试码', '校验确认', {
            inputPattern: /^\d{6}$/,
            inputErrorMessage: '考试码为6位数字'
          }).then(({ value }) => {
            validateAuthenticationCode({ paperId: id, code: value }).then((res) => {
              if (res.data) {
                jump(value)
              }
            })
          })
        } else {
          jump()
        }
      })
    },
    /** 查看试卷 */
    handleResults(row) {
      getOwnAnswerResultList({
        examePaperType: row.examePaperType,
        examObjectId: this.examObjectId,
        exameType: row.exameType,
        paperId: row.id,
      }).then(res => {
        this.resultList = res?.data || []
        this.resultOpen = true
      })
    },
    handleResultPreview(id) {
      const url = this.$router.resolve({
        path: '/examResult',
        query: { id }
      }).href;
      window.open(url, '_blank');
    },
    handleResultDownload(url) {
      window.location.href = url
    },
  },
  created() {
    this.handleQuery();
  },
}
</script>

<style lang="scss" scoped>

</style>
