<template>
  <div class="app-container">
    <el-row :gutter="40">
      <el-col :span="12">
        <!-- 搜索工作栏 -->
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="70px"
        >
          <el-form-item label="科室名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入科室名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item
            label="轮转科室名称"
            prop="rotationDepartmentName"
            label-width="100px"
          >
            <el-input
              v-model="queryParams.rotationDepartmentName"
              placeholder="请输入轮转科室名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:department:create']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-dropdown v-hasPermi="['system:department:import']">
              <el-button
                type="warning"
                plain
                icon="el-icon-upload2"
                size="mini"
                :loading="exportLoading"
              >
                导入<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="handleImport"
                  >选择文件导入</el-dropdown-item
                >
                <el-dropdown-item @click.native="handleExportTemplate"
                  >下载导入模版</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <!-- 列表 -->
        <el-table
          v-loading="loading"
          :data="list"
          highlight-current-row
          @row-click="handleRowClick"
        >
          <el-table-column label="医院科室" align="center" prop="name" />
          <el-table-column label="科室类型" align="center" prop="deptType">
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.SYSTEM_DEPARTMENT_TYPE"
                :value="scope.row.deptType"
              />
            </template>
          </el-table-column>
          <el-table-column label="显示顺序" align="center" prop="sort" />
          <el-table-column label="科室状态" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.COMMON_STATUS"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:department:update']"
                >修改</el-button
              >
              <!--<el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['system:department:delete']">删除</el-button>-->
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 对话框(添加 / 修改) -->
        <el-dialog
          :title="title"
          :visible.sync="open"
          width="500px"
          v-dialogDrag
          append-to-body
        >
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="科室名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入科室名称" />
            </el-form-item>
            <el-form-item label="科室类型" prop="deptType">
              <el-select v-model="form.deptType" placeholder="请选择科室类型">
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_DEPARTMENT_TYPE
                  )"
                  :key="dict.value"
                  :value="parseInt(dict.value)"
                  :label="dict.label"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="显示顺序" prop="sort">
              <el-input-number
                v-model="form.sort"
                placeholder="请输入显示顺序"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
            <el-form-item label="科室状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="parseInt(dict.value)"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>

        <!-- 科室导入对话框 -->
        <el-dialog
          :title="upload.title"
          :visible.sync="upload.open"
          width="400px"
          append-to-body
        >
          <el-upload
            ref="upload"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url + '?updateSupport=' + upload.updateSupport"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip text-center" slot="tip">
              <el-checkbox v-model="upload.updateSupport" />
              是否更新已经存在的科室数据
              <p>
                仅允许导入xls、xlsx格式文件。
                <el-link
                  style="font-size: 12px"
                  type="primary"
                  :underline="false"
                  @click="handleExportTemplate"
                  >下载模板</el-link
                >
              </p>
            </div>
          </el-upload>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
          </div>
        </el-dialog>
      </el-col>
      <el-col :span="12">
        <!-- 操作工具栏 -->
        <el-form size="small" :inline="true">
          <el-form-item
            :label="`${rotationParams.parentName}下的轮转科室`"
          ></el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAddRotation"
              v-hasPermi="['system:department:create']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-dropdown v-hasPermi="['system:department:import']">
              <el-button
                type="warning"
                plain
                icon="el-icon-upload2"
                size="mini"
                :loading="exportRotationLoading"
              >
                导入<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="handleRotationImport"
                  >选择文件导入</el-dropdown-item
                >
                <el-dropdown-item @click.native="handleRotationExportTemplate"
                  >下载导入模版</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </el-col>
          <right-toolbar @queryTable="handleRotationQuery"></right-toolbar>
        </el-row>

        <!-- 列表 -->
        <el-table v-loading="rotationLoading" :data="rotationList">
          <el-table-column label="轮转科室" align="center" prop="name" />
          <el-table-column label="容纳人数" align="center" prop="capacity" />
          <el-table-column label="显示顺序" align="center" prop="sort" />
          <el-table-column label="科室状态" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.COMMON_STATUS"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdateRotation(scope.row)"
                v-hasPermi="['system:department:update']"
                >修改</el-button
              >
              <!--<el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['system:department:delete']">删除</el-button>-->
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination
          v-show="rotationTotal > 0"
          :total="rotationTotal"
          :page.sync="rotationParams.pageNo"
          :limit.sync="rotationParams.pageSize"
          @pagination="handleRotationQuery"
        />

        <!-- 对话框(添加 / 修改) -->
        <el-dialog
          :title="rotationTitle"
          :visible.sync="rotationOpen"
          width="500px"
          v-dialogDrag
          append-to-body
        >
          <el-form
            ref="rotationForm"
            :model="rotationForm"
            :rules="rotationRules"
            label-width="80px"
          >
            <el-form-item label="医院科室" prop="parentName">
              <el-input
                v-model="rotationForm.parentName"
                placeholder="请输入医院科室名称"
                disabled
              />
            </el-form-item>
            <el-form-item label="轮转科室" prop="name">
              <el-input
                v-model="rotationForm.name"
                placeholder="请输入轮转科室名称"
              />
            </el-form-item>
            <el-form-item label="容纳人数" prop="capacity">
              <el-input-number
                v-model="rotationForm.capacity"
                placeholder="请输入容纳人数"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
            <el-form-item label="显示顺序" prop="sort">
              <el-input-number
                v-model="rotationForm.sort"
                placeholder="请输入显示顺序"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
            <el-form-item label="科室地址" prop="address">
              <el-input
                v-model="rotationForm.address"
                placeholder="请输入科室地址"
              />
            </el-form-item>
            <el-form-item label="科室状态" prop="status">
              <el-radio-group v-model="rotationForm.status">
                <el-radio
                  v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="parseInt(dict.value)"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFormRotation"
              >确 定</el-button
            >
            <el-button @click="handleCancelRotation">取 消</el-button>
          </div>
        </el-dialog>

        <!-- 科室导入对话框 -->
        <el-dialog
          :title="rotationUpload.title"
          :visible.sync="rotationUpload.open"
          width="400px"
          append-to-body
        >
          <el-upload
            ref="rotationUpload"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="rotationUpload.headers"
            :action="
              rotationUpload.url +
              '?updateSupport=' +
              rotationUpload.updateSupport
            "
            :disabled="rotationUpload.isUploading"
            :on-progress="handleRotationFileUploadProgress"
            :on-success="handleRotationFileSuccess"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip text-center" slot="tip">
              <el-checkbox v-model="rotationUpload.updateSupport" />
              是否更新已经存在的轮转科室数据
              <p>
                仅允许导入xls、xlsx格式文件。
                <el-link
                  style="font-size: 12px"
                  type="primary"
                  :underline="false"
                  @click="handleRotationExportTemplate"
                  >下载模板</el-link
                >
              </p>
            </div>
          </el-upload>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitRotationFileForm"
              >确 定</el-button
            >
            <el-button @click="rotationUpload.open = false">取 消</el-button>
          </div>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartment,
  getDepartmentPage,
  exportDepartmentTemplate,
  exportRotationDepartmentTemplate,
} from "@/api/system/department";
import { getBaseHeader } from "@/utils/request";

export default {
  name: "Department",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 科室列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        parentId: 0,
        name: null,
        rotationDepartmentName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "科室名称不能为空", trigger: "blur" },
        ],
        deptType: [
          { required: true, message: "科室类型不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "科室类型不能为空", trigger: "blur" },
        ],
        sort: [
          { required: true, message: "显示顺序不能为空", trigger: "blur" },
        ],
      },
      // 科室导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的科室数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API + "/admin-api/system/department/import",
      },
      // 轮转科室属性
      rotationLoading: false,
      rotationList: [],
      rotationTotal: 0,
      rotationParams: {
        parentId: null,
        parentName: "",
        pageNo: 1,
        pageSize: 10,
      },
      rotationOpen: false,
      rotationTitle: "",
      rotationForm: {},
      rotationRules: {
        parentName: [
          { required: true, message: "医院科室名称不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "轮转科室名称不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "科室状态不能为空", trigger: "blur" },
        ],
        sort: [
          { required: true, message: "显示顺序不能为空", trigger: "blur" },
        ],
        address: [
          { required: true, message: "科室地址不能为空", trigger: "blur" },
        ],
      },
      // 轮转科室导入
      exportRotationLoading: false,
      rotationUpload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的科室数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/admin-api/system/department/import-rotation",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getDepartmentPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        parentId: 0,
        sort: 0,
        status: 0,
        deptType: undefined,
        capacity: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加科室";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getDepartment(id).then((response) => {
        this.form = response.data;
        this.form.parentId = 0;
        this.open = true;
        this.title = "修改科室";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateDepartment(this.form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createDepartment(this.form).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除科室编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteDepartment(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExportTemplate() {
      exportDepartmentTemplate()
        .then((response) => {
          this.$download.excel(response, "科室导入模版.xlsx");
        })
        .catch(() => {});
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "科室导入";
      this.upload.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      let text = "创建成功数量：" + data.createDepartmentnames.length;
      for (const name of data.createDepartmentnames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      }
      text += "<br />更新成功数量：" + data.updateDepartmentnames.length;
      for (const name of data.updateDepartmentnames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      }
      text +=
        "<br />更新失败数量：" +
        Object.keys(data.failureDepartmentnames).length;
      for (const name in data.failureDepartmentnames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          name +
          "：" +
          data.failureDepartmentnames[name];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 点击医院科室行 */
    handleRowClick(row) {
      this.rotationParams.parentId = row.id;
      this.rotationParams.parentName = row.name;
      this.getRotationList();
    },
    /** 轮转科室方法 */
    getRotationList() {
      this.rotationLoading = true;
      // 执行查询
      getDepartmentPage(this.rotationParams).then((response) => {
        this.rotationList = response.data.list;
        this.rotationTotal = response.data.total;
        this.rotationLoading = false;
      });
    },
    /** 取消轮转科室 */
    handleCancelRotation() {
      this.rotationOpen = false;
      this.rotationReset();
    },
    /** 轮转科室表单重置 */
    rotationReset() {
      this.rotationForm = {
        id: undefined,
        name: undefined,
        parentId: this.rotationParams.parentId,
        parentName: this.rotationParams.parentName,
        sort: 0,
        status: 0,
        deptType: undefined,
        capacity: undefined,
        address: undefined,
      };
      this.resetForm("rotationForm");
    },
    /** 搜索按钮操作 */
    handleRotationQuery() {
      this.rotationParams.pageNo = 1;
      this.getRotationList();
    },
    /** 轮转科室新增按钮操作 */
    handleAddRotation() {
      if (!this.rotationParams.parentId) {
        this.$message.warning("请先点击左侧一行表格选择医院科室");
        return;
      }
      this.rotationReset();
      this.rotationOpen = true;
      this.rotationTitle = "添加轮转科室";
    },
    /** 科室轮转修改按钮操作 */
    handleUpdateRotation(row) {
      this.rotationReset();
      getDepartment(row.id).then((response) => {
        this.rotationForm = response.data;
        this.rotationForm.parentId = this.rotationParams.parentId;
        this.rotationForm.parentName = this.rotationParams.parentName;
        this.rotationOpen = true;
        this.rotationTitle = "修改轮转科室";
      });
    },
    /** 科室轮转提交按钮 */
    submitFormRotation() {
      this.$refs["rotationForm"].validate((valid) => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.rotationForm.id != null) {
          updateDepartment(this.rotationForm).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.rotationOpen = false;
            this.getRotationList();
          });
          return;
        }
        // 添加的提交
        createDepartment(this.rotationForm).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.rotationOpen = false;
          this.getRotationList();
        });
      });
    },
    /** 轮转可是导出按钮操作 */
    handleRotationExportTemplate() {
      exportRotationDepartmentTemplate()
        .then((response) => {
          this.$download.excel(response, "轮转科室导入模版.xlsx");
        })
        .catch(() => {});
    },
    /** 轮转导入按钮操作 */
    handleRotationImport() {
      this.rotationUpload.title = "轮转科室导入";
      this.rotationUpload.open = true;
    },
    // 文件上传中处理
    handleRotationFileUploadProgress(event, file, fileList) {
      this.rotationUpload.isUploading = true;
    },
    // 文件上传成功处理
    handleRotationFileSuccess(response, file, fileList) {
      this.rotationUpload.isUploading = false;
      this.$refs.rotationUpload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.rotationUpload.open = false;

      // 拼接提示语
      let data = response.data;
      let text = "创建成功数量：" + data.createDepartmentnames.length;
      for (const name of data.createDepartmentnames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      }
      text += "<br />更新成功数量：" + data.updateDepartmentnames.length;
      for (const name of data.updateDepartmentnames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      }
      text +=
        "<br />更新失败数量：" +
        Object.keys(data.failureDepartmentnames).length;
      for (const name in data.failureDepartmentnames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          name +
          "：" +
          data.failureDepartmentnames[name];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getRotationList();
    },
    // 提交上传文件
    submitRotationFileForm() {
      this.$refs.rotationUpload.submit();
    },
  },
};
</script>
