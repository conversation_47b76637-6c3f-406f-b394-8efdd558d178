<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年级" prop="grade">
        <el-select v-model="queryParams.grade" placeholder="请选择学员类型" filterable clearable size="small">
          <el-option v-for="grade in gradeOptions"
                     :key="grade" :label="grade" :value="grade"/>
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入学员姓名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input v-model="queryParams.username" placeholder="请输入用户名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" filterable clearable size="small" @change="queryStudentTypeChange">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select v-model="queryParams.major" placeholder="请选择培训专业" filterable clearable size="small">
          <el-option v-for="major in majorOptions"
                     :key="major.code" :label="major.name" :value="major.code"/>
        </el-select>
      </el-form-item>
      <el-form-item label="派送单位" prop="dispatchingUnit">
        <el-input v-model="queryParams.dispatchingUnit" placeholder="请输入派送单位" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="完成时间" prop="completeDates">
        <el-date-picker
          style="width: 240px"
          v-model="queryParams.completeDates"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:student-enrollment:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="学员姓名" align="center" prop="nickname" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType" />
        </template>
      </el-table-column>
      <el-table-column label="培训专业" align="center" prop="majorName" />
      <el-table-column label="教学活动准时签到数" align="center" prop="teachingActiveOnTimeNum" />
      <el-table-column label="教学活动有效评价" align="center" prop="studentAppraiseActiveEffectiveNum">
        <template v-slot="scope">
          <el-link type="primary" @click="showEffective(scope.row, 'active')">{{ scope.row.studentAppraiseActiveEffectiveNum }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="对带教有效评价" align="center" prop="studentAppraiseTeacherEffectiveNum">
        <template v-slot="scope">
          <el-link type="primary" @click="showEffective(scope.row, 'teacher')">{{ scope.row.studentAppraiseTeacherEffectiveNum }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="对科室有效评价" align="center" prop="studentAppraiseDeptEffectiveNum">
        <template v-slot="scope">
          <el-link type="primary" @click="showEffective(scope.row, 'dept')">{{ scope.row.studentAppraiseDeptEffectiveNum }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="按时提交出科" align="center" prop="onTimeGraduationApplyNum" />
      <el-table-column label="总计" align="center" prop="total" />
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog :visible.sync="effectiveVisible" :title="effectiveTitle" width="80%">
      <el-table :data="effectiveData">
        <el-table-column
          v-for="(head, index) in effectiveHead"
          :key="index"
          :label="head.label"
          :prop="head.prop"
          align="center"
          :width="head.width"
        ></el-table-column>
      </el-table>
      <pagination
        v-show="effectiveTotal > 0"
        :total="effectiveTotal"
        :page.sync="effectiveParams.pageNo"
        :limit.sync="effectiveParams.pageSize"
        @pagination="getEffectiveList"/>
    </el-dialog>
  </div>
</template>

<script>
import { getSimpleMajorList } from "@/api/system/major";
import { getStudentGradeList } from "@/api/system/userStudent";
import { getStudentGpaStatisticPage, exportStudentGpaStatisticPage } from "@/api/rotation/studentgpastatistic";
import { getStudentEffectiveAppraiseActivePage, getStudentEffectiveAppraiseTeacherPage, getStudentEffectiveAppraiseDeptPage } from "@/api/rotation/studentgpastatistic";

export default {
  name: "StudentGpaStatistic",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员入科列表
      list: [],
      // 年级列表
      gradeOptions: [],
      // 培训专业列表
      majorOptions: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        grade: '',
        nickname: '',
        username: '',
        studentType: '',
        major: '',
        dispatchingUnit: '',
        completeDates: []
      },
      // 有效评价弹窗
      effectiveVisible: false,
      effectiveTitle: "",
      effectiveType: "active",
      effectiveParams: {
        pageNo: 1,
        pageSize: 10,
        userId: "",
        completeDates: [],
      },
      effectiveHead: [],
      effectiveData: [],
      effectiveTotal: 0,
    }
  },
  created() {
    this.getList();
    this.getGradeList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getStudentGpaStatisticPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /**查询年级 */
    getGradeList() {
      this.gradeOptions = []
      getStudentGradeList().then(res => {
        this.gradeOptions = res.data
      })
    },
    /**根据学员类型获取培训专业 */
    queryStudentTypeChange(val) {
      this.majorOptions = []
      getSimpleMajorList({studentType: val}).then(res => {
        this.majorOptions = res.data
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有学员绩点统计?').then(() => {
        this.exportLoading = true;
        return exportStudentGpaStatisticPage(params);
      }).then(response => {
        this.$download.excel(response, '学员绩点统计.xlsx');
        this.exportLoading = false;
      }).catch(() => {});
    },
    /** 展示有效评价数据 */
    showEffective(row, type) {
      this.effectiveType = type;
      this.effectiveParams = {
        pageNo: 1,
        pageSize: 10,
        userId: row.userId,
        completeDates: this.queryParams.completeDates,
      };
      const titleMap = {
        active: "教学活动有效评价",
        teacher: "对带教有效评价",
        dept: "对科室有效评价",
      }
      this.effectiveTitle = titleMap[type] || "有效评价";
      const headMap = {
        active: [
          { label: "活动名称", prop: "activeName", width: 180 },
          { label: "活动类型", prop: "activeType" },
          { label: "培训科室", prop: "departmentName" },
          { label: "培训人", prop: "speakerNickname" },
          { label: "培训时间", prop: "startEndTime", width: 280 },
          { label: "评价学员", prop: "appraiseNickname" },
          { label: "评价得分", prop: "score" },
          { label: "评价建议", prop: "comments", width: 180 },
        ],
        teacher: [
          { label: "带教老师", prop: "teacherNickname" },
          { label: "科室", prop: "departmentName" },
          { label: "学员姓名", prop: "appraiseNickname" },
          { label: "得分", prop: "score" },
          { label: "评价建议", prop: "comments" },
          { label: "评价时间", prop: "appraiseTime" },
        ],
        dept: [
          { label: "科室", prop: "departmentName" },
          { label: "学员姓名", prop: "appraiseNickname" },
          { label: "得分", prop: "score" },
          { label: "评价建议", prop: "comments" },
          { label: "评价时间", prop: "appraiseTime" },
        ],
      };
      this.effectiveHead = headMap[type] || [];
      this.getEffectiveList();
      this.effectiveVisible = true;
    },
    getEffectiveList() {
      const queryMethodMap = {
        active: getStudentEffectiveAppraiseActivePage,
        teacher: getStudentEffectiveAppraiseTeacherPage,
        dept: getStudentEffectiveAppraiseDeptPage,
      };
      const queryMethod = queryMethodMap[this.effectiveType];
      queryMethod && queryMethod(this.effectiveParams).then(res => {
        this.effectiveData = res.data.list.map(item => {
          const _item = { ...item };
          if (_item.activeType) {
            _item.activeType = this.getDictDataLabel(this.DICT_TYPE.ROTATION_ACTIVE_TYPE, _item.activeType);
          }
          return _item;
        });
        this.effectiveTotal = res.data.total;
      });
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
