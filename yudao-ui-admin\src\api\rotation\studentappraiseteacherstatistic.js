import request from '@/utils/request'

// 获得学员评价带教统计分页
export function getStudentappraiseteacherstatistic(query) {
  return request({
    url: '/rotation/studentappraiseteacherstatistic/page',
    method: 'get',
    params: query
  })
}

// 获得学员评价带教详情分页
export function getAppraiseStudentList(query) {
    return request({
      url: '/rotation/studentappraiseteacherstatistic/page-student',
      method: 'get',
      params: query
    })
  }

// 导出学员评价带教统计数据
export function exportStudentAppraiseTeacherStatistic(query) {
  return request({
    url: '/rotation/studentappraiseteacherstatistic/export',
    method: 'get',
    responseType: 'blob',
    params: query
  })
}

