<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="活动名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入活动名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动类型" prop="activeType">
        <el-select
          v-model="queryParams.activeType"
          placeholder="请选择活动类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="活动状态" prop="publishStatus">
        <el-select
          v-model="queryParams.publishStatus"
          placeholder="请选中活动状态"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.ROTATION_PUBLISH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="培训科室" prop="departmentName">
        <el-select
          v-model="queryParams.departmentName"
          filterable
          clearable
          placeholder="请选择培训科室"
          size="small"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训人" prop="speakerUsername">
        <el-input
          v-model="queryParams.speakerUsername"
          placeholder="请输入培训人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开展状态" prop="activeDevolepStatus">
        <el-select
          v-model="queryParams.activeDevolepStatus"
          placeholder="请选择开展状态"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ACTIVE_DEVOLEP_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训时间" prop="developDates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.developDates"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:teacher-teaching-active:create']">新增</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:teacher-teaching-active-plan:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="编号" align="center" prop="id" /> -->
      <el-table-column label="活动名称" align="center" prop="name" width="180">
        <template v-slot="scope">
          {{ scope.row.name }}
          <dict-tag
            class="valid-tag"
            size="mini"
            :type="DICT_TYPE.ACTIVE_DEVOLEP_STATUS"
            :value="scope.row.activeDevolepStatus"
          />
          <!-- <el-tag class="valid-tag" size="mini" :type="getActiveStatus(scope.row).type">
            {{ getActiveStatus(scope.row).text }}
          </el-tag> -->
        </template>
      </el-table-column>
      <el-table-column label="活动类型" align="center" prop="activeType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_ACTIVE_TYPE"
            :value="scope.row.activeType"
          />
        </template>
      </el-table-column>
      <el-table-column label="培训科室" align="center" prop="departmentName" />
      <el-table-column label="培训人" align="center" prop="speakerUsername" />
      <!-- <el-table-column label="学员类型" align="center" prop="studentTypes">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentTypes" />
        </template>
      </el-table-column> -->
      <el-table-column label="发布状态" align="center" prop="publishStatus">
        <template v-slot="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_PUBLISH_STATUS"
            :value="scope.row.publishStatus"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="待反馈数量"
        align="center"
        prop="supervisePendingFeedbackCnt"
        width="120"
      >
        <template v-slot="scope">{{
          scope.row.supervisePendingFeedbackCnt || "--"
        }}</template>
      </el-table-column>
      <el-table-column
        label="计划时间"
        align="center"
        prop="startTime"
        width="290"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.planStartTime && scope.row.planEndTime
              ? parseTime(scope.row.planStartTime, "{y}-{m}-{d} {h}:{i}") +
                " ~ " +
                parseTime(scope.row.planEndTime, "{y}-{m}-{d} {h}:{i}")
              : "--"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="培训时间"
        align="center"
        prop="endTime"
        width="290"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.startTime && scope.row.endTime
              ? parseTime(scope.row.startTime, "{y}-{m}-{d} {h}:{i}") +
                " ~ " +
                parseTime(scope.row.endTime, "{y}-{m}-{d} {h}:{i}")
              : "--"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="出勤率"
        align="center"
        prop="attendance"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewStudentDetail(scope.row)"
            >{{ scope.row.attendance || "--" }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="学员评价"
        align="center"
        prop="evaluationScore"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <div class="rate-wrapper">
            <div
              class="rate-click"
              :style="{
                cursor: scope.row.evaluationScore ? 'pointer' : 'default',
              }"
              @click="handleScoreClick(scope.row)"
            ></div>
            <el-rate
              v-model="scope.row.evaluationScore"
              disabled
              show-score
              text-color="#ff9900"
              :max="5"
              score-template="{value}"
            >
            </el-rate>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="督导得分"
        align="center"
        prop="avgSuperviseScore"
        width="120"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewSuperviseRecord(scope.row)"
            >{{ scope.row.avgSuperviseScore || "--" }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="310"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.publishStatus == 0"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rotation:teacher-teaching-active-plan:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handlePics(scope.row)"
            v-hasPermi="['rotation:teacher-teaching-active-plan:update']"
          >
            <el-badge type="success" is-dot :hidden="!scope.row.pictures">
              <div style="padding: 2px">活动照片</div>
            </el-badge>
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleCoursewares(scope.row)"
          >
            上传课件
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleFiles(scope.row)"
            v-hasPermi="['rotation:teacher-teaching-active-plan:update']"
          >
            <el-badge type="success" is-dot :hidden="!scope.row.files">
              <div style="padding: 2px">上传附件</div>
            </el-badge>
          </el-button>
          <!-- <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['rotation:teacher-teaching-active-plan:delete']">删除</el-button> -->
          <el-button
            v-if="
              scope.row.publishStatus == 0 &&
              scope.row.startTime &&
              scope.row.endTime
            "
            size="mini"
            type="text"
            icon="el-icon-s-promotion"
            @click="handlePublish(scope.row)"
            v-hasPermi="['rotation:teacher-teaching-active-plan:update']"
            >发布</el-button
          >
          <el-button
            v-if="scope.row.publishStatus == 1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handlePublishRevoke(scope.row)"
            v-hasPermi="['rotation:teacher-teaching-active-plan:update']"
            >撤销</el-button
          >
          <el-button
            v-if="scope.row.publishStatus == 1"
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handleViewQrcode(scope.row)"
            v-hasPermi="['rotation:teacher-teaching-active-plan:update']"
            >查看二维码</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-chat-line-round"
            @click="handleActiveNotice(scope.row)"
            >活动须知</el-button
          >
          <el-button
            v-if="scope.row.publishStatus == 1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleSelfAssessment(scope.row)"
            v-hasPermi="['rotation:teacher-teaching-active-plan:update']"
            >活动自评</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="教学活动名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入教学活动名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="活动类型" prop="activeType">
              <el-select
                v-model="form.activeType"
                filterable
                placeholder="请选择活动类型"
                @change="handleQueryPaperOptions"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.ROTATION_ACTIVE_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
            <el-form-item
              label="培训科室"
              prop="departmentId"
              label-width="80px"
            >
              <el-select
                v-model="form.departmentId"
                filterable
                placeholder="请选择培训科室"
                @change="getUserworkData"
                style="width: 100%"
              >
                <el-option
                  v-for="item in departmentPermissionOptions"
                  :key="parseInt(item.id)"
                  :label="item.name"
                  :value="parseInt(item.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="培训人" prop="speakerUserId">
              <el-select
                v-model="form.speakerUserId"
                filterable
                placeholder="请选择培训人"
              >
                <el-option
                  v-for="user in userWorkerOptions"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
            <el-form-item
              label="计划时间"
              prop="timeValuePlan"
              label-width="80px"
            >
              <el-date-picker
                style="width: 100%"
                v-model="form.timeValuePlan"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="开展方式" prop="developWay">
              <el-select
                v-model="form.developWay"
                filterable
                placeholder="请选择开展方式"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.ROTATION_DEVELOP_WAY
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
            <el-form-item label="培训时间" prop="timeValue" label-width="80px">
              <el-date-picker
                style="width: 100%"
                v-model="form.timeValue"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                @input="timeValueChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10" v-if="form.developWay == 2">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="参与方式" prop="joinWay" label-width="110px">
              <el-input
                v-model="form.joinWay"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="通知对象" prop="studentTypes">
              <el-select
                v-model="form.studentTypes"
                multiple
                filterable
                placeholder="请选择通知对象"
                style="width: 100%"
              >
                <el-option
                  v-for="user in studentTypesOptions"
                  :key="user.value"
                  :label="user.label"
                  :value="user.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="活动试卷" prop="paperId">
              <el-select
                v-model="form.paperId"
                filterable
                clearable
                placeholder="请选择活动试卷"
                style="width: 100%"
              >
                <el-option
                  v-for="user in paperOptions"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="学员专业" prop="studentMajors">
              <el-select
                v-model="form.studentMajors"
                placeholder="请选择学员专业"
                multiple
                filterable
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in queryMajorList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                  <span style="float: left">{{ item.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    <dict-tag
                      :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                      :value="item.studentType"
                    />
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="学员年级" prop="grades">
              <el-select
                v-model="form.grades"
                multiple
                filterable
                placeholder="请选择年级"
                style="width: 100%"
              >
                <el-option
                  v-for="grade in studentGradeList"
                  :key="grade"
                  :label="grade"
                  :value="grade"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="活动地点" prop="adress">
              <el-input
                v-model="form.adress"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="活动课件">
              <FileUpload
                v-model="form.coursewares"
                :limit="999"
                :fileSize="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('save')">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm('savePublish')"
          >直接发布</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      title="活动照片"
      :visible.sync="openPics"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div style="margin-bottom: 10px" v-if="activeNote">
        活动须知：{{ activeNote }}
      </div>
      <div>
        <div style="color: #303133; font-size: 18px; margin: 30px 0">
          现场照片
        </div>
        <imageUpload
          v-model="pictures"
          :limit="9999"
          :activeTypeName="
            this.getDictDataLabel(
              DICT_TYPE.ROTATION_ACTIVE_TYPE,
              curActive.activeType
            )
          "
        />
        <div style="color: #303133; font-size: 18px; margin: 30px 0">
          记录照片/资料照片
        </div>
        <imageUpload
          v-model="recordPictures"
          :limit="9999"
          :activeTypeName="
            this.getDictDataLabel(
              DICT_TYPE.ROTATION_ACTIVE_TYPE,
              curActive.activeType
            )
          "
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPics">保 存</el-button>
        <el-button @click="cancelSubmitPics">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="活动课件"
      :visible.sync="openCoursewares"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <fileUpload
        v-model="coursewares"
        :file-size="50"
        :limit="9999"
        activeTypeName=""
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCoursewares">保 存</el-button>
        <el-button @click="cancelSubmitCoursewares">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="活动附件"
      :visible.sync="openFiles"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div style="margin-bottom: 20px" v-if="activeNote">
        活动须知：{{ activeNote }}
      </div>
      <div>
        <fileUpload
          v-model="files"
          :file-size="50"
          :limit="9999"
          activeTypeName=""
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFiles">保 存</el-button>
        <el-button @click="cancelSubmitFiles">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="活动二维码"
      :visible.sync="openQrCode"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <div style="text-align: center">
        <img
          width="220"
          height="220"
          :src="'data:image/png;base64,' + curQrcode"
        />
      </div>
    </el-dialog>

    <el-dialog
      title="参加学员数详情"
      :visible.sync="openStudentDetail"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <div style="display: flex; justify-content: space-between">
          <el-form
            :model="queryStuListParams"
            size="small"
            :inline="true"
            label-width="80px"
          >
            <el-form-item label="是否签到" prop="isSigned">
              <el-select
                v-model="queryStudentParams.isSigned"
                placeholder="请选择是否已签到"
                clearable
                size="small"
                @change="
                  (val) => {
                    this.queryStuListParams.isSigned = val;
                    getStudentList(curActive.id);
                  }
                "
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.INFRA_BOOLEAN_STRING
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <div style="text-align: right">
            <el-button size="mini" @click="handleAddStudent">
              添加学员
            </el-button>
          </div>
        </div>
        <el-table v-loading="studentDetailLoading" :data="studentDetailList">
          <el-table-column
            label="学员姓名"
            align="left"
            prop="nickname"
            width="160"
          >
            <template v-slot="scope">
              <div style="display: flex; align-items: center">
                <span style="margin-right: 5px">{{ scope.row.nickname }}</span>
                <el-tag v-if="scope.row.isLeave" size="mini">请假中</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学员类型" prop="studentType" align="center">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                :value="scope.row.studentType"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column label="培训专业" align="center" prop="majorName" />
          <el-table-column label="年级" align="center" prop="grade" />
          <el-table-column label="扫码时间" align="center" prop="scanningTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.scanningTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="参与形式" align="center" prop="joinType">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.ROTATION_JOIN_TYPE"
                :value="scope.row.joinType"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="120"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-if="!scope.row.scanningTime"
                size="mini"
                type="text"
                @click="handleJoin(scope.row)"
                >确认参加</el-button
              >
              <el-button
                v-else
                size="mini"
                type="text"
                @click="handleRevoke(scope.row)"
                >撤销参加</el-button
              >
              <el-button
                v-if="!scope.row.scanningTime"
                size="mini"
                type="text"
                @click="handleStudentDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <el-dialog
      title="添加学员"
      :visible.sync="openAddStudents"
      width="900px"
      v-dialogDrag
      append-to-body
    >
      <div class="add-student-table">
        <div class="head-info">
          <el-form
            :model="curActive"
            ref="curActiveForm"
            size="small"
            :inline="true"
            label-width="120px"
          >
            <el-form-item label="培训时间" prop="timeArr" label-width="80px">
              <el-date-picker
                style="width: 100%"
                v-model="curActive.timeArr"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="true"
              />
            </el-form-item>
            <el-form-item
              label="学员类型"
              prop="studentTypesArr"
              label-width="100px"
            >
              <el-select
                v-model="curActive.studentTypesArr"
                multiple
                filterable
                placeholder="请选择"
                :disabled="true"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_STUDENT_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <el-form
          :model="queryStudentParams"
          ref="queryStudentForm"
          size="small"
          :inline="true"
          label-width="120px"
        >
          <!-- <el-form-item label="学员姓名" prop="nickname" label-width="80px">
            <el-input v-model="queryStudentParams.nickname" placeholder="请输入姓名" clearable @keyup.enter.native="handleQueryStudent" style="width: 120px"/>
          </el-form-item> -->
          <el-form-item label="教研室" prop="staffRoomValue" label-width="80px">
            <el-select
              v-model="queryStudentParams.staffRoomValue"
              filterable
              clearable
              placeholder="请选择教研室"
              @change="getDepartment"
            >
              <el-option
                v-for="dict in this.getDictDatas(DICT_TYPE.STAFF_ROOM)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="专业" prop="major" label-width="40px">
            <el-select
              v-model="queryStudentParams.major"
              filterable
              clearable
              placeholder="请选择专业"
            >
              <el-option
                v-for="item in queryMajorList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="年级" prop="grade" label-width="40px">
            <el-select
              v-model="queryStudentParams.grade"
              filterable
              clearable
              placeholder="请选择年级"
            >
              <el-option
                v-for="grade in studentGradeList"
                :key="grade"
                :label="grade"
                :value="grade"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="派送单位"
            prop="dispatchingUnit"
            label-width="80px"
          >
            <el-input
              v-model="queryStudentParams.dispatchingUnit"
              clearable
              placeholder="请输入派送单位"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="当前医院科室"
            prop="departmentIds"
            label-width="100px"
          >
            <el-select
              v-model="queryStudentParams.departmentIds"
              multiple
              filterable
              clearable
              placeholder="请选择"
              size="small"
              style="width: 400px"
            >
              <el-option
                v-for="item in departmentOptions"
                :key="parseInt(item.id)"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleQueryStudent"
              >学员搜索</el-button
            >
          </el-form-item>
        </el-form>
        <div style="display: flex; justify-content: center">
          <el-table
            v-loading="queryStudentLoading"
            :data="studentAddList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="学员姓名" align="center" prop="nickname" />
            <el-table-column label="学员类型" prop="studentType" align="center">
              <template v-slot="scope">
                <dict-tag
                  :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                  :value="scope.row.studentType"
                ></dict-tag>
              </template>
            </el-table-column>
            <el-table-column label="年级" align="center" prop="grade" />
            <el-table-column label="专业" align="center" prop="majorName" />
            <el-table-column
              label="派送单位"
              align="center"
              prop="dispatchingUnit"
            />
            <el-table-column
              label="当前轮转科室"
              align="center"
              prop="rotationDepartmentName"
            />
          </el-table>
        </div>
        <div
          slot="footer"
          class="dialog-footer"
          style="text-align: right; padding-top: 15px"
        >
          <el-button type="primary" @click="submitAddStudents"
            >确认添加</el-button
          >
          <el-button @click="cancelAddStudents">取 消</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="评价得分情况"
      :visible.sync="openScore"
      width="900px"
      v-dialog-drag
      append-to-body
      custom-class="appraise-score-dialog"
    >
      <el-tabs style="padding-bottom: 20px">
        <el-tab-pane label="指标平均得分">
          <el-form inline>
            <el-form-item label="评价对象：" style="margin-right: 30px">{{
              appraiseObject
            }}</el-form-item>
            <el-form-item label="授课平均分：">{{
              synthesizeScore
            }}</el-form-item>
          </el-form>
          <el-table style="margin-bottom: 20px" :data="indexScoreList">
            <el-table-column
              label="序号"
              type="index"
              align="center"
            ></el-table-column>
            <el-table-column label="活动类型" align="center">
              <template v-slot="scope">
                <dict-tag
                  :type="DICT_TYPE.ROTATION_ACTIVE_TYPE"
                  :value="currentActive && currentActive.activeType"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="评价指标"
              prop="appraiseKpi"
              align="center"
            ></el-table-column>
            <el-table-column
              label="指标得分"
              prop="score"
              align="center"
            ></el-table-column>
          </el-table>

          <el-form>
            <el-form-item label="教师对学员评价的集中反馈">
              <el-input
                v-model="indexFeedback"
                type="textarea"
                :rows="3"
                placeholder="请先查看学员对您的评价，针对学员对您的评价做出反馈！"
              ></el-input>
            </el-form-item>
            <el-button type="primary" @click="submitIndexFeedback">
              提交
            </el-button>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="学员评分详情">
          <!-- <el-button
            class="exportBtn"
            v-if="!studentTeacherAppraise"
            type="primary"
            @click="handleExport"
            :loading="exportLoading"
          >
            导出
          </el-button> -->
          <el-button
            class="exportBtn"
            type="primary"
            @click="handleExport"
            :loading="exportLoading"
          >
            导出
          </el-button>
          <el-form inline>
            <el-form-item label="评价对象：" style="margin-right: 30px">{{
              appraiseObject
            }}</el-form-item>
            <el-form-item label="综合得分：">{{
              synthesizeScore
            }}</el-form-item>
          </el-form>
          <el-table :data="studentScoreList">
            <el-table-column
              label="学员姓名"
              prop="nickname"
              align="center"
            ></el-table-column>
            <el-table-column label="学员类型" prop="studentType" align="center">
              <template v-slot="scope">
                <dict-tag
                  :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                  :value="scope.row.studentType"
                ></dict-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="轮转科室"
              prop="rotationDepartmentName"
              align="center"
            ></el-table-column>
            <el-table-column label="评价得分" prop="score" align="center">
              <template v-slot="scope">
                <el-link
                  type="primary"
                  @click.native="showAppraiseDetail(scope.row)"
                  >{{ scope.row.score }}</el-link
                >
              </template>
            </el-table-column>
            <el-table-column
              label="评价建议"
              prop="comments"
              align="center"
              min-width="150"
            ></el-table-column>
            <el-table-column
              label="评价时间"
              prop="createTime"
              align="center"
              width="150"
            >
              <template v-slot="scope">
                {{ new Date(scope.row.createTime).toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template v-slot="scope">
                <el-link
                  type="primary"
                  v-if="!scope.row.feedback"
                  @click.native="showSpeakFeedback(scope.row)"
                  >主讲反馈</el-link
                >
                <span v-else>已反馈</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="studentScoreTotal > 0"
            :total="studentScoreTotal"
            :page.sync="studentScoreQuery.pageNo"
            :limit.sync="studentScoreQuery.pageSize"
            @pagination="queryAppraiseDetails"
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog
      title="主讲反馈"
      :visible.sync="openSpeakerFeedback"
      width="400px"
      append-to-body
    >
      <el-input
        type="textarea"
        placeholder="请在此填写您的评价反馈"
        :rows="3"
        v-model="speakerFeedback"
      ></el-input>
      <template v-slot:footer>
        <el-button type="primary" @click="sureSpeakerFeedback">确定</el-button>
        <el-button @click="cancelSpeakerFeedback">取消</el-button>
      </template>
    </el-dialog>

    <el-dialog
      title="活动自评"
      :visible.sync="openActiveSelfAssessment"
      width="400px"
      append-to-body
    >
      <el-form :model="selfAssessmentForm" :rule="selfAssessmentRules">
        <el-form-item style="margin-bottom: 0" label="活动名称">{{
          selfAssessmentForm.name
        }}</el-form-item>
        <el-form-item label="活动自评" prop="selfAssessment">
          <el-input
            type="textarea"
            v-model="selfAssessmentForm.selfAssessment"
            placeholder="请输入活动自评"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="submitSelfAssessment">提交</el-button>
        <el-button @click="openActiveSelfAssessment = false">取消</el-button>
      </span>
    </el-dialog>

    <appraise-dialog
      v-if="appraiseDetailData"
      :title="appraiseTitle"
      :open="openAppraiseDetail"
      :data="appraiseDetailData"
      disabled
      @setOpen="openAppraiseDetail = $event"
    ></appraise-dialog>

    <supervise-record-dialog
      :visible.sync="superviseRecordVisible"
      :supervise-object="superviseRow"
      can-feedback
    ></supervise-record-dialog>

    <!-- <el-dialog title="添加学员" :visible.sync="openAddStudents" width="760px" v-dialogDrag append-to-body>
      <div class="add-student-transfer">
        <el-form :model="queryStudentParams" ref="queryStudentForm" size="small" :inline="true" label-width="58px">
          <el-form-item label="姓名" prop="nickname">
            <el-input v-model="queryStudentParams.nickname" placeholder="请输入姓名" clearable @keyup.enter.native="handleQueryStudent" style="width: 120px"/>
          </el-form-item>
          <el-form-item label="年级" prop="grade">
            <el-select v-model="queryStudentParams.grade" placeholder="请选择年级" filterable clearable size="small" style="width: 120px">
              <el-option v-for="grade in studentGradeList" :key="grade" :label="grade" :value="grade" />
            </el-select>
          </el-form-item>
          <el-form-item label="专业" prop="major">
            <el-select v-model="queryStudentParams.major" placeholder="请选择培训专业" filterable clearable size="small" style="width: 150px">
          <el-option v-for="item in queryMajorList" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQueryStudent">学员搜索</el-button>
          </el-form-item>
        </el-form>
        <div style="display: flex; justify-content: center;">
          <el-transfer v-model="userIds" :data="studentAddList" :props="{key: 'id', label: 'nickname'}"></el-transfer>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAddStudents">确认添加</el-button>
        <el-button @click="cancelAddStudents">取 消</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import {
  getDepartmentSimpleList,
  getDepartmentPermissionList,
} from "@/api/system/department";
import { getUserWorkerCurrentList } from "@/api/system/userWorker";
import { getStudentTypes } from "@/api/system/user";
import {
  getStudentGradeList,
  getNotJoinedStuList,
} from "@/api/system/userStudent";
import { getSimpleMajorList } from "@/api/system/major";
import { getStaffRoomDepartmentPage } from "@/api/system/staffRoomDepartment";
import {
  updatePublishTeachingActive,
  updateTeachingActive,
  deleteTeachingActive,
  getTeachingActive,
  getTeachingActivePage,
  exportTeachingActiveExcel,
  getStudentsList,
  confirmJoin,
  revokeJoin,
  deleteStudentUsers,
  publishTeachingActive,
  revokeTeachingActive,
  addStudentUsers,
  updateSelfAssessment,
  updateTeachingActivePictures,
  updateTeachingActiveFiles,
  updateTeachingActiveCoursewares,
} from "@/api/rotation/teachingActivePublish";
import {
  getComprehensiveByParam,
  getAppraiseDetails,
  saveFeedbackResult,
  exportAppraiseDetailsExcel,
} from "@/api/rotation/appraiseActiveFeedbackResult";
import {
  saveFeedback,
  getAppraiseResult,
  getAppraiseActiveNote,
} from "@/api/rotation/appraiseActive";
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";
import AppraiseDialog from "../studentTeachingActive/appraiseDialog";
import SuperviseRecordDialog from "../teachingActiveSupervise/supervise-record-dialog";
import { getTeachingActivityPaper } from '@/api/exam/paper'

export default {
  name: "TeachingActivePublish",
  components: {
    FileUpload,
    ImageUpload,
    AppraiseDialog,
    SuperviseRecordDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      studentDetailLoading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 教学活动列表
      list: [],
      queryStuListParams: {
        isSigned: null,
      },
      studentDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openPics: false,
      pictures: "",
      recordPictures: "",
      openFiles: false,
      files: "",
      openCoursewares: false,
      coursewares: [],
      openStudentDetail: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        activeType: null,
        publishStatus: null,
        departmentName: null,
        studentType: null,
        speakerUsername: "",
        activeDevolepStatus: null,
        developDates: [],
        isNeedQrcode: true,
      },
      paperOptions: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "教学活动名称不能为空", trigger: "blur" },
        ],
        activeType: [
          { required: true, message: "活动类型不能为空", trigger: "change" },
        ],
        departmentId: [
          { required: true, message: "培训科室不能为空", trigger: "change" },
        ],
        speakerUserId: [
          { required: true, message: "培训人不能为空", trigger: "blur" },
        ],
        studentTypes: [
          {
            type: "array",
            required: true,
            message: "学员类型不能为空",
            trigger: "change",
          },
        ],
        timeValue: [
          { required: true, message: "请选择培训时间", trigger: "change" },
        ],
        developWay: [
          { required: true, message: "开展方式不能为空", trigger: "change" },
        ],
        joinWay: [
          { required: true, message: "参与方式不能为空", trigger: "blur" },
        ],
      },
      departmentOptions: [],
      userWorkerOptions: [],
      departmentPermissionOptions: [],
      studentTypesOptions: [],
      pickerOptions: {},
      curActive: {},
      openAddStudents: false,
      studentAddList: [],
      userIds: [],
      studentGradeList: [],
      queryMajorList: [],
      queryStudentParams: {
        staffRoomValue: "",
        major: "",
        dispatchingUnit: "",
        departmentIds: [],
        grade: "",
      },
      openQrCode: false,
      curQrcode: "",
      queryStudentLoading: false,
      staffRoomList: [],
      // 评价反馈
      openScore: false,
      currentActive: null,
      appraiseObject: null,
      synthesizeScore: "",
      indexFeedbackId: "",
      appraiseActiveId: "",
      indexScoreList: [],
      indexFeedback: "",
      studentScoreQuery: {
        pageNo: 1,
        pageSize: 10,
      },
      studentScoreTotal: 0,
      studentScoreList: [],
      handleStudentScore: null,
      openSpeakerFeedback: false,
      speakerFeedback: "",
      openAppraiseDetail: false,
      appraiseTitle: "",
      appraiseDetailData: null,
      // 督导得分
      superviseRecordVisible: false,
      superviseRow: null,
      // 活动自评
      openActiveSelfAssessment: false,
      selfAssessmentForm: { selfAssessment: "" },
      selfAssessmentRules: {
        selfAssessment: [
          { required: true, message: "请输入活动自评", trigger: "blur" },
        ],
      },
      // 活动须知
      activeNote: "",
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    this.getPermissionDepartment();
    this.getStudentTypesList();
    getStudentGradeList().then((res) => {
      this.studentGradeList = res.data;
    });
    getSimpleMajorList().then((res) => {
      this.queryMajorList = res.data;
    });
    // this.getStaffRoom()
    getAppraiseActiveNote("1").then((res) => {
      this.activeNote = res.data.notice;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getTeachingActivePage(this.queryParams).then((response) => {
        const list = response.data.list;
        list.forEach((item) => {
          if (!item.score) {
            item.evaluationScore = 0;
          } else {
            const _score = (item.evaluationScore / item.score) * 5;
            item.evaluationScore = _score;
          }
        });
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment(val = "") {
      // 获得科室列表
      if (val) {
        this.queryStudentParams.departmentIds = [];
      }
      getDepartmentSimpleList(0, val).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    getPermissionDepartment() {
      // 获得科室列表
      const params = { component: "rotation/teachingActivePublish/index" };
      getDepartmentPermissionList(params).then((res) => {
        // 处理 roleOptions 参数
        this.departmentPermissionOptions = [];
        this.departmentPermissionOptions.push(...res.data);
      });
    },
    getStudentTypesList() {
      const params = { component: "rotation/teachingActivePublish/index" };
      getStudentTypes(params).then((res) => {
        this.studentTypesOptions = [];
        this.studentTypesOptions.push(...res.data);
      });
    },
    handleQueryPaperOptions(reset = true) {
      if (reset) this.form.paperId = undefined;
      const { departmentId, activeType } = this.form;
      if (!departmentId || !activeType) {
        this.paperOptions = [];
        return;
      }
      getTeachingActivityPaper({ departmentId, teachingActiveType: activeType }).then(res => {
        this.paperOptions = res.data;
      });
    },
    async getUserworkData(val) {
      this.handleQueryPaperOptions();
      this.form.speakerUserId = "";
      const params = {
        departmentId: val,
      };
      const { data } = await getUserWorkerCurrentList(params);
      this.userWorkerOptions = data || [];
    },
    // getStaffRoom() {
    //   const params = {
    //     pageNo: 1,
    //     pageSize: 999,
    //   }
    //   getStaffRoomDepartmentPage(params).then(response => {
    //     this.staffRoomList = response.data.list;
    //   });
    // },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelSubmitPics() {
      this.openPics = false;
    },
    cancelSubmitCoursewares() {
      this.openCoursewares = false;
    },
    cancelSubmitFiles() {
      this.openFiles = false;
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        activeType: undefined,
        departmentId: undefined,
        speakerUserId: undefined,
        studentTypes: [],
        paperId: undefined,
        studentMajors: [],
        grades: [],
        timeValue: null,
        timeValuePlan: [],
        startTime: undefined,
        endTime: undefined,
        planEndTime: undefined,
        planStartTime: undefined,
        developWay: undefined,
        joinWay: undefined,
        examineId: undefined,
        adress: undefined,
        coursewares: undefined,
        pictures: undefined,
        recordPictures: undefined,
      };
      this.resetForm("form");
    },
    timeValueChange(values) {
      console.log("timeValueChange====", values);
      this.form.startTime = undefined;
      this.form.endTime = undefined;
      this.form.timeValue = null;
      if (values) {
        this.form.startTime = values[0];
        this.form.endTime = values[1];
        this.form.timeValue = values;
      }
      this.$forceUpdate();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加教学活动";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getTeachingActive(id).then(async (response) => {
        await this.getUserworkData(response.data.departmentId);
        this.form = response.data;
        this.form.studentTypes = this.form.studentTypes
          ? this.form.studentTypes.split(",")
          : [];
        this.form.studentMajors = this.form.studentMajors
          ? this.form.studentMajors.split(",")
          : [];
        this.form.grades = this.form.grades ? this.form.grades.split(",") : [];
        if (this.form.startTime && this.form.endTime) {
          this.form.timeValue = [this.form.startTime, this.form.endTime];
        }
        this.form.timeValuePlan = null;
        if (this.form.planStartTime && this.form.planEndTime) {
          this.form.timeValuePlan = [
            this.form.planStartTime,
            this.form.planEndTime,
          ];
        }
        this.pickerOptions = {
          disabledDate: (time) => {
            return (
              time.getTime() > new Date(this.form.planEndTime).getTime() ||
              time.getTime() <
                new Date(this.form.planStartTime).getTime() -
                  1 * 24 * 60 * 60 * 1000
            );
          },
        };

        this.form.coursewares = this.form.coursewares
          ? JSON.parse(this.form.coursewares)
          : [];
        this.handleQueryPaperOptions(false);
        this.open = true;
        this.title = "修改教学活动";
      });
    },
    handlePics(row) {
      this.curActive = row;
      getTeachingActive(row.id).then((response) => {
        this.pictures = response.data.pictures;
        this.recordPictures = response.data.recordPictures;
        this.openPics = true;
      });
    },
    handleCoursewares(row) {
      this.curActive = row;
      getTeachingActive(row.id).then((response) => {
        const coursewares = response.data.coursewares;
        this.coursewares = coursewares ? JSON.parse(coursewares) : null;
        this.openCoursewares = true;
      });
    },
    handleFiles(row) {
      this.curActive = row;
      getTeachingActive(row.id).then((response) => {
        const { files, coursewares } = response.data;
        this.files = files ? JSON.parse(files) : null;
        this.coursewares = coursewares ? JSON.parse(coursewares) : null;
        this.openFiles = true;
      });
    },
    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = { ...this.form };
        delete params.timeValue;
        delete params.timeValuePlan;
        params.studentTypes = params.studentTypes
          ? params.studentTypes.join(",")
          : "";
        params.studentMajors = params.studentMajors
          ? params.studentMajors.join(",")
          : "";
        params.grades = params.grades ? params.grades.join(",") : "";
        params.coursewares = params.coursewares
          ? JSON.stringify(params.coursewares)
          : "";
        // 保存
        if (type === "save") {
          updateTeachingActive(params).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 保存发布提交
        if (type === "savePublish") {
          getAppraiseActiveNote(params.activeType).then((res) => {
            const note = res.data?.notice || "";
            const submitFn = () =>
              updatePublishTeachingActive(params).then((response) => {
                this.$modal.msgSuccess("发布成功");
                this.open = false;
                this.getList();
              });
            if (note) {
              this.$confirm(note, "活动须知").then(() => submitFn());
            } else {
              submitFn();
            }
          });
        }
      });
    },
    handlePublish(row) {
      getAppraiseActiveNote(row.activeType).then((res) => {
        const note = res.data?.notice || "";
        const submitFn = () =>
          publishTeachingActive(row.id).then((response) => {
            this.$modal.msgSuccess("发布成功");
            this.getList();
          });
        if (note) {
          this.$confirm(note, "活动须知").then(() => submitFn());
        } else {
          submitFn();
        }
      });
    },
    handlePublishRevoke(row) {
      revokeTeachingActive(row.id).then((response) => {
        this.$modal.msgSuccess("撤销成功");
        this.getList();
      });
    },
    submitPics() {
      updateTeachingActivePictures({
        id: this.curActive.id,
        pictures: this.pictures,
        recordPictures: this.recordPictures,
      }).then((response) => {
        this.$modal.msgSuccess("保存成功");
        this.openPics = false;
        this.getList();
      });
    },
    submitCoursewares() {
      updateTeachingActiveCoursewares({
        id: this.curActive.id,
        coursewares:
          this.coursewares && this.coursewares.length > 0
            ? JSON.stringify(this.coursewares)
            : "",
      }).then(() => {
        this.$modal.msgSuccess("保存成功");
        this.openCoursewares = false;
        this.getList();
      });
    },
    submitFiles() {
      updateTeachingActiveFiles({
        id: this.curActive.id,
        files:
          this.files && this.files.length > 0 ? JSON.stringify(this.files) : "",
      }).then(() => {
        this.$modal.msgSuccess("保存成功");
        this.openFiles = false;
        this.getList();
      });
    },
    handleSelfAssessment(row) {
      this.selfAssessmentForm.id = row.id;
      this.selfAssessmentForm.name = row.name;
      getTeachingActive(row.id).then((res) => {
        this.selfAssessmentForm.selfAssessment = res.data?.selfAssessment || "";
      });
      this.openActiveSelfAssessment = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除教学活动编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteTeachingActive(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有教学活动数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportTeachingActiveExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "教学活动.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    viewStudentDetail(row) {
      if (!row.attendance) {
        return;
      }
      this.getStudentList(row.id, () => {
        this.openStudentDetail = true;
        this.curActive = row;
        this.curActive.timeArr =
          row.startTime && row.endTime ? [row.startTime, row.endTime] : [];
        this.curActive.studentTypesArr = row.studentTypes
          ? row.studentTypes.split(",")
          : null;
        this.queryStudentParams.departmentIds = [row.departmentId];
      });
    },
    getStudentList(id, callback) {
      const params = {
        isSigned: this.queryStuListParams.isSigned,
        teachingActiveId: id,
      };
      this.studentDetailLoading = true;
      getStudentsList(params).then((response) => {
        this.studentDetailList = response.data || [];
        if (callback) {
          callback();
        }
        this.studentDetailLoading = false;
      });
    },
    handleJoin(row) {
      const params = {
        teachingActiveId: row.teachingActiveId,
        userId: row.userId,
      };
      confirmJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getStudentList(row.teachingActiveId);
        this.getList();
      });
    },
    handleRevoke(row) {
      const params = {
        teachingActiveId: row.teachingActiveId,
        userId: row.userId,
      };
      revokeJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getStudentList(row.teachingActiveId);
        this.getList();
      });
    },
    handleQueryStudent() {
      const params = {
        teachingActiveId: this.curActive.id,
        staffRoomValue: this.queryStudentParams.staffRoomValue,
        major: this.queryStudentParams.major,
        grade: this.queryStudentParams.grade,
        dispatchingUnit: this.queryStudentParams.dispatchingUnit,
        departmentIds: this.queryStudentParams.departmentIds.join(","),
      };
      getNotJoinedStuList(params).then((res) => {
        this.studentAddList = res.data || [];
      });
    },
    handleAddStudent() {
      this.handleQueryStudent();
      this.openAddStudents = true;
    },
    cancelAddStudents() {
      this.openAddStudents = false;
    },
    handleStudentDelete(row) {
      const id = row.id;
      this.$modal
        .confirm(`是否确认删除该的学员?`)
        .then(() => {
          const params = {
            teachingActiveId: this.curActive.id,
            userId: row.userId,
          };
          return deleteStudentUsers(params);
        })
        .then(() => {
          this.getStudentList(this.curActive.id);
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    submitAddStudents() {
      const params = {
        teachingActiveId: this.curActive.id,
        userReqVOList: this.userIds,
      };
      addStudentUsers(params).then((res) => {
        this.$modal.msgSuccess("添加成功");
        this.openAddStudents = false;
        this.getStudentList(this.curActive.id);
        this.getList();
      });
    },
    handleViewQrcode(row) {
      this.curQrcode = row.qrcode;
      this.openQrCode = true;
    },
    /** 查看活动须知 */
    handleActiveNotice(row) {
      this.$alert(row.activeNotice || "暂无活动须知", "活动须知");
    },
    handleSelectionChange(val) {
      console.log("handleSelectionChange=====", val);
      this.userIds = val.map((item) => {
        return {
          scheduleDetailsId: item.scheduleDetailsId || null,
          userId: item.studentId,
        };
      });
    },
    // 获取学员评分详情
    queryAppraiseDetails() {
      const { pageNo, pageSize } = this.studentScoreQuery;
      const { id, activeType } = this.currentActive;
      getAppraiseDetails({
        activeId: id,
        activeType,
        appraiseActiveType: "1",
        pageNo,
        pageSize,
      }).then((res) => {
        this.studentScoreList = res.data.list;
        this.studentScoreTotal = res.data.total;
      });
    },
    // 导出
    handleExport() {
      // 处理查询参数
      const { pageNo, pageSize } = this.studentScoreQuery;
      const { id, activeType } = this.currentActive;
      const params = {
        activeId: id,
        activeType,
        appraiseActiveType: "1",
        pageNo,
        pageSize,
      };
      this.$modal
        .confirm("是否确认导出学员评价详情?")
        .then(() => {
          this.exportLoading = true;
          return exportAppraiseDetailsExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "学员评价详情.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    // 查看评价得分情况
    handleScoreClick(item) {
      if (!item.evaluationScore) return;
      this.openScore = true;
      this.studentScoreQuery.pageNo = 1;
      this.currentActive = item;
      const { id, activeType } = item;
      getComprehensiveByParam({
        activeId: id,
        activeType,
        appraiseActiveType: "1",
      }).then((res) => {
        this.appraiseObject = res.data.targetObject;
        this.synthesizeScore = res.data.score;
        this.indexFeedbackId = res.data.id;
        this.appraiseActiveId = res.data.appraiseActiveId;
        this.indexFeedback = res.data.feedback;
        this.indexScoreList = res.data.resultItemRespVOS;
      });
      this.queryAppraiseDetails();
    },
    // 提交活动评价综合反馈结果
    submitIndexFeedback() {
      if (!this.indexFeedback) {
        this.$message.warning("请先填写综合反馈！");
        return;
      }
      const { id, activeType } = this.currentActive;
      saveFeedbackResult({
        id: this.indexFeedbackId,
        activeId: id,
        appraiseActiveId: this.appraiseActiveId,
        activeType,
        appraiseActiveType: "1",
        feedback: this.indexFeedback,
      }).then(() => {
        this.$message.success("提交综合反馈成功！");
      });
    },
    // 主讲反馈
    showSpeakFeedback(row) {
      this.handleStudentScore = row;
      this.openSpeakerFeedback = true;
    },
    cancelSpeakerFeedback() {
      this.openSpeakerFeedback = false;
      this.handleStudentScore = null;
      this.speakerFeedback = "";
    },
    sureSpeakerFeedback() {
      if (!this.speakerFeedback) {
        this.$message.warning("请输入评价反馈再提交！");
        return;
      }
      saveFeedback({
        id: this.handleStudentScore.id,
        feedback: this.speakerFeedback,
      }).then(() => {
        this.$message.success("保存主讲反馈成功！");
        this.queryAppraiseDetails();
        this.openSpeakerFeedback = false;
      });
    },
    // 显示评价详情
    showAppraiseDetail(row) {
      getAppraiseResult({ id: row.id }).then((response) => {
        this.appraiseDetailData = response.data;
        this.openAppraiseDetail = true;
        this.appraiseTitle = `查看评价-${row.nickname}`;
      });
    },
    // 获取活动状态
    getActiveStatus(row) {
      const curTime = new Date(row.currentTime).getTime();
      const endTime = new Date(row.endTime).getTime();
      if (row.publishStatus === 0 || curTime < endTime) {
        return { text: "待完成", type: "primary" };
      }
      if (row.publishStatus === 1 && row.pictures && row.evaluationScore) {
        return { text: "有效", type: "success" };
      }
      return { text: "无效", type: "warning" };
    },
    // 督导得分
    viewSuperviseRecord(row) {
      this.superviseRow = row;
      this.superviseRecordVisible = true;
    },
    // 活动自评
    submitSelfAssessment() {
      updateSelfAssessment(this.selfAssessmentForm).then(() => {
        this.$message.success("发布活动自评成功！");
        this.openActiveSelfAssessment = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.rate-wrapper {
  position: relative;
}

.rate-click {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.valid-tag {
  position: absolute;
  top: 1px;
  right: 0;
}

.appraise-score-dialog {
  .el-tabs__content {
    position: relative;
  }
  .exportBtn {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>
