{"remainingRequest": "D:\\rotation\\ruoyi-vue-pro\\yudao-ui-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\rotation\\ruoyi-vue-pro\\yudao-ui-admin\\src\\views\\teachers\\evaluationPlanApplyConfirm\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\rotation\\ruoyi-vue-pro\\yudao-ui-admin\\src\\views\\teachers\\evaluationPlanApplyConfirm\\index.vue", "mtime": 1753963651819}, {"path": "D:\\rotation\\ruoyi-vue-pro\\yudao-ui-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1715608490663}, {"path": "D:\\rotation\\ruoyi-vue-pro\\yudao-ui-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1712655483706}, {"path": "D:\\rotation\\ruoyi-vue-pro\\yudao-ui-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1715608490663}, {"path": "D:\\rotation\\ruoyi-vue-pro\\yudao-ui-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1715608492836}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBGaWxlVXBsb2FkIGZyb20gIkAvY29tcG9uZW50cy9GaWxlVXBsb2FkSW5mbyI7CmltcG9ydCB1c2VXb3JkZXJEaWFsb2cgZnJvbSAiLi4vLi4vc3lzdGVtL3VzZXJXb3JrZXIvdXNlV29yZGVyRGlhbG9nIjsKaW1wb3J0IHVzZXJTdHVkZW50RGlhbG9nIGZyb20gIi4uLy4uL3N5c3RlbS91c2VyU3R1ZGVudC91c2VyU3R1ZGVudERpYWxvZyI7CmltcG9ydCB7CiAgZGVsZXRlRXZhbHVhdGlvblBsYW5BcHBseUNvbmZpcm0sCiAgZ2V0RXZhbHVhdGlvblBsYW5BcHBseUNvbmZpcm1QYWdlLAogIGV4cG9ydEV2YWx1YXRpb25QbGFuQXBwbHlDb25maXJtRXhjZWwsCiAgdXBkYXRlRXZhbHVhdGlvblNjb3JlLAogIHVwZGF0ZUJhdGNoUmVzdWx0LAogIGdldEV2YWx1YXRpb25QbGFuQXBwbHksCiAgdXBkYXRlU2VsZWN0aW9uUmVzdWx0LAp9IGZyb20gIkAvYXBpL3RlYWNoZXJzL2V2YWx1YXRpb25QbGFuQXBwbHlDb25maXJtIjsKaW1wb3J0IHsgZ2V0QWNjZXNzVG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOwppbXBvcnQgYXhpb3MgZnJvbSAiYXhpb3MiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJFdmFsdWF0aW9uUGxhbkFwcGx5Q29uZmlybSIsCiAgY29tcG9uZW50czogeyBGaWxlVXBsb2FkLCB1c2VXb3JkZXJEaWFsb2csIHVzZXJTdHVkZW50RGlhbG9nIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDlr7zlh7rpga7nvanlsYIKICAgICAgZXhwb3J0TG9hZGluZzogZmFsc2UsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOW4iOi1hOivhOS8mOiuoeWIkueUs+ivt+ehruiupOWIl+ihqAogICAgICBsaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTm86IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBhcHBseU5pY2tuYW1lOiB1bmRlZmluZWQsCiAgICAgICAgeWVhcjogdW5kZWZpbmVkLAogICAgICAgIHRlYWNoZXJzRXZhbHVhdGlvblByb2plY3Q6IHVuZGVmaW5lZCwKICAgICAgICBzZWxlY3Rpb25SZXN1bHQ6IG51bGwsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdGVhY2hlcnNFdmFsdWF0aW9uUGxhbklkOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+E5LyY6K6h5YiSaWTkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIHNlbGVjdGlvblJlc3VsdDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumBtOmAiee7k+aenOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgb3BlbkJhdGNoOiBmYWxzZSwKICAgICAgYmF0Y2hGb3JtOiB7CiAgICAgICAgc2VsZWN0aW9uUmVzdWx0OiAiYmVfc2VsZWN0ZWQiLAogICAgICB9LAogICAgICBiYXRjaFJ1bGVzOiB7CiAgICAgICAgc2VsZWN0aW9uUmVzdWx0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6YG06YCJ57uT5p6c5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfSwKICAgICAgICBdLAogICAgICB9LAogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sCiAgICAgIG9wdDogIiIsCiAgICAgIG9wZW5Db250OiBmYWxzZSwKICAgICAgY29udEZvcm06IHsKICAgICAgICBjb250ZW50OiAiIiwKICAgICAgfSwKICAgICAgdXNlSW5mb0RpYWxvZ1RpdGxlOiAi6IGM5bel5qGj5qGI5L+h5oGvIiwKICAgICAgdXNlSW5mb0RpYWxvZ09wZW46IGZhbHNlLAogICAgICBzdHVkZW50SW5mb0RpYWxvZ1RpdGxlOiAi5ZGY5bel5qGj5qGI5L+h5oGvIiwKICAgICAgc3R1ZGVudEluZm9EaWFsb2dPcGVuOiBmYWxzZSwKICAgICAgY3VyUm93OiB7fSwKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAvLyDmiafooYzmn6Xor6IKICAgICAgZ2V0RXZhbHVhdGlvblBsYW5BcHBseUNvbmZpcm1QYWdlKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgY29uc3QgbGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdCB8fCBbXTsKICAgICAgICBsaXN0LmZvckVhY2goKGl0ZW0pID0+IHsKICAgICAgICAgIGlmIChpdGVtLmFwcGxpY2F0aW9uRG9jdW1lbnQpIHsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICBpdGVtLmFwcGxpY2F0aW9uRG9jdW1lbnQgPSBKU09OLnBhcnNlKGl0ZW0uYXBwbGljYXRpb25Eb2N1bWVudCk7CiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgaXRlbS5hcHBsaWNhdGlvbkRvY3VtZW50ID0gW107CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLmxpc3QgPSByZXNwb25zZS5kYXRhLmxpc3Q7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlj5bmtojmjInpkq4gKi8KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvKiog6KGo5Y2V6YeN572uICovCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiB1bmRlZmluZWQsCiAgICAgICAgc2VsZWN0aW9uUmVzdWx0OiAiYmVfc2VsZWN0ZWQiLAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VObyA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOW4iOi1hOivhOS8mOiuoeWIkueUs+ivt+ehruiupCI7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVXBkYXRlKHJvdywgb3B0KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcHQgPSBvcHQ7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkOwogICAgICBnZXRFdmFsdWF0aW9uUGxhbkFwcGx5KHsgaWQgfSkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICBjb25zdCBkZXRhaWxzID0gcmVzcG9uc2UuZGF0YSB8fCB7fTsKICAgICAgICBjb25zdCB7IHBsYW5BcHBseUNvbmZpcm1SZXNwVk8sIHBsYW5BcHBseVJlc3BWTywgcGxhblJlc3BWTyB9ID0gZGV0YWlsczsKICAgICAgICBwbGFuQXBwbHlDb25maXJtUmVzcFZPLmNvbmZpcm1JZCA9IHBsYW5BcHBseUNvbmZpcm1SZXNwVk8uaWQ7CiAgICAgICAgaWYgKHBsYW5BcHBseVJlc3BWTy5hcHBsaWNhdGlvbkRvY3VtZW50KSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICBwbGFuQXBwbHlSZXNwVk8uYXBwbGljYXRpb25Eb2N1bWVudCA9IEpTT04ucGFyc2UoCiAgICAgICAgICAgICAgcGxhbkFwcGx5UmVzcFZPLmFwcGxpY2F0aW9uRG9jdW1lbnQKICAgICAgICAgICAgKTsKICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgIHBsYW5BcHBseVJlc3BWTy5hcHBsaWNhdGlvbkRvY3VtZW50ID0gW107CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIGNvbnN0IGRhdGEgPSBPYmplY3QuYXNzaWduKAogICAgICAgICAge30sCiAgICAgICAgICBwbGFuQXBwbHlDb25maXJtUmVzcFZPLAogICAgICAgICAgcGxhbkFwcGx5UmVzcFZPLAogICAgICAgICAgcGxhblJlc3BWTwogICAgICAgICk7CiAgICAgICAgdGhpcy5mb3JtID0gZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSBvcHQgPT09ICJ2aWV3IiA/ICLmn6XnnIvpgbTpgInor6bmg4UiIDogIumBtOmAieehruiupCI7CiAgICAgIH0pOwogICAgfSwKICAgIHZpZXdVc2VySW5mbyhyb3cpIHsKICAgICAgY29uc3QgeyB1c2VyVHlwZSB9ID0gcm93OwogICAgICB0aGlzLmN1clJvdyA9IHJvdzsKICAgICAgLy8g6IGM5bel55So5oi3CiAgICAgIGlmICh1c2VyVHlwZSA9PT0gMSkgewogICAgICAgIHRoaXMudXNlSW5mb0RpYWxvZ09wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudXNlSW5mb0RpYWxvZ1RpdGxlID0gYCR7cm93LmFwcGx5Tmlja25hbWV96IGM5bel5qGj5qGI5L+h5oGvYDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnN0dWRlbnRJbmZvRGlhbG9nT3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy5zdHVkZW50SW5mb0RpYWxvZ1RpdGxlID0gYCR7cm93LmFwcGx5Tmlja25hbWV95ZGY5bel5qGj5qGI5L+h5oGvYDsKICAgICAgfQogICAgfSwKICAgIHZpZXdQZGYodXJsLCBuYW1lKSB7CiAgICAgIGNvbnN0IHRva2VuID0gZ2V0QWNjZXNzVG9rZW4oKTsKICAgICAgY29uc3QgZmlsZVVybCA9IGAke3VybH0/dG9rZW49JHt0b2tlbn1gOwogICAgICBheGlvcwogICAgICAgIC5nZXQoZmlsZVVybCwgeyByZXNwb25zZVR5cGU6ICJibG9iIiB9KQogICAgICAgIC50aGVuKChkYXRhKSA9PiB7CiAgICAgICAgICBsZXQgcmVzcG9uc2VEYXRhID0gZGF0YS5kYXRhOwogICAgICAgICAgbGV0IGJsb2IgPSBuZXcgQmxvYihbcmVzcG9uc2VEYXRhXSwgewogICAgICAgICAgICB0eXBlOiAiYXBwbGljYXRpb24vcGRmO2NoYXJzZXQ9dXRmLTgiLAogICAgICAgICAgfSk7CiAgICAgICAgICBjb25zdCBocmVmID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsKICAgICAgICAgIHdpbmRvdy5vcGVuKGhyZWYsICJfYmxhbmsiKTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIlBERumihOiniOWksei0pToiLCBlcnJvcik7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigiUERG6aKE6KeI5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7CiAgICAgICAgfSk7CiAgICB9LAogICAgdmlld0NvbnRlbnQocm93KSB7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkOwogICAgICBnZXRFdmFsdWF0aW9uUGxhbkFwcGx5KHsgaWQgfSkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICBjb25zdCBkZXRhaWxzID0gcmVzcG9uc2UuZGF0YSB8fCB7fTsKICAgICAgICBjb25zdCB7IHBsYW5BcHBseVJlc3BWTyB9ID0gZGV0YWlsczsKICAgICAgICB0aGlzLm9wZW5Db250ID0gdHJ1ZTsKICAgICAgICB0aGlzLmNvbnRGb3JtLmNvbnRlbnQgPSBwbGFuQXBwbHlSZXNwVk8uY29udGVudDsKICAgICAgfSk7CiAgICB9LAogICAgdXBkYXRlU2NvcmUodmFsLCByb3cpIHsKICAgICAgY29uc3QgaWQgPSByb3cuaWQ7CiAgICAgIGNvbnN0IHBhcmFtcyA9IHsKICAgICAgICBpZDogaWQsCiAgICAgICAgc2VsZWN0aW9uU2NvcmU6IHZhbCwKICAgICAgfTsKICAgICAgdXBkYXRlRXZhbHVhdGlvblNjb3JlKHBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICghdmFsaWQpIHsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgY29uc3QgcGFyYW1zID0gewogICAgICAgICAgaWQ6IHRoaXMuZm9ybS5jb25maXJtSWQsCiAgICAgICAgICBzZWxlY3Rpb25SZXN1bHQ6IHRoaXMuZm9ybS5zZWxlY3Rpb25SZXN1bHQsCiAgICAgICAgfTsKICAgICAgICAvLyDmt7vliqDnmoTmj5DkuqQKICAgICAgICB1cGRhdGVTZWxlY3Rpb25SZXN1bHQocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5o+Q5Lqk5oiQ5YqfIik7CiAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkOwogICAgICB0aGlzLiRtb2RhbAogICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTluIjotYTor4TkvJjorqHliJLnlLPor7fnoa7orqTnvJblj7fkuLoiJyArIGlkICsgJyLnmoTmlbDmja7pobk/JykKICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICByZXR1cm4gZGVsZXRlRXZhbHVhdGlvblBsYW5BcHBseUNvbmZpcm0oaWQpOwogICAgICAgIH0pCiAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICAvLyDlpITnkIbmn6Xor6Llj4LmlbAKICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9OwogICAgICBwYXJhbXMucGFnZU5vID0gdW5kZWZpbmVkOwogICAgICBwYXJhbXMucGFnZVNpemUgPSB1bmRlZmluZWQ7CiAgICAgIHRoaXMuJG1vZGFsCiAgICAgICAgLmNvbmZpcm0oIuaYr+WQpuehruiupOWvvOWHuuaJgOacieW4iOi1hOivhOS8mOiuoeWIkueUs+ivt+ehruiupOaVsOaNrumhuT8iKQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IHRydWU7CiAgICAgICAgICByZXR1cm4gZXhwb3J0RXZhbHVhdGlvblBsYW5BcHBseUNvbmZpcm1FeGNlbChwYXJhbXMpOwogICAgICAgIH0pCiAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICB0aGlzLiRkb3dubG9hZC5leGNlbChyZXNwb25zZSwgIuW4iOi1hOivhOS8mOiuoeWIkueUs+ivt+ehruiupC54bHMiKTsKICAgICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UodmFsKSB7CiAgICAgIGNvbnNvbGUubG9nKCLlpJrpgInvvJoiLCB2YWwpOwogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gdmFsOwogICAgfSwKICAgIGhhbmRsZUJhdGNoKCkgewogICAgICBpZiAodGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGggPT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1dhcm5pbmcoIuivt+mAieaLqeaVsOaNrumhuSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLm9wZW5CYXRjaCA9IHRydWU7CiAgICB9LAogICAgc3VibWl0QmF0Y2hGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJiYXRjaEZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAoIXZhbGlkKSB7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIGNvbnN0IGxpc3QgPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgaWQ6IGl0ZW0uaWQsCiAgICAgICAgICAgIHNlbGVjdGlvblJlc3VsdDogdGhpcy5iYXRjaEZvcm0uc2VsZWN0aW9uUmVzdWx0LAogICAgICAgICAgfTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLiRtb2RhbAogICAgICAgICAgLmNvbmZpcm0oIuehruiupOWvueaJgOmAieS6uuWRmOi/m+ihjOaJuemHj+WFpemAiS/okL3pgInmk43kvZzlkJc/IikKICAgICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgcmV0dXJuIHVwZGF0ZUJhdGNoUmVzdWx0KGxpc3QpOwogICAgICAgICAgfSkKICAgICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5vcGVuQmF0Y2ggPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaJuemHj+ehruiupOaIkOWKnyIpOwogICAgICAgICAgfSkKICAgICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7CiAgICAgIH0pOwogICAgfSwKICAgIGNhbmNlbEJhdGNoKCkgewogICAgICB0aGlzLm9wZW5CYXRjaCA9IGZhbHNlOwogICAgfSwKICB9LAp9Owo="}, null]}