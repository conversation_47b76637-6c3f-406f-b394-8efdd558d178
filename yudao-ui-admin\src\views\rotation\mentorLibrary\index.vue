<template>
  <div class="app-container mentorLibrary-page">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="queryParams.sex" placeholder="请选择性别" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="导师层次" prop="mentorLevel">
        <el-select v-model="queryParams.mentorLevel" placeholder="请选择导师层次" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MENTOR_LEVEL)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="导师类型" prop="mentorType">
        <el-select v-model="queryParams.mentorType" placeholder="请选择导师类型" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.MENTOR_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="职称" prop="positionalTitles">
        <el-select v-model="queryParams.positionalTitles" placeholder="请选择职称" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="mentor-list" v-loading="loading">
      <el-row :gutter="15">
        <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="6" v-for="(item, index) in list" :key="index">
          <el-card>
            <img v-if="item.mentorPhoto" class="photo" :src="item.mentorPhoto" />
            <div v-else class="no-photo">
              <i class="el-icon-picture-outline" style="font-size: 28px"></i>
              <span>暂无图片</span>
            </div>
            <div class="info">
              <div class="title">{{ item.nickname }}</div>
              <div class="sub-title">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES, item.positionalTitles) }}</div>
              <div class="desc"><span>个人简介：</span>{{ item.personalProfile }}</div>
              <div class="links">
                <el-button type="text" class="button" @click="showMore(item)">查看更多 ></el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                :page-sizes="[8, 16, 24, 40]" @pagination="getList"/>

    <el-dialog title="导师详情" :visible.sync="open" width="1000px" v-dialogDrag append-to-body>
      <div class="mentorLibrary-detail-box">
        <div class="base-info">
          <img v-if="detailInfo.mentorPhoto" class="photo" :src="detailInfo.mentorPhoto" />
          <div v-else class="no-photo">
            <i class="el-icon-picture-outline" style="font-size: 28px"></i>
            <span>暂无图片</span>
          </div>
          <div class="info-box">
            <div class="time"> 最后更新时间：{{detailInfo.updateTime}}</div>
            <el-descriptions class="margin-top" :column="2" border>
              <el-descriptions-item>
                <template slot="label">
                  姓名
                </template>
                {{ detailInfo.nickname }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  性别
                </template>
                {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_SEX, detailInfo.sex) }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  名族
                </template>
                {{ getDictDataLabel(DICT_TYPE.SYSTEM_NATION, detailInfo.nation) }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  导师层次
                </template>
                {{ getDictDataLabel(DICT_TYPE.MENTOR_LEVEL, detailInfo.mentorLevel) }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  职称
                </template>
                {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES, detailInfo.positionalTitles) }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  导师类型
                </template>
                {{ getDictDataLabel(DICT_TYPE.MENTOR_TYPE, detailInfo.mentorType) }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  最后学历
                </template>
                {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_EDUCATION, detailInfo.education) }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  最后学位
                </template>
                {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_DEGREE, detailInfo.degree) }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  行政职务
                </template>
                {{ getDictDataLabel(DICT_TYPE.JOB_TITLE, detailInfo.jobTitle) }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  个人邮箱
                </template>
                {{ detailInfo.email }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  工作单位
                </template>
                {{ detailInfo.organizationName }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  邮政编码
                </template>
                {{ detailInfo.postalCode }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <div class="desc-box">
          <div class="title">个人简介</div>
          <div class="cont">
            <p>{{ detailInfo.personalProfile }}</p>
          </div>
        </div>

        <div class="desc-box">
          <div class="title">研究领域</div>
          <div class="cont">
            <p>{{ detailInfo.researchArea }}</p>
          </div>
        </div>

        <div class="desc-box">
          <div class="title">个人成果</div>
          <div class="cont">
            <p>{{ detailInfo.personalAchievements }}</p>
          </div>
        </div>

      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="cancel">关闭</el-button>
        <!-- <el-button @click="cancel">取 消</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAccessToken } from "@/utils/auth";
import { getMentorPage, getMentorDetail } from "@/api/rotation/mentorLibrary";


export default {
  name: "MentorLibrary",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 8,
        nickname: "",
        sex: null,
        mentorLevel: null,
        mentorType: null,
        positionalTitles: null
      },
      open: false,
      detailInfo: {},
    };
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getMentorPage(this.queryParams).then(response => {
        const list = response.data.list
        list.forEach(element => {
          if (element.mentorPhoto) {
            if (element.mentorPhoto.indexOf('?') > -1) {
              element.mentorPhoto = element.mentorPhoto.split('?')[0]
            }
            element.mentorPhoto = element.mentorPhoto + '?token=' + getAccessToken()
          }
        });
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    showMore(row) {
      const id = row.id;
      getMentorDetail(id).then((response) => {
        const {data = {}} = response

        if (data.mentorPhoto) {
          if (data.mentorPhoto.indexOf('?') > -1) {
            data.mentorPhoto = data.mentorPhoto.split('?')[0]
          }
          data.mentorPhoto = data.mentorPhoto + '?token=' + getAccessToken()
        }

        this.detailInfo = data;

        this.open = true;
      });
    },
    cancel() {
      this.open = false;
    },
  }
};
</script>

<style lang="scss">
.mentorLibrary-page{
  .el-card{
    margin-bottom: 15px;
  }
  .el-card__body{
    display: flex;
    padding: 15px;

    .photo{
      width: 200px;
      height: 280px;
    }

    .no-photo{
      width: 200px;
      height: 280px;
      background: #f8f8f8;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 14px;

      span{
        margin-top: 10px;
      }
    }

    .info{
      width: calc(100% - 200px);
      padding-left: 15px;
    }

    .title{
      font-size: 16px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 10px;
    }
    .sub-title{
      font-size: 14px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 10px;
    }
    .desc{
      line-height: 25px;
      overflow: hidden;
      overflow:hidden;
      text-overflow:ellipsis;
      display:-webkit-box;
      -webkit-line-clamp:7;
      -webkit-box-orient:vertical;

      span{
        font-size: 14px;
        font-weight: bold;

      }
    }
    .links{
      text-align: right;
    }
  }
}

.mentorLibrary-detail-box{
  .base-info{
    width: 100%;
    display: flex;

    .photo{
      width: 200px;
      height: 280px;
    }

    .no-photo{
      width: 200px;
      height: 280px;
      background: #f8f8f8;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 14px;

      span{
        margin-top: 10px;
      }
    }

    .info-box{
      width: calc(100% - 220px);
      padding-left: 20px;

      .time{
        text-align: right;
        margin-bottom: 10px;
      }
    }
  }

  .desc-box{
    margin: 20px 0 30px 0;

    .title{
      position: relative;
      padding-left: 16px;
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 10px;

      &::before{
        content: ' ';
        display: inline-block;
        width: 4px;
        height: 14px;
        background: #1890ff;
        position: absolute;
        left: 0;
        top: 3px;
      }
    }

    .cont{
      border: 1px #eee solid;
      padding: 10px;

      p{
        margin: 0;
        line-height: 25px;
      }
    }
  }
}
</style>
