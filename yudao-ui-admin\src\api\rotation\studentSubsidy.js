import request from "@/utils/request";

// 创建学员轮转补助
export function createStudentSubsidy(data) {
  return request({
    url: "/rotation/student-subsidy/create",
    method: "post",
    data: data,
  });
}

// 更新学员轮转补助
export function updateStudentSubsidy(data) {
  return request({
    url: "/rotation/student-subsidy/update",
    method: "put",
    data: data,
  });
}

// 删除学员轮转补助
export function deleteStudentSubsidy(id) {
  return request({
    url: "/rotation/student-subsidy/delete?id=" + id,
    method: "delete",
  });
}

// 获得学员轮转补助
export function getStudentSubsidy(id) {
  return request({
    url: "/rotation/student-subsidy/get?id=" + id,
    method: "get",
  });
}

// 获得学员轮转补助分页
export function getStudentSubsidyPage(query) {
  return request({
    url: "/rotation/student-subsidy/page",
    method: "get",
    params: query,
  });
}

// 导出学员轮转补助 Excel
export function exportStudentSubsidyExcel(query) {
  return request({
    url: "/rotation/student-subsidy/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获得学员轮转补助配置
export function getStudentSubsidyPageConfig(query) {
  return request({
    url: "/rotation/student-subsidy-config/get",
    method: "get",
    params: query,
  });
}

// 更新学员轮转补助配置
export function updateStudentSubsidyConfig(data) {
  return request({
    url: "/rotation/student-subsidy-config/update",
    method: "put",
    data: data,
  });
}
