<template>
  <div class="app-container enrollexamine">

    <div class="top-radio-box">
      <el-radio-group v-model="queryParams.checkStatus" size="medium" class="top-radio-group" @input="handleRadioChange">
        <el-radio-button label="to_be_check">待报到</el-radio-button>
        <el-radio-button label="checked">已报到</el-radio-button>
        <el-radio-button label="cancel_check">取消报到</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" filterable placeholder="请选择学员类型">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                    :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item label="报名项目" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入报名项目" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="queryParams.sex" placeholder="请选择性别" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="学历" prop="highestAcademic">
        <el-select v-model="queryParams.highestAcademic" filterable placeholder="请选择学历">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
                    :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="职称" prop="positionalTitles">
        <el-select v-model="queryParams.positionalTitles" filterable placeholder="请选择职称">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)"
                    :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="时长" prop="recruitMonths">
        <el-input v-model="queryParams.recruitMonths" placeholder="请输入时长" clearable/>
      </el-form-item>
      <el-form-item label="工龄">
        <div class="seniority-box">
          <el-input v-model="queryParams.seniorities[0]" clearable/>
        </div>
        <div class="seniority-line">~</div>
        <div class="seniority-box">
          <el-input v-model="queryParams.seniorities[1]" clearable/>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-if="radioVal == 1" type="primary" plain size="mini" @click="handleCheckQR">报到确认码</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button v-if="radioVal == 1" type="primary" icon="el-icon-finished" size="mini" @click="handleBatchAudit"
                   v-hasPermi="['recruitment:check:update']">批量录取</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['recruitment:check:update']">导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        width="55"
        fixed
      >
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="nickname" width="100" fixed>
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="viewUserInfo(scope.row)">{{ scope.row.nickname }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" prop="generateUserName" width="80" fixed></el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="queryParams.checkStatus !== 'checked'" size="mini" type="text" @click="checkClick(scope.row)"
                     v-hasPermi="['recruitment:check:update']">报到</el-button>
          <el-button v-if="queryParams.checkStatus === 'to_be_check'" size="mini" type="text" @click="checkCancelClick(scope.row)"
                     v-hasPermi="['recruitment:check:update']">取消报到</el-button>
          <el-button size="mini" type="text" @click="handleEdit(scope.row)"
                     v-hasPermi="['recruitment:check:update']">信息修改</el-button>
        </template>
      </el-table-column>

      <el-table-column label="报到时间" align="center" prop="checkTime" width="150"></el-table-column>
      <el-table-column label="性别" align="center" prop="sex" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="学历" align="center" prop="highestAcademic" width="120">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_EDUCATION" :value="scope.row.highestAcademic" />
        </template>
      </el-table-column>
      <el-table-column label="职称" align="center" prop="positionalTitles" width="120">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES" :value="scope.row.positionalTitles" />
        </template>
      </el-table-column>
      <el-table-column label="工龄" align="center" prop="seniority" width="80" />
      <el-table-column label="工作单位" align="center" prop="selectedUnit" width="200" />
      <el-table-column label="报名项目" align="center" prop="projectName" width="180" />
      <el-table-column label="时长(月)" align="center" prop="recruitMonths" width="80" />
      <el-table-column label="提交时间" align="center" prop="commitTime" width="180" />
      <el-table-column label="学费(元)" align="center" prop="tuition" width="100" />
      <el-table-column label="备注" align="center" prop="remarks" />
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="报名项目：" prop="recruitmentProjectId">
              <el-select v-model="editForm.recruitmentProjectId" @change="handleProjectChange" style="width: 100%;">
                <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="选择时长：" prop="recruitmentProjectDetailed">
              <el-select v-model="editForm.recruitmentProjectDetailed" style="width: 100%;">
                <el-option v-for="item in projectDetailedList" :key="item.id" :label="`${item.recruitMonths}个月`" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学费：" prop="detailedTuition">
              <el-input-number :controls="false" disabled :value="curRow.tuition" style="width: 100%;"></el-input-number>
            </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
        <el-button type="primary" @click="submitEditForm">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="报到确认"
      :visible.sync="checkDialogVisible"
      width="550px"
      custom-class="check-dialog">
      <el-form ref="checkForm" :model="checkForm" :rules="checkFormRules" label-width="250px">
        <el-form-item :label="`请为${curRow.nickname}设置系统登录账号`" prop="username">
          <el-input v-model="checkForm.username" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="checkDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleCheck">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="提示"
      :visible.sync="cancelVisible"
      width="300px"
      custom-class="check-dialog">
      <span>{{`确认学员${curRow.nickname}取消报到么？`}}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleCancelCheck">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="editTitle" :visible.sync="openEdit" width="500px" v-dialogDrag append-to-body>
      <el-form ref="editform" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="报名项目：" prop="recruitmentProjectId">
          <el-select v-model="editForm.recruitmentProjectId" @change="handleProjectChange" style="width: 100%;">
            <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择时长：" prop="recruitmentProjectDetailed">
          <el-select v-model="editForm.recruitmentProjectDetailed" style="width: 100%;">
            <el-option v-for="item in projectDetailedList" :key="item.id" :label="`${item.recruitMonths}个月`" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学费：" prop="detailedTuition">
          <el-input-number :controls="false" disabled :value="curRow.tuition" style="width: 100%;"></el-input-number>
        </el-form-item>
        <template v-if="editForm.baseinfoPartUpdateReqVO">
          <el-form-item label="是否住宿" prop="baseinfoPartUpdateReqVO.isStay">
            <el-radio-group v-model="editForm.baseinfoPartUpdateReqVO.isStay">
              <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
                        :key="dict.value" :label="dict.value === 'true'">{{ dict.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="需要白大褂" prop="baseinfoPartUpdateReqVO.isNeedWhiteCoat">
            <el-radio-group v-model="editForm.baseinfoPartUpdateReqVO.isNeedWhiteCoat">
              <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
                        :key="dict.value" :label="dict.value === 'true'">{{ dict.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="白大褂费用" prop="baseinfoPartUpdateReqVO.whiteCoatFee" v-if="editForm.baseinfoPartUpdateReqVO.isNeedWhiteCoat">
            <el-input-number v-model="editForm.baseinfoPartUpdateReqVO.whiteCoatFee" :controls="false" style="width: 100%;"></el-input-number>
          </el-form-item>
        </template>
        <el-form-item label="报到时间" prop="checkTime">
          <el-date-picker
            v-model="editForm.checkTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择报到时间"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelEdit">关闭</el-button>
        <el-button type="primary" @click="submitEditForm">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="openQR" width="500px" v-dialog-drag append-to-body>
      <div class="qr-content" id="qr-content">
        <h2>{{ QRTitle }}</h2>
        <img :src="QRUrl" alt="QR">
        <p>请扫上方二维码进行报到确认</p>
      </div>
      <div class="qr-control">
        <el-button size="small" @click="handleRefreshQR">刷新</el-button>
        <el-button size="small" @click="openQR = false">关闭</el-button>
        <el-button type="primary" size="small" :loading="exporting" @click="handleDownloadQR">下载二维码</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { exportSuperviseFormExcel } from "@/api/rotation/superviseForm";
import { getRegistration } from "@/api/recruitment/noticesend";
import {getCheckPage, approve, reject, updateRegistrationinfo, getQrcode, updateQrcode } from "@/api/recruitment/check";
import { getProjectsByPlanId, getPlanProjectDetailedList, getResidencyPartBaseInfo } from "@/api/recruitment/registration";
import { exportPDF } from '@/utils/exportUtils'

export default {
  name: "Enrollexamine",
  data() {
    return {
      radioVal: '1',
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督导表单列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        checkStatus: 'to_be_check',
        studentType: '',
        projectName: '',
        sex: '',
        highestAcademic: '',
        positionalTitles: '',
        recruitMonths: '',
        seniorities: ['', '']
      },
      multipleSelection: [],

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        formType: [{ required: true, message: "请选择", trigger: "change" }],
      },

      curRow: {},
      checkForm: {
        username: ''
      },
      checkFormRules: {
        username: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      checkDialogVisible: false,
      cancelVisible: false,

      editTitle: '',
      openEdit: false,
      editRules: {
        recruitmentProjectId: [{ required: true, message: "请选择", trigger: "change" }],
        recruitmentProjectDetailed: [{ required: true, message: "请选择", trigger: "change" }],
        checkTime: [{ required: true, message: "请选择", trigger: "change" }],
        "baseinfoPartUpdateReqVO.isNeedWhiteCoat": [{ required: true, message: "请选择", trigger: "change" }],
        "baseinfoPartUpdateReqVO.isStay": [{ required: true, message: "请选择", trigger: "change" }],
      },
      editForm: {},
      projectList: [],
      projectDetailedList: [],
      planId: '',

      openQR: false,
      QRTitle: '',
      QRUrl: '',
      uuidSign: '',
      exporting: false,
    };
  },
  created() {
    this.getList();

  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.list = []
      // 执行查询
      getCheckPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    handleRadioChange(val){
      this.resetForm("queryForm");
      this.queryParams.checkStatus = val
      this.queryParams.pageNo = 1
      this.getList()
    },

    viewUserInfo(row){
      getRegistration({id: row.recruitmentRegistrationId}).then(res => {
        const data = res.data
        const { planId } = data
        this.$router.push({
          path: 'personalInfo',
          query: { planId, recruitmentRegistrationId: row.recruitmentRegistrationId },
        });
      })
    },

    handleEdit(row){
      this.curRow = row
      this.editTitle = `修改信息-${row.nickname}`
      getRegistration({id: row.recruitmentRegistrationId}).then(res => {
        const data = res.data
        const { planId, recruitmentProjectId } = data
        this.planId = planId
        getProjectsByPlanId(planId).then(res => {
          this.projectList = res.data;

          this.handleProjectChange(recruitmentProjectId)

          this.editForm = data
          getResidencyPartBaseInfo(row.recruitmentRegistrationId).then(res => {
            this.$set(this.editForm, "baseinfoPartUpdateReqVO", res.data);
            this.openEdit = true;
          });
        });
      })
    },

    cancelEdit(){
      this.openEdit = false
    },

    handleProjectChange(value) {
      getPlanProjectDetailedList(this.planId, value).then(res => {
        this.projectDetailedList = res.data;
      });
    },

    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.editForm = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    checkClick(row){
      this.curRow = row
      this.checkForm.username = row.mobilePhone
      this.checkDialogVisible = true
    },

    handleCheck(){
        const params = {
          recruitmentRegistrationId: this.curRow.recruitmentRegistrationId,
          username: this.checkForm.username
        }
        approve(params).then(res => {
          this.$modal.msgSuccess("操作成功");
          this.checkDialogVisible = false
          this.handleQuery();
        })
    },

    checkCancelClick(row){
      this.curRow = row
      this.cancelVisible = true
    },

    handleCancelCheck() {
      const params = {
        recruitmentRegistrationId: this.curRow.recruitmentRegistrationId
      }
      reject(params).then(res => {
        this.$modal.msgSuccess("操作成功");
        this.cancelVisible = false
        this.handleQuery();
      })
    },

    // handleCheck(row, type){
    //   const tips = type === 'confirm' ? '确认学员张三已报到么?' : '确认学员张三取消报到么?'
    //   this.$confirm(`${tips}`, '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {

    //     if (type === 'confirm') {
    //       const params = {
    //         recruitmentRegistrationId: row.recruitmentRegistrationId
    //       }
    //       approve(params).then(res => {
    //         this.$modal.msgSuccess("操作成功");
    //         this.handleQuery();
    //       })

    //     } else {
    //       const params = {
    //         recruitmentRegistrationId: row.recruitmentRegistrationId
    //       }
    //       reject(params).then(res => {
    //         this.$modal.msgSuccess("操作成功");
    //         this.handleQuery();
    //       })
    //     }
    //   }).catch(() => {});
    // },

    submitEditForm(){
      this.$refs["editform"].validate(valid => {
        if (!valid) {
          return;
        }

        const params = {
          id: this.curRow.recruitmentRegistrationId,
          recruitmentProjectDetailed: this.editForm.recruitmentProjectDetailed,
          recruitmentProjectId: this.editForm.recruitmentProjectId,
          checkTime: this.editForm.checkTime,
          baseinfoPartUpdateReqVO: this.editForm.baseinfoPartUpdateReqVO,
        }

        updateRegistrationinfo(params).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.openEdit = false;
          this.getList();
        });
      });
    },


    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有督导表单数据项?').then(() => {
          this.exportLoading = true;
          return exportSuperviseFormExcel(params);
        }).then(response => {
          this.$download.excel(response, '督导表单.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    },

    handleCheckQR() {
      this.openQR = true;
      getQrcode({url: process.env.VUE_APP_BASE_API + '/h5/#/pages/recruitment/check/index'}).then((res) => {
        this.QRTitle = res.data.name;
        this.QRUrl = "data:image/png;base64," + res.data.qrcode;
        this.uuidSign = res.data.uuidSign;
      });
    },
    handleRefreshQR() {
      this.$prompt('报到码名称', '刷新确认', {
        inputPlaceholder: '请填写刷新后您需要展示的名称',
        inputPattern: /^.+$/,
        inputErrorMessage: '报到码名称不能为空'
      }).then(({ value }) => {
        updateQrcode({ url: process.env.VUE_APP_BASE_API + '/h5/#/pages/recruitment/check/index', name: value }).then((res) => {
          this.QRTitle = res.data.name;
          this.QRUrl = "data:image/png;base64," + res.data.qrcode;
          this.uuidSign = res.data.uuidSign;
        });
      });
    },
    handleDownloadQR() {
      this.exporting = true;
      exportPDF("qr-content", "报到确认码", () => {
        this.exporting = false;
      });
    },
  }
};
</script>

<style lang="scss">
.enrollexamine {
  position: relative;

  .top-radio-box{
    position: relative;
    margin-bottom: 15px;
    &::before{
      position: absolute;
      content: ' ';
      display: block;
      width: 100%;
      height: 1px;
      background: #ddd;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .el-radio-button:first-child .el-radio-button__inner{
      border-radius: 4px 0 0 0;
    }
    .el-radio-button:last-child .el-radio-button__inner{
      border-radius: 0 4px 0 0;
    }
  }

  .seniority-box{
    display: inline-block;
    width: 80px;
  }
  .seniority-line{
    display: inline-block;
    width: 25px;
    text-align: center;
  }
}

.superviseForm-indicator-dialog{

  .el-dialog__body{
    padding-top: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .indicators-wapper{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .indicators-wapper-head{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 8px;
      border-bottom: 1px #ddd solid;
    }

    .indicators-wapper-tables{
      max-height: calc(100vh - 240px);
      flex: auto;
      padding-top: 13px;
      overflow-y: auto;
    }
  }
}

.check-dialog{
  .el-dialog__body{
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

.qr-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 300px;
  padding: 20px;
  margin: 0 auto 20px auto;

  h2 {
    margin-top: 0;
    font-size: 20px;
    font-weight: 600;
    color: #000;
    margin-bottom: 30px;
    text-align: center;
  }

  img {
    max-width: 80%;
  }

  p {
    font-size: 14px;
  }
}

.qr-control {
  text-align: center;
}
</style>
