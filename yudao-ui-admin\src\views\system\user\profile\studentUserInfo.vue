<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="110px">
    <el-row :gutter="10">
      <el-col :md="24" :lg="24" :xl="24">
        <el-row :gutter="10">
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="姓名" prop="nickname">
              <el-input
                v-model="form.nickname"
                placeholder="请输入姓名"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="form.username"
                placeholder="请输入用户名"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="证件类型" prop="certificateType">
              <el-select
                v-model="form.certificateType"
                filterable
                placeholder="请选择证件类型"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_CERTIFICATE_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="证件号码" prop="certificateNumber">
              <el-input
                v-model="form.certificateNumber"
                placeholder="请输入证件号码"
                @blur="getIdcardInfoHandler"
                maxlength="25"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <el-row :gutter="10">

                </el-row> -->

        <el-row :gutter="10">
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="用户性别" prop="sex">
              <el-select
                v-model="form.sex"
                filterable
                placeholder="请选择用户性别"
              >
                <el-option
                  v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker
                clearable
                v-model="form.birthday"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择出生日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="婚姻状况" prop="maritalStatus">
              <el-select
                v-model="form.maritalStatus"
                filterable
                placeholder="请选择婚姻状况"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_MARITAL_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="民族" prop="nation">
              <el-select
                v-model="form.nation"
                filterable
                placeholder="请选择民族"
              >
                <el-option
                  v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_NATION)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="是否党员" prop="isPartyMember">
              <el-select
                v-model="form.isPartyMember"
                filterable
                placeholder="请选择是否党员"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.INFRA_BOOLEAN_STRING
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="健康状况" prop="healthStatus">
              <el-select
                v-model="form.healthStatus"
                filterable
                placeholder="请选择健康状况"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_HEALTH_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="手机号码" prop="mobile">
              <el-input v-model="form.mobile" placeholder="请输入手机号码" />
            </el-form-item>
          </el-col>
          <el-col :md="6" :lg="6" :xl="6">
            <el-form-item label="用户邮箱" prop="email">
              <el-input
                type="email"
                v-model="form.email"
                placeholder="请输入用户邮箱"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :md="18" :lg="18" :xl="18">
        <el-row :gutter="10">
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="毕业时间：" prop="graduationSchoolDate">
              <el-date-picker
                clearable
                v-model="form.graduationSchoolDate"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择毕业时间"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="学历：" prop="education">
              <el-select
                v-model="form.education"
                filterable
                placeholder="请选择学历"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_EDUCATION
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="学位：" prop="degree">
              <el-select
                v-model="form.degree"
                filterable
                placeholder="请选择学位"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_DEGREE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="毕业院校：" prop="graduationSchool">
              <el-input
                v-model="form.graduationSchool"
                placeholder="请输入毕业院校"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="开户银行" prop="depositBank">
              <el-input
                v-model="form.depositBank"
                placeholder="请输入开户银行"
                maxlength="25"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="银行卡号" prop="bankCardNo">
              <el-input
                v-model="form.bankCardNo"
                placeholder="请输入银行卡号"
                maxlength="25"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="户口省份" prop="residenceProvince">
              <el-select
                v-model="form.residenceProvince"
                filterable
                placeholder="请选择户口省份"
              >
                <el-option
                  v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_PROVINCE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="计算机能力" prop="computerAbility">
              <el-select
                v-model="form.computerAbility"
                filterable
                placeholder="请选择计算机能力"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_COMPUTER_ABILITY
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="外语能力" prop="foreignLanguageAbility">
              <el-select
                v-model="form.foreignLanguageAbility"
                filterable
                placeholder="请选择外语能力"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_FOREIGN_LANGUAGE_ABILITY
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="兴趣爱好" prop="hobby">
              <el-input
                v-model="form.hobby"
                placeholder="请输入兴趣爱好"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="通讯地址" prop="postalAddress">
              <el-input
                v-model="form.postalAddress"
                placeholder="请输入通讯地址"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="邮政编码" prop="postalCode">
              <el-input
                v-model="form.postalCode"
                placeholder="请输入邮政编码"
                maxlength="25"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="紧急联系人" prop="emergencyContact">
              <el-input
                v-model="form.emergencyContact"
                placeholder="请输入紧急联系人"
                maxlength="25"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="与本人关系" prop="relationship">
              <el-input
                v-model="form.relationship"
                placeholder="请输入与本人关系"
                maxlength="25"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="紧急联系人手机" prop="emergencyContactPhone">
              <el-input
                v-model="form.emergencyContactPhone"
                placeholder="请输入紧急联系人手机"
              />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="紧急联系人地址" prop="emergencyContactAddres">
              <el-input
                v-model="form.emergencyContactAddres"
                placeholder="请输入紧急联系人地址"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :md="6" :lg="6" :xl="6">
        <el-row :gutter="10" style="padding-left: 20px">
          <el-col :md="24" :lg="24" :xl="24">
            <div style="margin-bottom: 5px"><b>学历证书：</b></div>
            <el-form-item label="" label-width="0" prop="educationCertificate">
              <imageUpload
                v-model="form.educationCertificate"
                :limit="1"
                :isShowTip="false"
              />
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="24" :xl="24">
            <div style="margin-bottom: 5px"><b>学位证书：</b></div>
            <el-form-item label="" label-width="0" prop="degreeCertificate">
              <imageUpload
                v-model="form.degreeCertificate"
                :limit="1"
                :isShowTip="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :md="18" :lg="18" :xl="18">
        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item
              label="是否获得医师资格证书："
              prop="hasMedicalLicense"
              label-width="280"
            >
              <el-radio-group v-model="form.hasMedicalLicense">
                <el-radio
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.INFRA_BOOLEAN_STRING
                  )"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.hasMedicalLicense === 'true'"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <el-form-item
              label="取得医师资格证书时间："
              prop="medicalLicenseDate"
              label-width="280"
            >
              <el-date-picker
                clearable
                v-model="form.medicalLicenseDate"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                style="width: 180px"
              />
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.hasMedicalLicense === 'true'"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <el-form-item
              label="医师资格级别："
              prop="medicalLicenseLevel"
              label-width="280"
            >
              <el-select
                v-model="form.medicalLicenseLevel"
                filterable
                placeholder="请选择医师资格级别"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.MEDICAL_LICENSE_LEVEL
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.hasMedicalLicense === 'true'"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <el-form-item
              label="医师资格证书编码："
              prop="medicalLicenseNumber"
              label-width="280"
            >
              <el-input
                v-model="form.medicalLicenseNumber"
                placeholder="请输入医师资格证书编码"
                style="width: 210px"
              />
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.hasMedicalLicense === 'true'"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <el-form-item
              label="医师资格类别："
              prop="medicalLicenseCategory"
              label-width="280"
            >
              <el-select
                v-model="form.medicalLicenseCategory"
                filterable
                placeholder="请选择医师资格类别"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.MEDICAL_LICENSE_CATEGORY
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item
              label="是否获得医师执业证书："
              prop="hasPracticingLicense"
              label-width="280"
            >
              <el-radio-group v-model="form.hasPracticingLicense">
                <el-radio
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.INFRA_BOOLEAN_STRING
                  )"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.hasPracticingLicense === 'true'"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <el-form-item
              label="取得医师执业证书时间："
              prop="practicingLicenseDate"
              label-width="280"
            >
              <el-date-picker
                clearable
                v-model="form.practicingLicenseDate"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                style="width: 180px"
              />
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.hasPracticingLicense === 'true'"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <el-form-item
              label="执业类型："
              prop="practicingType"
              label-width="280"
            >
              <el-select
                v-model="form.practicingType"
                filterable
                placeholder="请选择执业类型"
              >
                <el-option
                  v-for="dict in this.getDictDatas(DICT_TYPE.PRACTICING_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.hasPracticingLicense === 'true'"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <el-form-item
              label="医师执业证书编码："
              prop="practicingLicenseNumber"
              label-width="280"
            >
              <el-input
                v-model="form.practicingLicenseNumber"
                placeholder="请输入医师执业证书编码"
                style="width: 210px"
              />
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.hasPracticingLicense === 'true'"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <el-form-item
              label="执业范围："
              prop="practicingScope"
              label-width="280"
            >
              <el-select
                v-model="form.practicingScope"
                filterable
                placeholder="请选择执业范围"
              >
                <el-option
                  v-for="dict in this.getDictDatas(DICT_TYPE.PRACTICING_SCOPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :md="6" :lg="6" :xl="6">
        <el-row :gutter="10" style="padding-left: 20px">
          <el-col
            v-if="form.hasMedicalLicense === 'true'"
            :md="24"
            :lg="24"
            :xl="24"
          >
            <div style="margin-bottom: 5px"><b>医师资格证书：</b></div>
            <el-form-item label="" label-width="0" prop="medicalLicenseImage">
              <imageUpload
                v-model="form.medicalLicenseImage"
                :limit="1"
                :isShowTip="false"
              />
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.hasPracticingLicense === 'true'"
            :md="24"
            :lg="24"
            :xl="24"
          >
            <div style="margin-bottom: 5px"><b>医师执业证书：</b></div>
            <el-form-item
              label=""
              label-width="0"
              prop="practicingLicenseImage"
            >
              <imageUpload
                v-model="form.practicingLicenseImage"
                :limit="1"
                :isShowTip="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import userAvatar from "./studentUserAvatar";
import { getUserStudent, getIdcardInfo } from "@/api/system/userStudent";
import { updateUserStudent } from "@/api/system/user";
import ImageUpload from "@/components/ImageUpload";

export default {
  props: {
    user: {
      type: Object,
    },
  },
  components: { userAvatar, ImageUpload },
  data() {
    const checkCertificateNumber = (rule, value, callback) => {
      if (this.form.certificateType == 1 && value) {
        const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的证件号码"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const checkMobile = (rule, value, callback) => {
      if (value) {
        const reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的手机号码"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const checkEmail = (rule, value, callback) => {
      if (value) {
        const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的邮箱地址"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      form: {},
      // 表单校验
      rules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
          {
            min: 4,
            max: 16,
            message: "长度在 4 到 16 个字符",
            trigger: "blur",
          },
          // {
          //     pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,16}$/,
          //     message: "用户名由数字和字母组成",
          //     trigger: "blur"
          // }
        ],
        nickname: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        certificateNumber: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkCertificateNumber, trigger: "blur" },
        ],
        mobile: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkMobile, trigger: "blur" },
        ],
        email: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkEmail, trigger: "blur" },
        ],
        emergencyContactPhone: [{ validator: checkMobile, trigger: "blur" }],
      },
    };
  },
  created() {
    const id = this.user.id;
    getUserStudent(id).then((response) => {
      this.form = response.data;
      if (response.data.hasMedicalLicense !== null) {
        this.form.hasMedicalLicense =
          response.data.hasMedicalLicense.toString();
      }

      if (response.data.hasPracticingLicense !== null) {
        this.form.hasPracticingLicense =
          response.data.hasPracticingLicense.toString();
      }
    });
  },
  methods: {
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            hasMedicalLicense:
              this.form.hasMedicalLicense === "true" ? true : false,
            hasPracticingLicense:
              this.form.hasPracticingLicense === "true" ? true : false,
          };
          updateUserStudent(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
          });
        }
      });
    },
    close() {
      this.$tab.closePage();
    },
    /** 设置头像*/
    getAvatar(picUrl) {
      this.form.avatar = picUrl;
    },
    /** 获取身份证相关信息*/
    getIdcardInfoHandler(e) {
      if (this.form.certificateType == 1) {
        const params = {
          certificateNumber: this.form.certificateNumber,
          certificateType: "1",
        };
        getIdcardInfo(params).then((response) => {
          let birthday = response.data.birthday;
          let tempARR = birthday.split("");
          tempARR.splice(4, 0, "-");
          tempARR.splice(7, 0, "-");
          const _birthday = tempARR.join("");

          this.form.sex = response.data.sex;
          this.form.birthday = _birthday;
        });
      }
    },
  },
};
</script>
