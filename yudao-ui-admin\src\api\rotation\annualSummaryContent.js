import request from "@/utils/request";

// 创建年度总结内容
export function createAnnualSummaryContent(data) {
  return request({
    url: "/rotation/annual-summary-content/create",
    method: "post",
    data: data,
  });
}

// 更新年度总结内容
export function updateAnnualSummaryContent(data) {
  return request({
    url: "/rotation/annual-summary-content/update",
    method: "put",
    data: data,
  });
}

// 删除年度总结内容
export function deleteAnnualSummaryContent(id) {
  return request({
    url: "/rotation/annual-summary-content/delete?id=" + id,
    method: "delete",
  });
}

// 获得年度总结内容
export function getAnnualSummaryContent(id) {
  return request({
    url: "/rotation/annual-summary-content/get?id=" + id,
    method: "get",
  });
}

// 获得年度总结内容分页
export function getAnnualSummaryContentPage(query) {
  return request({
    url: "/rotation/annual-summary-content/page-manage",
    method: "get",
    params: query,
  });
}

// 导出年度总结内容 Excel
export function exportAnnualSummaryContentExcel(query) {
  return request({
    url: "/rotation/annual-summary-content/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 下载成绩导入模板
export function importTemplate() {
  return request({
    url: "/rotation/annual-summary-content/get-import-template",
    method: "get",
    responseType: "blob",
  });
}

// 获得年度总结(流程嵌套页调用)
export function getAnnualSummarybpm(query) {
  return request({
    url: "/rotation/annual-summary-content/get-bpm",
    method: "get",
    params: query,
  });
}
