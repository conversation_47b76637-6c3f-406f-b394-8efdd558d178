<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="登记日期" prop="createTimes">
        <el-date-picker
          v-model="queryParams.createTimes"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="examineStatus">
        <el-select
          v-model="queryParams.examineStatus"
          filterable
          clearable
          placeholder="请选择审核状态"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.INTERN_CASE_EXAMINE_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:intern-case-record:create']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:intern-case-record:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" class="internCaseRecord-table">
      <el-table-column
        label="登记日期"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="病例附件" align="center" prop="files">
        <template slot-scope="scope">
          <imageUpload :value="scope.row.files" disabled />
        </template>
      </el-table-column>
      <el-table-column
        label="审核人"
        align="center"
        prop="examineUserNickname"
      />
      <el-table-column label="审核状态" align="center" prop="examineStatus">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.INTERN_CASE_EXAMINE_STATUS"
            :value="scope.row.examineStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="100"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:intern-case-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="病例附件" prop="files">
          <imageUpload v-model="form.files" :limit="9999" />
        </el-form-item>
        <el-form-item label="选择审核人" prop="examineUserId">
          <el-select
            v-model="form.examineUserId"
            filterable
            clearable
            placeholder="请选择审核人"
            style="width: 100%"
          >
            <el-option
              v-for="item in mentorOptions"
              :key="parseInt(item.id)"
              :label="item.nickname"
              :value="parseInt(item.id)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createInternCaseRecord,
  updateInternCaseRecord,
  deleteInternCaseRecord,
  getInternCaseRecord,
  getInternCaseRecordPage,
  exportInternCaseRecordExcel,
} from "@/api/rotation/internCaseRecord";
import ImageUpload from "@/components/ImageUpload";
import { getUserWorkerSimpleList } from "@/api/system/userWorker";

export default {
  name: "InternCaseRecord",
  components: { ImageUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 见习病例书写列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        examineStatus: "",
        createTimes: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        examineUserId: [
          { required: true, message: "审核人不能为空", trigger: "blur" },
        ],
        // examineStatus: [
        //   { required: true, message: "审核状态不能为空", trigger: "blur" },
        // ],
        files: [{ required: true, message: "附件不能为空", trigger: "blur" }],
      },
      mentorOptions: [],
    };
  },
  created() {
    this.getMentor();
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getInternCaseRecordPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        examineUserId: undefined,
        examineStatus: undefined,
        examineTime: undefined,
        files: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    getMentor() {
      // 获得教师列表
      getUserWorkerSimpleList().then((res) => {
        // 处理 roleOptions 参数
        this.mentorOptions = [];
        this.mentorOptions.push(...res.data);
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加见习病例书写";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getInternCaseRecord(id).then((response) => {
        this.form = response.data;
        this.form.files = this.form.files ? JSON.parse(this.form.files) : [];
        this.open = true;
        this.title = "修改见习病例书写";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        // this.form.files = this.form.files
        //   ? JSON.stringify(this.form.files)
        //   : "[]";
        // 修改的提交
        if (this.form.id != null) {
          updateInternCaseRecord(this.form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createInternCaseRecord(this.form).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm("是否确认删除该见习病例书写的数据项?")
        .then(function () {
          return deleteInternCaseRecord(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有见习病例书写数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportInternCaseRecordExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "见习病例书写.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.internCaseRecord-table {
  ::v-deep .el-upload-list__item {
    width: 50px !important;
    height: 50px !important;
    margin-right: 10px;
  }
}
</style>
