<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="800px"
    v-dialogDrag
    append-to-body
    @close="cancel"
  >
    <div class="appraise-conts">
      <div v-for="(item, index) in list" :key="index" class="edit-item">
        <div class="item-title">
          {{ `${index + 1}、${item.appraiseKpi}`
          }}<span style="color: #ccc"
            >（满分: {{ item.appraiseProcessItemScore }}分）</span
          >
        </div>
        <div class="item-cont">
          <el-input-number
            v-if="item.appraiseProcessItemId"
            v-model="item.score"
            :min="0"
            :max="item.appraiseProcessItemScore"
          ></el-input-number>
        </div>
      </div>
      <div class="edit-item">
        <div class="item-title">
          {{ `${list.length + 1}、意见反馈或其它意见` }}
        </div>
        <div class="item-cont">
          <el-input
            v-model="summary"
            :disabled="disabled"
            type="textarea"
            placeholder="请填写扣分原因，如未扣分请填无"
          />
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="!disabled" type="primary" @click="submitForm"
        >确 定</el-button
      >
      <el-button @click="cancel">{{ !disabled ? "取 消" : "关 闭" }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateAppraiseResult } from "@/api/rotation/mthlyDeptEvalStu";

export default {
  name: "AppraiseDialog",
  props: {
    title: {
      type: String,
    },
    open: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      default: [],
    },
    appraiseSourceId: {
      type: Number,
    },
    personalSummary: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    list() {
      if (this.disabled) {
        const _list = this.data;
        return _list;
      }
      return this.data;
    },
  },
  data() {
    return {
      summary: this.personalSummary,
    };
  },
  watch: {
    personalSummary: {
      deep: true, // 深度监听
      handler(newVal) {
        this.summary = newVal;
      },
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("setOpen", false);
    },
    submitForm() {
      const list = JSON.parse(JSON.stringify(this.list));
      let scoreFlag = true;
      let commentsFlag = true;
      list.forEach((item) => {
        if (item.appraiseProcessItemId) {
          if (item.score === null || item.score === undefined) {
            scoreFlag = false;
          }
        } else {
          if (!item.comments) {
            commentsFlag = false;
          }
        }
      });
      if (!scoreFlag) {
        return this.$modal.msgError("请完成评分");
      }
      if (!commentsFlag) {
        return this.$modal.msgError("请填写意见或反馈");
      }

      const params = {
        appraiseProcessResultItemUpdateReqVOList: list,
        id: this.appraiseSourceId,
        personalSummary: this.summary,
      };

      this.$modal
        .confirm("评价后不可修改，是否确认评价?")
        .then(() => {
          updateAppraiseResult(params).then((response) => {
            this.$modal.msgSuccess("评价成功");
            this.$emit("setOpen", false);
            this.$emit("refreshList");
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.appraise-conts {
  border: 1px #ddd solid;
  border-bottom: none;

  .item-title {
    padding: 10px 15px 10px 10px;
    border-bottom: 1px #ddd solid;

    &::before {
      content: "*";
      display: inline-block;
      color: red;
      padding-right: 5px;
    }
  }

  .item-cont {
    padding: 10px 15px;
    border-bottom: 1px #ddd solid;
  }
}
</style>
