<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="角色" prop="roleIds">
        <el-select
          v-model="queryParams.roleIds"
          multiple
          filterable
          placeholder="请选择角色"
          clearable
          size="small"
        >
          <el-option
            v-for="item in roleOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="科室" prop="deptIds">
        <el-select
          v-model="queryParams.deptIds"
          multiple
          filterable
          clearable
          placeholder="请选择医院科室"
          size="small"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="专业基地" prop="professionalBaseValues">
        <el-select
          v-model="queryParams.professionalBaseValues"
          multiple
          filterable
          clearable
          placeholder="请选择专业基地"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.PROFESSIONAL_BASE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="教研室" prop="staffRoomValues">
        <el-select
          v-model="queryParams.staffRoomValues"
          multiple
          filterable
          clearable
          placeholder="请选择教研室"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.STAFF_ROOM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="职工类型" prop="workTypes">
        <el-select v-model="queryParams.workTypes" placeholder="请选择职工类型" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_WORK_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item> -->
      <el-form-item label="用户性别" prop="sex">
        <el-select
          v-model="queryParams.sex"
          placeholder="请选择用户性别"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="证件类型" prop="certificateType">
        <el-select v-model="queryParams.certificateType" placeholder="请选择证件类型" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_CERTIFICATE_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item> -->
      <el-form-item label="学历" prop="educations">
        <el-select
          v-model="queryParams.educations"
          multiple
          placeholder="请选择学历"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="学位" prop="degree">
        <el-select v-model="queryParams.degree" placeholder="请选择学位" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_DEGREE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item> -->
      <el-form-item label="职称" prop="positionalTitleses">
        <el-select
          v-model="queryParams.positionalTitleses"
          multiple
          placeholder="请选择职称"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="证书类型" prop="certificateTypes">
        <el-select
          v-model="queryParams.certificateTypes"
          multiple
          placeholder="请选择"
          @change="(val) => rowCellChange(val, scope.$index, 'certificateType')"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_CERTIFICATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="证书级别" prop="certificateLevels">
        <el-select
          v-model="queryParams.certificateLevels"
          multiple
          placeholder="请选择"
          @change="
            (val) => rowCellChange(val, scope.$index, 'certificateLevel')
          "
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.SYSTEM_CERTIFICATE_LEVEL
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否全科师资" prop="isGeneralTeacher">
        <el-select v-model="queryParams.isGeneralTeacher" placeholder="请选择">
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否骨干师资" prop="isKeyTeacher">
        <el-select v-model="queryParams.isKeyTeacher">
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户状态" prop="status">
        <el-switch
          v-model="queryParams.status"
          :active-value="1"
          :inactive-value="0"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:user-worker:create']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:user-worker:export']"
        >
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click.native="handleImport"
          v-hasPermi="['system:user-worker:import']"
        >
          导入
        </el-button>
        <!-- <el-dropdown v-hasPermi="['system:user-worker:import']">
          <el-button type="warning" plain icon="el-icon-upload2" size="mini">
            导入<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="handleImport">选择文件导入</el-dropdown-item>
            <el-dropdown-item @click.native="handleExportTemplate">下载导入模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="用户ID" align="center" prop="id" /> -->
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="姓名" align="center" prop="nickname" />
      <el-table-column label="用户性别" align="center" prop="sex">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="角色" align="center" prop="roleNames" width="250">
        <!-- <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TYPE" :value="scope.row.workTypes" />
        </template> -->
      </el-table-column>
      <el-table-column label="学历" align="center" prop="education">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_USER_EDUCATION"
            :value="scope.row.education"
          />
        </template>
      </el-table-column>
      <el-table-column label="职称" align="center" prop="positionalTitles">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES"
            :value="scope.row.positionalTitles"
          />
        </template>
      </el-table-column>
      <el-table-column label="手机号码" align="center" prop="mobile" />
      <el-table-column label="用户邮箱" align="center" prop="email" />
      <el-table-column label="用户状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <!-- <el-table-column label="头像地址" align="center" prop="avatar" />
      <el-table-column label="证件类型" align="center" prop="certificateType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_CERTIFICATE_TYPE" :value="scope.row.certificateType" />
        </template>
      </el-table-column>
      <el-table-column label="证件号码" align="center" prop="certificateNumber" />
      <el-table-column label="出生日期" align="center" prop="birthday" />
      <el-table-column label="婚姻状况" align="center" prop="maritalStatus">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_MARITAL_STATUS" :value="scope.row.maritalStatus" />
        </template>
      </el-table-column>
      <el-table-column label="民族" align="center" prop="nation">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_NATION" :value="scope.row.nation" />
        </template>
      </el-table-column>
      <el-table-column label="是否党员" align="center" prop="isPartyMember">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.isPartyMember" />
        </template>
      </el-table-column>
      <el-table-column label="健康状况" align="center" prop="healthStatus">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_HEALTH_STATUS" :value="scope.row.healthStatus" />
        </template>
      </el-table-column>
      <el-table-column label="毕业时间" align="center" prop="graduationDate" />
      <el-table-column label="毕业院校" align="center" prop="graduationSchool" />
      <el-table-column label="学位" align="center" prop="degree">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_DEGREE" :value="scope.row.degree" />
        </template>
      </el-table-column>
      <el-table-column label="工作开始时间" align="center" prop="workStartDate" />

      <el-table-column label="职称证书地址" align="center" prop="positionalTitlesCertificate" />
      <el-table-column label="取得时间" align="center" prop="obtainDate" />
      <el-table-column label="开户银行" align="center" prop="depositBank" />
      <el-table-column label="银行卡号" align="center" prop="bankCardNo" />
      <el-table-column label="是否全科师资" align="center" prop="isGeneralTeacher">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.isGeneralTeacher" />
        </template>
      </el-table-column>
      <el-table-column label="是否骨干师资" align="center" prop="isKeyTeacher">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.isKeyTeacher" />
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="160"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row, 'edit')"
            v-hasPermi="['system:user-worker:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleUpdate(scope.row, 'view')"
            v-hasPermi="['system:user-worker:query']"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:user-worker:delete']"
            >删除</el-button
          >
          <el-dropdown
            @command="
              (command) => handleCommand(command, scope.$index, scope.row)
            "
            v-hasPermi="[
              'system:user:delete',
              'system:user:update-password',
              'system:permission:assign-user-role',
            ]"
          >
            <span class="el-dropdown-link">
              <i class="el-icon-d-arrow-right el-icon--right"></i>更多
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="handleResetPwd"
                size="mini"
                type="text"
                icon="el-icon-key"
                v-hasPermi="['system:user:update-password']"
                >重置密码</el-dropdown-item
              >
              <el-dropdown-item
                command="handleRole"
                size="mini"
                type="text"
                icon="el-icon-circle-check"
                v-hasPermi="['system:permission:assign-user-role']"
                >分配角色</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />
          是否更新已经存在的职工档案数据
          <p>
            仅允许导入xls、xlsx格式文件。
            <el-link
              style="font-size: 12px"
              type="primary"
              :underline="false"
              @click="handleExportTemplate"
              >下载模板</el-link
            >
          </p>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 分配角色 -->
    <el-dialog
      title="分配角色"
      custom-class="user-role-dialog"
      :visible.sync="openRole"
      width="850px"
      append-to-body
    >
      <el-form :model="form" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="form.username" :disabled="true" />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="form.nickname" :disabled="true" />
        </el-form-item>
        <div v-if="form.pmsList">
          <el-row
            :gutter="10"
            v-for="(item, index) in form.pmsList"
            :key="index"
            style="margin-top: 30px"
          >
            <el-col :md="24" :lg="24" :xl="24">
              <el-form-item
                label="角色"
                :prop="'pmsList.' + index + '.roleId'"
                :rules="{
                  required: true,
                  message: '请选择角色',
                  trigger: 'change',
                }"
              >
                <el-select
                  v-model="item.roleId"
                  filterable
                  clearable
                  placeholder="请选择角色"
                  @change="(val) => handleRoleChange(val, index)"
                  style="width: 90%"
                >
                  <el-option
                    v-for="item in roleOptions"
                    :key="parseInt(item.id)"
                    :label="item.name"
                    :value="parseInt(item.id)"
                  ></el-option>
                </el-select>
                <i
                  v-if="index === form.pmsList.length - 1"
                  class="el-icon-circle-plus-outline"
                  @click="addPms"
                ></i>
                <i
                  v-if="index !== 0"
                  class="el-icon-remove-outline"
                  @click="delPms(index)"
                ></i>
              </el-form-item>
            </el-col>

            <el-col :md="24" :lg="24" :xl="24">
              <el-form-item
                label="学员类型"
                :prop="'pmsList.' + index + '.studentTypes'"
                :rules="{
                  type: 'array',
                  required: true,
                  message: '请选择学员类型',
                  trigger: 'change',
                }"
              >
                <el-select
                  v-model="item.studentTypes"
                  multiple
                  filterable
                  placeholder="请选择学员类型"
                  @change="
                    doSelectChange(
                      'studentTypes',
                      index,
                      studentTypeOptions,
                      'isIndeterminateStudent',
                      'studentCheckAll'
                    )
                  "
                  style="width: 90%"
                >
                  <el-checkbox
                    :style="{ paddingLeft: '20px' }"
                    :indeterminate="item.isIndeterminateStudent"
                    v-model="item.studentCheckAll"
                    @change="
                      handleCheckAllChange(
                        'studentTypes',
                        index,
                        studentTypeOptions,
                        'value',
                        'isIndeterminateStudent',
                        $event
                      )
                    "
                  >
                    全选
                  </el-checkbox>
                  <el-option
                    v-for="dict in studentTypeOptions"
                    :key="parseInt(dict.value)"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col
              :md="24"
              :lg="24"
              :xl="24"
              v-if="
                ['professional_base_admin', 'staff_room_admin'].indexOf(
                  form.pmsList[index].roleCode
                ) == -1
              "
            >
              <el-form-item
                label="科室"
                :prop="'pmsList.' + index + '.deptIds'"
                :rules="{
                  type: 'array',
                  required: true,
                  message: '请选择科室',
                  trigger: 'change',
                }"
              >
                <el-select
                  v-model="item.deptIds"
                  multiple
                  filterable
                  clearable
                  placeholder="请选择科室"
                  @change="
                    doSelectChange(
                      'deptIds',
                      index,
                      departmentOptions,
                      'isIndeterminateDept',
                      'deptCheckAll'
                    )
                  "
                  style="width: 90%"
                >
                  <el-checkbox
                    :style="{ paddingLeft: '20px' }"
                    :indeterminate="item.isIndeterminateDept"
                    v-model="item.deptCheckAll"
                    @change="
                      handleCheckAllChange(
                        'deptIds',
                        index,
                        departmentOptions,
                        'id',
                        'isIndeterminateDept',
                        $event
                      )
                    "
                  >
                    全选
                  </el-checkbox>
                  <el-option
                    v-for="item in departmentOptions"
                    :key="parseInt(item.id)"
                    :label="item.name"
                    :value="parseInt(item.id)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col
              :md="24"
              :lg="24"
              :xl="24"
              v-if="form.pmsList[index].roleCode === 'professional_base_admin'"
            >
              <el-form-item
                label="专业"
                :prop="'pmsList.' + index + '.majorCodes'"
                :rules="{
                  type: 'array',
                  required: false,
                  message: '请选择专业',
                  trigger: 'change',
                }"
              >
                <el-select
                  v-model="item.majorCodes"
                  multiple
                  filterable
                  placeholder="请选择专业"
                  @change="
                    doSelectChange(
                      'majorCodes',
                      index,
                      majorList,
                      'isIndeterminateMajor',
                      'majorCheckAll'
                    )
                  "
                  style="width: 90%"
                >
                  <el-checkbox
                    :style="{ paddingLeft: '20px' }"
                    :indeterminate="item.isIndeterminateMajor"
                    v-model="item.studentCheckAll"
                    @change="
                      handleCheckAllChange(
                        'majorCodes',
                        index,
                        majorList,
                        'code',
                        'isIndeterminateMajor',
                        $event
                      )
                    "
                  >
                    全选
                  </el-checkbox>
                  <el-option
                    v-for="dict in majorList"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col
              :md="24"
              :lg="24"
              :xl="24"
              v-if="form.pmsList[index].roleCode === 'staff_room_admin'"
            >
              <el-form-item
                label="教研室"
                :prop="'pmsList.' + index + '.staffRoomValues'"
                :rules="{
                  type: 'array',
                  required: false,
                  message: '请选择教研室',
                  trigger: 'change',
                }"
              >
                <el-select
                  v-model="item.staffRoomValues"
                  multiple
                  filterable
                  placeholder="请选择教研室"
                  @change="
                    doSelectChange(
                      'staffRoomValues',
                      index,
                      majorList,
                      'isIndeterminateMajor',
                      'majorCheckAll'
                    )
                  "
                  style="width: 90%"
                >
                  <el-checkbox
                    :style="{ paddingLeft: '20px' }"
                    :indeterminate="item.isIndeterminateMajor"
                    v-model="item.studentCheckAll"
                    @change="
                      handleCheckAllChange(
                        'staffRoomValues',
                        index,
                        majorList,
                        'code',
                        'isIndeterminateMajor',
                        $event
                      )
                    "
                  >
                    全选
                  </el-checkbox>
                  <el-option
                    v-for="dict in staffRoomList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRole">确 定</el-button>
        <el-button @click="cancelRole">取 消</el-button>
      </div>
    </el-dialog>

    <use-worder-dialog
      :dialogTitle="title"
      :dialogOpen="open"
      :userId="curRow.id"
      :opt="opt"
      @update:dialogOpen="(value) => (open = value)"
      @refresh="getList"
    />
  </div>
</template>

<script>
import useWorderDialog from "./useWorderDialog";
import {
  deleteUserWorker,
  getUserWorker,
  getUserWorkerPage,
  exportUserWorkerExcel,
  exportTemplate,
  updateUserStatus,
} from "@/api/system/userWorker";
import { resetUserPwd } from "@/api/system/user";
import { getSimpleMajorList } from "@/api/system/major";
import { assignUserRole, listUserresources } from "@/api/system/permission";
import { getDepartmentSimpleList } from "@/api/system/department";
import { listSimpleRoles } from "@/api/system/role";
import { getBaseHeader } from "@/utils/request";
import { DICT_TYPE, getDictDatas } from "@/utils/dict";

import { CommonStatusEnum } from "@/utils/constants";

export default {
  name: "UserWorker",
  components: { useWorderDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 职工用户列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      form: {},
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        username: null,
        nickname: null,
        deptIds: [],
        roleIds: [],
        studentType: null,
        sex: null,
        certificateType: null,
        educations: [],
        degree: null,
        positionalTitleses: [],
        staffRoomValues: [],
        professionalBaseValues: [],
        isGeneralTeacher: null,
        isKeyTeacher: null,
        certificateTypes: [],
        certificateLevels: [],
        status: 1,
      },

      opt: "",
      departmentOptions: [],
      roleOptions: [],
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的科室数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API + "/admin-api/system/user-worker/import",
      },
      // 是否显示弹出层（角色权限）
      openRole: false,
      studentTypeOptions: getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE),
      staffRoomList: getDictDatas(DICT_TYPE.STAFF_ROOM),
      curRow: {},
      majorList: [],
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    // 获得角色列表
    this.roleOptions = [];
    listSimpleRoles().then((response) => {
      const list = response.data;
      list.forEach((item) => {
        if (item.code !== "super_admin" && item.code !== "student") {
          this.roleOptions.push(item);
        }
      });
      // this.roleOptions.push(...response.data);
    });
    getSimpleMajorList().then((res) => {
      this.majorList = res.data;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getUserWorkerPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        username: undefined,
        nickname: undefined,
        workTypes: undefined,
        avatar: undefined,
        sex: undefined,
        email: undefined,
        mobile: undefined,
        certificateType: undefined,
        certificateNumber: undefined,
        birthday: undefined,
        maritalStatus: undefined,
        nation: undefined,
        isPartyMember: "false",
        isMentor: "false",
        healthStatus: undefined,
        graduationDate: undefined,
        graduationSchool: undefined,
        education: undefined,
        degree: undefined,
        workStartDate: undefined,
        positionalTitles: undefined,
        positionalTitlesCertificate: undefined,
        educationCertificate: undefined,
        degreeCertificate: undefined,
        obtainDate: undefined,
        chiefPhysicianObtainDate: undefined,
        depositBank: undefined,
        bankCardNo: undefined,
        isGeneralTeacher: "false",
        isKeyTeacher: "false",
        pmsList: [],
        majorCodes: [],
        staffRoomValues: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.curRow = {};
      this.open = true;
      this.opt = "add";
      this.title = "添加职工用户";
    },
    /** 修改按钮操作 */
    handleUpdate(row, type) {
      this.curRow = row;
      this.open = true;
      this.opt = type;
      this.title = type === "edit" ? "修改职工用户" : "查看职工用户";
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === CommonStatusEnum.ENABLE ? "启用" : "锁定";
      this.$modal
        .confirm('确认要"' + text + '""' + row.username + '"用户吗?')
        .then(function () {
          return updateUserStatus(row.id, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status =
            row.status === CommonStatusEnum.ENABLE
              ? CommonStatusEnum.DISABLE
              : CommonStatusEnum.ENABLE;
        });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除职工用户编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteUserWorker(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有职工用户数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportUserWorkerExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "职工用户.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "职工档案导入";
      this.upload.open = true;
    },
    /** 导出按钮操作 */
    handleExportTemplate() {
      exportTemplate()
        .then((response) => {
          this.$download.excel(response, "职工档案导入模版.xlsx");
        })
        .catch(() => {});
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      let text = "创建成功数量：" + data.createUsernames.length;
      // for (const name of data.createUsernames) {
      //   text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + name;
      // }
      text += "<br />更新成功数量：" + data.updateUsernames.length;
      // for (const name of data.updateUsernames) {
      //   text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + name;
      // }
      text +=
        "<br />更新失败数量：" + Object.keys(data.failureUsernames).length;
      for (const name in data.failureUsernames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          name +
          "：" +
          data.failureUsernames[name];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 更多操作
    handleCommand(command, index, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleRole":
          this.handleRole(row);
          break;
        default:
          break;
      }
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.username + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,16}$/,
        inputErrorMessage:
          "必须包含大小写字母和数字的组合，可以使用特殊字符，长度8到16位",
      })
        .then(({ value }) => {
          resetUserPwd(row.id, value).then((response) => {
            this.$modal.msgSuccess("修改成功，新密码是：" + value);
          });
        })
        .catch(() => {});
    },
    handleRoleChange(val, index) {
      const _list = JSON.parse(JSON.stringify(this.form.pmsList));
      const roleCode = this.roleOptions.find(
        (item) => item.id === parseInt(val)
      ).code;
      _list[index].roleId = val;
      _list[index].roleCode = roleCode;
      this.form.pmsList = _list;
    },
    /** 分配用户角色操作 */
    handleRole(row) {
      this.reset();
      const id = row.id;
      // 处理了 form 的用户 username 和 nickname 的展示
      this.form.id = id;
      this.form.username = row.username;
      this.form.nickname = row.nickname;
      // 打开弹窗
      this.openRole = true;

      // 获得角色拥有的菜单集合
      listUserresources(id).then((response) => {
        // 设置选中
        if (!response.data.roleIds || response.data.roleIds.length === 0) {
          this.form.pmsList = [
            {
              roleId: "",
              isIndeterminateDept: false,
              deptCheckAll: false,
              deptIds: [],
              isIndeterminateStudent: false,
              studentCheckAll: false,
              studentTypes: [],
              isIndeterminateMajor: false,
              majorCheckAll: false,
              majorCodes: [],
              staffRoomValues: [],
              isIndeterminateStaffRoom: false,
              staffRoomCheckAll: false,
            },
          ];
        } else {
          const list = [];
          response.data.roleIds.forEach((item) => {
            const roleDeptIds = response.data.roleDeptIds[item];
            const roleStudentTypes = response.data.roleStudentTypes[item];
            const roleMajorCodes = response.data.majorCodes[item] || [];
            const roleStaffRoomValues =
              response.data.staffRoomValues[item] || [];

            let isIndeterminateDept = false;
            let deptCheckAll = false;
            if (
              roleDeptIds.length > 0 &&
              roleDeptIds.length < this.departmentOptions.length
            ) {
              isIndeterminateDept = true;
            }
            if (roleDeptIds.length === this.departmentOptions.length) {
              deptCheckAll = true;
            }

            let isIndeterminateStudent = false;
            let studentCheckAll = false;
            if (
              roleStudentTypes.length > 0 &&
              roleStudentTypes.length < this.studentTypeOptions.length
            ) {
              isIndeterminateStudent = true;
            }
            if (roleStudentTypes.length === this.studentTypeOptions.length) {
              studentCheckAll = true;
            }

            let isIndeterminateMajor = false;
            let majorCheckAll = false;
            if (
              roleMajorCodes.length > 0 &&
              roleMajorCodes.length < this.majorList.length
            ) {
              isIndeterminateMajor = true;
            }
            if (roleMajorCodes.length === this.majorList.length) {
              majorCheckAll = true;
            }

            let isIndeterminateStaffRoom = false;
            let staffRoomCheckAll = false;
            if (
              roleStaffRoomValues.length > 0 &&
              roleStaffRoomValues.length <
                getDictDatas(DICT_TYPE.STAFF_ROOM).length
            ) {
              isIndeterminateStaffRoom = true;
            }
            if (
              roleStaffRoomValues.length ===
              getDictDatas(DICT_TYPE.STAFF_ROOM).length
            ) {
              staffRoomCheckAll = true;
            }

            const roleCode = this.roleOptions.find(
              (role) => role.id === parseInt(item)
            ).code;

            list.push({
              roleCode,
              roleId: item,
              isIndeterminateDept,
              deptCheckAll,
              deptIds: roleDeptIds,
              isIndeterminateStudent,
              studentCheckAll,
              studentTypes: roleStudentTypes,
              isIndeterminateMajor,
              majorCheckAll,
              majorCodes: roleMajorCodes,
              majorCodes: roleMajorCodes,
              staffRoomValues: roleStaffRoomValues,
              isIndeterminateStaffRoom,
              staffRoomCheckAll,
            });
          });
          this.form.pmsList = list;
        }
      });
    },
    // 取消按钮（角色权限）
    cancelRole() {
      this.openRole = false;
      this.reset();
    },
    doSelectChange(prop, index, options, indeterminate, checkAll) {
      const _list = JSON.parse(JSON.stringify(this.form.pmsList));
      const checkLength = _list[index][prop].length;
      // console.log('doSelectChange', _list[index][prop])
      _list[index][indeterminate] =
        checkLength > 0 && checkLength < options.length;
      _list[index][checkAll] = checkLength === options.length;
      this.form.pmsList = _list;
    },
    handleCheckAllChange(prop, index, options, pid, indeterminate, value) {
      // console.log('handleCheckAllChange', value)
      const _list = JSON.parse(JSON.stringify(this.form.pmsList));
      // console.log('handleCheckAllChange', options.map(p => p[pid]))
      _list[index][prop] = value
        ? options.map((p) => parseInt(p[pid])) || []
        : [];
      _list[index][indeterminate] = false;
      this.form.pmsList = _list;
    },
    addPms() {
      this.form.pmsList.push({
        roleId: "",
        roleCode: "",
        deptIds: [],
        studentTypes: [],
      });
    },
    delPms(index) {
      this.form.pmsList.splice(index, 1);
    },
    /** 提交按钮（角色权限） */
    submitRole: function () {
      // console.log('submitRole====', this.form)
      if (this.form.id !== undefined) {
        let deptIds = {};
        let studentTypes = {};
        let roleIds = [];
        let majorCodes = {};
        let staffRoomValues = {};

        this.form.pmsList.forEach((item) => {
          roleIds.push(item.roleId);
          deptIds[item.roleId] = item.deptIds;
          studentTypes[item.roleId] = item.studentTypes;
          majorCodes[item.roleId] = item.majorCodes;
          staffRoomValues[item.roleId] = item.staffRoomValues;
        });

        assignUserRole({
          userId: this.form.id,
          // roleIds: roleIds.join(','),
          roleIds: roleIds,
          deptIds: deptIds,
          studentTypes: studentTypes,
          majorCodes,
          staffRoomValues,
        }).then((response) => {
          this.$modal.msgSuccess("分配角色成功");
          this.openRole = false;
          this.getList();
        });
      }
    },

    delProject(index) {
      let list = JSON.parse(JSON.stringify(this.formItems || []));
      list.splice(index, 1);
      this.formItems = list;
      this.computeScore(list);
    },
    projectNamechange(val, index) {
      let list = JSON.parse(JSON.stringify(this.formItems || []));
      list[index].name = val;
      this.formItems = list;
    },
  },
};
</script>

<style lang="scss">
.user-role-dialog {
  .el-form-item__content {
    position: relative;
  }
  .el-icon-circle-plus-outline,
  .el-icon-remove-outline {
    font-weight: bold;
    font-size: 24px;
    position: absolute;
    top: 5px;
    right: 6px;
    cursor: pointer;
  }
  .el-icon-remove-outline {
    right: 36px;
  }
}
</style>
