import request from '@/utils/request'

// 获得院级督导开展分页
export function getHospitalSuperviseDevelopList(query) {
  return request({
    url: '/rotation/hospital-supervise-develop/page',
    method: 'get',
    params: query
  })
}

// 更新院级督导反馈
export function updateHospitalSuperviseDevelop(data) {
  return request({
    url: '/rotation/hospital-supervise-develop/update',
    method: 'put',
    data
  })
}

// 获得院级督导信息
export function getHospitalSuperviseInfos(id) {
  return request({
    url: '/rotation/hospital-supervise-develop/get-supervise-infos',
    method: 'get',
    params: { id }
  })
}
