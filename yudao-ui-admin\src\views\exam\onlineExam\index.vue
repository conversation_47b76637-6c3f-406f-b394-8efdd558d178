<template>
  <div class="onlineExam-container">
    <div class="onlineExam-main">
      <div class="onlineExam-header">
        <div class="onlineExam-title">
          <div class="title">{{ paperInfo.paperName }}</div>
          <div class="onlineExam-attrs">
            <span>总分：{{ paperInfo.paperTotalScore }}</span>
            <span>考试时间：{{ paperInfo.answerMinuteTime || 90 }}分钟</span>
          </div>
        </div>
        <div class="onlineExam-time">
          {{ time }}
        </div>
      </div>

      <div class="onlineExam-cont">
        <div class="onlineExam-left">
          <div class="left-header">
            <div class="left-title">答题卡</div>
            <div class="left-title-tips">
              <div class="done">已答</div>
              <div class="todo">未答</div>
            </div>
          </div>
          <div class="left-cont">
            <div
              class="left-group"
              v-for="element in answerQuestionTypes"
              :key="element.questionType"
            >
              <div class="left-group-title">
                <dict-tag
                  :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                  :value="element.questionType"
                />
              </div>
              <div class="left-group-cont">
                <a
                  v-for="(item, index) in element.answerQuestionResults"
                  :class="{ done: getDoneStatus(item) }"
                  :key="index"
                  :href="'#' + element.questionType + '-' + index"
                >
                  {{ index + 1 }}
                </a>
              </div>
            </div>
          </div>
          <div class="left-bottom">
            <el-button
              type="primary"
              :loading="submitLoading"
              @click="handleSubmit()"
              >提交</el-button
            >
          </div>
        </div>

        <div class="onlineExam-right">
          <div
            class="right-group"
            v-for="element in answerQuestionTypes"
            :key="element.questionType"
          >
            <div class="left-group-title">
              <dict-tag
                :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                :value="element.questionType"
              />
            </div>
            <div
              class="question-item"
              v-for="(item, index) in element.answerQuestionResults"
              :key="index"
              :id="element.questionType + '-' + index"
            >
              <div class="question-item-header">
                <span class="NO">{{ index + 1 }}、</span>
                <span class="type"
                  >【<dict-tag
                    :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                    :value="element.questionType"
                  />】</span
                >
                <span class="title" v-if="element.questionType !== '3'">
                  <text-to-html
                    :text="item.content.title"
                    :images="getPreviewImages(item.content.titleImages)"
                  ></text-to-html
                  >（ ）
                </span>
                <span class="score">({{ item.score }}分)</span>
                <div class="compat-choice" v-if="element.questionType === '3'">
                  <div v-for="(choice, key) in item.content.choiceList">
                    {{ key }}、<text-to-html
                      :text="choice"
                      :images="
                        getPreviewImages((item.content.choiceImages || {})[key])
                      "
                    ></text-to-html>
                  </div>
                </div>
              </div>

              <template
                v-if="['1', '2', '8'].indexOf(element.questionType) > -1"
              >
                <div class="question-item-cont">
                  <div
                    v-if="
                      element.questionType === '1' ||
                      element.questionType === '8'
                    "
                  >
                    <el-radio-group v-model="item.answerResult">
                      <el-radio
                        v-for="(choice, key) in item.content.choiceList"
                        :label="key"
                        :key="key"
                      >
                        {{ key }}、<text-to-html
                          :text="choice"
                          :images="
                            getPreviewImages(
                              (item.content.choiceImages || {})[key]
                            )
                          "
                        ></text-to-html>
                      </el-radio>
                    </el-radio-group>
                  </div>
                  <div v-if="element.questionType === '2'">
                    <el-checkbox-group v-model="item.answerResult">
                      <el-checkbox
                        v-for="(choice, key) in item.content.choiceList"
                        :label="key"
                        :key="key"
                      >
                        {{ key }}、<text-to-html
                          :text="choice"
                          :images="
                            getPreviewImages(
                              (item.content.choiceImages || {})[key]
                            )
                          "
                        ></text-to-html>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </template>
              <template v-if="element.questionType === '3'">
                <div class="sub-questions">
                  <div
                    v-for="(title, key, index) in item.content.titleList"
                    :key="key"
                  >
                    {{ key }}、<text-to-html
                      :text="title"
                      :images="
                        getPreviewImages(
                          (item.content.titleListImages || {})[key]
                        )
                      "
                    ></text-to-html>
                    (
                    <el-select
                      class="compat-select"
                      size="mini"
                      v-model="item.answerResult[index]"
                    >
                      <el-option
                        v-for="(choice, key) in item.content.choiceList"
                        :label="key"
                        :value="key"
                        :key="key"
                      ></el-option>
                    </el-select>
                    )
                  </div>
                </div>
              </template>
              <template v-if="element.questionType === '4'">
                <div class="sub-questions">
                  <div
                    v-for="(subsetTitle, index) in item.content.subsetTitles"
                    :key="index"
                  >
                    <div class="question-item-header">
                      <span class="NO">{{ index + 1 }}、</span>
                      <span class="title"
                        ><text-to-html
                          :text="subsetTitle.title"
                          :images="getPreviewImages(subsetTitle.titleImages)"
                        ></text-to-html
                        >（ ）</span
                      >
                    </div>
                    <div class="question-item-cont">
                      <el-checkbox-group v-model="item.answerResult[index]">
                        <el-checkbox
                          v-for="(choice, key) in subsetTitle.choiceList"
                          :label="key"
                          :key="key"
                        >
                          {{ key }}、<text-to-html
                            :text="choice"
                            :images="
                              getPreviewImages(
                                (subsetTitle.choiceImages || {})[key]
                              )
                            "
                          ></text-to-html>
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </template>
              <template v-if="['10'].indexOf(element.questionType) > -1">
                <div class="question-item-cont">
                  <div
                    v-for="(result, index) in item.answerResult"
                    class="question-tiankong-row"
                  >
                    <span>
                      第
                      {{
                        [
                          "一",
                          "二",
                          "三",
                          "四",
                          "五",
                          "六",
                          "七",
                          "八",
                          "九",
                          "十",
                        ][index]
                      }}
                      空
                    </span>
                    <el-input
                      :key="index"
                      v-model="item.answerResult[index]"
                      placeholder="请输入对应空答案"
                    ></el-input>
                  </div>
                </div>
              </template>

              <template v-if="['5', '6'].indexOf(element.questionType) > -1">
                <div class="question-item-cont">
                  <el-input
                    v-model="item.answerResult"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 6 }"
                    placeholder="请输入试题答案"
                  ></el-input>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPaper, createResult } from "@/api/exam/answerResult";
import TextToHtml from "../components/TextToHtml";
import { getAccessToken } from "@/utils/auth";

export default {
  name: "OnlineExam",
  components: { TextToHtml },
  data() {
    return {
      radio: "A",
      timer: null,
      count: 0,
      queryParams: {
        examObjectId: "",
        paperId: "",
      },
      paperInfo: {},
      answerQuestionTypes: [],
      submitLoading: false,
    };
  },
  computed: {
    time() {
      let hour = Math.floor(this.count / 3600);
      let min = Math.floor((this.count - hour * 3600) / 60);
      let sec = Math.round(this.count - hour * 3600 - min * 60);
      hour = hour < 10 ? `0${hour}` : hour;
      min = min < 10 ? `0${min}` : min;
      sec = sec < 10 ? `0${sec}` : sec;
      return `${hour}:${min}:${sec}`;
    },
  },
  created() {
    const query = window.location.search.substr(1); // 去除问号
    const params = query.split("&"); // 将参数用 & 分割成数组
    const obj = {}; // 存储参数的对象
    params.forEach((param) => {
      const arr = param.split("="); // 将键值对用等号分割成数组
      const key = decodeURIComponent(arr[0]); // 解码参数名
      const value = decodeURIComponent(arr[1]); // 解码参数值
      obj[key] = value; // 存储参数到对象
    });
    console.log(obj); // 输出参数对象
    this.queryParams.examObjectId = obj.examObjectId;
    this.queryParams.paperId = obj.paperId;
    this.queryParams.code = obj.code || "";
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getPaper(this.queryParams).then((response) => {
        this.paperInfo = response.data;
        const {
          answerQuestionTypes = [],
          startTime,
          currentTime,
          isFirst,
        } = this.paperInfo;
        answerQuestionTypes.forEach((element) => {
          const { answerQuestionResults = [] } = element;
          answerQuestionResults.forEach((item) => {
            try {
              item.content = JSON.parse(item.content);
            } catch (e) {
              item.content = { title: "", choiceList: [] };
            }
            item.score = element.score;
            if (item.questionType === "2") {
              item.answerResult =
                !isFirst && item.answerResult
                  ? item.answerResult.split("")
                  : [];
            }
            if (item.questionType === "3") {
              item.answerResult =
                !isFirst && item.answerResult
                  ? item.answerResult.split(",")
                  : [];
            }
            if (item.questionType === "4") {
              const subsetTitles = item.content.subsetTitles || [];
              item.answerResult =
                !isFirst && item.answerResult
                  ? item.answerResult
                      .split(",")
                      .map((t) => (t ? t.split("") : []))
                  : subsetTitles.map(() => []);
            }
            if (item.questionType === "10") {
              // 匹配中文括号对（）或英文括号对()或下划线__________
              const regex = /(?:（）|\(\)|_{6,})/g;
              const matches = item.content?.title?.match(regex);
              item.answerResult =
                matches?.map((item) => item.replace(/（|）|\(|\)|_/g, "")) ||
                [];
            }
          });
        });
        this.answerQuestionTypes = answerQuestionTypes;
        let minTime = this.paperInfo.answerMinuteTime || 90;
        if (!isFirst) {
          const costMin =
            (new Date(currentTime).getTime() - new Date(startTime).getTime()) /
            60000;
          minTime = minTime - costMin;
          if (minTime < 0) {
            this.$message.error("已超过答题时间了！");
            minTime = 0;
          }
        }
        this.handleTime(minTime);
        console.log("this.answerQuestionTypes====", this.answerQuestionTypes);
        this.loading = false;
      });
    },
    getDoneStatus(item) {
      if (item.questionType === "2") {
        return item.answerResult.length > 1;
      } else if (item.questionType === "3") {
        return (
          item.answerResult.filter((answer) => answer).length ===
          Object.keys(item.content.titleList).length
        );
      } else if (item.questionType === "4") {
        return (
          item.answerResult.filter((answers) => answers.length > 0).length ===
          item.content.subsetTitles.length
        );
      } else if (item.questionType === "10") {
        // 匹配中文括号对（）或英文括号对()
        const regex = /(?:（）|\(\)|_{6,})/g;
        const matches = item.content?.title.match(regex);
        return (
          item.answerResult &&
          item.answerResult.length &&
          matches?.length ===
            item.answerResult.filter((answers) => answers.length > 0).length
        );
      } else {
        return item.answerResult;
      }
    },
    getPreviewImages(images) {
      return (images || []).map(
        (item) =>
          `${process.env.VUE_APP_BASE_API}${item.url}?token=${getAccessToken()}`
      );
    },
    handleTime(min) {
      this.count = Math.round(min * 60);

      // 修正setInterval时间不精确问题
      let count = 0;
      const lastTimer = new Date().getTime();
      const func = () => {
        count++;
        let delayTimer = new Date().getTime() - (1000 * count + lastTimer);
        if (delayTimer < 0) {
          delayTimer = 0;
        }
        const nextTimer = 1000 - delayTimer;
        this.count -= 1;
        if (this.count <= 0) {
          this.handleSubmit(true);
          clearTimeout(this.timer);
          clearInterval(this.interval);
        } else {
          this.timer = setTimeout(func, nextTimer);
        }
      };
      this.timer = setTimeout(func, 1000);
    },
    handleSubmit(force = false) {
      const _answerQuestionTypes = JSON.parse(
        JSON.stringify(this.answerQuestionTypes)
      );
      let flag = true;
      _answerQuestionTypes.forEach((element) => {
        const { answerQuestionResults = [] } = element;
        answerQuestionResults.forEach((item) => {
          item.content = JSON.stringify(item.content);
          if (item.questionType === "2") {
            item.answerResult = item.answerResult.sort().join("");
          }
          if (item.questionType === "3" || item.questionType === "10") {
            item.answerResult = item.answerResult.join(",");
          }
          if (item.questionType === "4") {
            item.answerResult = item.answerResult
              .map((val) => val.join(""))
              .join(",");
          }
          if (!item.answerResult) {
            flag = false;
          }
        });
      });

      if (force) {
        this.submit(_answerQuestionTypes);
      } else {
        this.$modal
          .confirm(flag ? "是否确认提交?" : "还有题目未作答，是否确认提交?")
          .then(() => {
            this.submit(_answerQuestionTypes);
          });
      }
    },
    submit(answerQuestionTypes) {
      this.submitLoading = true;
      this.paperInfo.answerQuestionTypes = answerQuestionTypes;
      this.paperInfo.examAnswerStatus = "submitted";
      createResult(this.paperInfo)
        .then((response) => {
          this.submitLoading = false;
          this.$router.push({
            path: "/examResult",
            query: { id: response.data },
          });
        })
        .catch((err) => {
          this.submitLoading = false;
        });
    },
    handleAutoSave() {
      if (this.count <= 0) return;
      const _answerQuestionTypes = JSON.parse(
        JSON.stringify(this.answerQuestionTypes)
      );
      _answerQuestionTypes.forEach((element) => {
        const { answerQuestionResults = [] } = element;
        answerQuestionResults.forEach((item) => {
          item.content = JSON.stringify(item.content);
          if (item.questionType === "2") {
            item.answerResult = item.answerResult.sort().join("");
          }
          if (item.questionType === "3" || item.questionType === "10") {
            item.answerResult = item.answerResult.join(",");
          }
          if (item.questionType === "4") {
            item.answerResult = item.answerResult
              .map((val) => val.join(""))
              .join(",");
          }
        });
      });
      this.paperInfo.answerQuestionTypes = _answerQuestionTypes;
      this.paperInfo.examAnswerStatus = "in_progress";
      createResult(this.paperInfo);
    },
    closeWindow() {
      window.close();
    },
  },
  mounted() {
    this.interval = setInterval(this.handleAutoSave, 30 * 1000);
  },
  beforeDestroy() {
    clearInterval(this.interval);
  },
};
</script>

<style lang="scss">
.onlineExam-container {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: #fafafa;

  .onlineExam-main {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .onlineExam-header {
      background-color: #ffffff;
      -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      padding: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .onlineExam-title {
        .title {
          font-size: 18px;
          color: #333;
        }

        .onlineExam-attrs {
          margin-top: 5px;
          span {
            color: #999;
            font-size: 12px;
            margin-right: 25px;
          }
        }
      }

      .onlineExam-time {
        font-size: 28px;
        font-weight: bold;
        color: crimson;
      }
    }

    .onlineExam-cont {
      height: calc(100% - 75px);
      padding: 15px;
      display: flex;
      justify-content: space-between;

      .onlineExam-left {
        width: 25%;
        height: 100%;
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        position: relative;
        display: flex;
        flex-direction: column;

        .left-header {
          padding: 10px 15px;
          border-bottom: 1px #eee solid;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .left-title {
            font-size: 16px;
          }

          .left-title-tips {
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .done,
            .todo {
              position: relative;
              padding-left: 15px;
            }
            .done {
              margin-left: 15px;
              &::before {
                content: " ";
                display: inline-block;
                width: 10px;
                height: 10px;
                background: cornflowerblue;
                position: absolute;
                left: 0;
                top: 3px;
              }
            }
            .todo {
              margin-left: 15px;
              &::before {
                content: " ";
                display: inline-block;
                width: 10px;
                height: 10px;
                background: #eee;
                position: absolute;
                left: 0;
                top: 3px;
              }
            }
          }
        }

        .left-cont {
          height: calc(100% - 93px);
          padding: 15px;
          overflow-y: auto;

          .left-group {
            .left-group-title {
              font-size: 14px;
              font-weight: bold;
              color: #333;
              position: relative;
              padding-left: 12px;

              &::before {
                content: " ";
                display: inline-block;
                width: 5px;
                height: 14px;
                background: cornflowerblue;
                position: absolute;
                left: 0;
                top: 3px;
              }
            }
            .left-group-cont {
              padding-top: 10px;
              padding-bottom: 20px;
              margin-right: -10px;

              a {
                display: inline-block;
                width: 30px;
                height: 30px;
                text-align: center;
                line-height: 30px;
                background: #eee;
                color: #333;
                font-size: 14px;
                margin-right: 10px;
                margin-bottom: 10px;
                cursor: pointer;

                &.done {
                  background: cornflowerblue;
                  color: #fff;
                }
              }
            }
          }
        }

        .left-bottom {
          height: 50px;
          display: flex;
          align-items: center;
          padding: 0 15px;
          position: absolute;
          width: 100%;
          bottom: 0;

          .el-button {
            width: 100%;
          }
        }
      }

      .onlineExam-right {
        overflow-y: auto;
        flex: 1;
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        margin-left: 15px;
        padding: 15px;

        .left-group-title {
          background: #fafafa;
          font-size: 14px;
          font-weight: bold;
          color: #333;
          position: relative;
          padding: 12px 15px 12px 28px;
          margin: -15px -15px 15px -15px;
          border-bottom: 1px #eee solid;

          &::before {
            content: " ";
            display: inline-block;
            width: 5px;
            height: 14px;
            background: cornflowerblue;
            position: absolute;
            left: 15px;
            top: 15px;
          }
        }

        .question-item {
          margin-bottom: 30px;
          font-size: 15px;
          .question-item-header {
            .type {
              color: dodgerblue;
            }
            .score {
              color: #999;
            }
          }
          .question-item-cont {
            padding-top: 15px;
            padding-left: 25px;
            .el-radio {
              display: block;
              margin-bottom: 10px;
              width: fit-content;
            }
            .el-checkbox {
              display: block;
              margin-bottom: 10px;
              width: fit-content;
            }
            .question-tiankong-row {
              display: flex;
              align-items: center;
              margin-bottom: 10px;

              span {
                display: inline-block;
                width: 60px;
                margin-right: 10px;
                text-align: right;
              }
            }
          }
          .el-radio-group {
            display: block;
          }
        }

        .compat-choice {
          padding-top: 15px;
          padding-left: 25px;
          > div {
            margin-bottom: 10px;
          }
        }

        .compat-select {
          width: 82px;
          .el-input__inner {
            border-radius: 0;
            border-width: 0 0 1px 0;
            text-align: center;
            padding-left: 10px;
          }
        }

        .sub-questions {
          padding-top: 10px;
          padding-left: 15px;
          > div {
            margin-bottom: 15px;
          }
        }
      }
    }
  }
}
</style>
