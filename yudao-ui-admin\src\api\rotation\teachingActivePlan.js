import request from "@/utils/request";

// 创建教学活动
export function createTeachingActive(data) {
  return request({
    url: "/rotation/teaching-active-plan/create",
    method: "post",
    data: data,
  });
}

// 更新教学活动
export function updateTeachingActive(data) {
  return request({
    url: "/rotation/teaching-active-plan/update",
    method: "put",
    data: data,
  });
}

// 更新教学活动照片
export function updateTeachingActivePictures(data) {
  return request({
    url: "/rotation/teaching-active-plan/update-pictures",
    method: "put",
    data: data,
  });
}

// 更新教学活动附件
export function updateTeachingActiveFiles(data) {
  return request({
    url: "/rotation/teaching-active-plan/update-files",
    method: "put",
    data: data,
  });
}

// 删除教学活动
export function deleteTeachingActive(id) {
  return request({
    url: "/rotation/teaching-active-plan/delete?id=" + id,
    method: "delete",
  });
}

// 获得教学活动
export function getTeachingActive(id) {
  return request({
    url: "/rotation/teaching-active-plan/get?id=" + id,
    method: "get",
  });
}

// 获得教学活动分页
export function getTeachingActivePage(query) {
  return request({
    url: "/rotation/teaching-active-plan/page",
    method: "get",
    params: query,
    headers: { component: "rotation/teachingActivePlan/index" },
  });
}

// 导出教学活动 Excel
export function exportTeachingActiveExcel(query) {
  return request({
    url: "/rotation/teaching-active-plan/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获得参加学员列表
export function getStudentsList(query) {
  return request({
    url: "/rotation/teaching-active-student-plan/list-students",
    method: "get",
    params: query,
  });
}

// 确认参加
export function confirmJoin(data) {
  return request({
    url: "/rotation/teaching-active-student-plan/confirm-join",
    method: "put",
    data: data,
  });
}

// 撤销参加
export function revokeJoin(data) {
  return request({
    url: "/rotation/teaching-active-student-plan/revoke-join",
    method: "put",
    data: data,
  });
}

// 下载用户导入模板
export function importTemplate() {
  return request({
    url: "/rotation/teaching-active-plan/get-import-template",
    method: "get",
    responseType: "blob",
  });
}

// 导出参加学员 Excel
export function exportStudentExcel(query) {
  return request({
    url: "/rotation/teaching-active-student-plan/export-students-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 发布教学活动
export function createPublishTeachingActive(data) {
  return request({
    url: "/rotation/teaching-active-plan/publish",
    method: "post",
    data: data,
  });
}

// 获得用户拥有的学员类型列表
export function getStudentTypeListByUser(query) {
  return request({
    url: "/system/permission/list-user-student-types",
    method: "get",
    params: query,
  });
}

// 删除教学活动用户
export function deleteTeachingUser(params) {
  return request({
    url: "/rotation/teaching-active-student-plan/deleteUser",
    method: "delete",
    params: params,
  });
}
