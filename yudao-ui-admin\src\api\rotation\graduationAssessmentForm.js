import request from '@/utils/request'

// 创建出科技能考核
export function createGraduationAssessmentForm(data) {
  return request({
    url: '/rotation/graduation-assessment-form/create',
    method: 'post',
    data: data
  })
}

// 更新出科技能考核
export function updateGraduationAssessmentForm(data) {
  return request({
    url: '/rotation/graduation-assessment-form/update',
    method: 'put',
    data: data
  })
}

// 删除出科技能考核
export function deleteGraduationAssessmentForm(id) {
  return request({
    url: '/rotation/graduation-assessment-form/delete?id=' + id,
    method: 'delete'
  })
}

// 获得出科技能考核
export function getGraduationAssessmentForm(id) {
  return request({
    url: '/rotation/graduation-assessment-form/get?id=' + id,
    method: 'get'
  })
}

// 获得出科技能考核分页
export function getGraduationAssessmentFormPage(query) {
  return request({
    url: '/rotation/graduation-assessment-form/page',
    method: 'get',
    params: query
  })
}

// 导出出科技能考核 Excel
export function exportGraduationAssessmentFormExcel(query) {
  return request({
    url: '/rotation/graduation-assessment-form/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取维护指标
export function getGraduationAssessmentFormItems(id) {
  return request({
    url: '/rotation/graduation-assessment-form/get-form-item?id=' + id,
    method: 'get'
  })
}

// 创建出科技能考核
export function createFormItems(data) {
  return request({
    url: '/rotation/graduation-assessment-form/create-form-item',
    method: 'post',
    data: data
  })
}
