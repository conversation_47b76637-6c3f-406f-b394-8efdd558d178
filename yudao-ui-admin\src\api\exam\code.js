import request from '@/utils/request'

// 获得考试码
export function getCode() {
  return request({
    url: '/exam/code/get',
    method: 'get'
  })
}

// 重置考试码
export function resetCode() {
  return request({
    url: '/exam/code/reset',
    method: 'get'
  })
}

// 是否启用考试码
export function updateCode(state) {
  return request({
    url: '/exam/code/update',
    method: 'put',
    data: { isEnable: state }
  })
}

// 获得是否开启考试码
export function getIsEnable() {
  return request({
    url: '/exam/code/getIsEnable',
    method: 'get'
  })
}

// 验证考试码
export function validateCode(code) {
  return request({
    url: '/exam/code/validateCode',
    method: 'get',
    params: { code }
  })
}
