import request from '@/utils/request'

// 创建科室
export function createDepartment(data) {
  return request({
    url: '/system/department/create',
    method: 'post',
    data: data
  })
}

// 更新科室
export function updateDepartment(data) {
  return request({
    url: '/system/department/update',
    method: 'put',
    data: data
  })
}

// 删除科室
export function deleteDepartment(id) {
  return request({
    url: '/system/department/delete?id=' + id,
    method: 'delete'
  })
}

// 获得科室
export function getDepartment(id) {
  return request({
    url: '/system/department/get?id=' + id,
    method: 'get'
  })
}

// 获得科室分页
export function getDepartmentPage(query) {
  return request({
    url: '/system/department/page',
    method: 'get',
    params: query
  })
}

// 导出科室 Excel
export function exportDepartmentExcel(query) {
  return request({
    url: '/system/department/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导出医院科室模版
export function exportDepartmentTemplate() {
  return request({
    url: '/system/department/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 导出轮转科室模版
export function exportRotationDepartmentTemplate() {
  return request({
    url: '/system/department/get-import-rotation-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 获取医院科室精简列表
export function getDepartmentSimpleList(parentId, staffRoomValue = '') {
  const parentIdArr = (parentId || "").split(",");
  const params = { staffRoomValue };
  if (parentIdArr.length > 1) {
    params.parentIds = parentIdArr;
  } else {
    params.parentId = parentId;
  }
  return request({
    url: '/system/department/list-all-simple',
    method: 'get',
    params
  })
}

// 获取科室树精简列表
export function getDepartmentSimpleTree() {
  return request({
    url: '/system/department/list-tree',
    method: 'get',
  })
}

// 获得用户拥有的医院科室列表
export function getDepartmentPermissionList(query) {
  return request({
    url: '/system/permission/list-current-user-depts',
    method: 'get',
    params: query
  })
}

// 获得用户拥有的轮转科室列表
export function getRotationDepartmentPermissionList(query) {
  return request({
    url: '/system/permission/list-current-user-rotation-depts',
    method: 'get',
    params: query
  })
}

// 获取轮转科室精简信息列表
export function getRotationDepartmentSimpleList() {
  return request({
    url: '/system/department/list-rotation-all-simple',
    method: 'get'
  })
}

// 获取用户轮转科室精简信息列表
export function getUserRotationAllSimple(query) {
  return request({
    url: '/system/department/list-user-rotation-all-simple',
    method: 'get',
    params: query
  })
}
