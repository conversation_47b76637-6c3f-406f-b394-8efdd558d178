<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="800px"
    v-dialogDrag
    append-to-body
    @close="cancel"
  >
    <div class="appraise-conts" id="appraise-conts-box">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="edit-item export-item"
      >
        <div class="item-title">{{ `${index + 1}、${item.appraiseKpi}` }}</div>
        <div class="item-cont">
          <el-rate
            v-if="item.appraise360ItemId"
            v-model="item.score"
            allow-half
            text-color="#ff9900"
            :max="5"
            :disabled="disabled"
            score-template="{value}"
          >
          </el-rate>
          <div v-else>
            <el-input
              v-if="!disabled"
              v-model="item.comments"
              type="textarea"
              :placeholder="`请输入${item.appraiseKpi}`"
            />
            <div
              v-else
              style="
                border: 1px #eee solid;
                padding: 5px 15px;
                background: #f8f8f8;
                border-radius: 4px;
                color: #999;
                min-height: 50px;
              "
            >
              {{ item.comments }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="!disabled" type="primary" @click="submitForm"
        >确 定</el-button
      >
      <el-button
        v-if="disabled"
        type="primary"
        @click="handleDown"
        :loading="exportloading"
        >导出</el-button
      >
      <el-button @click="cancel">{{ !disabled ? "取 消" : "关 闭" }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createAppraise } from "@/api/rotation/studentappraiseteacher";
import { exportPDF } from "@/utils/exportUtils";

export default {
  name: "AppraiseDialog",
  props: {
    title: {
      type: String,
    },
    open: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: {},
    },
    appraiseSourceId: {
      type: Number,
    },
    appraiseTargetId: {
      type: Number,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    pdfName: {
      type: String,
      default: "",
    },
  },
  computed: {
    list() {
      if (this.disabled) {
        const _list = this.data.resultItemRespVOS;
        _list.forEach((item) => {
          if (item.appraise360ItemId) {
            const _score = (item.score / item.appraise360ItemScore) * 5;
            item.score = _score;
          }
        });
        return _list;
      }
      return this.data.appraise360ResultItemCreateReqVOS;
    },
  },
  data() {
    return {
      exportloading: false,
    };
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("setOpen", false);
    },
    submitForm() {
      const list = JSON.parse(JSON.stringify(this.list));
      let scoreFlag = true;
      let commentsFlag = true;
      list.forEach((item) => {
        if (item.appraise360ItemId) {
          if (!item.score) {
            scoreFlag = false;
          }
          const _score = (item.appraise360ItemScore / 10) * item.score * 2;
          item.score = _score;
        } else {
          if (!item.comments) {
            commentsFlag = false;
          }
        }
      });
      if (!scoreFlag) {
        return this.$modal.msgError("请完成评分");
      }
      if (!commentsFlag) {
        return this.$modal.msgError("请填写意见或反馈");
      }
      const params = JSON.parse(JSON.stringify(this.data));
      params.appraiseSourceId = this.appraiseSourceId;
      params.appraiseTargetId = this.appraiseTargetId;
      params.appraise360ResultItemCreateReqVOS = list;

      this.$modal
        .confirm("评价后不可修改，是否确认评价?")
        .then(() => {
          createAppraise(params).then((response) => {
            this.$modal.msgSuccess("评价成功");
            this.$emit("setOpen", false);
            this.$emit("refreshList");
          });
        })
        .catch(() => {});
    },

    handleDown() {
      this.exportloading = true;
      exportPDF("appraise-conts-box", this.pdfName, () => {
        this.exportloading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.appraise-conts {
  border: 1px #ddd solid;
  border-bottom: none;

  .item-title {
    padding: 10px 15px 10px 10px;
    border-bottom: 1px #ddd solid;

    &::before {
      content: "*";
      display: inline-block;
      color: red;
      padding-right: 5px;
    }
  }

  .item-cont {
    padding: 10px 15px;
    border-bottom: 1px #ddd solid;
  }
}
</style>
