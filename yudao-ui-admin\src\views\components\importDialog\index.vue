<template>
  <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body>
    <el-upload
      ref="upload"
      accept=".xlsx, .xls"
      drag
      :headers="headers"
      :action="url"
      :limit="1"
      :disabled="isUploading"
      :auto-upload="false"
      :on-progress="handleFileUploadProgress"
      :on-success="handleFileSuccess"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip text-center" slot="tip">
        <span>仅允许导入xls、xlsx格式文件。</span>
        <el-link
          class="download-link"
          type="primary"
          :underline="false"
          @click="importTemplate"
          >下载模板</el-link
        >
      </div>
    </el-upload>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitFileForm">确 定</el-button>
      <el-button @click="open = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getBaseHeader } from "@/utils/request";

export default {
  name: "import-dialog",
  data() {
    return {
      open: false,
      title: "导入",
      url: "",
      resultTextMethod: null,
      templateApi: null,
      templateName: "",
      headers: getBaseHeader(),
      isUploading: false,
    };
  },
  methods: {
    /** 导入按钮操作 */
    handleImport(option = {}) {
      this.title = option.title || "导入";
      this.url = option.url || "";
      this.resultTextMethod = option.resultTextMethod || null;
      this.templateApi = option.templateApi || null;
      this.templateName = option.templateName || "";
      this.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.templateApi().then((response) => {
        this.$download.excel(response, this.templateName || "模板.xlsx");
      });
    },
    // 文件上传中处理
    handleFileUploadProgress() {
      this.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      this.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.open = false;

      // 拼接提示语
      let data = response.data;
      let text = "";
      if (this.resultTextMethod) {
        text = this.resultTextMethod(data);
      } else {
        text = "创建成功数量：" + data.createActiveNames.length;
        for (const name of data.createActiveNames) {
          text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
        }
        text +=
          "<br />创建失败数量：" + Object.keys(data.failureActiveNames).length;
        for (const name in data.failureActiveNames) {
          text +=
            "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
            name +
            "：" +
            data.failureActiveNames[name];
        }
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.$emit("success");
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="scss" scoped>
.download-link {
  font-size: 12px;
  vertical-align: baseline;
}
</style>
