<template>
  <div>
    <h3 class="sheet-name">{{ scoreForm.formTitle }}</h3>

    <el-form class="form-info" style="margin-bottom: 20px" inline>
      <el-form-item style="margin-right: 30px" label="姓名：">{{ scoreForm.nickName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="专业：">{{ scoreForm.majorName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="派送单位：">{{ scoreForm.dispatchingUnit }}</el-form-item>
      <el-form-item label="轮转科室：">{{ scoreForm.rotationDepartmentName }}</el-form-item>
      <el-form-item label="轮转时间：">{{ scoreForm.rotationTime }}</el-form-item>
    </el-form>

    <table class="score-table">
      <tr>
        <th style="width: 25%">考核内容</th>
        <th style="width: 60%">评分标准</th>
        <th style="width: 15%">得分</th>
      </tr>
      <tr>
        <td>出科考核（50分）</td>
        <td>出科理论考核实际分数 <span class="examine-score">{{ form.syncExamResultsActualScore }}</span> * 0.50</td>
        <td>{{ form.syncExamResultsScore || "未参加" }}</td>
      </tr>
      <tr>
        <td rowspan="3">平时成绩（50分）</td>
        <td>
          <span style="display: inline-block; width: 26px;"></span>考勤实际分数
          <span v-if="check" class="examine-score">{{ form.attendanceActualScore }}</span>
          <el-input-number
            v-else
            class="score-input"
            v-model="form.attendanceActualScore"
            controls-position="right"
            step-strictly
            :min="0"
            :max="100"
            :step="1"
            :disabled="check"
            @change="handleChange('attendance')"
          ></el-input-number>
          * 0.10</td>
        <td>{{ form.attendanceScore || "未参加" }}</td>
      </tr>
      <tr>
        <td>
          医德医风实际分数
          <span v-if="check" class="examine-score">{{ form.medicalEthicsActualScore }}</span>
          <el-input-number
            v-else
            class="score-input"
            v-model="form.medicalEthicsActualScore"
            controls-position="right"
            step-strictly
            :min="0"
            :max="100"
            :step="1"
            :disabled="check"
            @change="handleChange('medicalEthics')"
          ></el-input-number>
          * 0.10</td>
        <td>{{ form.medicalEthicsScore || "未参加" }}</td>
      </tr>
      <tr>
        <td>
          课堂表现实际分数
          <span v-if="check" class="examine-score">{{ form.classroomPerformanceActualScore }}</span>
          <el-input-number
            v-else
            class="score-input"
            v-model="form.classroomPerformanceActualScore"
            controls-position="right"
            step-strictly
            :min="0"
            :max="100"
            :step="1"
            :disabled="check"
            @change="handleChange('classroomPerformance')"
          ></el-input-number>
          * 0.30</td>
        <td>{{ form.classroomPerformanceScore || "未参加" }}</td>
      </tr>
      <tr>
        <td colspan="2">考核综合成绩</td>
        <td>{{ form.comprehensiveScore }}</td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  name: 'sheet-third',
  components: {},
  props: {
    scoreForm: Object,
    check: Boolean,
  },
  data() {
    return {
      form: {}
    }
  },
  methods: {
    formValidate() {
      if (this.form.attendanceActualScore === undefined) {
        this.$message.warning("请填写考勤实际分数~");
        return false;
      }
      if (this.form.medicalEthicsActualScore === undefined) {
        this.$message.warning("请填写医德医风实际分数~");
        return false;
      }
      if (this.form.classroomPerformanceActualScore === undefined) {
        this.$message.warning("请填写课堂表现实际分数~");
        return false;
      }
      return true;
    },
    handleChange(type) {
      switch (type) {
        case "attendance":
          this.form.attendanceScore = (this.form.attendanceActualScore * 0.1).toFixed(1);
          break;
        case "medicalEthics":
          this.form.medicalEthicsScore = (this.form.medicalEthicsActualScore * 0.1).toFixed(1);
          break;
        case "classroomPerformance":
          this.form.classroomPerformanceScore = (this.form.classroomPerformanceActualScore * 0.3).toFixed(1);
          break;
      }
      this.form.comprehensiveScore = ((+this.form.syncExamResultsScore || 0) + (+this.form.attendanceScore || 0)
        + (+this.form.medicalEthicsScore || 0) + (+this.form.classroomPerformanceScore || 0)).toFixed(1);
    },
  },
  created() {
    this.form = this.scoreForm;
    if (this.scoreForm.attendanceActualScore === null) {
      this.form.attendanceActualScore = undefined;
    }
    if (this.scoreForm.medicalEthicsActualScore === null) {
      this.form.medicalEthicsActualScore = undefined;
    }
    if (this.scoreForm.classroomPerformanceActualScore === null) {
      this.form.classroomPerformanceActualScore = undefined;
    }
  },
}
</script>

<style lang="scss" scoped>
.sheet-name {
  font-size: 15px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 10px;
}

.form-info {
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}

.score-table {
  line-height: 1.5;
  border-collapse: collapse;
  width: 100%;

  th, td {
    border: 1px solid #DCDFE6;
    text-align: center;
    padding: 7px 5px;
  }
}

.examine-score {
  display: inline-block;
  min-width: 30px;
  padding: 5px;
  border-bottom: 1px solid #eee;
}

.score-input {
  width: 100px;
}
</style>
