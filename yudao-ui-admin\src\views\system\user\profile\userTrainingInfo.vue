<template>
  <el-form ref="form" :model="form" label-width="110px">
    <div>
      <el-row>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="学员类型：" prop="studentType">
            <!-- <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="form.studentType" /> -->
            <el-select
              v-model="form.studentType"
              placeholder="学员类型"
              :disabled="true"
              style="width: 100%"
            >
              <el-option
                v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="8"></el-col>
        <el-col :md="8" :lg="8" :xl="8"></el-col>
      </el-row>

      <el-row>
        <el-col :md="16" :lg="16" :xl="16">
          <el-form-item
            v-if="form.studentType == 1"
            label="人员类型："
            prop="personnelType"
          >
            <!-- <dict-tag :type="DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE" :value="form.personnelType" /> -->
            <el-radio-group
              v-model="form.personnelType"
              @input="personnelTypeChange"
              :disabled="true"
            >
              <el-radio
                v-for="dict in this.getDictDatas(
                  DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE
                )"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col
          v-if="form.personnelType == 2 || form.personnelType == 4"
          :md="8"
          :lg="8"
          :xl="8"
        >
          <el-form-item
            v-if="form.studentType == 1"
            label="派送单位："
            prop="dispatchingUnit"
          >
            <!-- <span>{{form.dispatchingUnit}}</span> -->
            <el-input
              v-model="form.dispatchingUnit"
              placeholder="派送单位"
              maxlength="50"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="培训开始日期：" prop="trainingStartDate">
            <!-- <span>{{form.trainingStartDate}}</span> -->
            <el-date-picker
              clearable
              v-model="form.trainingStartDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="培训开始日期"
              style="width: 100%"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="年级：" prop="grade">
            <!-- <span>{{form.grade}}</span> -->
            <el-date-picker
              v-model="form.grade"
              type="year"
              format="yyyy"
              value-format="yyyy"
              placeholder="年级"
              style="width: 100%"
              :disabled="true"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="培训专业：" prop="major">
            <!-- <span>{{form.majorName}}</span> -->
            <!-- <dict-tag :type="DICT_TYPE.SYSTEM_MAJOR" :value="form.major" /> -->
            <!-- <el-select v-model="form.major" placeholder="请选择培训专业" :disabled="form.id">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_MAJOR)"
                        :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select> -->
            <el-select
              v-model="form.major"
              filterable
              placeholder="培训专业"
              style="width: 100%"
              :disabled="true"
            >
              <el-option
                v-for="major in majorList"
                :key="major.code"
                :label="major.name"
                :value="major.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="培训方案：" prop="standardSchemeId">
            <!-- <span>{{standardSchemeMap[form.standardSchemeId]}}</span> -->
            <el-select
              v-model="form.standardSchemeId"
              placeholder="培训方案"
              style="width: 100%"
              :disabled="true"
            >
              <el-option
                v-for="standardScheme in standardSchemeList"
                :key="standardScheme.id"
                :label="standardScheme.name"
                :value="standardScheme.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="培训时长：" prop="rotationTime">
            <!-- <span>{{form.rotationTime}}</span> -->
            <el-input
              v-model="form.rotationTime"
              placeholder="培训时长"
              style="width: 100%"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="结业年份：" prop="graduationYear">
            <!-- <span>{{form.graduationYear}}</span> -->
            <el-date-picker
              v-model="form.graduationYear"
              type="year"
              format="yyyy"
              value-format="yyyy"
              placeholder="结业年份"
              style="width: 100%"
              :disabled="true"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="结业时间：" prop="graduationDate">
            <!-- <span>{{form.graduationDate}}</span> -->
            <el-date-picker
              clearable
              v-model="form.graduationDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="结业时间"
              style="width: 100%"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="责任导师：" prop="responsibleMentorUserId">
            <!-- <span>{{form.responsibleMentorUserId}}</span> -->
            <el-input
              v-model="form.responsibleMentorUserId"
              placeholder="责任导师"
              maxlength="25"
              style="width: 100%"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="8"></el-col>
      </el-row>

      <el-row
        v-if="
          form.studentType == '12' ||
          (form.studentType == '1' && form.personnelType == '4')
        "
      >
        <p>注意：以下内容请上传大小不超过50M的PDF文件！</p>
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="培养计划：" prop="cultivationPlanFile">
            <FileUpload
              v-model="form.cultivationPlanFile"
              :fileSize="50"
              :fileType="['pdf']"
              :isShowTip="false"
            />
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="开题报告：" prop="proposalFile">
            <FileUpload
              v-model="form.proposalFile"
              :fileSize="50"
              :fileType="['pdf']"
              :isShowTip="false"
            />
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="中期考核：" prop="midTermAssessmentFile">
            <FileUpload
              v-model="form.midTermAssessmentFile"
              :fileSize="50"
              :fileType="['pdf']"
              :isShowTip="false"
            />
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="毕业论文：" prop="graduationThesisFile">
            <FileUpload
              v-model="form.graduationThesisFile"
              :fileSize="50"
              :fileType="['pdf']"
              :isShowTip="false"
            />
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="学位成绩单：" prop="degreeTranscriptFile">
            <FileUpload
              v-model="form.degreeTranscriptFile"
              :fileSize="50"
              :fileType="['pdf']"
              :isShowTip="false"
            />
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="其他材料：" prop="otherFile">
            <FileUpload
              v-model="form.otherFile"
              :fileSize="50"
              :fileType="['pdf']"
              :isShowTip="false"
            />
          </el-form-item>
        </el-col>
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item>
            <el-button type="primary" size="mini" @click="submit">
              保存
            </el-button>
            <el-button type="danger" size="mini" @click="close">
              关闭
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import {
  getUserStudent,
  getStandardSchemeList,
} from "@/api/system/userStudent";
import { updateUserStudent } from "@/api/system/user";
import { getSimpleMajorList } from "@/api/system/major";

export default {
  props: {
    user: {
      type: Object,
    },
  },
  components: {
    FileUpload,
  },
  data() {
    return {
      // 表单参数
      form: {},
      standardSchemeList: [],
      standardSchemeMap: {},
      majorList: [],
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      const id = this.user.id;
      getUserStudent(id).then((response) => {
        this.form = response.data;

        if (response.data.cultivationPlanFile) {
          this.form.cultivationPlanFile = JSON.parse(
            response.data.cultivationPlanFile
          );
        } else {
          this.form.cultivationPlanFile = [];
        }

        if (response.data.proposalFile) {
          this.form.proposalFile = JSON.parse(response.data.proposalFile);
        } else {
          this.form.proposalFile = [];
        }

        if (response.data.midTermAssessmentFile) {
          this.form.midTermAssessmentFile = JSON.parse(
            response.data.midTermAssessmentFile
          );
        } else {
          this.form.midTermAssessmentFile = [];
        }

        if (response.data.graduationThesisFile) {
          this.form.graduationThesisFile = JSON.parse(
            response.data.graduationThesisFile
          );
        } else {
          this.form.graduationThesisFile = [];
        }

        if (response.data.degreeTranscriptFile) {
          this.form.degreeTranscriptFile = JSON.parse(
            response.data.degreeTranscriptFile
          );
        } else {
          this.form.degreeTranscriptFile = [];
        }

        if (response.data.otherFile) {
          this.form.otherFile = JSON.parse(response.data.otherFile);
        } else {
          this.form.otherFile = [];
        }

        this.getStandardScheme();
        this.getSimpleMajorListHandler(this.form);
      });
    },
    getStandardScheme() {
      const { studentType, major } = this.form;
      if (studentType && major) {
        const params = {
          studentType,
          major,
        };
        getStandardSchemeList(params).then((res) => {
          const data = res.data;
          this.standardSchemeList = data;
          let obj = {};
          data.forEach((ele) => {
            obj[ele.id] = ele.name;
          });
          this.standardSchemeMap = obj;
        });
      }
    },
    getSimpleMajorListHandler(row) {
      const params = {
        studentType: row.studentType,
      };
      getSimpleMajorList(params).then((res) => {
        this.majorList = res.data;
      });
    },

    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
          };
          params.cultivationPlanFile = JSON.stringify(
            this.form.cultivationPlanFile || []
          );
          params.proposalFile = JSON.stringify(this.form.proposalFile || []);
          params.midTermAssessmentFile = JSON.stringify(
            this.form.midTermAssessmentFile || []
          );
          params.graduationThesisFile = JSON.stringify(
            this.form.graduationThesisFile || []
          );
          params.degreeTranscriptFile = JSON.stringify(
            this.form.degreeTranscriptFile || []
          );
          params.otherFile = JSON.stringify(this.form.otherFile || []);

          updateUserStudent(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
          });
        }
      });
    },
    close() {
      this.$tab.closePage();
    },
  },
};
</script>
