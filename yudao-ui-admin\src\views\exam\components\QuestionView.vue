<template>
  <div>
    <div v-if="curQuestion" class="question-item">
      <div class="question-item-header">
        <span class="NO">1、</span>
        <span class="type"
          >【<dict-tag
            :type="DICT_TYPE.EXAM_QUESTION_TYPE"
            :value="curQuestion.questionType"
          />】</span
        >
        <span class="title" v-if="curQuestion.questionType !== '3'">
          <text-to-html
            :text="curQuestion.content.title"
            :images="getPreviewImages(curQuestion.content.titleImages)"
          ></text-to-html
          >（ ）
        </span>
        <div class="compat-choice" v-if="curQuestion.questionType === '3'">
          <div v-for="(choice, key) in curQuestion.content.choiceList">
            {{ key }}、<text-to-html
              :text="choice"
              :images="
                getPreviewImages((curQuestion.content.choiceImages || {})[key])
              "
            ></text-to-html>
          </div>
        </div>
      </div>

      <template v-if="['1', '2', '8'].indexOf(curQuestion.questionType) > -1">
        <div class="question-item-cont">
          <div
            v-if="
              curQuestion.questionType === '1' ||
              curQuestion.questionType === '8'
            "
          >
            <div
              v-for="(choice, key) in curQuestion.content.choiceList"
              :key="key"
            >
              {{ key }}、<text-to-html
                :text="choice"
                :images="
                  getPreviewImages(
                    (curQuestion.content.choiceImages || {})[key]
                  )
                "
              ></text-to-html>
            </div>
          </div>
          <div v-if="curQuestion.questionType === '2'">
            <div
              v-for="(choice, key) in curQuestion.content.choiceList"
              :key="key"
            >
              {{ key }}、<text-to-html
                :text="choice"
                :images="
                  getPreviewImages(
                    (curQuestion.content.choiceImages || {})[key]
                  )
                "
              ></text-to-html>
            </div>
          </div>
        </div>
        <div class="question-item-answer">
          <strong>正确答案：</strong>
          {{ curQuestion.answer }}
        </div>
        <div class="question-item-analysis">
          <strong>试题解析：</strong> {{ curQuestion.analysis }}
        </div>
      </template>

      <template v-if="curQuestion.questionType === '3'">
        <div class="sub-questions">
          <div
            v-for="(title, key, index) in curQuestion.content.titleList"
            :key="key"
          >
            {{ key }}、<text-to-html
              :text="title"
              :images="
                getPreviewImages(
                  (curQuestion.content.titleListImages || {})[key]
                )
              "
            ></text-to-html>
            (
            <!-- <el-select class="compat-select" size="mini">
                <el-option
                  v-for="(choice, key) in curQuestion.content.choiceList"
                  :label="key"
                  :value="key"
                ></el-option>
              </el-select> -->
            )
          </div>
        </div>
        <!-- <div class="question-item-answer">
          正确答案：
          <template
            v-for="ans in (curQuestion.rightAnswer || '')
              .split(',')
              .map((val, index) => `${index + 1})、${val}`)"
          >
            {{ ans }}
            <span style="display: inline-block; width: 20px"></span>
          </template>
        </div> -->
        <div class="question-item-answer">
          <strong>正确答案：</strong>
          {{ curQuestion.answer }}
        </div>
        <div class="question-item-analysis">
          <strong>试题解析：</strong> {{ curQuestion.analysis }}
        </div>
      </template>

      <template v-if="curQuestion.questionType === '4'">
        <div class="sub-questions">
          <div
            v-for="(subsetTitle, index) in curQuestion.content.subsetTitles ||
            []"
            :key="index"
          >
            <div class="question-item-header">
              <span class="NO">{{ index + 1 }}、</span>
              <span class="title"
                ><text-to-html
                  :text="subsetTitle.title"
                  :images="getPreviewImages(subsetTitle.titleImages)"
                ></text-to-html
                >（）</span
              >
            </div>
            <div class="question-item-cont">
              <div v-for="(choice, key) in subsetTitle.choiceList" :key="key">
                {{ key }}、<text-to-html
                  :text="choice"
                  :images="
                    getPreviewImages((subsetTitle.choiceImages || {})[key])
                  "
                ></text-to-html>
              </div>
            </div>
            <div class="question-item-answer">
              <strong>正确答案：</strong> {{ subsetTitle.answer }}
            </div>
            <div class="question-item-analysis" style="margin-bottom: 20px">
              <strong>试题解析：</strong> {{ subsetTitle.analysis }}
            </div>
          </div>
        </div>
      </template>

      <template v-if="['10'].indexOf(curQuestion.questionType) > -1">
        <div class="question-item-cont">
          <div
            v-for="(choice, index) in curQuestion.choiceArr ||
            curQuestion.answerResult"
            class="question-tiankong-row"
          >
            <span>
              第{{
                ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"][
                  index
                ]
              }}空
            </span>
            <el-input :key="index"></el-input>
          </div>
        </div>
        <div class="question-item-answer">
          <strong>正确答案：</strong>
          {{ curQuestion.answer }}
        </div>
        <div class="question-item-analysis">
          <strong>试题解析：</strong> {{ curQuestion.analysis }}
        </div>
      </template>

      <template v-if="['5', '6'].indexOf(curQuestion.questionType) > -1">
        <div class="question-item-cont">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" />
        </div>
        <div class="question-item-answer">
          <strong>正确答案：</strong>
          {{ curQuestion.answer }}
        </div>
        <div class="question-item-analysis">
          <strong>试题解析：</strong> {{ curQuestion.analysis }}
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import TextToHtml from "./TextToHtml.vue";
import { getAccessToken } from "@/utils/auth";

export default {
  name: "QuestionView",
  components: { TextToHtml },
  props: {
    curQuestion: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    getPreviewImages(images) {
      return (images || []).map(
        (item) =>
          `${process.env.VUE_APP_BASE_API}${item.url}?token=${getAccessToken()}`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.question-item {
  margin-bottom: 30px;
  font-size: 15px;
  position: relative;
  padding-right: 35px;
  margin-top: 10px;

  .question-item-header {
    .type {
      color: dodgerblue;
    }
    .score {
      color: #999;
    }
  }
  .question-item-cont {
    padding-top: 15px;
    padding-left: 25px;
    .el-radio {
      display: block;
      margin-bottom: 10px;
      width: fit-content;
    }
    .el-checkbox {
      display: block;
      margin-bottom: 10px;
      width: fit-content;
    }
    .wrong-select {
      color: #f56c6c;

      ::v-deep .el-radio__inner,
      ::v-deep .el-checkbox__inner {
        border-color: #f56c6c;
        background: #f56c6c;
      }
      ::v-deep .el-radio__label,
      ::v-deep .el-checkbox__label {
        color: #f56c6c;
      }

      ::v-deep .el-input__inner {
        color: #f56c6c;
        border-color: #f56c6c;
      }

      ::v-deep .el-select__caret {
        color: #f56c6c;
      }
    }

    .question-tiankong-row {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      span {
        display: inline-block;
        width: 60px;
        margin-right: 10px;
        text-align: right;
      }
    }
  }
  .question-item-answer {
    padding: 10px 0 0 0;
  }
  .question-item-analysis {
    padding: 10px 0 0 0;
  }
  .el-radio-group {
    display: block;
  }

  .collect-box {
    position: absolute;
    display: inline-block;
    right: 10px;
    top: 10px;

    i {
      color: #409eff;
      font-size: 20px;
    }
  }
}
</style>
