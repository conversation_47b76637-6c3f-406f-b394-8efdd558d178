<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="教学方式" prop="teachingMethods">
        <el-select
          v-model="queryParams.teachingMethods"
          placeholder="请选择教学方式"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_METHODS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训科室" prop="departmentId">
        <el-select
          v-model="queryParams.departmentId"
          filterable
          clearable
          placeholder="请选择培训科室"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="parseInt(item.id)"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="培训教师" prop="trainingUserNickname">
        <el-input
          v-model="queryParams.trainingUserNickname"
          placeholder="请输入培训教师"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:graduate-summary:create']"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:graduate-summary:export']"
          >导出</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" class="graduateSummary-table">
      <el-table-column
        label="教学方式"
        align="center"
        prop="teachingMethods"
        width="150"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_METHODS"
            :value="scope.row.teachingMethods"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="培训科室"
        align="center"
        prop="departmentName"
        width="150"
      />
      <el-table-column
        label="培训教师"
        align="center"
        prop="trainingUserNickname"
        width="120"
      />
      <el-table-column
        label="见习时间"
        align="center"
        prop="startTime"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }} ~ {{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学生人数" align="center" prop="studentNumber" />
      <el-table-column label="图片附件" align="center" prop="files">
        <template slot-scope="scope">
          <imageUpload :value="JSON.parse(scope.row.files)" disabled />
        </template>
      </el-table-column>
      <el-table-column label="登记人" align="center" prop="creatorNickname" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isShowBt"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row, 'update')"
            v-hasPermi="['rotation:graduate-summary:update']"
          >
            修改
          </el-button>
          <el-button
            v-if="scope.row.isShowBt"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:graduate-summary:delete']"
          >
            删除
          </el-button>
          <el-button
            v-if="!scope.row.isShowBt"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row, 'detail')"
            v-hasPermi="['rotation:graduate-summary:update']"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1000px"
      v-dialogDrag
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="170px"
        :disabled="opt === 'detail'"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="教学方式" prop="teachingMethods">
              <el-select
                v-model="form.teachingMethods"
                placeholder="请选择教学方式"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_METHODS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="培训科室" prop="departmentId">
              <el-select
                v-model="form.departmentId"
                filterable
                clearable
                placeholder="请选择培训科室"
                style="width: 100%"
              >
                <el-option
                  v-for="item in departmentOptions"
                  :key="parseInt(item.id)"
                  :label="item.name"
                  :value="parseInt(item.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="培训教师" prop="trainingUserId">
              <el-select
                v-model="form.trainingUserId"
                filterable
                clearable
                placeholder="请选择培训教师"
                style="width: 100%"
              >
                <el-option
                  v-for="item in mentorOptions"
                  :key="parseInt(item.id)"
                  :label="item.nickname"
                  :value="parseInt(item.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="见习病种" prop="internDiseaseType">
              <el-input
                v-model="form.internDiseaseType"
                placeholder="请输入见习病种"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="学生组别" prop="studentGroup">
              <el-input
                v-model="form.studentGroup"
                placeholder="请输入学生组别"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="学生人数" prop="studentNumber">
              <el-input-number
                v-model="form.studentNumber"
                controls-position="right"
                :min="1"
                placeholder="请输入学生人数"
                style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="见习时间" prop="startTime">
              <el-date-picker
                v-model="form.trainingTimes"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                style="width: 100%"
                @change="
                  (val) => {
                    form.startTime = val[0];
                    form.endTime = val[1];
                  }
                "
              >
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="带教地点" prop="teachingAddress">
              <el-input
                v-model="form.teachingAddress"
                placeholder="请输入带教地点"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="见习目的与要求"
              prop="internPurposeRequirements"
            >
              <el-input
                v-model="form.internPurposeRequirements"
                placeholder="请输入见习目的与要求"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="见习教学的组织与实施"
              prop="internOrganizeImplement"
            >
              <el-input
                v-model="form.internOrganizeImplement"
                placeholder="请输入见习教学的组织与实施"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="见习小结" prop="internSummary">
              <el-input
                v-model="form.internSummary"
                placeholder="请输入见习小结"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="见习准备" prop="internPreparation">
              <el-input
                v-model="form.internPreparation"
                placeholder="请输入见习准备"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="图片附件" prop="files">
              <imageUpload
                v-model="form.files"
                :limit="9999"
                :disabled="opt === 'detail'"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="opt !== 'detail'">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createGraduateSummary,
  updateGraduateSummary,
  deleteGraduateSummary,
  getGraduateSummary,
  getGraduateSummaryPage,
  exportGraduateSummaryExcel,
} from "@/api/rotation/graduateSummary";
import { getDepartmentSimpleList } from "@/api/system/department";
import { getUserWorkerSimpleList } from "@/api/system/userWorker";
import ImageUpload from "@/components/ImageUpload";
import FileUploadInfo from "@/components/FileUploadInfo";

export default {
  name: "GraduateSummary",
  components: { ImageUpload, FileUploadInfo },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 见习出科小结列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        teachingMethods: null,
        departmentId: null,
        trainingUserNickname: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        teachingMethods: [
          { required: true, message: "教学方式不能为空", trigger: "change" },
        ],
        departmentId: [
          { required: true, message: "培训科室不能为空", trigger: "blur" },
        ],
        trainingUserId: [
          { required: true, message: "培训教师不能为空", trigger: "blur" },
        ],
        internDiseaseType: [
          { required: true, message: "见习病种不能为空", trigger: "change" },
        ],
        studentGroup: [
          { required: true, message: "学生组别不能为空", trigger: "blur" },
        ],
        studentNumber: [
          { required: true, message: "学生人数不能为空", trigger: "blur" },
        ],
        startTime: [
          { required: true, message: "见习开始时间不能为空", trigger: "blur" },
        ],
        endTime: [
          { required: true, message: "见习结束时间不能为空", trigger: "blur" },
        ],
        internPurposeRequirements: [
          {
            required: true,
            message: "见习目的与要求不能为空",
            trigger: "blur",
          },
        ],
        internOrganizeImplement: [
          {
            required: true,
            message: "见习教学的组织与实施不能为空",
            trigger: "blur",
          },
        ],
        internSummary: [
          { required: true, message: "见习小结不能为空", trigger: "blur" },
        ],
        internPreparation: [
          { required: true, message: "见习准备不能为空", trigger: "blur" },
        ],
        teachingAddress: [
          { required: true, message: "带教地点不能为空", trigger: "blur" },
        ],
      },
      departmentOptions: [],
      mentorOptions: [],
      opt: "",
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    this.getMentor();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getGraduateSummaryPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        teachingMethods: undefined,
        departmentId: undefined,
        trainingUserId: undefined,
        internDiseaseType: undefined,
        studentGroup: undefined,
        studentNumber: undefined,
        startTime: undefined,
        endTime: undefined,
        internPurposeRequirements: undefined,
        internOrganizeImplement: undefined,
        internSummary: undefined,
        internPreparation: undefined,
        teachingAddress: undefined,
        files: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.opt = "";
      this.open = true;
      this.title = "添加见习出科小结";
    },
    /** 修改按钮操作 */
    handleUpdate(row, opt) {
      this.reset();
      const id = row.id;
      this.opt = opt;
      getGraduateSummary(id).then((response) => {
        this.form = response.data;
        this.form.files = this.form.files ? JSON.parse(this.form.files) : [];
        this.form.trainingTimes = [this.form.startTime, this.form.endTime];
        this.open = true;
        this.title = "修改见习出科小结";
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    getMentor() {
      // 获得教师列表
      getUserWorkerSimpleList().then((res) => {
        // 处理 roleOptions 参数
        this.mentorOptions = [];
        this.mentorOptions.push(...res.data);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        this.form.files = this.form.files
          ? JSON.stringify(this.form.files)
          : "[]";
        // 修改的提交
        if (this.form.id != null) {
          updateGraduateSummary(this.form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createGraduateSummary(this.form).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除见习出科小结编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteGraduateSummary(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有见习出科小结数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportGraduateSummaryExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "见习出科小结.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.graduateSummary-table {
  ::v-deep .el-upload-list__item {
    width: 50px !important;
    height: 50px !important;
    margin-right: 10px;
  }
}
</style>
