import request from '@/utils/request'

// 创建请假申请
export function createDuty(data) {
  return request({
    url: '/bpm/oa/duty/create',
    method: 'post',
    data: data
  })
}

// 获得请假申请
export function getDuty(id) {
  return request({
    url: '/bpm/oa/duty/get?id=' + id,
    method: 'get'
  })
}

// 获得请假申请分页
export function getDutyPage(query) {
  return request({
    url: '/bpm/oa/duty/page',
    method: 'get',
    params: query
  })
}
