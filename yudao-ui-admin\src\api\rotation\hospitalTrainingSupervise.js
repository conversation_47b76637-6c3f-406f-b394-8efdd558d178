import request from '@/utils/request'

// 创建院级培训
export function createHospitalTraining(data) {
  return request({
    url: '/rotation/hospital-training/create',
    method: 'post',
    data: data
  })
}

// 更新院级培训
export function updateHospitalTraining(data) {
  return request({
    url: '/rotation/hospital-training/update',
    method: 'put',
    data: data
  })
}

// 删除院级培训
export function deleteHospitalTraining(id) {
  return request({
    url: '/rotation/hospital-training/delete?id=' + id,
    method: 'delete'
  })
}

// 获得院级培训
export function getHospitalTraining(id) {
  return request({
    url: '/rotation/hospital-training/get?id=' + id,
    method: 'get'
  })
}

// 获得院级培训分页
export function getHospitalTrainingPage(query) {
  return request({
    url: '/rotation/hospital-training-supervise/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/hospitalTraining/index'}
  })
}

// 导出院级培训 Excel
export function exportHospitalTrainingExcel(query) {
  return request({
    url: '/rotation/hospital-training-supervise/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

export function getTrainingSelectedNumber(query) {
  return request({
    url: '/rotation/hospital-training/get-training-selected-number',
    method: 'get',
    params: query
  })
}

// 撤销
export function hospitalTrainingRevoke(data) {
  return request({
    url: `/rotation/hospital-training/revoke?id=${data.id}`,
    method: 'put',
    data: data
  })
}

// 发布
export function hospitalTrainingPublish(data) {
  return request({
    url: `/rotation/hospital-training/publish?id=${data.id}`,
    method: 'put',
    data: data
  })
}

export function getStudentPage(query) {
  return request({
    url: '/rotation/hospital-training-user/page-student',
    method: 'get',
    params: query
  })
}

export function getWorkerPage(query) {
  return request({
    url: '/rotation/hospital-training-user/page-work',
    method: 'get',
    params: query
  })
}

// 确认参加
export function confirmJoin(data) {
  return request({
    url: '/rotation/hospital-training-user/confirm-join',
    method: 'put',
    data: data
  })
}

// 撤销参加
export function revokeJoin(data) {
  return request({
    url: '/rotation/hospital-training-user/revoke-join',
    method: 'put',
    data: data
  })
}

// 获得督导记录分页
export function getHosSuperviseRecordList(query) {
  return request({
    url: '/rotation/hospital-training-supervise/page-hospital-training-supervise-record',
    method: 'get',
    params: query
  })
}

export function getTrainerSuperviseRecordList(query) {
  return request({
    url: '/rotation/hospital-training-supervise/page-trainer-hospital-training-supervise',
    method: 'get',
    params: query
  })
}

