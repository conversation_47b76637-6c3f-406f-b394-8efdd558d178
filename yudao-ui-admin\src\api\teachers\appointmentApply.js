import request from '@/utils/request'

// 创建师资聘用申请
export function createAppointmentApply(data) {
  return request({
    url: '/teachers/appointment-apply/create',
    method: 'post',
    data: data
  })
}

// 更新师资聘用申请
export function updateAppointmentApply(data) {
  return request({
    url: '/teachers/appointment-apply/update',
    method: 'put',
    data: data
  })
}

// 删除师资聘用申请
export function deleteAppointmentApply(id) {
  return request({
    url: '/teachers/appointment-apply/delete?id=' + id,
    method: 'delete'
  })
}

// 获得师资聘用申请
export function getAppointmentApply(id) {
  return request({
    url: '/teachers/appointment-apply/get?id=' + id,
    method: 'get'
  })
}

// 获得师资聘用申请（流程中用）
export function getAppointmentApplyProcessInfo(id) {
  return request({
    url: '/teachers/appointment-apply/get-apply-process-info?id=' + id,
    method: 'get'
  })
}

// 获得师资聘用申请分页
export function getAppointmentApplyPage(query) {
  return request({
    url: '/teachers/appointment-apply/page',
    method: 'get',
    params: query
  })
}

// 导出师资聘用申请 Excel
export function exportAppointmentApplyExcel(query) {
  return request({
    url: '/teachers/appointment-apply/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
