import request from '@/utils/request'

// 获得学员评价带教统计分页
export function getStudentappraisedeptstatistic(query) {
  return request({
    url: '/rotation/studentappraisedeptstatistic/page',
    method: 'get',
    params: query
  })
}

// 获得学员评价带教详情分页
export function getAppraiseStudentList(query) {
    return request({
      url: '/rotation/studentappraisedeptstatistic/page-student',
      method: 'get',
      params: query
    })
}

// 导出学员评价科室统计数据
export function exportStudentAppraiseDeptStatistic(query) {
  return request({
    url: '/rotation/studentappraisedeptstatistic/export',
    method: 'get',
    responseType: 'blob',
    params: query
  })
}
