import request from '@/utils/request'

// 创建院级督导整改
export function createHospitalSuperviseComprehensive(data) {
  return request({
    url: '/rotation/hospital-supervise-comprehensive/create',
    method: 'post',
    data: data
  })
}

// 更新院级督导整改
export function updateHospitalSuperviseComprehensive(data) {
  return request({
    url: '/rotation/hospital-supervise-comprehensive/update',
    method: 'put',
    data: data
  })
}

// 删除院级督导整改
export function deleteHospitalSuperviseComprehensive(id) {
  return request({
    url: '/rotation/hospital-supervise-comprehensive/delete?id=' + id,
    method: 'delete'
  })
}

// 获得院级督导整改
export function getHospitalSuperviseComprehensive(id) {
  return request({
    url: '/rotation/hospital-supervise-comprehensive/get?id=' + id,
    method: 'get'
  })
}

// 获得院级督导整改分页
export function getHospitalSuperviseComprehensivePage(query) {
  return request({
    url: '/rotation/hospital-supervise-comprehensive/page',
    method: 'get',
    params: query
  })
}

// 导出院级督导整改 Excel
export function exportHospitalSuperviseComprehensiveExcel(query) {
  return request({
    url: '/rotation/hospital-supervise-comprehensive/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
