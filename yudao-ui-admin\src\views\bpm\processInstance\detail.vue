<template>
  <div class="app-container">
    <!-- 审批信息 -->
    <el-card
      class="box-card"
      v-loading="processInstanceLoading"
      v-for="(item, index) in runningTasks"
      :key="index"
    >
      <div slot="header" class="clearfix">
        <span class="el-icon-picture-outline">审批任务【{{ item.name }}】</span>
      </div>
      <el-col :span="16" :offset="6">
        <el-form
          :ref="'form' + index"
          :model="auditForms[index]"
          :rules="auditRule"
          label-width="100px"
        >
          <el-form-item
            label="流程名"
            v-if="processInstance && processInstance.name"
          >
            {{ processInstance.name }}
          </el-form-item>
          <el-form-item
            label="流程发起人"
            v-if="processInstance && processInstance.startUser"
          >
            {{ processInstance.startUser.nickname }}
            <!-- <el-tag type="info" size="mini">{{ processInstance.startUser.deptName }}</el-tag> -->
          </el-form-item>
          <el-form-item
            label="用户名"
            v-if="form && form.username">
            {{ form.username }}
          </el-form-item>
          <el-form-item
            label="人员类型"
            v-if="form && form.personnelType"
          >
            <dict-tag
              :type="DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE"
              :value="form.personnelType"
            />
          </el-form-item>
          <el-form-item
            label="学员类型"
            v-if="processInstance && processInstance.startUser"
          >
            <dict-tag
              :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
              :value="processInstance.startUser.studentType"
            />
          </el-form-item>
          <el-form-item
            label="专业"
            v-if="processInstance && processInstance.startUser"
          >
            {{ processInstance.startUser.majorName }}
          </el-form-item>
          <el-form-item
            label="年级"
            v-if="processInstance && processInstance.startUser"
          >
            {{ processInstance.startUser.grade }}
          </el-form-item>
          <el-form-item label="审批建议" prop="reason">
            <el-input
              type="textarea"
              v-model="auditForms[index].reason"
              placeholder="请输入审批建议"
            />
          </el-form-item>
        </el-form>
        <div style="margin-left: 10%; margin-bottom: 20px; font-size: 14px">
          <el-button
            icon="el-icon-edit-outline"
            type="success"
            size="mini"
            :loading="auditLoading"
            @click="handleAudit(item, true)"
            >通过</el-button
          >
          <el-button
            icon="el-icon-circle-close"
            type="danger"
            size="mini"
            :loading="auditLoading"
            @click="handleAudit(item, false)"
            >不通过</el-button
          >
          <!-- <el-button  icon="el-icon-edit-outline" type="primary" size="mini" @click="handleUpdateAssignee(item)">转办</el-button>
          <el-button icon="el-icon-edit-outline" type="primary" size="mini" @click="handleDelegate(item)">委派</el-button>
          <el-button icon="el-icon-refresh-left" type="warning" size="mini" @click="handleBack(item)">退回</el-button> -->
        </div>
      </el-col>
    </el-card>
    <!-- 申请信息 -->
    <el-card class="box-card" v-loading="processInstanceLoading">
      <div slot="header" class="clearfix">
        <span class="el-icon-document"
          >申请信息【{{ processInstance.name }}】</span
        >
      </div>
      <el-col
        v-if="
          this.processInstance.processDefinition &&
          this.processInstance.processDefinition.formType === 10
        "
        :span="16"
        :offset="6"
      >
        <div>
          <parser :key="new Date().getTime()" :form-conf="detailForm" />
        </div>
      </el-col>
      <div
        v-if="
          this.processInstance.processDefinition &&
          this.processInstance.processDefinition.formType === 20
        "
      >
        <!-- <router-link :to="this.processInstance.processDefinition.formCustomViewPath + '?id='
                          + this.processInstance.businessKey">
          <el-button type="primary">点击查看</el-button>
        </router-link> -->
        <el-form
          v-if="
            processInstance.name === '请假审批' ||
            processInstance.name === '值班申请'
          "
          ref="form"
          :model="form"
          :rules="rules"
          label-width="100px"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="开始时间：" prop="startTime">
                {{ parseTime(form.startTime, "{y}-{m}-{d}") }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结束时间：" prop="endTime">
                {{ parseTime(form.endTime, "{y}-{m}-{d}") }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                v-if="processInstance.name === '请假审批'"
                label="请假类型："
                prop="type"
              >
                <dict-tag
                  :type="DICT_TYPE.BPM_OA_LEAVE_TYPE"
                  :value="form.type"
                />
              </el-form-item>
              <el-form-item v-else label="值班类型：" prop="type">
                <dict-tag
                  :type="DICT_TYPE.BPM_OA_DUTY_TYPE"
                  :value="form.type"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                :label="
                  processInstance.name === '请假审批'
                    ? '请假时长'
                    : '值班时长：'
                "
                prop="type"
              >
                {{ form.day }}
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="原因：" prop="reason">
                {{ form.reason }}
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="附件" prop="pictures">
                <FileUpload
                  v-model="form.pictures"
                  :limit="999"
                  :fileSize="50"
                  :disabled="true"
                  :fileType="null"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          v-if="processInstance.name === '结业申请'"
          ref="form"
          :model="graduationApplicationForm"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item label="结业时间" prop="graduationDate">
            <el-date-picker
              v-model="graduationApplicationForm.graduationDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :disabled="true"
            />
          </el-form-item>

          <el-form-item label="结业总结" prop="graduationSummary">
            <el-input
              v-model="graduationApplicationForm.graduationSummary"
              type="textarea"
              autosize
              placeholder="请输入内容"
              :disabled="true"
            />
          </el-form-item>

          <el-form-item label="上传结业材料" prop="graduationMaterials">
            <FileUpload
              v-model="graduationApplicationForm.graduationMaterials"
              :limit="999"
              :fileSize="50"
              :fileType="['pdf']"
              :disabled="true"
            />
          </el-form-item>
        </el-form>

        <el-form
          v-if="processInstance.name === '年度总结'"
          ref="form"
          :model="annualSummaryForm"
          label-width="180px"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="姓名：" prop="nickname">
                {{ annualSummaryForm.nickname }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="性别：" prop="sex">
                <dict-tag
                  :type="DICT_TYPE.SYSTEM_USER_SEX"
                  :value="annualSummaryForm.sex"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出生年月：" prop="birthday">
                {{ parseTime(annualSummaryForm.birthday, "{y}-{m}-{d}") }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="名族：" prop="nation">
                <dict-tag
                  :type="DICT_TYPE.SYSTEM_NATION"
                  :value="annualSummaryForm.nation"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否党员：" prop="isPartyMember">
                <dict-tag
                  :type="DICT_TYPE.INFRA_BOOLEAN_STRING"
                  :value="annualSummaryForm.isPartyMember"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="专业基地：" prop="professionalBase">
                <dict-tag
                  :type="DICT_TYPE.PROFESSIONAL_BASE"
                  :value="annualSummaryForm.professionalBase"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年级：" prop="grade">
                {{ annualSummaryForm.grade }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核年份：" prop="releaseYear">
                {{ annualSummaryForm.releaseYear }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="人员类型：" prop="personnelType">
                <dict-tag
                  :type="DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE"
                  :value="annualSummaryForm.personnelType"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年度理论考核成绩：" prop="theoryScore">
                {{ annualSummaryForm.theoryScore }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考年度技能考核成绩：" prop="skillScore">
                {{ annualSummaryForm.skillScore }}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="本人年度工作总结：" prop="type">
                <el-input
                  v-model="annualSummaryForm.summary"
                  type="textarea"
                  autosize
                  placeholder="请输入内容"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form v-if="processInstance.name === '师资聘任'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="申请人:">{{
                appointmentApplyForm.applyNickname
              }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户名:">{{
                appointmentApplyForm.applyUsername
              }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任教岗位:">
                {{
                  getMatchedLabel(
                    roleOptions,
                    appointmentApplyForm.applyPosition,
                    "id",
                    "name"
                  )
                }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任教学员类型:">{{
                appointmentApplyForm.studentTypeNames
              }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任教科室:">{{
                appointmentApplyForm.teachDepartmentNames
              }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任职开始日期:">{{
                appointmentApplyForm.teachBeginDate
              }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任职结束日期:">{{
                appointmentApplyForm.teachEndDate
              }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="申报材料:">
                <file-upload
                  :value="
                    JSON.parse(appointmentApplyForm.applicationDocument || null)
                  "
                  disabled
                ></file-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form v-if="processInstance.name === '师资评优评先'">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="项目名称:">
                {{ evaluationPlanApplyForm.planRespVO.name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申报人:">
                {{ evaluationPlanApplyForm.planApplyRespVO.applyNickname }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用户名:">
                {{ evaluationPlanApplyForm.planApplyRespVO.applyUsername }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="评优年度:">
                {{ evaluationPlanApplyForm.planRespVO.year }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="评优项目:" prop="teachersEvaluationProject">
                <dict-tag
                  :type="DICT_TYPE.TEACHERS_EVALUATION_PROJECT"
                  :value="
                    evaluationPlanApplyForm.planRespVO.teachersEvaluationProject
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申报时间:">
                {{ evaluationPlanApplyForm.planApplyRespVO.applyTime }}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="评优内容:">
                <el-input
                  v-model="evaluationPlanApplyForm.planApplyRespVO.content"
                  type="textarea"
                  autosize
                  placeholder="请输入内容"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="申报材料:">
                <file-upload
                  :value="
                    JSON.parse(
                      evaluationPlanApplyForm.planApplyRespVO
                        .applicationDocument || null
                    )
                  "
                  disabled
                ></file-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form v-if="processInstance.name === '进修生报名' || processInstance.name === '进修生录取'">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="姓名:">
                {{ registrationForm.name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="性别:">
                {{ registrationForm.sex }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="学历:">
                {{ registrationForm.highestAcademic }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="职称:">
                {{ registrationForm.positionalTitles }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="工龄:">
                {{ registrationForm.seniority }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="工作单位:">
                {{ registrationForm.selectedUnit }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报名项目:">
                {{ registrationForm.projectName }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="时长（月）:">
                {{ registrationForm.recruitMonths }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="学费（元）:">
                {{ registrationForm.tuition }}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注:">
                {{ registrationForm.remarks }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>

    <!-- 审批记录 -->
    <el-card class="box-card" v-loading="tasksLoad">
      <div slot="header" class="clearfix">
        <span class="el-icon-picture-outline">审批记录</span>
      </div>
      <el-col :span="16" :offset="4">
        <div class="block">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in tasks"
              :key="index"
              :icon="getTimelineItemIcon(item)"
              :type="getTimelineItemType(item)"
            >
              <p style="font-weight: 700">任务：{{ item.name }}</p>
              <el-card :body-style="{ padding: '10px' }">
                <label
                  v-if="item.assigneeUser"
                  style="font-weight: normal; margin-right: 30px"
                >
                  审批人：{{ item.assigneeUser.nickname }}
                  <!-- <el-tag type="info" size="mini">{{ item.assigneeUser.deptName }}</el-tag> -->
                </label>
                <label style="font-weight: normal" v-if="item.createTime">
                  创建时间：</label
                >
                <label style="color: #8a909c; font-weight: normal">
                  {{ parseTime(item.createTime) }}
                </label>
                <label
                  v-if="item.endTime"
                  style="margin-left: 30px; font-weight: normal"
                >
                  审批时间：</label
                >
                <label
                  v-if="item.endTime"
                  style="color: #8a909c; font-weight: normal"
                >
                  {{ parseTime(item.endTime) }}</label
                >
                <label
                  v-if="item.durationInMillis"
                  style="margin-left: 30px; font-weight: normal"
                >
                  耗时：</label
                >
                <label
                  v-if="item.durationInMillis"
                  style="color: #8a909c; font-weight: normal"
                >
                  {{ getDateStar(item.durationInMillis) }}
                </label>
                <p v-if="item.reason">
                  <el-tag :type="getTimelineItemType(item)">
                    {{ item.reason }}
                  </el-tag>
                </p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-col>
    </el-card>

    <!-- 高亮流程图 -->
    <el-card class="box-card" v-loading="processInstanceLoading">
      <div slot="header" class="clearfix">
        <span class="el-icon-picture-outline">流程图</span>
      </div>
      <my-process-viewer
        key="designer"
        v-model="bpmnXML"
        v-bind="bpmnControlForm"
        :activityData="activityList"
        :processInstanceData="processInstance"
        :taskData="tasks"
      />
    </el-card>

    <!-- 对话框(转派审批人) -->
    <el-dialog
      title="转派审批人"
      :visible.sync="updateAssignee.open"
      width="500px"
      append-to-body
    >
      <el-form
        ref="updateAssigneeForm"
        :model="updateAssignee.form"
        :rules="updateAssignee.rules"
        label-width="110px"
      >
        <el-form-item label="新审批人" prop="assigneeUserId">
          <el-select
            v-model="updateAssignee.form.assigneeUserId"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in userOptions"
              :key="parseInt(item.id)"
              :label="item.nickname"
              :value="parseInt(item.id)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpdateAssigneeForm">
          确 定
        </el-button>
        <el-button @click="cancelUpdateAssigneeForm"> 取 消 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getProcessDefinitionBpmnXML } from "@/api/bpm/definition";
import { DICT_TYPE, getDictDatas } from "@/utils/dict";
import store from "@/store";
import { decodeFields } from "@/utils/formGenerator";
import Parser from "@/components/parser/Parser";
import {
  createProcessInstance,
  getProcessInstance,
} from "@/api/bpm/processInstance";
import {
  approveTask,
  getTaskListByProcessInstanceId,
  rejectTask,
  updateTaskAssignee,
  backTask,
} from "@/api/bpm/task";
import { getApplicationApplication } from "@/api/rotation/graduationApplication";
import { getAnnualSummarybpm } from "@/api/rotation/annualSummaryContent";
import { getAppointmentApplyProcessInfo } from "@/api/teachers/appointmentApply";
import { getEvaluationPlanProcessInfo } from "@/api/teachers/evaluationPlanApply";
import { getDate } from "@/utils/dateUtils";
import { listSimpleUsers } from "@/api/system/user";
import { getActivityList } from "@/api/bpm/activity";
import { getLeave } from "@/api/bpm/leave";
import { getDuty } from "@/api/bpm/oaDuty";
import FileUpload from "@/components/FileUploadInfo";
import { listSimpleRoles } from "@/api/system/role";
import { getApplicationInfo } from "@/api/recruitment/registration";
import { getProject } from "@/api/recruitment/project";
import { getProjectDetailed } from "@/api/recruitment/projectDetailed";

// 流程实例的详情页，可用于审批
export default {
  name: "ProcessInstanceDetail",
  components: {
    Parser,
    FileUpload,
  },
  data() {
    return {
      // 遮罩层
      processInstanceLoading: true,
      // 流程实例
      id: undefined, // 流程实例的编号
      processInstance: {},

      // 流程表单详情
      detailForm: {
        fields: [],
      },

      // BPMN 数据
      bpmnXML: null,
      bpmnControlForm: {
        prefix: "flowable",
      },
      activityList: [],

      // 审批记录
      tasksLoad: true,
      tasks: [],

      // 审批表单
      runningTasks: [],
      auditForms: [],
      auditRule: {
        reason: [
          { required: true, message: "审批建议不能为空", trigger: "blur" },
        ],
      },

      // 转派审批人
      userOptions: [],
      updateAssignee: {
        open: false,
        form: {
          assigneeUserId: undefined,
        },
        rules: {
          assigneeUserId: [
            { required: true, message: "新审批人不能为空", trigger: "change" },
          ],
        },
      },

      // 表单参数
      form: {
        startTime: undefined,
        endTime: undefined,
        type: undefined,
        reason: undefined,
        day: undefined,
        pictures: undefined,
      },

      graduationApplicationForm: {},

      // 数据字典
      categoryDictDatas: getDictDatas(DICT_TYPE.BPM_MODEL_CATEGORY),
      auditLoading: false,

      // 年度总结 申请信息
      annualSummaryForm: {},
      // 师资聘任申请信息
      appointmentApplyForm: {},
      // 师资评优评先
      evaluationPlanApplyForm: {},
      // 岗位列表
      roleOptions: [],
      // 招录报名录取信息
      registrationForm: {},
    };
  },
  created() {
    // 获得用户列表
    this.userOptions = [];
    listSimpleUsers().then((response) => {
      this.userOptions.push(...response.data);
    });
    listSimpleRoles().then((response) => {
      const excludeRoleCodes = [
        "super_admin",
        "admin",
        "student",
        "hospital_admin",
        "recruitment_user",
      ];
      this.roleOptions = response.data.filter(
        (item) => !excludeRoleCodes.includes(item.code)
      );
    });
  },
  activated() {
    this.id = this.$route.query.id;
    if (!this.id) {
      this.$message.error("未传递 id 参数，无法查看流程信息");
      return;
    }
    this.getDetail();
  },
  methods: {
    /** 获得流程实例 */
    getDetail() {
      // 获得流程实例相关
      this.processInstanceLoading = true;
      getProcessInstance(this.id).then((response) => {
        if (!response.data) {
          this.$message.error("查询不到流程信息！");
          return;
        }
        // 设置流程信息
        this.processInstance = response.data;

        if (this.processInstance.processDefinition.formType === 20) {
          if (this.processInstance.name === "请假审批") {
            getLeave(this.processInstance.businessKey).then((response) => {
              this.form = response.data;
              this.form.pictures = this.form.pictures
                ? JSON.parse(this.form.pictures)
                : undefined;
            });
          }
          if (this.processInstance.name === "值班申请") {
            getDuty(this.processInstance.businessKey).then((response) => {
              this.form = response.data;
              this.form.pictures = this.form.pictures
                ? JSON.parse(this.form.pictures)
                : undefined;
            });
          }

          if (this.processInstance.name === "结业申请") {
            getApplicationApplication({
              id: this.processInstance.businessKey,
            }).then((response) => {
              this.graduationApplicationForm = response.data;
              this.graduationApplicationForm.graduationMaterials = this
                .graduationApplicationForm.graduationMaterials
                ? JSON.parse(this.graduationApplicationForm.graduationMaterials)
                : [];
            });
          }

          if (this.processInstance.name === "年度总结") {
            getAnnualSummarybpm({
              id: this.processInstance.businessKey,
            }).then((response) => {
              this.annualSummaryForm = response.data;
            });
          }

          if (this.processInstance.name === "师资聘任") {
            getAppointmentApplyProcessInfo(
              this.processInstance.businessKey
            ).then((response) => {
              this.appointmentApplyForm = response.data;
            });
          }

          if (this.processInstance.name === "师资评优评先") {
            getEvaluationPlanProcessInfo({
              id: this.processInstance.businessKey,
            }).then((response) => {
              this.evaluationPlanApplyForm = response.data;
            });
          }

          if (this.processInstance.name === "进修生报名" || this.processInstance.name === "进修生录取") {
            getApplicationInfo(this.processInstance.businessKey).then((response) => {
              const { getDictDataLabel, DICT_TYPE } = this;
              const { baseinfoVO, certificateInfoVO, registrationVO, unitInfoVO } = response.data;

              this.registrationForm = {
                name: baseinfoVO?.name,
                sex: getDictDataLabel(DICT_TYPE.SYSTEM_USER_SEX, baseinfoVO?.sex),
                highestAcademic: getDictDataLabel(DICT_TYPE.SYSTEM_USER_EDUCATION, baseinfoVO?.highestAcademic),
                positionalTitles: getDictDataLabel(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES, certificateInfoVO?.positionalTitles),
                seniority: baseinfoVO?.seniority,
                selectedUnit: unitInfoVO?.selectedUnit,
                projectName: "",
                recruitMonths: "",
                tuition: "",
                remarks: "",
              };

              getProject(registrationVO.recruitmentProjectId).then((response) => {
                this.registrationForm.projectName = response.data.projectName;
              });

              getProjectDetailed(registrationVO.recruitmentProjectDetailed).then((response) => {
                this.registrationForm.recruitMonths = response.data.recruitMonths;
                this.registrationForm.tuition = response.data.tuition;
                this.registrationForm.remarks = response.data.remarks;
              });
            });
          }
        }

        // 设置表单信息
        if (this.processInstance.processDefinition.formType === 10) {
          this.detailForm = {
            ...JSON.parse(this.processInstance.processDefinition.formConf),
            disabled: true, // 表单禁用
            formBtns: false, // 按钮隐藏
            fields: decodeFields(
              this.processInstance.processDefinition.formFields
            ),
          };
          // 设置表单的值
          this.detailForm.fields.forEach((item) => {
            const val = this.processInstance.formVariables[item.__vModel__];
            if (val) {
              item.__config__.defaultValue = val;
            }
          });
        }

        // 加载流程图
        getProcessDefinitionBpmnXML(
          this.processInstance.processDefinition.id
        ).then((response) => {
          this.bpmnXML = response.data;
        });
        // 加载活动列表
        getActivityList({
          processInstanceId: this.processInstance.id,
        }).then((response) => {
          this.activityList = response.data;
        });

        // 取消加载中
        this.processInstanceLoading = false;
      });

      // 获得流程任务列表（审批记录）
      this.tasksLoad = true;

      this.auditForms = [];
      getTaskListByProcessInstanceId(this.id).then((response) => {
        // 审批记录
        this.tasks = [];
        this.runningTasks = [];
        // 移除已取消的审批
        response.data.forEach((task) => {
          if (task.result !== 4) {
            this.tasks.push(task);
          }
        });
        // 排序，将未完成的排在前面，已完成的排在后面；
        this.tasks.sort((a, b) => {
          // 有已完成的情况，按照完成时间倒序
          if (a.endTime && b.endTime) {
            return b.endTime - a.endTime;
          } else if (a.endTime) {
            return 1;
          } else if (b.endTime) {
            return -1;
            // 都是未完成，按照创建时间倒序
          } else {
            return b.createTime - a.createTime;
          }
        });
        console.log("this.tasks====", this.tasks);

        // 需要审核的记录
        const userId = store.getters.userId;
        this.tasks.forEach((task) => {
          if (task.result !== 1) {
            // 只有待处理才需要
            return;
          }
          if (!task.assigneeUser || task.assigneeUser.id !== userId) {
            // 自己不是处理人
            return;
          }
          this.runningTasks.push({ ...task });
          this.auditForms.push({
            reason: "",
          });
        });
        console.log("runningTasks", this.runningTasks);

        console.log("this.runningTasks====", this.runningTasks);

        // 取消加载中
        this.tasksLoad = false;
      });
    },
    /** 处理选择流程的按钮操作 **/
    handleSelect(row) {
      // 设置选择的流程
      this.selectProcessInstance = row;

      // 流程表单
      if (row.formId) {
        // 设置对应的表单
        this.detailForm = {
          ...JSON.parse(row.formConf),
          fields: decodeFields(row.formFields),
        };
      } else if (row.formCustomCreatePath) {
        this.$router.push({ path: row.formCustomCreatePath });
        // 这里暂时无需加载流程图，因为跳出到另外个 Tab；
      }
    },
    getDateStar(ms) {
      return getDate(ms);
    },
    getTimelineItemIcon(item) {
      if (item.result === 1) {
        return "el-icon-time";
      }
      if (item.result === 2) {
        return "el-icon-check";
      }
      if (item.result === 3) {
        return "el-icon-close";
      }
      if (item.result === 4) {
        return "el-icon-remove-outline";
      }
      return "";
    },
    getTimelineItemType(item) {
      if (item.result === 1) {
        return "primary";
      }
      if (item.result === 2) {
        return "success";
      }
      if (item.result === 3) {
        return "danger";
      }
      if (item.result === 4) {
        return "info";
      }
      return "";
    },
    /** 处理审批通过和不通过的操作 */
    handleAudit(task, pass) {
      const index = this.runningTasks.indexOf(task);
      this.$refs["form" + index][0].validate((valid) => {
        if (!valid) {
          return;
        }
        this.auditLoading = true;
        const data = {
          id: task.id,
          reason: this.auditForms[index].reason,
        };
        if (pass) {
          approveTask(data).then((response) => {
            this.$modal.msgSuccess("审批通过成功！");
            this.auditLoading = false;
            this.getDetail(); // 获得最新详情
          });
        } else {
          rejectTask(data).then((response) => {
            this.$modal.msgSuccess("审批不通过成功！");
            this.auditLoading = false;
            this.getDetail(); // 获得最新详情
          });
        }
      });
    },
    /** 处理转派审批人 */
    handleUpdateAssignee(task) {
      // 设置表单
      this.resetUpdateAssigneeForm();
      this.updateAssignee.form.id = task.id;
      // 设置为打开
      this.updateAssignee.open = true;
    },
    /** 提交转派审批人 */
    submitUpdateAssigneeForm() {
      this.$refs["updateAssigneeForm"].validate((valid) => {
        if (!valid) {
          return;
        }
        updateTaskAssignee(this.updateAssignee.form).then((response) => {
          this.$modal.msgSuccess("转派任务成功！");
          this.updateAssignee.open = false;
          this.getDetail(); // 获得最新详情
        });
      });
    },
    /** 取消转派审批人 */
    cancelUpdateAssigneeForm() {
      this.updateAssignee.open = false;
      this.resetUpdateAssigneeForm();
    },
    /** 重置转派审批人 */
    resetUpdateAssigneeForm() {
      this.updateAssignee.form = {
        id: undefined,
        assigneeUserId: undefined,
      };
      this.resetForm("updateAssigneeForm");
    },
    /** 处理审批退回的操作 */
    handleDelegate(task) {
      this.$modal.msgError("暂不支持【委派】功能，可以使用【转派】替代！");
    },
    /** 处理审批退回的操作 */
    handleBack(task) {
      this.$modal.msgError("暂不支持【退回】功能！");
      // 可参考 http://blog.wya1.com/article/636697030/details/7296
      // const data = {
      //   id: task.id,
      //   assigneeUserId: 1
      // }
      // backTask(data).then(response => {
      //   this.$modal.msgSuccess("回退成功！");
      //   this.getDetail(); // 获得最新详情
      // });
    },
  },
};
</script>

<style lang="scss">
.my-process-designer {
  height: calc(100vh - 200px);
}

.box-card {
  width: 100%;
  margin-bottom: 20px;
}
</style>
