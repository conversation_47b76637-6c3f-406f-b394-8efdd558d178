<template>
  <el-dialog
    :title="`${isView ? '查看' : ''}科室审核`"
    :visible="visible"
    @close="$emit('update:visible', false)"
  >
    <div id="deptAudit-cont">
      <h3 class="summary-title">出科总结</h3>
      <el-form label-width="98px">
        <div style="display: flex; justify-content: space-between">
          <el-form-item class="quarter-item" label="姓名：">{{
            row.nickname
          }}</el-form-item>
          <el-form-item class="quarter-item" label="年级：">{{
            row.grade
          }}</el-form-item>
          <el-form-item class="quarter-item" label="学员类型：">
            <dict-tag
              :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
              :value="row.studentType"
            ></dict-tag>
          </el-form-item>
          <el-form-item class="quarter-item" label="培训专业：">{{
            row.majorName
          }}</el-form-item>
        </div>
        <el-form-item class="full-item" label="个人小结：">
          <div class="border-wrapper">{{ applyInfo.personalSummary }}</div>
        </el-form-item>
        <div style="display: flex; justify-content: space-between">
          <el-form-item class="name-item" label="学员签名：">
            <span class="underline">{{ applyInfo.studentUserNickName }}</span>
          </el-form-item>
          <el-form-item class="date-item" label="日期：">
            <span class="underline">{{ applyInfo.createTime }}</span>
          </el-form-item>
        </div>
        <el-form-item label-width="0px" :key="applyInfo.id">
          <template v-if="visible && applyInfo.recoScoreForm">
            <general-score-sheet
              v-if="formType === 'general_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
            ></general-score-sheet>
            <default-score-sheet
              v-if="formType === 'default_reco_score'"
              :check="isView"
              :score-form="applyInfo.recoScoreForm"
              @evaluate="totalityAppraiseStatus = $event"
            ></default-score-sheet>
            <second-score-sheet
              v-if="formType === 'second_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
            ></second-score-sheet>
            <third-score-sheet
              v-if="formType === 'third_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
            ></third-score-sheet>
            <fourth-score-sheet
              v-if="formType === 'fourth_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
            ></fourth-score-sheet>
            <fifth-score-sheet
              v-if="formType === 'fifth_reco_score'"
              :score-form="applyInfo.recoScoreForm"
              :check="isView"
            ></fifth-score-sheet>
          </template>
        </el-form-item>
        <el-form-item class="full-item" label="带教评价：">
          <div class="border-wrapper">{{ applyInfo.teacherAppraise }}</div>
        </el-form-item>
        <div style="display: flex; justify-content: space-between">
          <el-form-item class="name-item" label="带教签名：">
            <span class="underline">{{ applyInfo.teacherUserNickName }}</span>
          </el-form-item>
          <el-form-item class="date-item" label="日期：">
            <span class="underline">{{
              applyInfo.teacherAuditTime
                ? formatDate(applyInfo.teacherAuditTime)
                : ""
            }}</span>
          </el-form-item>
        </div>
        <el-form-item class="full-item" label="科室评价：">
          <div class="border-wrapper" v-if="isView">
            {{ applyInfo.deptAppraise }}
          </div>
          <el-input type="textarea" v-model="deptAppraise" v-else></el-input>
        </el-form-item>
        <el-form-item class="full-item">
          <span slot="label" style="line-height: 1.3; display: inline-block"
            >所在科室考核小组总体评价</span
          >
          <dict-tag
            v-if="isView"
            :type="DICT_TYPE.GRADUATION_TOTALITY_APPRAISE_STATUS"
            :value="applyInfo.totalityAppraiseStatus"
          ></dict-tag>
          <el-radio-group
            v-model="totalityAppraiseStatus"
            @change="handleAppraiseStatusChange"
            v-else
          >
            <el-radio
              v-for="dict in this.getDictDatas(
                DICT_TYPE.GRADUATION_TOTALITY_APPRAISE_STATUS
              )"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          class="full-item"
          label="不合格原因："
          v-if="
            totalityAppraiseStatus === '0' ||
            applyInfo.totalityAppraiseStatus === '0'
          "
        >
          <span v-if="isView">{{
            applyInfo.totalityAppraiseUnqualifiedReason
          }}</span>
          <el-input
            type="textarea"
            v-model="totalityAppraiseUnqualifiedReason"
            v-else
          ></el-input>
        </el-form-item>
        <div style="display: flex; justify-content: space-between">
          <el-form-item class="name-item" label="科室签名：" v-if="isView">
            <span class="underline">{{ applyInfo.deptUserNickName }}</span>
          </el-form-item>
          <el-form-item
            class="date-item"
            style="padding-bottom: 20px"
            label="日期："
            v-if="isView"
          >
            <span class="underline">{{
              applyInfo.deptAuditTime ? formatDate(applyInfo.deptAuditTime) : ""
            }}</span>
          </el-form-item>
        </div>
        <div style="display: flex; justify-content: space-between">
          <el-form-item
            class="name-item"
            label="专业基地管理员："
            v-if="isView && applyInfo.professionalBaseAdminUserNickName"
          >
            <span class="underline">
              {{ applyInfo.professionalBaseAdminUserNickName }}
            </span>
          </el-form-item>
        </div>
        <!-- <span style="clear: both;"></span> -->
      </el-form>
    </div>
    <span slot="footer" v-if="!isView">
      <el-button @click="cancelVisible">取消</el-button>
      <el-popover
        style="margin: 0 10px"
        placement="top"
        width="320"
        v-model="repulseVisible"
      >
        <el-input
          style="margin-bottom: 10px"
          type="textarea"
          placeholder="请输入退回修改的原因！"
          v-model="deptRepulseReason"
        ></el-input>
        <span>
          <el-button type="primary" size="mini" @click="sureRepulse"
            >确定</el-button
          >
          <el-button size="mini" @click="cancelRepulse">取消</el-button>
        </span>
        <el-button slot="reference" type="primary">退回修改</el-button>
      </el-popover>
      <el-button type="primary" @click="agreeGraduation">同意出科</el-button>
    </span>
    <span slot="footer" v-if="isView">
      <el-button type="primary" @click="handleDown">导出</el-button>
    </span>
  </el-dialog>
</template>

<script>
import dayjs from "dayjs";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import {
  deptAuditSuccess,
  deptAuditFail,
} from "@/api/rotation/graduationAudit";
import { getGraduationApply } from "@/api/rotation/graduationApply";
import GeneralScoreSheet from "../../graduationTeacherAudit/sheetGeneral";
import DefaultScoreSheet from "../../graduationTeacherAudit/sheetSzlgrm";
import SecondScoreSheet from "../../graduationTeacherAudit/sheetSecond";
import ThirdScoreSheet from "../../graduationTeacherAudit/sheetThird";
import FourthScoreSheet from "../../graduationTeacherAudit/sheetFourth";
import FifthScoreSheet from "../../graduationTeacherAudit/sheetFifth";

export default {
  components: {
    GeneralScoreSheet,
    DefaultScoreSheet,
    SecondScoreSheet,
    ThirdScoreSheet,
    FourthScoreSheet,
    FifthScoreSheet,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 申请信息
      applyInfo: {},
      // 科室评价
      deptAppraise: "",
      // 总体评价状态
      totalityAppraiseStatus: "1",
      // 不合格原因
      totalityAppraiseUnqualifiedReason: "",
      // 退回弹窗
      repulseVisible: false,
      // 退回原因
      deptRepulseReason: "",
      // 表单类型
      formType: "default_reco_score",
    };
  },
  methods: {
    /** 格式化日期 */
    formatDate(date) {
      return dayjs(date).format("YYYY-MM-DD");
    },
    /** 查看申请信息 */
    getApplyInfo() {
      getGraduationApply(this.row.graduationApplyId).then((res) => {
        this.applyInfo = res.data;
        this.formType =
          res.data.recoScoreForm?.rotationGraduationFormType ||
          "default_reco_score";
      });
    },
    /** 修改科室评价状态 */
    handleAppraiseStatusChange(value) {
      if (value === "1") {
        this.totalityAppraiseUnqualifiedReason = "";
      }
    },
    /** 取消审核 */
    cancelVisible() {
      this.deptAppraise = "";
      this.totalityAppraiseStatus = "1";
      this.totalityAppraiseUnqualifiedReason = "";
      this.$emit("update:visible", false);
      this.cancelRepulse();
    },
    /** 同意出科 */
    agreeGraduation() {
      if (!this.deptAppraise) {
        this.$message.warning("请输入科室评价!");
        return;
      }
      deptAuditSuccess({
        id: this.applyInfo.id,
        deptAppraise: this.deptAppraise,
        totalityAppraiseStatus: this.totalityAppraiseStatus,
        totalityAppraiseUnqualifiedReason:
          this.totalityAppraiseUnqualifiedReason,
      }).then(() => {
        this.$message.success("同意出科提交成功!");
        this.cancelVisible();
        this.$emit("update-list");
      });
    },
    /** 退回修改 */
    cancelRepulse() {
      this.deptRepulseReason = "";
      this.repulseVisible = false;
    },
    sureRepulse() {
      if (!this.deptRepulseReason) {
        this.$message.warning("请输入退回修改的原因!");
        return;
      }
      deptAuditFail({
        id: this.applyInfo.id,
        deptRepulseReason: this.deptRepulseReason,
      }).then(() => {
        this.$message.success("退回修改提交成功!");
        this.cancelVisible();
        this.$emit("update-list");
      });
    },

    handleDown() {
      this.exportPDF("deptAudit-cont", `${this.row.nickname}出科考核结果`);
    },

    exportPDF(tableId, fileName) {
      const table = document.getElementById(tableId);
      html2canvas(table).then((canvas) => {
        // debugger
        const contentWidth = canvas.width;
        const contentHeight = canvas.height;
        const pageHeight = (contentWidth / 592.28) * 841.89;
        let leftHeight = contentHeight;
        let position = 30;
        const imgWidth = 595.28 - 60;
        const imgHeight = ((592.28 - 60) / contentWidth) * contentHeight;
        const pageData = canvas.toDataURL("image/jpeg", 1.0);
        const pdf = new jsPDF("", "pt", "a4");
        if (leftHeight < pageHeight) {
          pdf.addImage(pageData, "JPEG", 30, 30, imgWidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            pdf.addImage(pageData, "JPEG", 30, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;

            if (leftHeight > 0) {
              pdf.addPage();
            }
          }
        }
        pdf.save(`${fileName}.pdf`);
      });
    },
  },
  watch: {
    visible(value) {
      if (value) {
        this.getApplyInfo();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.name-item {
  // float: left;
  width: 72%;
}

.date-item {
  // float: left;
  width: 28%;
}

.full-item {
  // float: left;
  width: 100%;
}

.quarter-item {
  // float: left;
  width: 25%;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-align: center;
}

.underline {
  text-decoration: underline;
  text-underline-offset: 5px;
  text-underline-color: #e1e1e1;
}

.border-wrapper {
  min-height: 50px;
  border: 1px solid #f1f1f1;
  padding: 3px 10px;
  white-space: pre-wrap;
}
</style>
