<template>
    <el-dialog 
      title="添加学员" 
      :visible="visible"
      width="900px" 
      v-dialogDrag 
      append-to-body
      @update:visible="$emit('update:visible', $event)"
    >
      <div class="add-student-table">
        <el-form :model="queryStudentParams" ref="queryStudentForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="学员姓名" prop="nickname">
            <el-input v-model="queryStudentParams.nickname" placeholder="请输入学员姓名" clearable @keyup.enter.native="handleStudentQuery" style="width: 120"/>
          </el-form-item>
          <el-form-item label="学员类型" prop="studentType">
            <el-select v-model="queryStudentParams.studentType" placeholder="请选择学员类型" filterable clearable size="small" @change="handleQueryStudentTypeChange" style="width: 120">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                        :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="培训专业" prop="major">
            <el-select v-model="queryStudentParams.major" placeholder="请选择培训专业" filterable clearable size="small" style="width: 120">
              <el-option v-for="item in simpleMajorList" :key="item.code" :label="item.name" :value="item.code"/>
            </el-select>
          </el-form-item>
          <el-form-item label="年级" prop="grade">
            <el-select v-model="queryStudentParams.grade" placeholder="请选择年级" filterable clearable size="small" style="width: 100">
              <el-option v-for="grade in studentGradeList" :key="grade" :label="grade" :value="grade"/>
            </el-select>
          </el-form-item>
          <el-form-item label="派送单位" prop="dispatchingUnit">
            <el-input v-model="queryStudentParams.dispatchingUnit" placeholder="请输入派送单位" clearable />
          </el-form-item>
          <el-form-item label="分组" prop="studentGroup">
              <el-input v-model="queryStudentParams.studentGroup" placeholder="请输入分组" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleStudentQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetStudentQuery">重置</el-button>           
          </el-form-item>
        </el-form>
        <div>
          <el-table v-loading="queryStudentLoading" :data="studentAddList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="学员姓名" width="120" align="center" prop="nickname" />
            <el-table-column label="学员类型" prop="studentType" align="center">
              <template v-slot="scope">
                <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType"></dict-tag>
              </template>
            </el-table-column>
            <el-table-column label="年级" align="center" prop="grade" />
            <el-table-column label="专业" align="center" prop="majorName" />
            <el-table-column label="派送单位" align="center" prop="dispatchingUnit" />
            <el-table-column label="分组" align="center" prop="studentGroup" />
          </el-table>

          <pagination v-show="studentTotal > 0" :total="studentTotal" :page.sync="queryStudentParams.pageNo" :limit.sync="queryStudentParams.pageSize"
                @pagination="() => getStudentList()"/>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: right; padding-top: 15px">
          <el-button type="primary" @click="submitAddStudents">确认添加</el-button>
          <el-button @click="() => $emit('update:visible', false)">取 消</el-button>
        </div>
      </div>
    </el-dialog>
  </template>

  <script>
  import { getNotJoinedStudentsPage, addUsers } from "@/api/rotation/hospitalTraining";
  import { getSimpleMajorList } from '@/api/system/major';
  import { getStudentGradeList } from "@/api/system/userStudent";

  export default {
    name: "AddStudentsDialog",
    props: {
        visible: {
          type: Boolean,
          default: false
        },
        curActive: {
          type: Object,
          default: {}
        }
    },
    data() {
      return {
        queryStudentLoading: false,
        studentTotal: 0,
        queryStudentParams: {
          pageNo: 1,
          pageSize: 10,
          nickname: '',
          studentType: '',
          major: '',
          grade: '',
          dispatchingUnit: '',
          studentGroup: ''
        },
        userIds: [],
        userTypes: [],
        studentAddList: [],
        simpleMajorList: [],
        studentGradeList: [],
      };
    },
    watch: {
      visible(value) {
        if (value) {
          getStudentGradeList().then(res => {
            this.studentGradeList = res.data;
          });
          this.resetStudentQuery();
        }
      }
    },
    created() {},
    methods: {
      handleStudentQuery(){
        this.queryStudentParams.pageNo = 1;
        this.getStudentList();
      },
      resetStudentQuery() {
        this.resetForm("queryStudentForm");
        this.handleStudentQuery();
      },

      getStudentList() {
        this.queryStudentLoading = true
        const params = {
          ...this.queryStudentParams,
          hospitalTrainingId: this.curActive.id
        }
        getNotJoinedStudentsPage(params).then(response => {
          this.studentAddList = response.data.list || [];
          this.queryStudentLoading = false;
          this.studentTotal = response.data.total;
        });
      },
      
      handleSelectionChange(val) {
        // console.log('handleSelectionChange=====', val)
        this.userIds = val.map(item => item.userId);
        this.userTypes = val.map(item => item.userType);
      },

      handleQueryStudentTypeChange(value) {
        this.queryStudentParams.major = null;
        this.simpleMajorList = [];
        getSimpleMajorList({ studentType: value }).then(res => {
          this.simpleMajorList = res.data;
        });
      },

      submitAddStudents() {
        if (this.userIds.length === 0) {
          return this.$modal.msgWarning("请选择学员！");
        }
        const params = {
          hospitalTrainingId: this.curActive.id,
          userIds: this.userIds,
          userType: this.userTypes[0]
        }
        addUsers(params).then(res => {
          this.$modal.msgSuccess("添加成功");
          this.$emit('update:visible', false)
          this.$emit('refresh')
        })
      }
    }
  };
  </script>

  <style lang="scss" scoped>
  
  </style>
