<template>
  <el-popover
    v-model="popoverVisible"
    width="180px"
    placement="bottom-start"
    popper-class="popover-no-padding">
    <el-input
      slot="reference"
      :value="value"
      class="picker-input"
      prefix-icon="el-icon-date"
      :placeholder="placeholder"
      :clearable="clearable"
      @clear="handleClear"
    ></el-input>
    <div class="panel-content">
      <div class="spinner">
        <el-scrollbar
          @mousemove.native="adjustSpinners"
          class="spinner-wrapper"
          view-class="spinner-list"
          tag="ul"
          ref="months">
          <li
            v-for="m in months"
            :class="['spinner-item', { active: m === curMonth }]"
            :key="m"
            @click="handleMonthClick(m)"
          >{{ m }}</li>
        </el-scrollbar>
        <el-scrollbar
          @mousemove.native="adjustSpinners"
          class="spinner-wrapper"
          view-class="spinner-list"
          tag="ul"
          ref="days">
          <li
            v-for="d in days"
            :class="['spinner-item', { active: d === curDay }]"
            :key="d"
            @click="handleDayClick(d)"
          >{{ d }}</li>
        </el-scrollbar>
      </div>
    </div>
    <div class="panel-footer">
      <button class="panel-btn cancel" @click="handleCancel">取消</button>
      <button class="panel-btn confirm" @click="handleConfirm">确定</button>
    </div>
  </el-popover>
</template>

<script>
function getNumArr(n) {
  const arr = [];
  for (let i = 1; i < n + 1; i++) {
    arr.push(i);
  }
  return arr;
}

export default {
  name: 'month-day-picker',
  props: {
    placeholder: String,
    clearable: Boolean,
    value: String,
  },
  data() {
    return {
      popoverVisible: false,
      months: getNumArr(12),
      curMonth: 1,
      curDay: 1,
    }
  },
  computed: {
    days() {
      switch (this.curMonth) {
        case 1:
        case 3:
        case 5:
        case 7:
        case 8:
        case 10:
        case 12:
          return getNumArr(31);
        case 2:
          return getNumArr(29);
        case 4:
        case 6:
        case 9:
        case 11:
          return getNumArr(30);
        default:
          return [];
      }
    },
  },
  methods: {
    handleMonthClick(m) {
      this.curMonth = m;
      const lastDay = this.days[this.days.length - 1];
      if (this.curDay > lastDay) {
        this.curDay = lastDay
      }
      this.adjustSpinners();
    },
    handleDayClick(d) {
      this.curDay = d;
      this.adjustSpinners();
    },
    // 调整滚动高度匹配值
    adjustSpinners() {
      this.adjustSpinner('months', this.curMonth);
      this.adjustSpinner('days', this.curDay);
    },
    adjustSpinner(type, value) {
      const el = this.$refs[type].wrap;
      if (el) {
        el.scrollTop = Math.max(0, (value - 1) * this.typeItemHeight(type));
      }
    },
    typeItemHeight(type) {
      return this.$refs[type].$el.querySelector('li').offsetHeight;
    },
    // 监听滚动，设置当前值
    bindScrollEvent() {
      const bindFunction = (type) => {
        this.$refs[type].wrap.onscroll = (e) => {
          this.handleScroll(type, e);
        };
      };
      bindFunction("months");
      bindFunction("days");
    },
    handleScroll(type) {
      const scrollTop = this.$refs[type].wrap.scrollTop;
      const scrollBarHeight = this.$refs[type].$el.offsetHeight;
      const typeItemHeight = this.typeItemHeight(type);
      const value = Math.min(
        Math.round(scrollTop / typeItemHeight) + 1,
        (type === 'months' ? 12 : this.days[this.days.length - 1])
      );
      switch (type) {
        case "months":
          this.curMonth = value;
          break;
        case "days":
          this.curDay = value;
          break;
      }
    },
    handleClear() {
      this.curMonth = 1;
      this.curDay = 1;
      this.$emit("input", "");
      this.$emit("change", "");
    },
    handleCancel() {
      this.popoverVisible = false;
    },
    handleConfirm() {
      const paddingZero = (value) => value > 9 ? `${value}` : `0${value}`;
      const value = `${paddingZero(this.curMonth)}-${paddingZero(this.curDay)}`;
      this.$emit("input", value);
      this.$emit("change", value);
      this.popoverVisible = false;
    },
  },
  watch: {
    popoverVisible(value) {
      if (value) {
        const [m, d] = (this.value || "").split("-");
        this.curMonth = Number(m || "01");
        this.curDay = Number(d || "01");
        this.$nextTick(() => this.adjustSpinners());
      }
    }
  },
  mounted() {
    this.$nextTick(() => this.bindScrollEvent());
  },
}
</script>

<style scoped lang="scss">
.picker-input {
  width: 200px;
}

.panel-content {
  font-size: 0;
  position: relative;
  overflow: hidden;
  &::after, &::before {
    content: "";
    top: 50%;
    position: absolute;
    margin-top: -15px;
    height: 32px;
    z-index: -1;
    left: 12%;
    right: 12%;
    box-sizing: border-box;
    padding-top: 6px;
    text-align: left;
    border-top: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
  }
}

.spinner {
  width: 100%;
  white-space: nowrap;
}

.spinner-wrapper {
  display: inline-block;
  width: 50%;
  height: 190px;
  vertical-align: top;
  position: relative;
  overflow: hidden;

  ::v-deep .spinner-list {
    padding: 0;
    list-style: none;
    text-align: center;
    &::after, &::before {
      content: "";
      display: block;
      width: 100%;
      height: 80px;
    }
  }

  .spinner-item {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: #606266;
    margin: 0;
    list-style: none;
    &.active {
      color: #303133;
      font-weight: 700;
    }
    &:hover:not(.active) {
      background: #f5f7fa;
      cursor: pointer;
    }
  }
}

.panel-footer {
  border-top: 1px solid #e4e4e4;
  padding: 4px;
  height: 36px;
  line-height: 25px;
  text-align: right;
  box-sizing: border-box;

  .panel-btn {
    border: none;
    line-height: 28px;
    padding: 0 5px;
    margin: 0 5px;
    cursor: pointer;
    background-color: transparent;
    outline: none;
    font-size: 12px;
    color: #303133;
    &.confirm {
      font-weight: 800;
      color: #409eff;
    }
  }
}
</style>

<style>
.popover-no-padding {
  padding: 0;
}
</style>
