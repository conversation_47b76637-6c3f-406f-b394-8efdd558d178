<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="带教老师" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入带教老师" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item> -->

      <el-form-item label="教研室" prop="staffRoomValue" label-width="80px">
          <el-select v-model="queryParams.staffRoomValue" filterable clearable placeholder="请选择教研室">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.STAFF_ROOM)"
            :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
      </el-form-item>

      <el-form-item label="科室" prop="departmentId">
        <el-select v-model="queryParams.departmentId" filterable clearable placeholder="请选择医院科室" size="small">
          <el-option
              v-for="item in departmentOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="统计时间" prop="dates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.dates"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:dept-quality:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="科室" align="center" prop="departmentName" />
      <el-table-column label="带教学员数量" align="center" prop="stuCnt" />
      <el-table-column label="学员汇总评价" align="center" prop="studentAppraiseTeacher" />
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
  </div>
</template>

<script>
import { getDepartmentSimpleList } from "@/api/system/department";
import { getDeptQualityPage, exportDeptQuality } from "@/api/rotation/deptQuality";

export default {
  name: "DeptQuality",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员入科列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 50,
        // nickname: '',
        staffRoomValue: '',
        departmentId: '',
        dates: []
      },
      // 表单参数
      departmentOptions: [],
      currentRow: {}
    };
  },
  created() {
    this.getList();
    this.getDepartment();
  },
methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getDeptQualityPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then(res => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有数据项?').then(() => {
          this.exportLoading = true;
          return exportDeptQuality(params);
        }).then(response => {
          this.$download.excel(response, '科室教学质量.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
<style lang="scss">
</style>
