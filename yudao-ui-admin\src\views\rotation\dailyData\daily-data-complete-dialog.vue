<template>
  <el-dialog title="数据完成情况" width="1000px" :visible="visible" @close="$emit('close')">
    <el-form inline>
      <el-form-item class="mr" label="学员姓名">{{ studentInfo.nickname }}</el-form-item>
      <el-form-item class="mr" label="轮转科室">{{ studentInfo.rotationDepartmentName }}</el-form-item>
      <el-form-item class="mr" label="轮转数据项">
        <el-select v-model="auditQuery.rotationItem" size="small" @change="handleRotationItemChange">
          <el-option label="全部" value=""></el-option>
          <el-option v-for="dict in getDictDatas(DICT_TYPE.ROTAION_ITEM)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item class="mr" label="完成数/要求数">
        {{ studentInfo.auditSuccessCnt || 0 }}/{{ studentInfo.cases || 0 }}
        <el-tooltip content="完成数为审核通过的数据" placement="top" style="position: relative; top: -5px;">
          <i class="el-icon-info"></i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select style="width: 100px" v-model="auditQuery.auditStatus" size="small"  @change="queryAuditDailyData">
          <el-option value="" label="全部"></el-option>
          <el-option value="1" label="通过"></el-option>
          <el-option value="2" label="不通过"></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <el-table :data="auditData">
      <el-table-column label="轮转数据项" prop="rotaionItem" align="center">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.ROTAION_ITEM" :value="scope.row.rotaionItem" />
        </template>
      </el-table-column>
      <el-table-column label="病人姓名" prop="patientName" align="center"></el-table-column>
      <el-table-column label="病历号" prop="medicalCode" align="center"></el-table-column>
      <el-table-column label="填写日期" prop="createTime" align="center" width="150px">
        <template v-slot="scope">
          {{ scope.row.createTime && new Date(scope.row.createTime).toLocaleString() }}
        </template>
      </el-table-column>
      <el-table-column label="审核状态" prop="auditStatus" align="center">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.ROTATION_AUDIT_STATUS" :value="scope.row.auditStatus" />
        </template>
      </el-table-column>
      <el-table-column label="审核时间" prop="auditTime" align="center" width="150px">
        <template v-slot="scope">
          {{ scope.row.auditTime && new Date(scope.row.auditTime).toLocaleString() }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="scope">
          <el-link type="primary" @click="viewDetail(scope.row)">查看详情</el-link>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="auditTotal > 0" :total="auditTotal" :page.sync="auditQuery.pageNo" :limit.sync="auditQuery.pageSize"
                @pagination="queryAuditDailyData"/>
  </el-dialog>
</template>

<script>
import { getAuditDailyData, getDailyDataStudentInfo } from '@/api/rotation/dailyData'

export default {
  name: 'daily-data-complete-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    scheduleDetailsId: Number | String,
  },
  data() {
    return {
      auditQuery: {
        scheduleDetailsId: "",
        rotationItem: "",
        auditStatus: "",
        pageNo: 1,
        pageSize: 10
      },
      studentInfo: {},
      auditData: [],
      auditTotal: 0,
    }
  },
  methods: {
    queryStudentInfo() {
      getDailyDataStudentInfo(this.auditQuery).then(res => this.studentInfo = res?.data || {});
    },
    queryAuditDailyData() {
      getAuditDailyData(this.auditQuery).then(res => {
        this.auditData = res.data.list;
        this.auditTotal = res.data.total;
      });
    },
    handleRotationItemChange() {
      this.queryStudentInfo();
      this.queryAuditDailyData();
    },
    viewDetail(row) {
      this.$emit("show-detail", row);
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.auditQuery.scheduleDetailsId = this.scheduleDetailsId;
        this.queryStudentInfo();
        this.queryAuditDailyData();
      }
    },
  },
}
</script>

<style scoped lang="scss">

</style>
