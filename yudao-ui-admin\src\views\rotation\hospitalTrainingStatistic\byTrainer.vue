<template>
  <div>
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="培训人" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入培训人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:teaching-active-statistics:export']"
          >导出</el-button
        >
      </el-col> -->
      <!-- <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar> -->
    </el-row>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      show-summary
      :summary-method="summaryMethod"
    >
      <el-table-column label="培训人" align="center" prop="nickname" fixed />
      <el-table-column label="用户名" align="center" prop="username" fixed />
      <template v-for="dict in activeTypeList">
        <el-table-column
          :label="`${dict.label}`"
          align="center"
          :prop="`training_type_effective_${dict.value}`"
        >
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click="viewDetail(scope.row, dict.value)"
              >{{ scope.row["training_type_effective_" + dict.value] }}</el-link
            >
          </template>
        </el-table-column>
      </template>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="
        '个人培训记录详情页-' +
        currentRow.nickname +
        '(' +
        currentRow.username +
        ')'
      "
      :visible.sync="openDetail"
      width="900px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <el-table v-loading="detailLoading" :data="detailList">
          <el-table-column label="培训名称" align="center" prop="name" />
          <el-table-column
            label="培训级别"
            align="center"
            prop="trainingLevel"
            width="130"
          >
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.TEACHING_TRAINING_LEVEL"
                :value="scope.row.trainingLevel"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="培训类型"
            align="center"
            prop="trainingType"
            width="120"
          >
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.TEACHING_TRAINING_TYPE"
                :value="scope.row.trainingType"
              />
            </template>
          </el-table-column>
          <el-table-column label="培训人" align="center" prop="nickname" />
          <el-table-column label="开展时间" align="center" width="260">
            <template v-slot="scope">
              <span>{{ scope.row.startTime }} ~ {{ scope.row.endTime }}</span>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="detailTotal > 0"
          :total="detailTotal"
          :page.sync="queryDetailParams.pageNo"
          :limit.sync="queryDetailParams.pageSize"
          @pagination="getList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDepartmentSimpleList } from "@/api/system/department";
import {
  getHospitaltrainingPagePerson,
  exportTeachingActiveExcelPerson,
} from "@/api/rotation/hospitalTrainingStatistic";
import { getHospitalTrainingPage } from "@/api/rotation/hospitalTraining";
import { DICT_TYPE, getDictDatas } from "@/utils/dict";

export default {
  name: "ByTrainer",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员入科列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 50,
        nickname: "",
      },
      // 表单参数
      departmentOptions: [],
      currentRow: {},

      openDetail: false,
      detailLoading: false,
      detailList: [],
      detailTotal: 0,
      queryDetailParams: {
        pageNo: 1,
        pageSize: 10,
      },
      activeTypeVal: "",
    };
  },
  created() {
    this.getList();
    this.getDepartment();
  },
  computed: {
    activeTypeList() {
      const vals = ["3", "13", "20"];
      return getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE).filter((item) =>
        vals.includes(item.value)
      );
    },
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getHospitaltrainingPagePerson(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportTeachingActiveExcelPerson(params);
        })
        .then((response) => {
          this.$download.excel(response, "教学活动按培训人统计.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    /** 合计计算方法 */
    summaryMethod({ columns, data }) {
      return columns.map((column) => {
        if (column.property === "nickname") {
          return "合计";
        } else if (column.property.startsWith("training_type_effective_")) {
          return data.reduce((prev, cur) => prev + cur[column.property], 0);
        } else {
          return null;
        }
      });
    },

    viewDetail(item, activeType) {
      this.currentRow = item;
      this.activeTypeVal = activeType;
      this.getDetailList(() => {
        this.openDetail = true;
      });
    },

    getDetailList(callback) {
      this.detailLoading = true;
      const params = {
        ...this.queryDetailParams,
        nickname: this.currentRow.nickname,
        courtTrainingUserId: this.currentRow.id,
        trainingType: this.activeTypeVal,
      };
      getHospitalTrainingPage(params).then((response) => {
        this.detailList = response.data.list;
        this.detailTotal = response.data.total;
        this.detailLoading = false;
        if (callback) {
          callback();
        }
      });
    },
  },
};
</script>
<style lang="scss"></style>
