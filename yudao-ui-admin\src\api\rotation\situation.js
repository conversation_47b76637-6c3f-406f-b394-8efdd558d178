import request from "@/utils/request";

// 获得排班分页
export function getSituationPage(query) {
  return request({
    url: "/rotation/situation/page",
    method: "get",
    params: query,
    headers: { component: "rotation/situation/index" },
  });
}

// 获得排班分页
export function getCurrentUserRotationDepts(query) {
  return request({
    url: "/system/permission/list-current-user-rotation-depts",
    method: "get",
    params: query,
  });
}

// 导出教学活动 Excel
export function exportSituationExcel(query) {
  return request({
    url: "/rotation/situation/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
    headers: { component: "rotation/situation/index" },
  });
}

// 下载用户导入模板
export function importTemplate() {
  return request({
    url: "/exam/answer-result/get-import-graduation-assessment",
    method: "get",
    responseType: "blob",
  });
}

// 下载用户导入模板
export function importGraduationTemplate() {
  return request({
    url: "/rotation/graduation-assessment-result/get-import-graduation-assessment",
    method: "get",
    responseType: "blob",
  });
}

// 获得学员入科带教集合
export function getTeacherList(id) {
  return request({
    url: "/rotation/student-enrollment/list-teacher?scheduleDetailsId=" + id,
    method: "get",
  });
}

// 确认专业基地管理员
export function confirmProfessionalUser(query = {}, data = {}) {
  return request({
    url: "/rotation/situation/confirm-professional-base-admin-user",
    method: "put",
    params: query,
    data: data,
  });
}

// 批量确认专业基地管理员
export function confirmProfessionalUsers(data = {}) {
  return request({
    url: "/rotation/situation/confirm-professional-base-admin-users",
    method: "put",
    data: data,
  });
}
