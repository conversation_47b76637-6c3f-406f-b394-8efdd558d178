import request from '@/utils/request'

// 获得形成性评价带教端待评价学员分页
export function getFormativeEvaluationResultStudentList(query) {
  return request({
    url: '/rotation/formative-evaluation-result/studentPage',
    method: 'get',
    params: query
  })
}

// 获得形成性评价表单列表-根据学员类型和科室类型
export function getFormativeEvaluationResultFormList(query) {
  return request({
    url: '/rotation/formative-evaluation-result/formList',
    method: 'get',
    params: query
  })
}

// 创建形成性评价
export function createFormativeEvaluationResult(data) {
  return request({
    url: '/rotation/formative-evaluation-result/create',
    method: 'post',
    data
  })
}

// 获得形成性评价列表-根据排班详情编号
export function getFormativeEvaluationResultList(scheduleDetailsId, isAll) {
  return request({
    url: '/rotation/formative-evaluation-result/resultList',
    method: 'get',
    params: { scheduleDetailsId, isAll }
  })
}

// 更新形成性评价
export function updateFormativeEvaluationResult(data) {
  return request({
    url: '/rotation/formative-evaluation-result/update',
    method: 'put',
    data
  })
}
