import request from '@/utils/request'

// 创建招录项目
export function createProject(data) {
  return request({
    url: '/recruitment/project/create',
    method: 'post',
    data: data
  })
}

// 更新招录项目
export function updateProject(data) {
  return request({
    url: '/recruitment/project/update',
    method: 'put',
    data: data
  })
}

// 删除招录项目
export function deleteProject(id) {
  return request({
    url: '/recruitment/project/delete?id=' + id,
    method: 'delete'
  })
}

// 获得招录项目
export function getProject(id) {
  return request({
    url: '/recruitment/project/get?id=' + id,
    method: 'get'
  })
}

// 获得招录项目分页
export function getProjectPage(query) {
  return request({
    url: '/recruitment/project/page',
    method: 'get',
    params: query
  })
}

// 导出招录项目 Excel
export function exportProjectExcel(query) {
  return request({
    url: '/recruitment/project/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得招录项目集合
export function getProjectList(query) {
  return request({
    url: '/recruitment/project/get-projects',
    method: 'get',
    params: query
  })
}
