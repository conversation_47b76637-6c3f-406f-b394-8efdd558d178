<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="请假类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择请假类型"
          clearable
        >
          <el-option
            v-for="dict in leaveTypeDictData"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="结果" prop="result">
        <el-select
          v-model="queryParams.result"
          placeholder="请选择流结果"
          clearable
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="原因" prop="reason">
        <el-input
          v-model="queryParams.reason"
          placeholder="请输入原因"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          v-hasPermi="['bpm:oa-leave:create']"
          @click="handleAdd"
        >
          发起请假
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="申请编号" align="center" prop="id" width="80" />
      <el-table-column label="状态" align="center" prop="result">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT"
            :value="scope.row.result"
          />
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime">
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime">
        <template slot-scope="scope">
          <span>{{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请假类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.BPM_OA_LEAVE_TYPE"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="原因"
        align="center"
        prop="reason"
        show-overflow-tooltip
      />
      <el-table-column
        label="申请时间"
        align="center"
        prop="applyTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleCancel(scope.row)"
            v-hasPermi="['bpm:oa-leave:create']"
            v-if="scope.row.result === 1"
            >取消请假</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['bpm:oa-leave:query']"
            >详情</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleProcessDetail(scope.row)"
            >审批进度</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        class="oa-creat-dialog-form"
      >
        <el-form-item label="请假类型" prop="type">
          <el-select
            v-if="opt === 'add'"
            v-model="form.type"
            placeholder="请选择"
            style="width: 100%"
            @change="leaveTypeChange"
          >
            <el-option
              v-for="dict in leaveTypeList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <dict-tag
            v-if="opt === 'detail'"
            :type="DICT_TYPE.BPM_OA_LEAVE_TYPE"
            :value="form.type"
          />
        </el-form-item>
        <el-form-item label="请假时间" prop="timeArr">
          <el-date-picker
            v-if="opt === 'add'"
            v-model="form.timeArr"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            popper-class="oa-creat-dialog-dataPicker"
            @change="onDatePickerChange"
          >
          </el-date-picker>
          <span v-if="opt === 'detail'">
            {{ parseTime(form.startTime, "{y}-{m}-{d}") }} ~
            {{ parseTime(form.endTime, "{y}-{m}-{d}") }}</span
          >
        </el-form-item>
        <el-form-item label="请假时长" prop="day">
          <el-input v-if="opt === 'add'" v-model="form.day">
            <template slot="prepend">共</template>
            <template slot="append">天</template>
          </el-input>
          <span v-if="opt === 'detail'"> 共 {{ form.day }} 天</span>
        </el-form-item>
        <!-- <el-form-item v-if="opt === 'add'" label="轮转科室" prop="type">
          <el-select v-model="form.rotationDepartmentId" placeholder="请选择" style="width: 100%;" disabled>
            <el-option v-for="dept in userRotationDepts" :key="parseInt(dept.id)" :label="dept.name" :value="parseInt(dept.id)" />
          </el-select>
        </el-form-item> -->

        <el-form-item label="原因" prop="reason">
          <el-input
            v-if="opt === 'add'"
            type="textarea"
            :rows="3"
            v-model="form.reason"
            placeholder="请输入原因"
            style="width: 100%"
          />
          <span v-if="opt === 'detail'"> {{ form.reason }} </span>
        </el-form-item>
        <el-form-item label="附件上传" prop="pictures">
          <FileUpload
            v-model="form.pictures"
            :limit="999"
            :fileSize="50"
            :disabled="opt === 'detail'"
            :fileType="null"
          />
        </el-form-item>
      </el-form>
      <div v-if="opt === 'add'" slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitForm"
          >提 交</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
      <div v-if="opt === 'detail'" slot="footer" class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getLeavePage,
  createLeave,
  getLeave,
  getLeaveLoginUserConfig,
} from "@/api/bpm/leave";
import { getDictDatas, DICT_TYPE } from "@/utils/dict";
import { cancelProcessInstance } from "@/api/bpm/processInstance";
import { getUserRotationAllSimple } from "@/api/system/department";
import { getScheduleByRotationDates } from "@/api/rotation/schedule";
import ImageUpload from "@/components/ImageUpload";
import FileUpload from "@/components/FileUploadInfo";
import { getConfigKey } from "@/api/infra/config";

export default {
  name: "Leave",
  components: {
    ImageUpload,
    FileUpload,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      submitLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 请假申请列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        result: null,
        type: null,
        reason: null,
        createTime: [],
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      form: {},
      opt: "add",
      userRotationDepts: [],
      scheduleDetail: [],
      leaveTip: "",
      leaveConfig: null,

      leaveTypeDictData: getDictDatas(DICT_TYPE.BPM_OA_LEAVE_TYPE),
      leaveResultData: getDictDatas(DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT),
      typeDictData: getDictDatas(DICT_TYPE.BPM_OA_LEAVE_TYPE),

      pickerOptions: {},
      start: "",
      end: "",
      rules: {
        type: [
          { required: true, message: "请选择请假类型", trigger: "change" },
        ],
        timeArr: [
          {
            type: "array",
            required: true,
            message: "请选择请假时间",
            trigger: "change",
          },
        ],
        day: [{ required: true, message: "请输入请假时长", trigger: "change" }],
        rotationDepartmentId: [
          { required: true, message: "请选择轮转科室", trigger: "change" },
        ],
        reason: [{ required: true, message: "请填写原因", trigger: "blur" }],
        pictures: [{ required: true, message: "请上传附件", trigger: "blur" }],
        scheduleDetailsId: [
          { required: true, message: this.leaveTip, trigger: "change" },
        ],
      },
    };
  },
  computed: {
    leaveTypeList() {
      const list = this.getDictDatas(DICT_TYPE.BPM_OA_LEAVE_TYPE);
      return this.leaveConfig?.leaveType
        ? list.filter(
            (item) =>
              this.leaveConfig.leaveType.split(",").indexOf(item.value) > -1
          )
        : list;
    },
  },
  created() {
    this.getList();
    getUserRotationAllSimple().then((res) => {
      this.userRotationDepts = res.data;
    });
    getConfigKey("oa_leave_tip").then((res) => {
      this.leaveTip = res.data || "您选择的请假时间暂无排班计划，请重新选择";
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getLeavePage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    leaveTypeChange(val) {
      if (val === "4") {
        this.rules.pictures[0].required = false;
      } else {
        this.rules.pictures[0].required = true;
      }
    },
    onDatePickerChange(val) {
      console.log("onDatePickerChange====", val);
      if (!val || val.length === 0) {
        return;
      }
      const diffDays = this.getDiffDay(val[0], val[1]);
      this.form.day = diffDays + 1;
      const params = {
        rotationBeginTime: val[0],
        rotationEndTime: val[1],
      };
      getScheduleByRotationDates(params).then((res) => {
        const { code, data } = res;
        if (code == 0 && data) {
          if (data.length === 0) {
            this.$message.warning(this.leaveTip);
          } else if (data.length === 1) {
            this.form.rotationDepartmentId = data[0].rotationDepartmentId;
            this.form.scheduleDetailsId = data[0].id;
          } else {
            let str = "";
            data.forEach((item) => {
              str =
                str +
                item.rotationDepartmentName +
                "(" +
                item.rotationBeginTime +
                "~" +
                item.rotationEndTime +
                ")" +
                ";";
            });
            this.$message.warning(
              `请重新选择时间段，您选的时间段跨了以下几个轮转计划：${str}`
            );
          }
        }
      });
    },
    getDiffDay(date_1, date_2) {
      // 计算两个日期之间的差值
      let totalDays, diffDate;
      let myDate_1 = Date.parse(date_1);
      let myDate_2 = Date.parse(date_2);
      // 将两个日期都转换为毫秒格式，然后做差
      diffDate = Math.abs(myDate_1 - myDate_2); // 取相差毫秒数的绝对值

      totalDays = Math.floor(diffDate / (1000 * 3600 * 24)); // 向下取整

      return totalDays; // 相差的天数
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      getLeaveLoginUserConfig().then((res) => {
        this.leaveConfig = res.data;
        const notice = res.data?.leaveNotice;
        const add = () => {
          this.reset();
          this.open = true;
          this.title = "发起请假";
          this.opt = "add";
        };
        if (notice) {
          this.$confirm(
            `<pre style="white-space: pre-wrap">${notice}</pre>`,
            "请假须知",
            {
              dangerouslyUseHTMLString: true,
              customClass: "oa-notice-width",
            }
          ).then(() => add());
        } else {
          add();
        }
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        rotationDepartmentId: undefined,
        scheduleDetailsId: undefined,
        startTime: undefined,
        endTime: undefined,
        type: undefined,
        reason: undefined,
        timeArr: undefined,
        day: undefined,
        pictures: undefined,
      };
      this.resetForm("form");
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.opt = "detail";
      this.open = true;
      this.title = "查看OA请假";
      getLeave(row.id).then((response) => {
        this.form = response.data;
        this.form.pictures = this.form.pictures
          ? JSON.parse(this.form.pictures)
          : undefined;
      });
    },
    /** 查看审批进度的操作 */
    handleProcessDetail(row) {
      this.$router.push({
        path: "/bpm/process-instance/detail",
        query: { id: row.processInstanceId },
      });
    },
    /** 取消请假 */
    handleCancel(row) {
      const id = row.processInstanceId;
      this.$prompt("请输入取消原因？", "取消流程", {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /^[\s\S]*.*[^\s][\s\S]*$/, // 判断非空，且非空格
        inputErrorMessage: "取消原因不能为空",
      })
        .then(({ value }) => {
          return cancelProcessInstance(id, value);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("取消成功");
        });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        if (!this.form.scheduleDetailsId) {
          this.$message.warning(this.leaveTip);
          return;
        }
        this.submitLoading = true;
        const params = {
          day: this.form.day,
          endTime: this.form.timeArr[1],
          pictures: this.form.pictures
            ? JSON.stringify(this.form.pictures)
            : "",
          reason: this.form.reason,
          rotationDepartmentId: this.form.rotationDepartmentId,
          scheduleDetailsId: this.form.scheduleDetailsId,
          startTime: this.form.timeArr[0],
          type: this.form.type,
        };

        // 添加的提交
        createLeave(params)
          .then((response) => {
            this.$modal.msgSuccess("发起成功");
            this.open = false;
            this.submitLoading = false;
            this.getList();
            // this.$tab.closeOpenPage({ path: "/bpm/oa/leave" });
          })
          .catch((err) => {
            this.submitLoading = false;
          });
      });
    },
  },
};
</script>

<style lang="scss">
.oa-creat-dialog-dataPicker {
  .el-picker-panel__sidebar {
    width: 200px !important;
  }
  .el-picker-panel__sidebar + .el-picker-panel__body {
    margin-left: 210px !important;
  }
}

.oa-notice-width {
  width: 600px;
}
</style>
