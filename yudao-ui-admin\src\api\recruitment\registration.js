import request from '@/utils/request'

// 创建招录报名
export function createRegistration(data) {
  return request({
    url: '/recruitment/registration/create',
    method: 'post',
    data: data
  })
}

// 更新招录报名
export function updateRegistration(data) {
  return request({
    url: '/recruitment/registration/update',
    method: 'put',
    data: data
  })
}

// 删除招录报名
export function deleteRegistration(id) {
  return request({
    url: '/recruitment/registration/delete?id=' + id,
    method: 'delete'
  })
}

// 获得招录报名
export function getRegistration(id) {
  return request({
    url: '/recruitment/registration/get?id=' + id,
    method: 'get'
  })
}

// 获得招录报名分页
export function getRegistrationPage(query) {
  return request({
    url: '/recruitment/registration/page',
    method: 'get',
    params: query
  })
}

// 导出招录报名 Excel
export function exportRegistrationExcel(query) {
  return request({
    url: '/recruitment/registration/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得招录报名列表
export function getRegistrationList(studentType) {
  return request({
    url: '/recruitment/registration/get-registrations',
    method: 'get',
    params: { studentType }
  })
}

// 获得招录项目集合根据计划id
export function getProjectsByPlanId(planId) {
  return request({
    url: '/recruitment/project/get-projects-by-plan-id',
    method: 'get',
    params: { planId }
  })
}

// 获得计划招录项目明细集合
export function getPlanProjectDetailedList(planId, projectId) {
  return request({
    url: '/recruitment/project-detailed/get-plan-project-detaileds',
    method: 'get',
    params: { planId, projectId }
  })
}

// 获得申请信息
export function getApplicationInfo(recruitmentRegistrationId) {
  return request({
    url: '/recruitment/registration/get-application-info',
    method: 'get',
    params: { recruitmentRegistrationId }
  })
}

// 暂存申请信息
export function stagingApplicationInfo(data) {
  return request({
    url: '/recruitment/registration/staging-application-info',
    method: 'post',
    data
  })
}

// 下一步申请表
export function nextStepApplicationInfo(data) {
  return request({
    url: '/recruitment/registration/next-step-application-form',
    method: 'post',
    data
  })
}

// 保存申请表报名（下一步）
export function saveApplicationForm(data) {
  return request({
    url: '/recruitment/registration/save-application-form',
    method: 'post',
    data
  })
}

// 获得审批信息
export function getExamineInfo(recruitmentRegistrationId) {
  return request({
    url: '/recruitment/registration/get-examine-info',
    method: 'get',
    params: { recruitmentRegistrationId }
  })
}

// 下载申请表模板
export function downloadApplicationTemplate() {
  return request({
    url: '/recruitment/registration/download-application-form',
    method: 'get',
    responseType: 'blob'
  })
}

// 根据计划获得招录报名
export function getApplicationInfoByPlanId(planId) {
  return request({
    url: '/recruitment/registration/get-application-info-by-planId',
    method: 'get',
    params: { planId }
  })
}

// 获得住院医师申请信息
export function getResidencyApplicationInfo(recruitmentRegistrationId) {
  return request({
    url: '/recruitment/resident-physician-registration/get-application-info',
    method: 'get',
    params: { recruitmentRegistrationId }
  })
}

// 暂存住院医师申请信息
export function stagingResidencyApplicationInfo(data) {
  return request({
    url: '/recruitment/resident-physician-registration/staging-application-info',
    method: 'post',
    data
  })
}

// 保存住院医师申请表报名
export function saveResidencyApplicationForm(data) {
  return request({
    url: '/recruitment/resident-physician-registration/save-application-form',
    method: 'post',
    data
  })
}

// 根据计划获得住院医师招录报名
export function getResidencyApplicationInfoByPlanId(planId) {
  return request({
    url: '/recruitment/resident-physician-registration/get-application-info-by-planId',
    method: 'get',
    params: { planId }
  })
}

// 获得住院医师招录报名
export function getResidencyRegistration(id) {
  return request({
    url: '/recruitment/resident-physician-registration/get?id=' + id,
    method: 'get'
  })
}

// 获得住院医师招录审批信息
export function getResidencyExamineInfo(recruitmentRegistrationId) {
  return request({
    url: '/recruitment/resident-physician-registration/get-examine-info',
    method: 'get',
    params: { recruitmentRegistrationId }
  })
}

// 撤回报名
export function revokeRegistration(recruitmentRegistrationId) {
  return request({
    url: '/recruitment/resident-physician-registration/revoke-registration',
    method: 'put',
    params: { recruitmentRegistrationId }
  })
}

// 获得招录报名部分基本信息
export function getResidencyPartBaseInfo(id) {
  return request({
    url: '/recruitment/registration/get-part-baseinfo?id=' + id,
    method: 'get'
  })
}
