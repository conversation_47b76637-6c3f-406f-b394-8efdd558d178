<template>
  <div class="app-container registration-examine-view">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入姓名查询" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="报名项目" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入报名项目查询" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="姓名" align="center" prop="nickname" min-width="100" fixed></el-table-column>
      <el-table-column label="性别" align="center" prop="sex" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="学历" align="center" prop="highestAcademic" width="120">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_EDUCATION" :value="scope.row.highestAcademic" />
        </template>
      </el-table-column>
      <el-table-column label="职称" align="center" prop="positionalTitles" min-width="120">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES" :value="scope.row.positionalTitles" />
        </template>
      </el-table-column>
      <el-table-column label="工龄" align="center" prop="seniority" width="80" />
      <el-table-column label="报名项目" align="center" prop="projectName" min-width="180" />
      <el-table-column label="时长(月)" align="center" prop="recruitMonths" width="80" />
      <el-table-column label="是否报名" align="center" prop="reported" width="120">
        <template slot-scope="scope">{{ scope.row.reported ? "已报名" : "未报名" }}</template>
      </el-table-column>
      <el-table-column label="报名审核" align="center" prop="reportResult" width="120">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="toFlow(scope.row.reportProcessInstanceId)">
            {{ {"1": "待审核", "2": "审核通过", "3": "审核不通过", "4": "退回修改"}[scope.row.reportResult] }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="录取审核" align="center" prop="enrollResult" width="120">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="toFlow(scope.row.enrollProcessInstanceId)">
            {{ getDictDataLabel(DICT_TYPE.RECRUITMENT_RESULT, scope.row.enrollResult) }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="通知书下发" align="center" prop="noticeSendStatus" width="120">
        <template slot-scope="scope">
          {{ getDictDataLabel(DICT_TYPE.REGISTRATION_NOTICE_SEND_STATUS, scope.row.noticeSendStatus) }}
        </template>
      </el-table-column>
      <el-table-column label="报到确认" align="center" prop="checkStatus" width="120">
        <template slot-scope="scope">
          {{ getDictDataLabel(DICT_TYPE.REGISTRATION_CHECK_STATUS, scope.row.checkStatus) }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
  </div>
</template>

<script>
import { getRegistrationExamineViewPage } from '@/api/recruitment/registrationexamineview'

export default {
  name: 'registration-examine-view',
  data() {
    return {
      queryParams: {
        nickname: "",
        projectName: "",
        pageNo: 1,
        pageSize: 10,
      },
      showSearch: true,
      loading: false,
      list: [],
      total: 0,
    };
  },
  methods: {
    // 查询表格
    getList() {
      this.loading = true;
      this.list = []
      getRegistrationExamineViewPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList()
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 查看审核流程
    toFlow(id) {
      this.$router.push('/bpm/process-instance/detail?id=' + id);
    },
  },
  created() {
    this.getList();
  },
}
</script>

<style lang="scss">
.registration-examine-view {
  .el-button {
    font-size: 14px;
  }
}
</style>
