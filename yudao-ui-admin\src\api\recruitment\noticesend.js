import request from '@/utils/request'

// 获得通知书发放分页
export function getNoticesendPage(query) {
    return request({
      url: '/recruitment/noticesend/page',
      method: 'get',
      params: query
    })
}

// 获得通知书发放
export function getNoticeContent(recruitmentRegistrationId) {
    return request({
      url: '/recruitment/noticesend/get-notice-content?recruitmentRegistrationId=' + recruitmentRegistrationId,
      method: 'get'
    })
}

// 提交
export function sendNotice(data) {
    return request({
      url: '/recruitment/noticesend/send-notice',
      method: 'put',
      data: data
    })
}

// 获得招录报名
export function getRegistration(query) {
  return request({
    url: '/recruitment/registration/get',
    method: 'get',
    params: query
  })
}

// 批量发放通知书
export function batchSendNotice(data) {
  return request({
    url: '/recruitment/noticesend/batch-send-notice',
    method: 'put',
    data: data
  })
}
