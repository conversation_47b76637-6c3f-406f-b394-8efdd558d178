<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="通知分类" prop="noticeType">
        <el-select
          v-model="queryParams.noticeType"
          placeholder="请选择通知分类"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_NOTICE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布人" prop="creatorName">
        <el-input
          v-model="queryParams.creatorName"
          placeholder="请输入发布人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布时间" prop="createTimes">
        <el-date-picker
          v-model="queryParams.createTimes"
          type="daterange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          clearable
          size="small"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:notice-info:create']"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column
        label="通知分类"
        align="center"
        prop="noticeType"
        width="150"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_NOTICE_TYPE"
            :value="scope.row.noticeType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="阅读情况"
        align="center"
        prop="readInfo"
        width="150"
      >
        <template v-slot="scope">
          <el-link type="primary" @click="showReadInfo(scope.row)">{{
            scope.row.readInfo
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="发布日期"
        align="center"
        prop="createTime"
        width="180"
      ></el-table-column>
      <el-table-column
        label="发布人"
        align="center"
        prop="creatorName"
        width="150"
      ></el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="150"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rotation:notice-info:update']"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:notice-info:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="980px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="通知分类" prop="noticeType">
          <el-select v-model="form.noticeType" placeholder="请选择通知分类">
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_NOTICE_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="接收对象"
          prop="selectedNumber"
          v-if="form.noticeType === '1'"
        >
          <el-popover
            v-model="popoverVisible"
            placement="bottom-start"
            width="852"
            trigger="manual"
          >
            <div class="receive-items" v-if="popoverVisible">
              <el-form-item
                label="接收对象"
                prop="receiveObjects"
                style="margin-right: 50%"
              >
                <el-select
                  :value="
                    form.receiveObjects ? form.receiveObjects.split(',') : []
                  "
                  multiple
                  clearable
                  placeholder="请选择接收对象"
                  @change="form.receiveObjects = $event.join(',')"
                >
                  <el-option
                    v-if="dict.value !== '0' && dict.value !== '3'"
                    v-for="dict in this.getDictDatas(
                      DICT_TYPE.SYSTEM_USER_TYPE
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="角色选择"
                prop="roleIds"
                v-if="
                  form.receiveObjects && form.receiveObjects.indexOf('1') > -1
                "
              >
                <el-select
                  :value="form.roleIds ? form.roleIds.split(',') : []"
                  multiple
                  clearable
                  filterable
                  collapse-tags
                  placeholder="请选择角色"
                  @change="form.roleIds = $event.join(',')"
                >
                  <el-option
                    v-for="item in roleOptions"
                    :key="parseInt(item.id)"
                    :label="item.name"
                    :value="item.id.toString()"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="科室选择"
                prop="workerDepartmentIds"
                v-if="
                  form.receiveObjects && form.receiveObjects.indexOf('1') > -1
                "
              >
                <el-select
                  :value="
                    form.workerDepartmentIds
                      ? form.workerDepartmentIds.split(',')
                      : []
                  "
                  multiple
                  filterable
                  clearable
                  collapse-tags
                  placeholder="请选择职工科室"
                  @change="form.workerDepartmentIds = $event.join(',')"
                >
                  <el-option
                    v-for="item in departmentOptions"
                    :key="parseInt(item.id)"
                    :label="item.name"
                    :value="item.id.toString()"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="学员类型"
                prop="studentTypes"
                v-if="
                  form.receiveObjects && form.receiveObjects.indexOf('2') > -1
                "
              >
                <el-select
                  :value="form.studentTypes ? form.studentTypes.split(',') : []"
                  multiple
                  filterable
                  collapse-tags
                  placeholder="请选择学员类型"
                  @change="form.studentTypes = $event.join(',')"
                >
                  <el-option
                    v-for="dict in this.getDictDatas(
                      DICT_TYPE.SYSTEM_STUDENT_TYPE
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="学员专业"
                prop="studentMajors"
                v-if="
                  form.receiveObjects && form.receiveObjects.indexOf('2') > -1
                "
              >
                <el-select
                  :value="
                    form.studentMajors ? form.studentMajors.split(',') : []
                  "
                  multiple
                  filterable
                  clearable
                  collapse-tags
                  placeholder="请选择学员专业"
                  @change="form.studentMajors = $event.join(',')"
                >
                  <el-option
                    v-for="item in majorList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  >
                    <span style="float: left">{{ item.name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">
                      <dict-tag
                        :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                        :value="item.studentType"
                      />
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="学员年级"
                prop="grades"
                v-if="
                  form.receiveObjects && form.receiveObjects.indexOf('2') > -1
                "
              >
                <el-select
                  :value="form.grades ? form.grades.split(',') : []"
                  multiple
                  filterable
                  clearable
                  collapse-tags
                  placeholder="请选择学员年级"
                  @change="form.grades = $event.join(',')"
                >
                  <el-option
                    v-for="grade in studentGradeList"
                    :key="grade"
                    :label="grade"
                    :value="grade"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="派送单位"
                prop="dispatchingUnit"
                v-if="
                  form.receiveObjects && form.receiveObjects.indexOf('2') > -1
                "
              >
                <el-input
                  v-model="form.dispatchingUnit"
                  placeholder="请输入派送单位"
                />
              </el-form-item>
              <el-form-item
                label="学员分组"
                prop="studentGroup"
                v-if="
                  form.receiveObjects && form.receiveObjects.indexOf('2') > -1
                "
              >
                <el-input
                  v-model="form.studentGroup"
                  placeholder="请输入学员分组"
                />
              </el-form-item>
            </div>
            <div class="receive-popover-btns">
              <el-button type="primary" @click="sureReceiveObject"
                >确定</el-button
              >
              <el-button type="cancel" @click="clearReceiveObject"
                >清除</el-button
              >
              <el-button type="cancel" @click="cancelReceiveObject"
                >取消</el-button
              >
            </div>
            <div
              class="selected-number"
              slot="reference"
              @click="handleReferenceClick"
            >
              {{ form.selectedNumber || 0 }}人
              <i class="el-icon-arrow-down"></i>
            </div>
          </el-popover>
          <p class="receive-object-tips">
            注意：若不选接收对象，则默认发给全部用户
          </p>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-form-item label="上传附件" prop="files">
          <file-upload-info
            v-model="form.files"
            :file-size="20"
            :limit="50"
            :file-type="['doc', 'xls', 'ppt', 'pptx', 'txt', 'pdf']"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="通知阅读情况查阅"
      :visible.sync="receiverOpen"
      width="980px"
      v-dialogDrag
      append-to-body
    >
      <el-form inline>
        <el-form-item label="阅读状态">
          <el-select
            v-model="receiverQueryParams.readed"
            clearable
            @change="queryReceivePage"
          >
            <el-option label="已阅" :value="true"></el-option>
            <el-option label="未阅" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户类型">
          <el-select
            v-model="receiverQueryParams.userType"
            clearable
            @change="queryReceivePage"
          >
            <el-option label="职工" :value="1"></el-option>
            <el-option label="学员" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            placeholder="接收人用户姓名"
            clearable
            v-model="receiverQueryParams.receiveNickName"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="queryReceivePage">查询</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="receiverList">
        <el-table-column
          label="用户名"
          prop="receiveNickName"
          align="center"
        ></el-table-column>
        <el-table-column label="用户类型" prop="userType" align="center">
          <template v-slot="scope">{{
            scope.row.userType === "1"
              ? "职工"
              : scope.row.userType === "2"
              ? "学员"
              : "其他"
          }}</template>
        </el-table-column>
        <el-table-column label="阅读状态" prop="readed" align="center">
          <template v-slot="scope">{{
            scope.row.readed ? "已阅" : "未阅"
          }}</template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="receiverTotal > 0"
        :total="receiverTotal"
        :page.sync="receiverQueryParams.pageNo"
        :limit.sync="receiverQueryParams.pageSize"
        @pagination="queryReceivePage"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  createNoticeInfo,
  updateNoticeInfo,
  deleteNoticeInfo,
  getNoticeInfo,
  getNoticeInfoPage,
  getNoticeReceiverPage,
} from "@/api/rotation/noticeInfo";
import FileUploadInfo from "@/components/FileUploadInfo";
import Editor from "@/components/Editor";
import { getDepartmentSimpleList } from "@/api/system/department";
import { getStudentGradeList } from "@/api/system/userStudent";
import { listSimpleRoles } from "@/api/system/role";
import { getSimpleMajorList } from "@/api/system/major";
import { getTrainingSelectedNumber } from "@/api/rotation/hospitalTraining";

export default {
  name: "NoticeInfo",
  components: {
    FileUploadInfo,
    Editor,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 通知列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        title: null,
        noticeType: null,
        creatorName: "",
        createTimes: [],
      },
      // 接收对象弹出层
      popoverVisible: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        noticeType: [
          { required: true, message: "通知分类不能为空", trigger: "change" },
        ],
        content: [{ required: true, message: "内容不能为空", trigger: "blur" }],
        receiveObjects: [
          { required: true, message: "接收对象不能为空", trigger: "change" },
        ],
        roleIds: [
          { required: true, message: "角色选择不能为空", trigger: "change" },
        ],
        studentTypes: [
          { required: true, message: "学员类型不能为空", trigger: "change" },
        ],
      },
      // 角色列表
      roleOptions: [],
      // 科室列表
      departmentOptions: [],
      // 学员专业列表
      majorList: [],
      // 学员年级列表
      studentGradeList: [],
      // 阅读情况弹窗
      receiverOpen: false,
      // 阅读情况查询参数
      receiverQueryParams: {
        noticeId: "",
        pageNo: 1,
        pageSize: 10,
        readed: true,
        userType: "",
        receiveNickName: "",
      },
      receiverList: [],
      receiverTotal: 0,
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    getStudentGradeList().then((res) => {
      this.studentGradeList = res.data;
    });
    this.getMajorList();
    listSimpleRoles().then((response) => {
      let list = [];
      response.data.forEach((item) => {
        if (item.code !== "super_admin" && item.code !== "student") {
          list.push(item);
        }
      });
      this.roleOptions = list;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getNoticeInfoPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        title: undefined,
        noticeType: "1",
        receiveObjects: undefined,
        roleIds: undefined,
        workerDepartmentIds: undefined,
        studentTypes: undefined,
        studentMajors: undefined,
        grades: undefined,
        dispatchingUnit: undefined,
        studentGroup: undefined,
        selectedNumber: 0,
        content: undefined,
        files: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加通知";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getNoticeInfo(id).then((response) => {
        this.form = response.data;
        this.form.files = response.data.files
          ? JSON.parse(response.data.files)
          : [];
        this.open = true;
        this.title = "修改通知";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const form = {
          ...this.form,
          files: this.form.files ? JSON.stringify(this.form.files) : undefined,
        };
        // 修改的提交
        if (form.id != null) {
          updateNoticeInfo(form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createNoticeInfo(form).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除通知编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteNoticeInfo(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 获得科室列表 */
    getDepartment() {
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    /** 获得专业列表 */
    getMajorList() {
      getSimpleMajorList().then((res) => {
        this.majorList = res.data;
      });
    },
    /** 接收对象点击 */
    handleReferenceClick() {
      if (!this.popoverVisible) {
        this.popoverVisible = true;
        this.copyForm = JSON.stringify(this.form);
      }
    },
    /** 确定接收对象 */
    sureReceiveObject() {
      let flag = false; // 验证失败标记
      this.$refs.form.validateField(
        ["receiveObjects", "roleIds", "studentTypes"],
        (errMessage) => {
          if (errMessage) {
            flag = true;
          }
        }
      );
      if (!flag) {
        getTrainingSelectedNumber({
          trainingObjects: this.form.receiveObjects,
          roleIds: this.form.roleIds,
          workerDepartmentIds: this.form.workerDepartmentIds,
          studentTypes: this.form.studentTypes,
          studentMajors: this.form.studentMajors,
          grades: this.form.grades,
          dispatchingUnit: this.form.dispatchingUnit,
          studentGroup: this.form.studentGroup,
        }).then((res) => {
          this.form.selectedNumber = res.data || 0;
          this.popoverVisible = false;
        });
      }
    },
    /** 清除接收对象 */
    clearReceiveObject() {
      this.form.receiveObjects = "";
      this.form.roleIds = "";
      this.form.workerDepartmentIds = "";
      this.form.studentTypes = "";
      this.form.studentMajors = "";
      this.form.grades = "";
      this.form.dispatchingUnit = "";
      this.form.studentGroup = "";
      this.form.selectedNumber = 0;
      this.popoverVisible = false;
    },
    /** 取消接收对象 */
    cancelReceiveObject() {
      this.popoverVisible = false;
      this.form = JSON.parse(this.copyForm);
    },
    /** 通知详情弹窗 */
    showReadInfo(row) {
      this.receiverQueryParams = {
        noticeId: row.id,
        pageNo: 1,
        pageSize: 10,
        readed: true,
        userType: "",
        receiveNickName: "",
      };
      this.receiverOpen = true;
      this.queryReceivePage();
    },
    /** 获取通知详情数据 */
    queryReceivePage() {
      getNoticeReceiverPage(this.receiverQueryParams).then((res) => {
        this.receiverList = res.data.list;
        this.receiverTotal = res.data.total;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.selected-number {
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  height: 36px;
  line-height: 36px;
  outline: none;
  padding: 0 15px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.receive-object-tips {
  color: #f56c6c;
  line-height: 1;
  margin: 10px 0 0 0;
}

.receive-items {
  padding: 5px;
  ::v-deep .el-form-item {
    display: inline-block;
    width: 50%;
    margin-right: 0;
  }
  ::v-deep .el-input,
  ::v-deep .el-select {
    width: 260px;
  }
}

.receive-popover-btns {
  text-align: center;
}
</style>
