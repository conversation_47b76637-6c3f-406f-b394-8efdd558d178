import request from "@/utils/request";

// 创建请假申请
export function createLeave(data) {
  return request({
    url: "/bpm/oa/leave/create",
    method: "post",
    data: data,
  });
}

// 获得请假申请
export function getLeave(id) {
  return request({
    url: "/bpm/oa/leave/get?id=" + id,
    method: "get",
  });
}

// 获得请假申请分页
export function getLeavePage(query) {
  return request({
    url: "/bpm/oa/leave/page",
    method: "get",
    params: query,
  });
}

// 请假记录查看
export function getLeaveManagePage(query) {
  return request({
    url: "/bpm/oa/leave/manage-page",
    method: "get",
    params: query,
    headers: { component: "bpm/oa/leave/record" },
  });
}

// 获得登录用户请假配置
export function getLeaveLoginUserConfig() {
  return request({
    url: "/bpm/oa/leave/get-config-loginuser",
    method: "get",
  });
}

// 管理员添加请假记录
export function manageCreateLeave(data) {
  return request({
    url: "/bpm/oa/leave/manage-create",
    method: "post",
    data: data,
  });
}

// 获取学生请假请假配置
export function getLeaveStudentUserConfig(studentUserId) {
  return request({
    url: "/bpm/oa/leave/get-config",
    method: "get",
    params: { studentUserId },
  });
}

// 导出请假 Excel
export function exportLeaveRecordExcel(query) {
  return request({
    url: "/bpm/oa/leave/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
