<template>
    <el-table
        :data="list"
        class="department-person-list"
        border
        size="small"
        style="width: 100%"
    >
        <el-table-column label="姓名" align="center" prop="name">
            <template slot-scope="scope">
                <el-input
                    v-model="scope.row.name"
                    size="mini"
                    placeholder="请输入姓名"
                    @change="(val) => rowCellChange(val, scope.$index, 'name')"
                />
            </template>
        </el-table-column>
        <el-table-column label="联系方式" align="center" prop="tel">
            <template slot-scope="scope">
                <el-input
                    v-model="scope.row.tel"
                    size="mini"
                    placeholder="请输入联系方式"
                    @change="(val) => rowCellChange(val, scope.$index, 'tel')"
                />
            </template>
        </el-table-column>
        <el-table-column label="职务" align="center" prop="post">
            <template slot-scope="scope">
                <el-select 
                    v-model="scope.row.post" 
                    placeholder="请选择职务" filterable clearable size="small"
                    @change="(val) => rowCellChange(val, scope.$index, 'post')"
                >
                    <el-option v-for="dict in postOptions"
                        :key="dict.value" :label="dict.label" :value="dict.value"
                    />
                </el-select>
            </template>
        </el-table-column>
        <el-table-column label="职称" align="center" prop="positionalTitle">
            <template slot-scope="scope">
                <el-select 
                    v-model="scope.row.positionalTitle" 
                    placeholder="请选择职称" filterable clearable size="small"
                    @change="(val) => rowCellChange(val, scope.$index, 'positionalTitle')"
                >
                    <el-option v-for="dict in positionalOptions"
                        :key="dict.value" :label="dict.label" :value="dict.value"
                    />
                </el-select>
            </template>
        </el-table-column>
        <el-table-column align="center">
            <template slot="header" slot-scope="scope">
                <div class="action-cell" @click="addRowHanler()">
                    <span>操作</span>
                    <i class="el-icon-circle-plus-outline"></i>
                </div>
            </template>
            <template slot-scope="scope">
                <i class="el-icon-delete" @click="delRowHanler(scope.$index)"></i>
            </template>
        </el-table-column>
    </el-table>
</template>
  
<script>
  
export default {
    props: {
        formData: {
            type: Object
        }
    },
    data() {
      return {
        postOptions: this.getDictDatas(this.DICT_TYPE.SYSTEM_USER_POST),
        positionalOptions: this.getDictDatas(this.DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)
      };
    },
    computed: {
      list() {
        return this.formData.departmentWorkers
      },
    },
    created() {},
    methods: {
        addRowHanler() {
            const list = this.list || []
            list.push({
                name: '',
                tel: '',
                post: '',
                positionalTitle: ''
            })
            this.$emit("change", list)
        },
        delRowHanler(index) {
            let list = this.list
            list.splice(index, 1)
            this.$emit("change", list)
        },
        rowCellChange(val, index, field) {
            let list = this.list
            list[index][field] = val
            this.$emit("change", list)
        }

    }
  };
</script>
<style scoped lang="scss">
  .department-person-list{
    .action-cell{
        cursor: pointer;
    }
  }
  
</style>
  