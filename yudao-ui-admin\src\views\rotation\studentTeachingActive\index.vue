<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="活动名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入活动名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动类型" prop="activeType">
        <el-select
          v-model="queryParams.activeType"
          placeholder="请选择活动类型"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训科室" prop="departmentName">
        <el-select
          v-model="queryParams.departmentName"
          filterable
          clearable
          placeholder="请选择培训科室"
          size="small"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                      :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item> -->
      <el-form-item label="培训人" prop="speakerUsername">
        <el-input
          v-model="queryParams.speakerUsername"
          placeholder="请输入培训人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开展时间" prop="developDate">
        <el-date-picker
          clearable
          v-model="queryParams.developDate"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="选择开展时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:teaching-active:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="活动名称"
        align="center"
        prop="name"
        width="150"
      />
      <el-table-column label="活动类型" align="center" prop="activeType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_ACTIVE_TYPE"
            :value="scope.row.activeType"
          />
        </template>
      </el-table-column>
      <el-table-column label="培训科室" align="center" prop="departmentName" />
      <el-table-column label="培训人" align="center" prop="speakerUsername" />
      <el-table-column
        label="开始时间"
        align="center"
        prop="startTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="签到时间"
        align="center"
        prop="scanningTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.scanningTime
              ? parseTime(scope.row.scanningTime)
              : "暂未签到"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="综合评价" align="center" prop="score" width="140">
        <template slot-scope="scope">
          <el-rate
            v-if="scope.row.score"
            v-model="scope.row.score"
            disabled
            show-score
            text-color="#ff9900"
            :max="5"
            score-template="{value}"
          >
          </el-rate>
          <span v-else>暂未评价</span>
        </template>
      </el-table-column>
      <el-table-column label="考核得分" align="center" prop="examineScore">
        <template slot-scope="scope">
          <span>{{
            scope.row.examineScore === null
              ? "暂未考试"
              : scope.row.examineScore
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="250"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row)"
          >
            查看详情
          </el-button>
          <el-button
            v-if="scope.row.scanningTime && !scope.row.score"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEditAppraise(scope.row)"
          >
            进入评价
          </el-button>
          <el-button
            v-if="scope.row.scanningTime && scope.row.score"
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handleViewAppraise(scope.row)"
          >
            查看评价
          </el-button>
          <el-button
            v-if="
              scope.row.scanningTime &&
              scope.row.paperId &&
              !scope.row.answerResultId
            "
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleExam(scope.row)"
          >
            进入考试
          </el-button>
          <el-button
            v-if="scope.row.scanningTime && scope.row.answerResultId"
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleExamView(scope.row)"
          >
            查看考试
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="110px"
        :disabled="true"
      >
        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="教学活动名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入教学活动名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="活动类型" prop="activeType">
              <el-select
                v-model="form.activeType"
                filterable
                placeholder="请选择活动类型"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.ROTATION_ACTIVE_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
            <el-form-item
              label="培训科室"
              prop="departmentId"
              label-width="80px"
            >
              <el-select
                v-model="form.departmentId"
                filterable
                placeholder="请选择培训科室"
                style="width: 100%"
              >
                <el-option
                  v-for="item in departmentOptions"
                  :key="parseInt(item.id)"
                  :label="item.name"
                  :value="parseInt(item.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="培训人" prop="speakerUserId">
              <el-select
                v-model="form.speakerUserId"
                filterable
                placeholder="请选择培训人"
              >
                <el-option
                  v-for="user in userWorkerOptions"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
            <el-form-item label="培训时间" prop="timeValue" label-width="80px">
              <el-date-picker
                style="width: 100%"
                v-model="form.timeValue"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="timeValueChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="开展方式" prop="developWay">
              <el-select
                v-model="form.developWay"
                filterable
                placeholder="请选择开展方式"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.ROTATION_DEVELOP_WAY
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
            <el-form-item
              v-if="form.developWay == 2"
              label="参与方式"
              prop="joinWay"
              label-width="80px"
            >
              <el-input
                v-model="form.joinWay"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="通知对象" prop="studentTypes">
              <el-select
                v-model="form.studentTypes"
                multiple
                filterable
                placeholder="请选择通知对象"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_STUDENT_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="是否考核" prop="isExamine">
              <el-radio-group v-model="form.isExamine" style="margin-right: 20px;">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
          </el-col>
        </el-row> -->

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="活动地点" prop="adress">
              <el-input
                v-model="form.adress"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="活动课件">
              <FileUpload
                v-model="form.coursewares"
                :limit="999"
                :fileSize="50"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="submitForm">确 定</el-button> -->
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>

    <AppraiseDialog
      v-if="formData"
      :title="appraiseDialogTitle"
      :open="appraiseDialogOpen"
      :data="formData"
      :curRow="curRow"
      :disabled="appraiseDisabled"
      @setOpen="setAppraiseDialogOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import AppraiseDialog from "./appraiseDialog";
import { getDepartmentSimpleList } from "@/api/system/department";
import { getUserWorkerSimpleList } from "@/api/system/userWorker";
import {
  getAppraiseResult,
  getAppraiseForm,
} from "@/api/rotation/appraiseActive";
import {
  createTeachingActive,
  updateTeachingActive,
  deleteTeachingActive,
  getTeachingActive,
  getTeachingActivePage,
  exportTeachingActiveExcel,
  getStudentsList,
  confirmJoin,
  revokeJoin,
} from "@/api/rotation/studentTeachingActive";
import {
  validateAuthenticationCode,
  getPaperPureConfig,
} from "@/api/exam/paperConfig";
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "StudentTeachingActive",
  components: {
    FileUpload,
    ImageUpload,
    AppraiseDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      studentDetailLoading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 教学活动列表
      list: [],
      studentDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openPics: false,
      openStudentDetail: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        activeType: null,
        departmentName: null,
        speakerUsername: null,
        developDate: null,
        // startTime: [],
        // endTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "教学活动名称不能为空", trigger: "blur" },
        ],
        activeType: [
          { required: true, message: "活动类型不能为空", trigger: "change" },
        ],
        departmentId: [
          { required: true, message: "培训科室不能为空", trigger: "change" },
        ],
        speakerUserId: [
          { required: true, message: "培训人不能为空", trigger: "blur" },
        ],
        studentTypes: [
          {
            type: "array",
            required: true,
            message: "学员类型集合,逗号分隔不能为空",
            trigger: "change",
          },
        ],
        timeValue: [
          { required: true, message: "请选择开展时间", trigger: "change" },
        ],
        developWay: [
          { required: true, message: "开展方式不能为空", trigger: "change" },
        ],
        joinWay: [
          { required: true, message: "参与方式不能为空", trigger: "blur" },
        ],
        isExamine: [
          { required: true, message: "是否考核不能为空", trigger: "change" },
        ],
      },
      departmentOptions: [],
      userWorkerOptions: [],
      formData: null,
      appraiseDialogTitle: "",
      appraiseDialogOpen: false,
      curRow: null,
      appraiseDisabled: false,
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    this.getUserworkData();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getTeachingActivePage(this.queryParams).then((response) => {
        const list = response.data.list;
        list.forEach((item) => {
          const _score = (item.score / item.appraiseActiveScore) * 5;
          item.score = _score;
        });
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    getUserworkData() {
      getUserWorkerSimpleList().then((res) => {
        this.userWorkerOptions = [];
        this.userWorkerOptions.push(...res.data);
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelSubmitPics() {
      this.openPics = false;
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        activeType: undefined,
        departmentId: undefined,
        speakerUserId: undefined,
        studentTypes: [],
        timeValue: [],
        startTime: undefined,
        endTime: undefined,
        developWay: undefined,
        joinWay: undefined,
        isExamine: false,
        examineId: undefined,
        adress: undefined,
        coursewares: undefined,
        pictures: undefined,
      };
      this.resetForm("form");
    },
    timeValueChange(values) {
      this.form.startTime = undefined;
      this.form.endTime = undefined;
      if (values) {
        this.form.startTime = values[0];
        this.form.endTime = values[1];
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getTeachingActive(id).then((response) => {
        this.form = response.data;
        this.form.studentTypes = this.form.studentTypes.split(",");
        this.form.timeValue = [this.form.startTime, this.form.endTime];
        this.form.coursewares = this.form.coursewares
          ? JSON.parse(this.form.coursewares)
          : [];

        this.open = true;
        this.title = "查看教学活动";
      });
    },
    handleViewAppraise(row) {
      this.appraiseDisabled = true;
      const params = {
        id: row.appraiseActiveResultId,
      };
      getAppraiseResult(params).then((response) => {
        this.formData = response.data;
        this.appraiseDialogOpen = true;
        this.appraiseDialogTitle = `查看评价-${row.name}`;
      });
    },
    handleEditAppraise(row) {
      this.curRow = row;
      this.appraiseDisabled = false;
      const params = {
        appraiseActiveType: "1",
        activeType: row.activeType,
      };
      getAppraiseForm(params).then((response) => {
        this.formData = response.data;
        this.appraiseDialogOpen = true;
        this.appraiseDialogTitle = `正在评价-${row.name}`;
      });
    },
    setAppraiseDialogOpen(flag) {
      this.appraiseDialogOpen = flag;
      this.formData = null;
      this.curRow = null;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = { ...this.form };
        delete params.timeValue;
        params.studentTypes = params.studentTypes.join(",");
        params.coursewares = JSON.stringify(params.coursewares);
        // 修改的提交
        if (this.form.id != null) {
          updateTeachingActive(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createTeachingActive(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    submitPics() {
      const params = { ...this.form };
      delete params.timeValue;
      updateTeachingActive(params).then((response) => {
        this.$modal.msgSuccess("保存成功");
        this.openPics = false;
        this.getList();
      });
    },
    handleExam(row) {
      getPaperPureConfig(row.paperId).then((res) => {
        const jump = (code = "") => {
          const url = this.$router.resolve({
            path: "/onlineExam",
            query: {
              examObjectId: row.teachingActiveStudentId,
              paperId: row.paperId,
              code,
            },
          }).href;
          window.open(url, "_blank");
        };
        if (res.data.isAuthentication) {
          this.$prompt("请输入考试码", "校验确认", {
            inputPattern: /^\d{6}$/,
            inputErrorMessage: "考试码为6位数字",
          }).then(({ value }) => {
            validateAuthenticationCode({
              paperId: row.paperId,
              code: value,
            }).then((res) => {
              if (res.data) {
                jump(value);
              }
            });
          });
        } else {
          jump();
        }
      });
    },
    handleExamView(row) {
      const url = this.$router.resolve({
        path: "/examResult",
        query: { id: row.answerResultId },
      }).href;
      window.open(url, "_blank");
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除教学活动编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteTeachingActive(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有教学活动数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportTeachingActiveExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "教学活动.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    viewStudentDetail(row) {
      if (!row.attendance) {
        return;
      }
      this.getStudentList(row.id, () => {
        this.openStudentDetail = true;
      });
    },
    getStudentList(id, callback) {
      const params = {
        teachingActiveId: id,
      };
      this.studentDetailLoading = true;
      getStudentsList(params).then((response) => {
        this.studentDetailList = response.data || [];
        if (callback) {
          callback();
        }
        this.studentDetailLoading = false;
      });
    },
    handleJoin(row) {
      const params = {
        teachingActiveId: row.teachingActiveId,
        userId: row.userId,
      };
      confirmJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getStudentList(row.teachingActiveId);
      });
    },
    handleRevoke(row) {
      const params = {
        teachingActiveId: row.teachingActiveId,
        userId: row.userId,
      };
      revokeJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getStudentList(row.teachingActiveId);
      });
    },
  },
};
</script>
