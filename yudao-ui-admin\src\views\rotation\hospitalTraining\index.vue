<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="培训名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入培训名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训级别" prop="trainingLevel">
        <el-select
          v-model="queryParams.trainingLevel"
          placeholder="请选择培训级别"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训类型" prop="trainingType">
        <el-select
          v-model="queryParams.trainingType"
          placeholder="请选择培训类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训对象" prop="trainingObject">
        <el-select
          v-model="queryParams.trainingObject"
          placeholder="请选择培训类型"
          clearable
          size="small"
        >
          <el-option
            v-if="dict.value != 0"
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训人" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入培训人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训科室" prop="departmentId">
        <el-select
          v-model="queryParams.departmentId"
          filterable
          clearable
          placeholder="请选择培训科室"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="parseInt(item.id)"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          filterable
          clearable
          size="small"
          @change="searchStudentTypeChange"
          style="width: 120px"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          filterable
          clearable
          size="small"
          style="width: 120px"
        >
          <el-option
            v-for="item in studentMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态" prop="publishStatus">
        <el-select
          v-model="queryParams.publishStatus"
          placeholder="请选择发布状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_PUBLISH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开展时间" prop="developDates">
        <!-- <el-date-picker clearable v-model="queryParams.developDates" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择开展时间" /> -->
        <el-date-picker
          v-model="queryParams.developDates"
          clearable
          style="width: 240px"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:hospital-training:create']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:hospital-training:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="编号" align="center" prop="id" /> -->
      <el-table-column
        label="培训名称"
        align="center"
        prop="name"
        fixed
        width="180"
      />
      <el-table-column
        label="培训级别"
        align="center"
        prop="trainingLevel"
        width="130"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_TRAINING_LEVEL"
            :value="scope.row.trainingLevel"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="培训类型"
        align="center"
        prop="trainingType"
        width="120"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_TRAINING_TYPE"
            :value="scope.row.trainingType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="开展时间"
        align="center"
        prop="startTime"
        width="300"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }} ~ {{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="开展结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="发布状态"
        align="center"
        prop="publishStatus"
        width="130"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_PUBLISH_STATUS"
            :value="scope.row.publishStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="培训人"
        align="center"
        prop="nickname"
        width="120"
      />
      <el-table-column
        label="培训地点"
        align="center"
        prop="trainingAddress"
        width="150"
      />
      <el-table-column
        label="出勤率"
        align="center"
        prop="attendance"
        width="150"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewStudentDetail(scope.row)"
            >{{ scope.row.attendance || "--" }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="评价得分"
        align="center"
        prop="evaluationScore"
        width="140"
      >
        <template slot-scope="scope">
          <div class="rate-wrapper">
            <div
              class="rate-click"
              :style="{
                cursor: scope.row.evaluationScore ? 'pointer' : 'default',
              }"
              @click="handleScoreClick(scope.row)"
            ></div>
            <el-rate
              v-model="scope.row.evaluationScore"
              disabled
              show-score
              text-color="#ff9900"
              :max="5"
              score-template="{value}"
            >
            </el-rate>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="督导得分"
        align="center"
        prop="avgSuperviseScore"
        width="120"
        v-if="showSuperviseScore"
      >
        <template v-slot="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewSuperviseRecord(scope.row)"
          >
            {{ scope.row.avgSuperviseScore || "--" }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="待反馈数量"
        align="center"
        prop="supervisePendingFeedbackCnt"
        width="120"
      >
        <template v-slot="scope">{{
          scope.row.supervisePendingFeedbackCnt || "--"
        }}</template>
      </el-table-column>
      <el-table-column
        label="听评课得分"
        align="center"
        prop="avgEvaluateScore"
        width="120"
        v-if="showEvaluateScore"
      >
        <template v-slot="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewEvaluateRecord(scope.row)"
          >
            {{ scope.row.avgEvaluateScore || "--" }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="270"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document-copy"
            @click="handleCopy(scope.row)"
            v-hasPermi="['rotation:hospital-training:update']"
            >复制</el-button
          >
          <el-button
            v-if="scope.row.publishStatus == 0"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rotation:hospital-training:update']"
            >修改</el-button
          >
          <el-button
            v-if="scope.row.publishStatus == 1"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['rotation:hospital-training:query']"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handlePics(scope.row)"
            v-hasPermi="['rotation:hospital-training:update']"
          >
            <el-badge type="success" is-dot :hidden="!scope.row.pictures">
              <div style="padding: 2px">活动照片</div>
            </el-badge>
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleFiles(scope.row)"
            v-hasPermi="['rotation:hospital-training:update']"
          >
            <el-badge
              type="success"
              is-dot
              :hidden="
                scope.row.coursewares && JSON.parse(scope.row.coursewares)
                  ? JSON.parse(scope.row.coursewares).length === 0
                  : true
              "
            >
              <div style="padding: 2px">上传附件</div>
            </el-badge>
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handleViewQrcode(scope.row)"
            v-hasPermi="['rotation:hospital-training:update']"
            >查看二维码</el-button
          >
          <el-button
            v-if="scope.row.publishStatus == 1"
            size="mini"
            type="text"
            icon="el-icon-refresh-left"
            @click="handleUnPublish(scope.row)"
            v-hasPermi="['rotation:hospital-training:update']"
            >撤销</el-button
          >
          <el-button
            v-if="scope.row.publishStatus == 0"
            size="mini"
            type="text"
            icon="el-icon-position"
            @click="handlePublish(scope.row)"
            v-hasPermi="['rotation:hospital-training:update']"
            >发布</el-button
          >
          <el-button
            v-if="scope.row.publishStatus == 1"
            size="mini"
            type="text"
            icon="el-icon-position"
            @click="handleSelfAssessment(scope.row)"
            v-hasPermi="['rotation:hospital-training:update']"
            >活动自评</el-button
          >
          <el-button
            v-if="scope.row.publishStatus == 0"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:hospital-training:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="680px"
      v-dialogDrag
      append-to-body
      custom-class="hospitalTraining-dialog"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">
        <el-form-item label="培训名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入培训名称"
            :disabled="isViewForm"
          />
        </el-form-item>
        <el-form-item label="培训级别" prop="trainingLevel">
          <el-select
            v-model="form.trainingLevel"
            placeholder="请选择培训级别"
            :disabled="isViewForm"
            style="width: 100%"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.TEACHING_TRAINING_LEVEL
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="培训类型" prop="trainingType">
          <el-select
            v-model="form.trainingType"
            placeholder="请选择培训类型"
            :disabled="isViewForm"
            style="width: 100%"
            @change="handleQueryPaperOptions"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.TEACHING_TRAINING_TYPE
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.trainingType == 4"
          label="专业名称"
          prop="majorCode"
        >
          <el-select
            v-model="form.majorCode"
            placeholder="请选择专业"
            filterable
            clearable
            :disabled="isViewForm"
            style="width: 100%"
          >
            <el-option
              v-for="item in queryMajorList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开展时间" prop="timeArr">
          <el-date-picker
            v-model="form.timeArr"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            :disabled="isViewForm"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="培训人类型" prop="trainingUserTypes">
          <el-select
            multiple
            v-model="form.trainingUserTypes"
            placeholder="请选择培训人类型"
            :disabled="isViewForm"
            style="width: 100%"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.TEACHING_TRAINING_USER_TYPE
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="培训科室" prop="departmentId">
          <el-select
            v-model="form.departmentId"
            filterable
            placeholder="请选择培训科室"
            :disabled="isViewForm"
            style="width: 100%"
            @change="handleQueryPaperOptions"
          >
            <el-option
              v-for="item in departmentOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="parseInt(item.id)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="培训试卷" prop="paperId">
          <el-select
            v-model="form.paperId"
            filterable
            clearable
            placeholder="请选择培训试卷"
            :disabled="isViewForm"
            style="width: 100%"
          >
            <el-option
              v-for="item in paperOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.trainingUserTypes && form.trainingUserTypes.includes('1')"
          label="本院培训人"
          prop="courtTrainingUserId"
        >
          <el-select
            v-model="form.courtTrainingUserId"
            filterable
            placeholder="请选择本院培训人"
            :disabled="isViewForm"
            style="width: 90%"
          >
            <el-option
              v-for="user in userWorkerOptions"
              :key="user.id"
              :label="user.nickname"
              :value="user.id"
            />
          </el-select>
          <i
            v-if="showAddOtherUser && !isViewForm"
            class="el-icon-circle-plus-outline"
            style="font-size: 20px; margin-left: 10px; cursor: pointer"
            @click="addOtherUser"
          ></i>
        </el-form-item>
        <el-form-item
          v-if="
            form.trainingUserTypes &&
            form.trainingUserTypes.includes('1') &&
            form.otherCourtTrainingUserIds
          "
          label=""
          prop="otherCourtTrainingUserIds"
        >
          <el-select
            v-model="form.otherCourtTrainingUserIds"
            multiple
            filterable
            placeholder="请选择本院培训人"
            :disabled="isViewForm"
            style="width: 90%"
          >
            <el-option
              v-for="user in userWorkerOptions"
              :key="user.id"
              :label="user.nickname"
              :value="user.id.toString()"
            />
          </el-select>
          <i
            v-if="!isViewForm"
            class="el-icon-remove-outline"
            style="font-size: 20px; margin-left: 10px; cursor: pointer"
            @click="delOtherUser"
          ></i>
        </el-form-item>

        <el-form-item
          v-if="form.trainingUserTypes && form.trainingUserTypes.includes('2')"
          label="外院培训人"
          prop="outerCourtyardTrainingUserName"
        >
          <el-input
            v-model="form.outerCourtyardTrainingUserName"
            placeholder="请输入外院培训人姓名"
            :disabled="isViewForm"
          />
        </el-form-item>

        <el-form-item label="培训地点" prop="trainingAddress">
          <el-input
            type="textarea"
            v-model="form.trainingAddress"
            placeholder="请输入培训地点"
            :disabled="isViewForm"
          />
        </el-form-item>
        <el-form-item label="是否手动选择培训对象" prop="isManualSelection">
          <el-radio-group
            v-model="form.isManualSelection"
            :disabled="isViewForm"
            @input="isManualSelectionChange"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="form.isManualSelection == true"
          label="培训对象"
          prop="trainingObjects"
        >
          <el-select
            multiple
            v-model="form.trainingObjects"
            placeholder="请选择培训对象"
            :disabled="isViewForm"
            style="width: 100%"
          >
            <el-option
              v-if="dict.value != 0 && dict.value != 3"
              v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            form.isManualSelection == true &&
            form.trainingObjects &&
            form.trainingObjects.indexOf('1') > -1
          "
          label="角色选择"
          prop="roleIds"
        >
          <el-select
            v-model="form.roleIds"
            multiple
            filterable
            clearable
            placeholder="请选择角色"
            style="width: 100%"
            :disabled="isViewForm"
            @change="getSelectedNumber"
          >
            <el-option
              v-for="item in roleOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="item.id.toString()"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            form.isManualSelection == true &&
            form.trainingObjects &&
            form.trainingObjects.indexOf('1') > -1
          "
          label="科室选择"
          prop="workerDepartmentIds"
        >
          <!-- <el-select v-model="form.workerDepartmentIds" multiple filterable clearable placeholder="请选择科室" style="width: 100%" @change="(val) => {form.workerDepartmentIds = val; getSelectedNumber()}"></el-select> -->
          <el-select
            v-model="form.workerDepartmentIds"
            multiple
            filterable
            clearable
            placeholder="请选择科室"
            :disabled="isViewForm"
            style="width: 100%"
            @change="getSelectedNumber"
          >
            <el-option
              v-for="item in departmentOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="item.id.toString()"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            form.isManualSelection == true &&
            form.trainingObjects &&
            form.trainingObjects.indexOf('2') > -1
          "
          label="学员类型"
          prop="studentTypes"
        >
          <el-select
            v-model="form.studentTypes"
            multiple
            filterable
            placeholder="请选择学员类型"
            :disabled="isViewForm"
            style="width: 100%"
            @change="getSelectedNumber"
          >
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            form.isManualSelection == true &&
            form.trainingObjects &&
            form.trainingObjects.indexOf('2') > -1
          "
          label="学员专业"
          prop="studentMajors"
        >
          <el-select
            v-model="form.studentMajors"
            placeholder="请选择学员专业"
            multiple
            filterable
            clearable
            :disabled="isViewForm"
            style="width: 100%"
            @change="getSelectedNumber"
          >
            <el-option
              v-for="item in queryMajorList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <span style="float: left">{{ item.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                <dict-tag
                  :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                  :value="item.studentType"
                />
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            form.isManualSelection == true &&
            form.trainingObjects &&
            form.trainingObjects.indexOf('2') > -1
          "
          label="学员年级"
          prop="grades"
        >
          <el-select
            v-model="form.grades"
            multiple
            filterable
            placeholder="请选择年级"
            :disabled="isViewForm"
            style="width: 100%"
            @change="getSelectedNumber"
          >
            <el-option
              v-for="grade in studentGradeList"
              :key="grade"
              :label="grade"
              :value="grade"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            form.isManualSelection == true &&
            form.trainingObjects &&
            form.trainingObjects.indexOf('2') > -1
          "
          label="派送单位"
          prop="dispatchingUnit"
        >
          <el-input
            v-model="form.dispatchingUnit"
            :disabled="isViewForm"
            placeholder="请输入派送单位"
            @blur="getSelectedNumber"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="
            form.isManualSelection == true &&
            form.trainingObjects &&
            form.trainingObjects.indexOf('2') > -1
          "
          label="分组"
          prop="studentGroup"
        >
          <el-input
            v-model="form.studentGroup"
            :disabled="isViewForm"
            placeholder="请输入分组"
            @blur="getSelectedNumber"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="form.isManualSelection == true"
          label="已选择培训人数"
          prop="trainingSelectedNumber"
        >
          <el-input v-model="form.trainingSelectedNumber" disabled />
        </el-form-item>
        <el-form-item label="是否允许自主报名参加" prop="isSelfRegistration">
          <el-radio-group
            v-model="form.isSelfRegistration"
            :disabled="isViewForm"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="报名人数限制" prop="registrationLimitNumber">
          <el-input-number
            v-model="form.registrationLimitNumber"
            :min="0"
            :max="9999"
            :disabled="isViewForm"
            label="请输入报名人数限制"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="培训课件" prop="coursewares">
          <FileUpload
            v-model="form.coursewares"
            :limit="999"
            :fileSize="50"
            :disabled="isViewForm"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!isViewForm">
        <el-button type="primary" @click="submitForm('save')">保存</el-button>
        <el-button type="primary" @click="submitForm('publish')"
          >直接发布</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="活动二维码"
      :visible.sync="openQrCode"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <div style="text-align: center">
        <img
          width="220"
          height="220"
          :src="'data:image/png;base64,' + curQrcode"
        />
      </div>
    </el-dialog>

    <el-dialog
      title="活动照片"
      :visible.sync="openPics"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <imageUpload v-model="pictures" :limit="9999" activeTypeName="" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPics">保 存</el-button>
        <el-button @click="cancelSubmitPics">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="活动附件"
      :visible.sync="openFiles"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <fileUpload
          v-model="files"
          :file-size="50"
          :limit="9999"
          activeTypeName=""
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFiles">保 存</el-button>
        <el-button @click="cancelSubmitFiles">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="参加学员数详情页面"
      :visible.sync="openStudentDetail"
      width="1280px"
      v-dialogDrag
      append-to-body
      custom-class="student-detail-dialog"
    >
      <el-tabs v-model="activeTabName" @tab-click="handleTabClick">
        <el-tab-pane label="学员" name="student">
          <div class="student-tab">
            <el-form
              :model="queryStudentParams"
              ref="queryStudentForm"
              size="small"
              :inline="true"
              label-width="100px"
            >
              <el-form-item label="学员姓名" prop="nickname">
                <el-input
                  v-model="queryStudentParams.nickname"
                  placeholder="请输入学员姓名"
                  clearable
                  @keyup.enter.native="handleStudentQuery"
                />
              </el-form-item>
              <el-form-item label="学员类型" prop="studentType">
                <el-select
                  v-model="queryStudentParams.studentType"
                  placeholder="请选择学员类型"
                  filterable
                  clearable
                  size="small"
                  @change="handleQueryStudentTypeChange"
                >
                  <el-option
                    v-for="dict in this.getDictDatas(
                      DICT_TYPE.SYSTEM_STUDENT_TYPE
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="培训专业" prop="major">
                <el-select
                  v-model="queryStudentParams.major"
                  placeholder="请选择培训专业"
                  filterable
                  clearable
                  size="small"
                >
                  <el-option
                    v-for="item in simpleMajorList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="年级" prop="grade">
                <el-select
                  v-model="queryStudentParams.grade"
                  placeholder="请选择年级"
                  filterable
                  clearable
                  size="small"
                >
                  <el-option
                    v-for="grade in studentGradeList"
                    :key="grade"
                    :label="grade"
                    :value="grade"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="派送单位" prop="dispatchingUnit">
                <el-input
                  v-model="queryStudentParams.dispatchingUnit"
                  placeholder="请输入派送单位"
                  clearable
                />
              </el-form-item>
              <el-form-item label="分组" prop="studentGroup">
                <el-input
                  v-model="queryStudentParams.studentGroup"
                  placeholder="请输入分组"
                  clearable
                />
              </el-form-item>
              <el-form-item label="是否签到" prop="isSigned">
                <el-select
                  v-model="queryStudentParams.isSigned"
                  placeholder="请选择是否已签到"
                  clearable
                  size="small"
                >
                  <el-option
                    v-for="dict in this.getDictDatas(
                      DICT_TYPE.INFRA_BOOLEAN_STRING
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleStudentQuery"
                  >搜索</el-button
                >
                <el-button icon="el-icon-refresh" @click="resetStudentQuery"
                  >重置</el-button
                >
                <el-button
                  type="warning"
                  plain
                  icon="el-icon-upload2"
                  size="mini"
                  @click.native="handleStudentImport"
                >
                  导入
                </el-button>
                <el-button
                  type="warning"
                  plain
                  icon="el-icon-download"
                  size="mini"
                  :loading="studentExportLoading"
                  @click="handleStudentExport"
                  >导出</el-button
                >
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="
                    () => {
                      addStudentsVisible = true;
                    }
                  "
                  >添加学员</el-button
                >
              </el-form-item>
            </el-form>

            <el-table
              v-loading="studentDetailLoading"
              :data="studentDetailList"
            >
              <el-table-column
                label="学员姓名"
                align="center"
                prop="nickname"
              />
              <el-table-column
                label="学员类型"
                prop="studentType"
                align="center"
              >
                <template v-slot="scope">
                  <dict-tag
                    :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                    :value="scope.row.studentType"
                  ></dict-tag>
                </template>
              </el-table-column>
              <el-table-column
                label="培训专业"
                align="center"
                prop="majorName"
              />
              <el-table-column label="年级" align="center" prop="grade" />
              <el-table-column
                label="派送单位"
                align="center"
                prop="dispatchingUnit"
              />
              <el-table-column
                label="分组"
                align="center"
                prop="studentGroup"
              />
              <el-table-column
                label="扫码时间"
                align="center"
                prop="scanningTime"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.scanningTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
                width="100"
              >
                <template slot-scope="scope">
                  <el-button
                    v-if="!scope.row.scanningTime"
                    size="mini"
                    type="text"
                    @click="handleJoin(scope.row)"
                    >确认参加</el-button
                  >
                  <el-button
                    v-else
                    size="mini"
                    type="text"
                    @click="handleRevoke(scope.row)"
                    >撤销参加</el-button
                  >
                  <el-button
                    v-if="!scope.row.scanningTime"
                    size="mini"
                    type="text"
                    @click="handleDelUser(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="studentTotal > 0"
              :total="studentTotal"
              :page.sync="queryStudentParams.pageNo"
              :limit.sync="queryStudentParams.pageSize"
              @pagination="() => getStudentList()"
            />
          </div>
        </el-tab-pane>

        <el-tab-pane label="职工" name="worker">
          <div class="student-tab">
            <el-form
              :model="queryWorkerParams"
              ref="queryWorkerForm"
              size="small"
              :inline="true"
              label-width="100px"
            >
              <el-form-item label="职工姓名" prop="nickname">
                <el-input
                  v-model="queryWorkerParams.nickname"
                  placeholder="请输入职工姓名"
                  clearable
                  @keyup.enter.native="handleStudentQuery"
                />
              </el-form-item>
              <el-form-item label="职工工号" prop="username">
                <el-input
                  v-model="queryWorkerParams.username"
                  placeholder="请输入职工工号"
                  clearable
                  @keyup.enter.native="handleStudentQuery"
                />
              </el-form-item>
              <el-form-item label="角色" prop="roleId">
                <el-select
                  v-model="queryWorkerParams.roleId"
                  filterable
                  clearable
                  placeholder="请选择角色"
                >
                  <el-option
                    v-for="item in roleOptions"
                    :key="parseInt(item.id)"
                    :label="item.name"
                    :value="parseInt(item.id)"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="是否签到" prop="isSigned">
                <el-select
                  v-model="queryWorkerParams.isSigned"
                  placeholder="请选择是否已签到"
                  clearable
                  size="small"
                >
                  <el-option
                    v-for="dict in this.getDictDatas(
                      DICT_TYPE.INFRA_BOOLEAN_STRING
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleWorkerQuery"
                  >搜索</el-button
                >
                <el-button icon="el-icon-refresh" @click="resetWorkerQuery"
                  >重置</el-button
                >
                <el-button
                  type="warning"
                  plain
                  icon="el-icon-upload2"
                  size="mini"
                  @click.native="handleWorkerImport"
                >
                  导入
                </el-button>
                <el-button
                  type="warning"
                  plain
                  icon="el-icon-download"
                  size="mini"
                  :loading="workerExportLoading"
                  @click="handleWorkerExport"
                  >导出</el-button
                >
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="
                    () => {
                      addWorkersVisible = true;
                    }
                  "
                  >添加职工</el-button
                >
              </el-form-item>
            </el-form>

            <el-table v-loading="worderDetailLoading" :data="worderDetailList">
              <el-table-column
                label="职工姓名"
                align="center"
                prop="nickname"
              />
              <el-table-column
                label="职工工号"
                align="center"
                prop="username"
              />
              <el-table-column label="角色" align="center" prop="roleNames" />
              <el-table-column
                label="扫码时间"
                align="center"
                prop="scanningTime"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.scanningTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
                width="180"
              >
                <template slot-scope="scope">
                  <el-button
                    v-if="!scope.row.scanningTime"
                    size="mini"
                    type="text"
                    @click="handleJoin(scope.row)"
                    >确认参加</el-button
                  >
                  <el-button
                    v-else
                    size="mini"
                    type="text"
                    @click="handleRevoke(scope.row)"
                    >撤销参加</el-button
                  >
                  <el-button
                    v-if="!scope.row.scanningTime"
                    size="mini"
                    type="text"
                    @click="handleDelUser(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="workerTotal > 0"
              :total="workerTotal"
              :page.sync="queryWorkerParams.pageNo"
              :limit.sync="queryWorkerParams.pageSize"
              @pagination="getWorkerList"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog
      title="活动自评"
      :visible.sync="openActiveSelfAssessment"
      width="400px"
      append-to-body
    >
      <el-form :model="selfAssessmentForm" :rule="selfAssessmentRules">
        <el-form-item style="margin-bottom: 0" label="活动名称">{{
          selfAssessmentForm.name
        }}</el-form-item>
        <el-form-item label="活动自评" prop="selfAssessment">
          <el-input
            type="textarea"
            v-model="selfAssessmentForm.selfAssessment"
            placeholder="请输入活动自评"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="submitSelfAssessment">提交</el-button>
        <el-button @click="openActiveSelfAssessment = false">取消</el-button>
      </span>
    </el-dialog>

    <appraise-score-dialog
      appraise-active-type="2"
      ref="appraiseScoreDialog"
    ></appraise-score-dialog>

    <supervise-record-dialog
      :visible.sync="recordListVisible"
      :supervise-object="curActive"
      can-feedback
    ></supervise-record-dialog>

    <el-dialog
      title="听评课记录"
      :visible.sync="evaluateListVisible"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-table :data="evaluateRecordList">
        <el-table-column label="督导人" align="center" prop="nickname" />
        <el-table-column label="评分" align="center" prop="score" />
        <el-table-column label="反馈状态" align="center" prop="feedback">
          <template v-slot="scope">
            {{ scope.row.feedback ? "已反馈" : "未反馈" }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="120"
          class-name="small-padding fixed-width"
        >
          <template v-slot="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleFeedback(scope.row)"
              >{{ scope.row.feedback ? "查看详情" : "督导反馈" }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      title="听评课反馈"
      width="1100px"
      :visible.sync="evaluateFeedbackVisible"
    >
      <div id="pdf-main-cont" class="pdf-main-cont" style="padding: 80px">
        <h2 style="text-align: center; margin: 0 0 30px 0">
          深圳市龙岗区人民医院听课记录表
        </h2>

        <el-form inline label-width="90px">
          <el-form-item
            label="培训名称："
            style="width: 60%; white-space: nowrap; margin-bottom: 0"
            >{{ curActive.name }}</el-form-item
          >
          <el-form-item label="培训人：" style="width: 30%; margin-bottom: 0">{{
            curActive.nickname
          }}</el-form-item>
          <el-form-item label="开展时间：" style="width: 100%; margin-bottom: 0"
            >{{ curActive.startTime }} ~ {{ curActive.endTime }}</el-form-item
          >
          <el-form-item
            label="培训地点："
            style="width: 100%; margin-bottom: 0"
            >{{ curActive.trainingAddress }}</el-form-item
          >
        </el-form>

        <p>
          请您根据评价标准和听课情况进行评价，并在得分栏打分，谢谢您的合作！
        </p>

        <div style="margin-bottom: 10px">
          <el-table :data="evaluateData" border :span-method="objectSpanMethod">
            <el-table-column label="评价项目" prop="projectName" width="100px">
              <template v-slot="scope">
                <div>
                  {{
                    scope.row.projectName ||
                    scope.row.sumText ||
                    scope.row.evlTitle
                  }}
                </div>
                <div v-if="scope.row.projectName">
                  {{ scope.row.totalScore }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="序号" prop="id" width="60px">
              <template v-slot="scope">
                <div v-if="scope.row.projectName">{{ scope.row.id }}</div>
                <div v-if="scope.row.sumText">
                  {{ scope.row.evaluateTotal }}
                </div>
                <div v-if="scope.row.evlTitle">
                  <el-input
                    type="textarea"
                    :rows="3"
                    placeholder="请输入总体评价、意见及建议"
                    :disabled="true"
                    v-model="scope.row.evaluateComments"
                  >
                  </el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="评价标准" prop="item"></el-table-column>
            <el-table-column
              label="分值"
              prop="itemScore"
              width="60px"
            ></el-table-column>
            <el-table-column label="得分" width="150px">
              <template v-slot="scope">
                <el-input-number
                  style="width: 120px"
                  v-model="scope.row.evaluateItem"
                  controls-position="right"
                  :min="0"
                  :max="scope.row.itemScore"
                  :disabled="true"
                ></el-input-number>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div style="text-align: right; margin-bottom: 20px">
          听课专家签名：{{ curUserName }}
        </div>
      </div>

      <el-form inline label-width="140px">
        <el-form-item
          label="现场图片："
          style="width: 24%; white-space: nowrap"
        >
          <imageUpload
            v-model="livePhotos"
            :limit="9999"
            activeTypeName=""
            :disabled="true"
          />
        </el-form-item>
      </el-form>

      <el-form label-width="140px">
        <el-form-item label="评课反馈：">
          <el-input
            type="textarea"
            v-model="evaluateFeedback"
            :rows="3"
            placeholder="请输入内容"
            :disabled="curEvaluateRecord.feedback"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="!curEvaluateRecord.feedback">
          <el-button type="primary" @click="submitEvaluateFeedback"
            >提交反馈</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="
          upload.url +
          '?updateSupport=' +
          upload.updateSupport +
          '&hospitalTrainingId=' +
          curActive.id
        "
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的数据
          <p>
            仅允许导入xls、xlsx格式文件。
            <el-link
              style="font-size: 12px"
              type="primary"
              :underline="false"
              @click="handleExportTemplate"
              >下载模板</el-link
            >
          </p>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <add-students-dialog
      :visible.sync="addStudentsVisible"
      :curActive="curActive"
      @refresh="handleStudentQuery"
    ></add-students-dialog>

    <add-workers-dialog
      :visible.sync="addWorkersVisible"
      :curActive="curActive"
      @refresh="handleWorkerQuery"
    ></add-workers-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";
import AppraiseScoreDialog from "@/views/components/appraiseScoreDialog";
import SuperviseRecordDialog from "../hospitalTrainingSupervise/supervise-record-dialog";
import AddStudentsDialog from "./components/addStudentsDialog";
import AddWorkersDialog from "./components/addWorkersDialog";
import { getStudentGradeList } from "@/api/system/userStudent";
import { getUserWorkerSimpleList } from "@/api/system/userWorker";
import { getSimpleMajorList } from "@/api/system/major";
import { listSimpleRoles } from "@/api/system/role";
import { getDepartmentSimpleList } from "@/api/system/department";
import {
  createHospitalTraining,
  updateHospitalTraining,
  deleteHospitalTraining,
  getHospitalTraining,
  getHospitalTrainingPage,
  exportHospitalTrainingExcel,
  getTrainingSelectedNumber,
  hospitalTrainingRevoke,
  hospitalTrainingPublish,
  getStudentPage,
  getWorkerPage,
  confirmJoin,
  revokeJoin,
  updateSelfAssessment,
  exportUserStudentExcel,
  exportUserWorkerExcel,
  exportStudentTemplate,
  exportWorkTemplate,
  copyHospitalTraining,
  deleteTeachingUser,
  updateHospitalTrainingPictures,
  updateHospitalTrainingFiles,
} from "@/api/rotation/hospitalTraining";
import {
  getEvaluateRecordPage,
  saveEvaluateFeedback,
  getHospitalTrainingEvaluate,
} from "@/api/rotation/hospitalTrainingEvaluate";
import { getBaseHeader } from "@/utils/request";
import { getHospitalTrainPaper } from "@/api/exam/paper";

export default {
  name: "HospitalTraining",
  components: {
    FileUpload,
    ImageUpload,
    AppraiseScoreDialog,
    SuperviseRecordDialog,
    AddStudentsDialog,
    AddWorkersDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级培训列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        trainingLevel: null,
        trainingType: null,
        trainingObject: null,
        majorCode: null,
        publishStatus: null,
        nickname: null,
        departmentId: null,
        developDates: [],
        isNeedQrcode: true,
        studentType: null,
        major: null,
      },
      studentMajorList: [],
      paperOptions: [],
      // 表单参数
      form: {
        trainingUserTypes: [],
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "培训名称不能为空", trigger: "blur" },
        ],
        trainingLevel: [
          { required: true, message: "培训级别不能为空", trigger: "change" },
        ],
        trainingType: [
          { required: true, message: "培训类型不能为空", trigger: "change" },
        ],
        majorCode: [
          { required: true, message: "培训专业不能为空", trigger: "change" },
        ],
        startTime: [
          { required: true, message: "开展开始时间不能为空", trigger: "blur" },
        ],
        endTime: [
          { required: true, message: "开展结束时间不能为空", trigger: "blur" },
        ],
        trainingUserTypes: [
          {
            type: "array",
            required: true,
            message: "培训人类型不能为空",
            trigger: "change",
          },
        ],
        courtTrainingUserId: [
          { required: true, message: "培训人不能为空", trigger: "change" },
        ],
        outerCourtyardTrainingUserName: [
          { required: true, message: "培训人不能为空", trigger: "change" },
        ],
        trainingAddress: [
          { required: true, message: "培训地点不能为空", trigger: "blur" },
        ],
        isManualSelection: [
          {
            required: true,
            message: "是否手动选择培训对象不能为空",
            trigger: "blur",
          },
        ],
        trainingObjects: [
          { required: true, message: "培训对象不能为空", trigger: "change" },
        ],
        roleIds: [
          {
            type: "array",
            required: true,
            message: "角色不能为空",
            trigger: "change",
          },
        ],
        studentTypes: [
          {
            type: "array",
            required: true,
            message: "学员类型不能为空",
            trigger: "change",
          },
        ],
        trainingSelectedNumber: [
          { required: true, message: "选择培训人数不能为空", trigger: "blur" },
        ],
        isSelfRegistration: [
          {
            required: true,
            message: "是否允许自主报名参加不能为空",
            trigger: "blur",
          },
        ],
        registrationLimitNumber: [
          { required: true, message: "报名人数限制不能为空", trigger: "blur" },
        ],
        publishStatus: [
          { required: true, message: "发布状态不能为空", trigger: "change" },
        ],
      },
      studentGradeList: [],
      userWorkerOptions: [],
      queryMajorList: [],
      roleOptions: [],
      openQrCode: false,
      curQrcode: "",
      openPics: false,
      pictures: "",
      openFiles: false,
      files: "",
      curActive: {},
      activeTabName: "student",
      simpleMajorList: [],
      openStudentDetail: false,
      queryStudentParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: "",
        studentType: "",
        major: "",
        grade: "",
        dispatchingUnit: "",
        studentGroup: "",
        isSigned: "",
      },
      studentTotal: 0,
      studentDetailLoading: false,
      studentDetailList: [],
      queryWorkerParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: "",
        username: "",
        roleId: "",
        isSigned: "",
      },
      workerTotal: 0,
      worderDetailLoading: false,
      worderDetailList: [],
      recordListVisible: false,

      showSuperviseScore: process.env.VUE_APP_SUPERVISE_SCORE !== "false",
      showEvaluateScore: process.env.VUE_APP_EVALUATE_SCORE === "true",
      evaluateListVisible: false,
      evaluateRecordList: [],
      evaluateData: [
        {
          id: 1,
          projectName: "教学态度",
          totalScore: 10,
          itemScore: 5,
          evaluateItem: 5,
          item: "仪表端庄、举止得体，精神饱满，富有激情。",
        },
        {
          id: 2,
          projectName: "教学态度",
          totalScore: 10,
          itemScore: 5,
          evaluateItem: 5,
          item: "教案撰写规范;严格进行课堂管理，遵守课息时间。",
        },

        {
          id: 3,
          projectName: "教学内容",
          totalScore: 40,
          itemScore: 6,
          evaluateItem: 6,
          item: "教学目标明确，符合教学大纲要求。",
        },
        {
          id: 4,
          projectName: "教学内容",
          totalScore: 40,
          itemScore: 6,
          evaluateItem: 6,
          item: "内容严谨充实，无科学性、政治性错误，无不当言论。",
        },
        {
          id: 5,
          projectName: "教学内容",
          totalScore: 40,
          itemScore: 12,
          evaluateItem: 12,
          item: "课堂时间分配合理；层次清晰，逻辑性强，重点突出难占进透。",
        },
        {
          id: 6,
          projectName: "教学内容",
          totalScore: 40,
          itemScore: 8,
          evaluateItem: 8,
          item: "理论联系实际，范例合理，适度介绍社会和学科发展。",
        },
        {
          id: 7,
          projectName: "教学内容",
          totalScore: 40,
          itemScore: 4,
          evaluateItem: 4,
          item: "适当运用本学科专业外语词汇(读、写)。",
        },
        {
          id: 8,
          projectName: "教学内容",
          totalScore: 40,
          itemScore: 4,
          evaluateItem: 4,
          item: "在专业教学中融入课程思政元素。",
        },

        {
          id: 9,
          projectName: "教学方法",
          totalScore: 30,
          itemScore: 8,
          evaluateItem: 8,
          item: "根据教学需求选用适当的教学方法，注重师生互动，有效调动学生 学习积极性。",
        },
        {
          id: 10,
          projectName: "教学方法",
          totalScore: 30,
          itemScore: 8,
          evaluateItem: 8,
          item: "突出学生主体地位，重视学生创新意识、批判性思维自主学习能 力的培养。",
        },
        {
          id: 11,
          projectName: "教学方法",
          totalScore: 30,
          itemScore: 8,
          evaluateItem: 8,
          item: "合理运用信息技术手段开展教学: 课件显示学校名称LOG0，内容 简明，素材丰富，运行流畅: 适当板书，利于知识传授。",
        },
        {
          id: 12,
          projectName: "教学方法",
          totalScore: 30,
          itemScore: 6,
          evaluateItem: 6,
          item: "普通话授课，教学语言准确、规范、简洁、生动，语速适中。",
        },

        {
          id: 13,
          projectName: "教学效果",
          totalScore: 10,
          itemScore: 6,
          evaluateItem: 6,
          item: "完成设定教学目标，促进学生思维能力、分析解决问题和学习能力 的提高。",
        },
        {
          id: 14,
          projectName: "教学效果",
          totalScore: 10,
          itemScore: 4,
          evaluateItem: 4,
          item: "学生参与程度高、学习兴趣浓，课堂氛围活跃。",
        },

        {
          id: 15,
          projectName: "教书育人",
          totalScore: 10,
          itemScore: 5,
          evaluateItem: 5,
          item: "为人师表，治学严谨，无不当言论;引导学生端正学习态度。",
        },
        {
          id: 16,
          projectName: "教书育人",
          totalScore: 10,
          itemScore: 5,
          evaluateItem: 5,
          item: "注重思想品德、职业道德教育，培养学生严谨求实的科学态度和作风。",
        },

        { id: 17, sumText: "合计", evaluateTotal: 100 },
        { id: 18, evlTitle: "总体评价、意见及建议", evaluateComments: "" },
      ],
      evaluateFeedbackVisible: false,
      curEvaluateRecord: {},
      curUserName: "",
      livePhotos: "",
      evaluateFeedback: "",

      // 活动自评
      openActiveSelfAssessment: false,
      selfAssessmentForm: { selfAssessment: "" },
      selfAssessmentRules: {
        selfAssessment: [
          { required: true, message: "请输入活动自评", trigger: "blur" },
        ],
      },
      // 学员职工导出遮罩层
      studentExportLoading: false,
      workerExportLoading: false,
      departmentOptions: [],
      addStudentsVisible: false, // 添加学员弹框
      addWorkersVisible: false, // 添加职工弹框
      showAddOtherUser: true,
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的科室数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/admin-api/system/user-student/import",
      },
    };
  },
  computed: {
    isViewForm() {
      return this.title.startsWith("查看");
    },
  },
  created() {
    this.getList();
    this.getDepartment();
    getStudentGradeList().then((res) => {
      this.studentGradeList = res.data;
    });
    this.getUserworkData();
    this.getMajorList();
    // 获得角色列表
    this.roleOptions = [];
    listSimpleRoles().then((response) => {
      const { data } = response;
      let list = [];
      data.forEach((item) => {
        if (item.code !== "super_admin" && item.code !== "student") {
          list.push(item);
        }
      });
      this.roleOptions = list;
    });
    getSimpleMajorList().then((res) => (this.majorList = res.data));
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getHospitalTrainingPage(this.queryParams).then((response) => {
        const list = response.data.list;
        list.forEach((item) => {
          if (!item.score) {
            item.evaluationScore = 0;
          } else {
            const _score = (item.evaluationScore / item.score) * 5;
            item.evaluationScore = _score;
          }
        });
        this.list = list;
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        trainingLevel: undefined,
        trainingType: undefined,
        majorCode: undefined,
        timeArr: [],
        startTime: undefined,
        endTime: undefined,
        trainingUserTypes: [],
        departmentId: undefined,
        paperId: undefined,
        courtTrainingUserId: undefined,
        outerCourtyardTrainingUserName: undefined,
        trainingAddress: undefined,
        isManualSelection: false,
        trainingObjects: [],
        roleIds: [],
        workerDepartmentIds: [],
        studentTypes: [],
        studentMajors: [],
        grades: [],
        studentGroup: undefined,
        dispatchingUnit: undefined,
        trainingSelectedNumber: 0,
        isSelfRegistration: true,
        registrationLimitNumber: 0,
        coursewares: undefined,
        pictures: undefined,
        publishStatus: undefined,
        otherCourtTrainingUserIds: undefined,
      };
      this.resetForm("form");
    },

    searchStudentTypeChange(value) {
      this.queryParams.major = null;
      this.studentMajorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.studentMajorList = res.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleStudentQuery() {
      this.queryStudentParams.pageNo = 1;
      this.getStudentList();
    },
    resetStudentQuery() {
      this.resetForm("queryStudentForm");
      this.handleStudentQuery();
    },
    handleWorkerQuery() {
      this.queryWorkerParams.pageNo = 1;
      this.getWorkerList();
    },
    resetWorkerQuery() {
      this.resetForm("queryWorkerForm");
      this.handleWorkerQuery();
    },
    isManualSelectionChange() {
      this.form.trainingObjects = [];
      this.form.roleIds = [];
      this.form.workerDepartmentIds = [];
      this.form.studentTypes = [];
      this.form.studentMajors = [];
      this.form.grades = [];
      this.form.dispatchingUnit = "";
      this.form.studentGroup = undefined;
      this.form.trainingSelectedNumber = 0;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.showAddOtherUser = true;
      this.open = true;
      this.title = "添加院级培训";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      this.curActive = row;
      return getHospitalTraining(id).then((response) => {
        const { data } = response;
        const { startTime, endTime } = data;
        this.form = { ...data, timeArr: [] };
        this.form.timeArr = [startTime, endTime];
        this.form.trainingUserTypes = data.trainingUserTypes
          ? data.trainingUserTypes.split(",")
          : [];
        this.form.trainingObjects = data.trainingObjects
          ? data.trainingObjects.split(",")
          : [];
        this.form.roleIds = data.roleIds ? data.roleIds.split(",") : [];
        this.form.workerDepartmentIds = data.workerDepartmentIds
          ? data.workerDepartmentIds.split(",")
          : [];
        this.form.trainingUserTypes = data.trainingUserTypes
          ? data.trainingUserTypes.split(",")
          : [];
        this.form.studentTypes = data.studentTypes
          ? data.studentTypes.split(",")
          : [];
        this.form.studentMajors = data.studentMajors
          ? data.studentMajors.split(",")
          : [];
        this.form.grades = data.grades ? data.grades.split(",") : [];
        this.form.coursewares = this.form.coursewares
          ? JSON.parse(this.form.coursewares)
          : [];
        this.form.otherCourtTrainingUserIds = data.otherCourtTrainingUserIds
          ? data.otherCourtTrainingUserIds.split(",")
          : undefined;
        this.handleQueryPaperOptions(false);
        // console.log('this.form===', this.form)
        if (this.form.otherCourtTrainingUserIds) {
          this.showAddOtherUser = false;
        } else {
          this.showAddOtherUser = true;
        }
        this.showAddOtherUser = true;
        this.open = true;
        this.title = "修改院级培训";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.handleUpdate(row).then(() => {
        this.title = "查看院级培训";
      });
    },
    handleQueryPaperOptions(reset = true) {
      if (reset) this.form.paperId = undefined;
      const { departmentId, trainingType } = this.form;
      if (!departmentId || !trainingType) {
        this.paperOptions = [];
        return;
      }
      getHospitalTrainPaper({
        departmentId,
        hospitalTrainingType: trainingType,
      }).then((res) => {
        this.paperOptions = res.data;
      });
    },
    addOtherUser() {
      this.showAddOtherUser = false;
      this.form.otherCourtTrainingUserIds = [];
    },
    delOtherUser() {
      this.showAddOtherUser = true;
      this.form.otherCourtTrainingUserIds = undefined;
    },
    handlePics(row) {
      this.curActive = row;
      getHospitalTraining(row.id).then((response) => {
        this.pictures = response.data.pictures;
        this.openPics = true;
      });
    },
    cancelSubmitPics() {
      this.openPics = false;
    },
    handleFiles(row) {
      this.curActive = row;
      getHospitalTraining(row.id).then((response) => {
        const files = response.data.coursewares;
        this.files = files ? JSON.parse(files) : null;
        this.openFiles = true;
      });
    },
    cancelSubmitFiles() {
      this.openFiles = false;
    },
    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = {
          name: this.form.name,
          trainingLevel: this.form.trainingLevel,
          trainingType: this.form.trainingType,
          majorCode: this.form.majorCode,
          startTime: this.form.timeArr[0],
          endTime: this.form.timeArr[1],
          trainingUserTypes: this.form.trainingUserTypes
            ? this.form.trainingUserTypes.join(",")
            : "",
          courtTrainingUserId: this.form.courtTrainingUserId,
          outerCourtyardTrainingUserName:
            this.form.outerCourtyardTrainingUserName,
          trainingAddress: this.form.trainingAddress,
          isManualSelection: this.form.isManualSelection,
          trainingObjects: this.form.trainingObjects
            ? this.form.trainingObjects.join(",")
            : "",
          roleIds: this.form.roleIds ? this.form.roleIds.join(",") : "",
          workerDepartmentIds: this.form.workerDepartmentIds
            ? this.form.workerDepartmentIds.join(",")
            : "",
          studentTypes: this.form.studentTypes
            ? this.form.studentTypes.join(",")
            : "",
          studentMajors: this.form.studentMajors
            ? this.form.studentMajors.join(",")
            : "",
          grades: this.form.grades ? this.form.grades.join(",") : "",
          dispatchingUnit: this.form.dispatchingUnit,
          trainingSelectedNumber: this.form.trainingSelectedNumber,
          isSelfRegistration: this.form.isSelfRegistration,
          registrationLimitNumber: this.form.registrationLimitNumber,
          coursewares:
            this.form.coursewares && this.form.coursewares.length > 0
              ? JSON.stringify(this.form.coursewares)
              : "",
          pictures: this.form.pictures,
          publishStatus: type === "save" ? 0 : 1,
          studentGroup: this.form.studentGroup,
          otherCourtTrainingUserIds: this.form.otherCourtTrainingUserIds
            ? this.form.otherCourtTrainingUserIds.join(",")
            : "",
          departmentId: this.form.departmentId,
          paperId: this.form.paperId,
        };
        // 修改的提交
        if (this.form.id != null) {
          params.id = this.form.id;
          updateHospitalTraining(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.showAddOtherUser = true;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createHospitalTraining(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    submitPics() {
      updateHospitalTrainingPictures({
        id: this.curActive.id,
        pictures: this.pictures,
      }).then((response) => {
        this.$modal.msgSuccess("保存成功");
        this.openPics = false;
        this.getList();
      });
    },
    submitFiles() {
      updateHospitalTrainingFiles({
        id: this.curActive.id,
        coursewares:
          this.files && this.files.length > 0 ? JSON.stringify(this.files) : "",
      }).then((response) => {
        this.$modal.msgSuccess("保存成功");
        this.openFiles = false;
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const { id, name } = row;
      this.$modal
        .confirm(`是否确认删除"${name}"?`)
        .then(function () {
          return deleteHospitalTraining(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      // debugger
      const { id, name } = row;
      this.$modal
        .confirm(`是否确认复制"${name}"?`)
        .then((res) => {
          return copyHospitalTraining(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("复制成功");
        })
        .catch(() => {});
    },
    handleUnPublish(row) {
      const { id, name } = row;
      this.$modal
        .confirm(`是否确认撤销"${name}"?`)
        .then(function () {
          return hospitalTrainingRevoke({ id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(`撤销成功`);
        })
        .catch(() => {});
    },
    handlePublish(row) {
      const { id, name } = row;
      this.$modal
        .confirm(`是否确认发布"${name}"?`)
        .then(function () {
          return hospitalTrainingPublish({ id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(`发布成功`);
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有院级培训数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportHospitalTrainingExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "院级培训.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    getUserworkData() {
      getUserWorkerSimpleList().then((res) => {
        this.userWorkerOptions = [];
        this.userWorkerOptions.push(...res.data);
      });
    },
    getMajorList() {
      this.queryMajorList = [];
      getSimpleMajorList().then((res) => {
        this.queryMajorList = res.data;
      });
    },
    getSelectedNumber(val) {
      console.log("getSelectedNumber===", val);
      const params = {
        trainingObjects: this.form.trainingObjects.join(","),
        roleIds: this.form.roleIds.join(","),
        workerDepartmentIds: this.form.workerDepartmentIds.join(","),
        studentTypes: this.form.studentTypes.join(","),
        studentMajors: this.form.studentMajors.join(","),
        grades: this.form.grades.join(","),
        dispatchingUnit: this.form.dispatchingUnit,
        studentGroup: this.form.studentGroup,
      };
      getTrainingSelectedNumber(params).then((res) => {
        this.form.trainingSelectedNumber = res.data || 0;
      });
    },
    handleViewQrcode(row) {
      this.curQrcode = row.qrcode;
      this.openQrCode = true;
    },
    viewStudentDetail(row) {
      if (!row.attendance) {
        return;
      }
      this.activeTabName = "student";
      this.curActive = row;
      this.getStudentList(() => {
        this.openStudentDetail = true;
      });
    },
    getStudentList(callback = null) {
      this.studentDetailLoading = true;
      const params = {
        ...this.queryStudentParams,
        hospitalTrainingId: this.curActive.id,
      };
      getStudentPage(params).then((response) => {
        this.studentDetailList = response.data.list || [];
        if (callback) {
          callback();
        }
        this.studentDetailLoading = false;
        this.studentTotal = response.data.total;
      });
    },
    getWorkerList() {
      this.worderDetailLoading = true;
      const params = {
        ...this.queryWorkerParams,
        hospitalTrainingId: this.curActive.id,
      };
      getWorkerPage(params).then((response) => {
        this.worderDetailList = response.data.list || [];
        this.worderDetailLoading = false;
        this.workerTotal = response.data.total;
      });
    },
    handleTabClick(tab, event) {
      // console.log('handleTabClick================', tab, event);
      this.activeTabName = tab.name;
      if (tab.name === "student") {
        this.getStudentList();
      } else {
        this.getWorkerList();
      }
    },
    handleQueryStudentTypeChange(value) {
      this.queryStudentParams.major = null;
      this.simpleMajorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.simpleMajorList = res.data;
      });
    },
    handleJoin(row) {
      const params = {
        hospitalTrainingId: row.hospitalTrainingId,
        userId: row.userId,
      };
      confirmJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        if (this.activeTabName === "student") {
          this.getStudentList();
        } else {
          this.getWorkerList();
        }
      });
    },
    handleRevoke(row) {
      const params = {
        hospitalTrainingId: row.hospitalTrainingId,
        userId: row.userId,
      };
      revokeJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        if (this.activeTabName === "student") {
          this.getStudentList();
        } else {
          this.getWorkerList();
        }
      });
    },
    handleDelUser(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(() => {
          this.handleDelUserFun(row);
        })
        .catch(() => {});
    },
    handleDelUserFun(row) {
      const params = {
        hospitalTrainingUserId: row.id,
      };
      deleteTeachingUser(params).then((res) => {
        this.$modal.msgSuccess("删除成功");
        if (this.activeTabName === "student") {
          this.getStudentList();
        } else {
          this.getWorkerList();
        }
      });
    },
    // 查看评分情况
    handleScoreClick(item) {
      this.$refs.appraiseScoreDialog.openScoreDialog({
        ...item,
        activeType: item.trainingType,
      });
    },
    exportStudent() {},
    exportWorker() {},
    viewSuperviseRecord(row) {
      this.curActive = row;
      this.recordListVisible = true;
    },
    viewEvaluateRecord(row) {
      this.curActive = row;
      this.evaluateListVisible = true;
      getEvaluateRecordPage({
        pageNo: 1,
        pageSize: 999,
        hospitalTrainingId: row.id,
      }).then((res) => {
        this.evaluateRecordList = res.data.list;
      });
    },
    handleFeedback(row) {
      this.curEvaluateRecord = row;
      this.evaluateFeedback = row.feedback;
      this.evaluateFeedbackVisible = true;

      if (row.evaluatetId) {
        getHospitalTrainingEvaluate(row.evaluatetId).then((response) => {
          const { data = {} } = response;
          const list = JSON.parse(JSON.stringify(this.evaluateData));
          list.forEach((item) => {
            if (item.projectName) {
              item.evaluateItem = data["evaluateItem" + item.id];
            }
            if (item.sumText) {
              item.evaluateTotal = data.evaluateTotal;
            }
            if (item.evlTitle) {
              item.evaluateComments = data.evaluateComments;
            }
          });
          this.livePhotos = data.livePhotos;
          this.curUserName = data.evaluateUserName;

          this.evaluateData = list;
        });
      }
    },
    submitEvaluateFeedback() {
      saveEvaluateFeedback({
        id: this.curEvaluateRecord.evaluatetId,
        feedback: this.evaluateFeedback,
      }).then(() => {
        this.$message.success("提交反馈成功！");
        this.evaluateFeedbackVisible = false;
        this.curEvaluateRecord.feedback = this.evaluateFeedback;
      });
    },
    // 活动自评
    handleSelfAssessment(row) {
      this.selfAssessmentForm.id = row.id;
      this.selfAssessmentForm.name = row.name;
      getHospitalTraining(row.id).then((res) => {
        this.selfAssessmentForm.selfAssessment = res.data?.selfAssessment || "";
      });
      this.openActiveSelfAssessment = true;
    },
    submitSelfAssessment() {
      updateSelfAssessment(this.selfAssessmentForm).then(() => {
        this.$message.success("发布活动自评成功！");
        this.openActiveSelfAssessment = false;
      });
    },
    /** 学员导出按钮操作 */
    handleStudentExport() {
      // 处理查询参数
      let params = { ...this.queryStudentParams };
      params.hospitalTrainingId = this.curActive.id;
      this.$modal
        .confirm("是否确认导出所有学员参加详情?")
        .then(() => {
          this.studentExportLoading = true;
          return exportUserStudentExcel(params);
        })
        .then((response) => {
          this.$download.excel(
            response,
            this.curActive.name + "学员参加情况.xlsx"
          );
          this.studentExportLoading = false;
        })
        .catch(() => {});
    },
    /** 职工导出按钮操作 */
    handleWorkerExport() {
      // 处理查询参数
      let params = { ...this.queryWorkerParams };
      params.hospitalTrainingId = this.curActive.id;
      this.$modal
        .confirm("是否确认导出所有职工参加详情?")
        .then(() => {
          this.workerExportLoading = true;
          return exportUserWorkerExcel(params);
        })
        .then((response) => {
          this.$download.excel(
            response,
            this.curActive.name + "职工参加情况.xlsx"
          );
          this.workerExportLoading = false;
        })
        .catch(() => {});
    },
    /** 合并表格单元格 */
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: 2,
            colspan: 1,
          };
        } else if (rowIndex === 2) {
          return {
            rowspan: 6,
            colspan: 1,
          };
        } else if (rowIndex === 8) {
          return {
            rowspan: 4,
            colspan: 1,
          };
        } else if (rowIndex === 12) {
          return {
            rowspan: 2,
            colspan: 1,
          };
        } else if (rowIndex === 14) {
          return {
            rowspan: 2,
            colspan: 1,
          };
        } else if (rowIndex === 16) {
          return {
            rowspan: 1,
            colspan: 4,
          };
        } else if (rowIndex === 17) {
          return {
            rowspan: 1,
            colspan: 2,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
      if (columnIndex === 1) {
        if (rowIndex === 17) {
          return {
            rowspan: 1,
            colspan: 3,
          };
        }
      }
    },
    /** 导入按钮操作 */
    handleStudentImport() {
      this.upload.title = "学员导入";
      this.upload.open = true;
      this.upload.url =
        process.env.VUE_APP_BASE_API +
        "/admin-api/rotation/hospital-training-user/import-user-student";
    },
    /** 导入按钮操作 */
    handleWorkerImport() {
      this.upload.title = "职工导入";
      this.upload.open = true;
      this.upload.url =
        process.env.VUE_APP_BASE_API +
        "/admin-api/rotation/hospital-training-user/import-user-work";
    },
    /** 导出按钮操作 */
    handleExportTemplate() {
      if (this.upload.title === "学员导入") {
        exportStudentTemplate()
          .then((response) => {
            this.$download.excel(response, "学员导入模版.xlsx");
          })
          .catch(() => {});
      }
      if (this.upload.title === "职工导入") {
        exportWorkTemplate()
          .then((response) => {
            this.$download.excel(response, "职工导入模版.xlsx");
          })
          .catch(() => {});
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      let text = "创建成功数量：" + data.createUsernames.length;
      for (const name of data.createUsernames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      }
      text += "<br />更新成功数量：" + data.updateUsernames.length;
      for (const name of data.updateUsernames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      }
      text +=
        "<br />更新失败数量：" + Object.keys(data.failureUsernames).length;
      for (const name in data.failureUsernames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          name +
          "：" +
          data.failureUsernames[name];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      if (this.activeTabName === "student") {
        this.getStudentList();
      } else {
        this.getWorkerList();
      }
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="scss">
.hospitalTraining-dialog {
  .el-dialog__body {
    padding-right: 100px;
  }
}

.student-tab {
  .btns-box {
    margin-bottom: 10px;
    text-align: right;
  }
}

.student-detail-dialog {
  .el-dialog__body {
    padding-top: 10px;

    .el-tabs__header {
      margin-bottom: 0;
    }

    .el-tabs__content {
      padding-top: 15px;
      height: calc(100vh - 220px);
      overflow-y: auto;
      margin-right: -15px;
      padding-right: 15px;
    }
  }
}
</style>

<style lang="scss" scoped>
.rate-wrapper {
  position: relative;
}

.rate-click {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
</style>
