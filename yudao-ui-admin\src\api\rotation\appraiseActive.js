import request from '@/utils/request'

// 创建活动评价项
export function createAppraiseActiveItem(data) {
  return request({
    url: '/rotation/appraise-active/create',
    method: 'post',
    data: data
  })
}

// 更新活动评价项
export function updateAppraiseActiveItem(data) {
  return request({
    url: '/rotation/appraise-active/update',
    method: 'put',
    data: data
  })
}

// 删除活动评价项
export function deleteAppraiseActiveItem(id) {
  return request({
    url: '/rotation/appraise-active/delete?id=' + id,
    method: 'delete'
  })
}

// 获得活动评价项
export function getAppraiseActiveItem(id) {
  return request({
    url: '/rotation/appraise-active/get?id=' + id,
    method: 'get'
  })
}

// 获得活动评价项分页
export function getAppraiseActiveItemPage(query) {
  return request({
    url: '/rotation/appraise-active/page',
    method: 'get',
    params: query
  })
}

// 导出活动评价项 Excel
export function exportAppraiseActiveItemExcel(query) {
  return request({
    url: '/rotation/appraise-active/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得学员评价科室表单
export function getAppraiseForm(query) {
  return request({
    url: '/rotation/appraise-active/get-form',
    method: 'get',
    params: query
  })
}

export function getAppraiseResult(query) {
  return request({
    url: '/rotation/appraise-active-result/get',
    method: 'get',
    params: query
  })
}

export function createAppraise(data) {
  return request({
    url: '/rotation/appraise-active-result/create',
    method: 'post',
    data: data
  })
}

// 保存活动评价结果反馈
export function saveFeedback(data) {
  return request({
    url: '/rotation/appraise-active-result/save',
    method: 'post',
    data
  })
}

// 更新活动评价须知
export function updateAppraiseActiveNote(data) {
  return request({
    url: '/rotation/appraise-active-notice/update',
    method: 'put',
    data
  })
}

// 获得活动评价须知
export function getAppraiseActiveNote(activeType) {
  return request({
    url: '/rotation/appraise-active-notice/get',
    method: 'get',
    params: { activeType }
  })
}
