import request from '@/utils/request'

// 创建师资评优计划申请人员
export function createEvaluationPlanUser(data) {
  return request({
    url: '/teachers/evaluation-plan-user/create',
    method: 'post',
    data: data
  })
}

// 更新师资评优计划申请人员
export function updateEvaluationPlanUser(data) {
  return request({
    url: '/teachers/evaluation-plan-user/update',
    method: 'put',
    data: data
  })
}

// 删除师资评优计划申请人员
export function deleteEvaluationPlanUser(id) {
  return request({
    url: '/teachers/evaluation-plan-user/delete?id=' + id,
    method: 'delete'
  })
}

// 获得师资评优计划申请人员
export function getEvaluationPlanUser(id) {
  return request({
    url: '/teachers/evaluation-plan-user/get?id=' + id,
    method: 'get'
  })
}

// 获得师资评优计划申请人员分页
export function getEvaluationPlanUserPage(query) {
  return request({
    url: '/teachers/evaluation-plan-user/page',
    method: 'get',
    params: query
  })
}

// 导出师资评优计划申请人员 Excel
export function exportEvaluationPlanUserExcel(query) {
  return request({
    url: '/teachers/evaluation-plan-user/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
