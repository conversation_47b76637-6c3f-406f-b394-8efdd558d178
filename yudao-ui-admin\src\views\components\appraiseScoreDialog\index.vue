<template>
  <div>
    <el-dialog
      title="评价得分情况"
      :visible.sync="openScore"
      width="900px"
      v-dialog-drag
      append-to-body
      custom-class="appraise-score-dialog"
    >
      <el-tabs style="padding-bottom: 20px">
        <el-tab-pane label="指标平均得分">
          <el-form inline>
            <el-form-item label="评价对象：" style="margin-right: 30px">{{
              appraiseObject
            }}</el-form-item>
            <el-form-item label="授课平均分：">{{
              synthesizeScore
            }}</el-form-item>
          </el-form>
          <el-table style="margin-bottom: 20px" :data="indexScoreList">
            <el-table-column
              label="序号"
              type="index"
              align="center"
            ></el-table-column>
            <el-table-column
              :label="appraiseActiveType === '2' ? '培训类型' : '活动类型'"
              align="center"
              prop="activeType"
              v-if="currentActive && currentActive.activeType"
            >
              <template slot-scope="scope">
                <dict-tag
                  :type="
                    appraiseActiveType === '2'
                      ? DICT_TYPE.TEACHING_TRAINING_TYPE
                      : DICT_TYPE.ROTATION_ACTIVE_TYPE
                  "
                  :value="currentActive && currentActive.activeType"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="评价指标"
              prop="appraiseKpi"
              align="center"
            ></el-table-column>
            <el-table-column
              label="指标得分"
              prop="score"
              align="center"
            ></el-table-column>
          </el-table>

          <el-form v-if="feedback">
            <el-form-item label="教师对学员评价的集中反馈">
              <el-input
                v-model="indexFeedback"
                type="textarea"
                :rows="3"
                placeholder="请先查看学员对您的评价，针对学员对您的评价做出反馈！"
              ></el-input>
            </el-form-item>
            <el-button type="primary" @click="submitIndexFeedback"
              >提交</el-button
            >
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          :label="`${studentTeacherAppraise ? '用户' : '学员'}评分详情`"
        >
          <el-button
            class="exportBtn"
            type="primary"
            @click="handleExport"
            :loading="exportLoading"
            >导出</el-button
          >
          <el-form inline>
            <el-form-item label="评价对象：" style="margin-right: 30px">{{
              appraiseObject
            }}</el-form-item>
            <el-form-item label="综合得分：">{{
              synthesizeScore
            }}</el-form-item>
          </el-form>
          <el-table :data="studentScoreList">
            <el-table-column
              :label="`${studentTeacherAppraise ? '用户' : '学员'}姓名`"
              prop="nickname"
              align="center"
            ></el-table-column>
            <el-table-column
              label="用户类型"
              prop="userType"
              align="center"
              v-if="studentTeacherAppraise"
            >
              <template v-slot="scope">
                <dict-tag
                  :type="DICT_TYPE.SYSTEM_USER_TYPE"
                  :value="scope.row.userType"
                ></dict-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="学员类型"
              prop="studentType"
              align="center"
              v-else
            >
              <template v-slot="scope">
                <dict-tag
                  :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                  :value="scope.row.studentType"
                ></dict-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="轮转科室"
              prop="rotationDepartmentName"
              align="center"
              v-if="appraiseActiveType !== '2'"
            ></el-table-column>
            <el-table-column label="评价得分" prop="score" align="center">
              <template v-slot="scope">
                <el-link
                  type="primary"
                  @click.native="showAppraiseDetail(scope.row)"
                  >{{ scope.row.score }}</el-link
                >
              </template>
            </el-table-column>
            <el-table-column
              label="评价建议"
              prop="comments"
              align="center"
              min-width="150"
            ></el-table-column>
            <el-table-column
              label="评价时间"
              prop="createTime"
              align="center"
              width="170"
            >
              <template v-slot="scope">
                {{ new Date(scope.row.createTime).toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" v-if="feedback">
              <template v-slot="scope">
                <el-link
                  v-if="!scope.row.feedback"
                  type="primary"
                  @click.native="showSpeakFeedback(scope.row)"
                >
                  主讲反馈
                </el-link>
                <span v-else>已反馈</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="studentScoreTotal > 0"
            :total="studentScoreTotal"
            :page.sync="studentScoreQuery.pageNo"
            :limit.sync="studentScoreQuery.pageSize"
            @pagination="queryAppraiseDetails"
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog
      title="主讲反馈"
      :visible.sync="openSpeakerFeedback"
      width="400px"
      append-to-body
    >
      <el-input
        type="textarea"
        placeholder="请在此填写您的评价反馈"
        :rows="3"
        v-model="speakerFeedback"
      ></el-input>
      <template v-slot:footer>
        <el-button type="primary" @click="sureSpeakerFeedback">确定</el-button>
        <el-button @click="cancelSpeakerFeedback">取消</el-button>
      </template>
    </el-dialog>

    <appraise-dialog
      v-if="appraiseDetailData"
      :title="appraiseTitle"
      :open="openAppraiseDetail"
      :data="appraiseDetailData"
      disabled
      @setOpen="openAppraiseDetail = $event"
    ></appraise-dialog>
  </div>
</template>

<script>
import {
  getAppraiseDetails,
  getComprehensiveByParam,
  saveFeedbackResult,
  exportAppraiseDetailsExcel,
  exportHospitalTrainingDetails,
} from "@/api/rotation/appraiseActiveFeedbackResult";
import { getAppraiseResult, saveFeedback } from "@/api/rotation/appraiseActive";
import AppraiseDialog from "@/views/rotation/studentTeachingActive/appraiseDialog";

export default {
  name: "appraiseScoreDialog",
  components: { AppraiseDialog },
  props: {
    appraiseActiveType: {
      type: String,
      required: true,
    },
    feedback: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      openScore: false,
      currentActive: null,
      appraiseObject: null,
      synthesizeScore: "",
      indexFeedbackId: "",
      appraiseActiveId: "",
      indexScoreList: [],
      indexFeedback: "",
      studentScoreQuery: {
        pageNo: 1,
        pageSize: 10,
      },
      studentScoreTotal: 0,
      studentScoreList: [],
      handleStudentScore: null,
      openSpeakerFeedback: false,
      speakerFeedback: "",
      appraiseDetailData: null,
      appraiseTitle: "",
      openAppraiseDetail: false,
      exportLoading: false,
    };
  },
  computed: {
    studentTeacherAppraise() {
      return ["2"].includes(this.appraiseActiveType);
    },
  },
  methods: {
    // 查看评价得分情况
    openScoreDialog(item) {
      if (!item.evaluationScore) return;
      this.openScore = true;
      this.studentScoreQuery.pageNo = 1;
      this.currentActive = item;
      const { id, activeType } = item;
      getComprehensiveByParam({
        activeId: id,
        activeType: activeType || "",
        appraiseActiveType: this.appraiseActiveType,
      }).then((res) => {
        this.appraiseObject = res.data.targetObject;
        this.synthesizeScore = res.data.score;
        this.indexFeedbackId = res.data.id;
        this.appraiseActiveId = res.data.appraiseActiveId;
        this.indexFeedback = res.data.feedback;
        this.indexScoreList = res.data.resultItemRespVOS;
      });
      this.queryAppraiseDetails();
    },
    // 获取学员评分详情
    queryAppraiseDetails() {
      const { pageNo, pageSize } = this.studentScoreQuery;
      const { id, activeType } = this.currentActive;
      getAppraiseDetails({
        activeId: id,
        activeType: activeType || "",
        appraiseActiveType: this.appraiseActiveType,
        pageNo,
        pageSize,
      }).then((res) => {
        this.studentScoreList = res.data.list;
        this.studentScoreTotal = res.data.total;
      });
    },
    // 导出
    handleExport() {
      // 处理查询参数
      const { pageNo, pageSize } = this.studentScoreQuery;
      const { id, activeType } = this.currentActive;
      const params = {
        activeId: id,
        activeType,
        appraiseActiveType: this.appraiseActiveType,
        pageNo,
        pageSize,
      };
      this.$modal
        .confirm(
          `是否确认导出${
            this.appraiseActiveType === "2" ? "用户" : "学员"
          }评价详情?`
        )
        .then(() => {
          this.exportLoading = true;
          if (this.appraiseActiveType === "2") {
            return exportHospitalTrainingDetails(params);
          } else {
            return exportAppraiseDetailsExcel(params);
          }
        })
        .then((response) => {
          this.$download.excel(
            response,
            `${this.appraiseActiveType === "2" ? "用户" : "学员"}评价详情.xlsx`
          );
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    // 提交活动评价综合反馈结果
    submitIndexFeedback() {
      if (!this.indexFeedback) {
        this.$message.warning("请先填写综合反馈！");
        return;
      }
      const { id, activeType } = this.currentActive;
      saveFeedbackResult({
        id: this.indexFeedbackId,
        activeId: id,
        appraiseActiveId: this.appraiseActiveId,
        activeType,
        appraiseActiveType: this.appraiseActiveType,
        feedback: this.indexFeedback,
      }).then(() => {
        this.$message.success("提交综合反馈成功！");
      });
    },
    // 主讲反馈
    showSpeakFeedback(row) {
      this.handleStudentScore = row;
      this.openSpeakerFeedback = true;
    },
    cancelSpeakerFeedback() {
      this.openSpeakerFeedback = false;
      this.handleStudentScore = null;
      this.speakerFeedback = "";
    },
    sureSpeakerFeedback() {
      if (!this.speakerFeedback) {
        this.$message.warning("请输入评价反馈再提交！");
        return;
      }
      saveFeedback({
        id: this.handleStudentScore.id,
        feedback: this.speakerFeedback,
      }).then(() => {
        this.$message.success("保存主讲反馈成功！");
        this.queryAppraiseDetails();
        this.openSpeakerFeedback = false;
      });
    },
    // 显示评价详情
    showAppraiseDetail(row) {
      getAppraiseResult({ id: row.id }).then((response) => {
        this.appraiseDetailData = response.data;
        this.openAppraiseDetail = true;
        this.appraiseTitle = `查看评价-${row.nickname}`;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.appraise-score-dialog {
  .el-tabs__content {
    position: relative;
  }
  .exportBtn {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>
