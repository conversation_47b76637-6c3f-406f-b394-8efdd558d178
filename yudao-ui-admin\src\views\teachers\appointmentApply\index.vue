<template>
  <div class="app-container">
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['teachers:appointment-apply:create']">新增申请</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="申请人" align="center" prop="nickname" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="申请岗位" align="center" prop="applyPosition">
        <template slot-scope="scope">{{ getMatchedLabel(roleOptions, scope.row.applyPosition, "id", "name") }}</template>
      </el-table-column>
      <el-table-column label="任教学员类型" align="center" prop="studentTypeNames" min-width="180" />
      <el-table-column label="任职科室" align="center" prop="teachDepartmentNames" min-width="180" />
      <el-table-column label="任职开始日期" align="center" prop="teachBeginDate" width="120" />
      <el-table-column label="任职结束日期" align="center" prop="teachEndDate" width="120" />
      <el-table-column label="申请状态" align="center" prop="applyResult">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.TEACHERS_APPOINTMENT_APPLY_RESULT" :value="scope.row.applyResult" />
        </template>
      </el-table-column>
      <el-table-column label="聘任结果" align="center" prop="selectionResult">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.TEACHERS_APPOINTMENT_SELECTION_RESULT" :value="scope.row.selectionResult" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看审批</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog title="遴选说明" :visible.sync="tipOpen" width="500px" append-to-body>
      <div>{{ selectionConfig.isEnabled ? selectionConfig.remarks : "尚未到师资聘任开始时间，请留意院内通知公告！" }}</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConditionAdd" v-if="selectionConfig.isEnabled">我已知悉，继续申请</el-button>
        <el-button @click="tipOpen = false">{{ selectionConfig.isEnabled ? "我再想想" : "关闭" }}</el-button>
      </div>
    </el-dialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="800px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" inline label-width="108px" label-position="left">
        <el-form-item class="half-item-left" label="申请人" prop="applyUserId">
          <el-input v-model="userProfile.nickname" disabled />
        </el-form-item>
        <el-form-item class="half-item-right" label="用户名" prop="applyUserId">
          <el-input v-model="userProfile.username" disabled />
        </el-form-item>
        <el-form-item class="half-item-left" label="申请岗位" prop="applyPosition">
          <el-select v-model="form.applyPosition" placeholder="请输入申请岗位">
            <el-option v-for="item in positionList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="half-item-right" label="任教学员类型" prop="teachStudentTypes">
          <el-select v-model="form.teachStudentTypes" placeholder="请输入任教学员类型" multiple filterable clearable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item class="half-item-left" label="任职科室" prop="teachDepartments">
          <el-select v-model="form.teachDepartments" placeholder="请输入任职科室" multiple filterable clearable>
            <el-option v-for="item in departmentOptions" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item class="half-item-right" label="任职开始日期" prop="teachBeginDate" required>
          <el-date-picker v-model="form.teachBeginDate" type="date" value-format="timestamp" placeholder="选择任职开始日期" />
        </el-form-item>
        <el-form-item class="half-item-left" label="任职结束日期" prop="teachEndDate" required>
          <el-date-picker v-model="form.teachEndDate" type="date" value-format="timestamp" placeholder="选择任职结束日期" />
        </el-form-item>
        <el-form-item label="申请材料" prop="applicationDocument">
          <file-upload-info v-model="form.applicationDocument" :file-type="['png', 'jpg', 'jpeg', 'pdf']" :limit="99" :file-size="50"></file-upload-info>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitForm">提 交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createAppointmentApply, getAppointmentApplyPage } from "@/api/teachers/appointmentApply";
import { getSelectionConfig } from '@/api/teachers/selectionConfig'
import { getUserProfile } from '@/api/system/user'
import { listSimpleRoles } from "@/api/system/role";
import { getDepartmentSimpleList } from "@/api/system/department";
import FileUploadInfo from '@/components/FileUploadInfo'

export default {
  name: "AppointmentApply",
  components: { FileUploadInfo },
  data() {
    const validBeginDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error('任职开始日期不能为空'));
      } else {
        if (this.form.teachEndDate) {
          this.$refs.form.validateField('teachEndDate')
        }
        callback()
      }
    }

    const validEndDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error('任职结束日期不能为空'));
      } else if (new Date(value).getTime() < new Date(this.form.teachBeginDate).getTime()) {
        callback(new Error('任职结束日期不能小于开始日期'));
      } else {
        callback()
      }
    }
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 师资聘用申请列表
      list: [],
      // 师资遴选配置
      selectionConfig: {},
      // 当前用户信息
      userProfile: {},
      // 角色列表
      roleOptions: [],
      // 科室列表
      departmentOptions: [],
      // 是否显示遴选说明弹出层
      tipOpen: false,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        applyPosition: null,
        applyResult: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        applyUserId: [{ required: true, message: "申请人员不能为空", trigger: "blur" }],
        applyPosition: [{ required: true, message: "申请岗位不能为空", trigger: "blur" }],
        teachStudentTypes: [{ required: true, message: "任教学员类型不能为空", trigger: "blur" }],
        teachDepartments: [{ required: true, message: "任职科室不能为空", trigger: "blur" }],
        teachBeginDate: [ { validator: validBeginDate, trigger: "change" }],
        teachEndDate: [{ validator: validEndDate, trigger: "change" }],
        applicationDocument: [{ required: true, message: "申请材料不能为空", trigger: "blur" }],
      },
      // 提交中
      submitLoading: false,
    };
  },
  computed: {
    positionList() {
      const positions = this.selectionConfig.selectionPositions;
      const positionArr = positions ? positions.split(",").map(n => +n) : [];
      return this.roleOptions.filter(item => positionArr.includes(item.id));
    }
  },
  created() {
    this.getList();
    getUserProfile().then(response => {
      this.userProfile = response.data
    });
    listSimpleRoles().then(response => {
      const excludeRoleCodes = ["super_admin", "admin", "student", "hospital_admin", "recruitment_user"];
      this.roleOptions = response.data.filter(item => !excludeRoleCodes.includes(item.code));
    });
    getDepartmentSimpleList(0).then((response) => {
      this.departmentOptions = response.data;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getAppointmentApplyPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        applyUserId: this.userProfile.id,
        applyPosition: undefined,
        teachStudentTypes: [],
        teachDepartments: [],
        teachBeginDate: undefined,
        teachEndDate: undefined,
        applicationDocument: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      getSelectionConfig().then(response => {
        this.selectionConfig = response.data;
        this.tipOpen = true;
      });
    },
    /** 新增按钮操作 */
    handleConditionAdd() {
      this.reset();
      this.open = true;
      this.title = "添加师资聘用申请";
      this.tipOpen = false;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        this.submitLoading = true;
        const params = { ...this.form };
        params.teachStudentTypes = params.teachStudentTypes.join(",");
        params.teachDepartments = params.teachDepartments.join(",");
        params.applicationDocument = JSON.stringify(params.applicationDocument);
        createAppointmentApply(params).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        }).finally(() => this.submitLoading = false);
      });
    },
    handleView(row) {
      this.$router.push({
        path: "/bpm/process-instance/detail?id=" + row.processInstanceId
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.half-item-left {
  width: 50%;
  padding-right: 10px;
  margin-right: 0;

  ::v-deep .el-select, ::v-deep .el-input, ::v-deep .el-date-picker {
    width: 250px;
  }
}

.half-item-right {
  width: 50%;
  padding-left: 10px;
  margin-right: 0;

  ::v-deep .el-select, ::v-deep .el-input, ::v-deep .el-date-picker {
    width: 250px;
  }
}
</style>
