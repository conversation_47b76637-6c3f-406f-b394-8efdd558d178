<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      class="filter-form"
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="试卷名称" prop="paperName">
        <el-input
          v-model="queryParams.paperName"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考生姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="得分高于" prop="higherThanScore">
        <el-input
          v-model="queryParams.higherThanScore"
          placeholder="请输入"
          type="number"
          clearable
          min="0"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="得分低于" prop="belowScore">
        <el-input
          v-model="queryParams.belowScore"
          placeholder="请输入"
          type="number"
          clearable
          min="0"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="试卷类型">
        <el-select v-model="queryParams.exameTypes" clearable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.EXAM_TYPE)"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="交卷时间" prop="completeDates">
        <el-date-picker
          v-model="queryParams.completeDates"
          type="daterange"
          clearable
          value-format="yyyy-MM-dd"
          start-placeholder="起始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-delete" size="mini" @click="handleDelete">批量删除</el-button>
      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload2"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
          >导出成绩</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload2"
          size="mini"
          :loading="exportPaperLoading"
          @click="handlePaperExport"
          >导出试卷</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column label="姓名" align="center" prop="nickname" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column
        label="试卷名称"
        align="center"
        prop="paperName"
        min-width="150px"
      />
      <el-table-column label="总分" align="center" prop="paperTotalScore" />
      <el-table-column label="及格分" align="center" prop="paperPassScore" />
      <el-table-column
        label="客观题得分"
        align="center"
        prop="objectiveScore"
      />
      <el-table-column
        label="主观题得分"
        align="center"
        prop="subjectiveScore"
      />
      <el-table-column label="得分" align="center" prop="score" />
      <el-table-column
        label="答题时间"
        align="center"
        prop="answerMinuteTime"
      />
      <el-table-column
        label="交卷时间"
        align="center"
        prop="endTime"
        width="180px"
      >
        <template v-slot="scope">{{ parseTime(scope.row.endTime) }}</template>
      </el-table-column>
      <el-table-column label="切屏次数" align="center" prop="cutScreenNum" />
      <el-table-column label="操作" align="center" width="200px">
        <template v-slot="scope">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-view"
            @click="handleView(scope.row.id)"
          >
            查看试卷
          </el-button>
          <el-button
            v-if="scope.row.isContainSubjective"
            type="text"
            size="mini"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
            v-hasPermi="['exam:exam-result:update-subjective-score']"
          >
            修改主观题得分
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="修改主观题得分"
      :visible.sync="open"
      width="680px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" label-width="90px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="试卷名称：" prop="paperName">
              <span>{{ form.paperName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="姓名：" prop="nickname">
              <span>{{ form.nickname }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="用户名：" prop="username">
              <span>{{ form.username }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="subjectiveScore-box">
        <span>
          本试卷主观题共计{{ totalSubjectiveScore }}分，当前主观题得分：
          <span class="blue">
            {{ form.subjectiveScore || 0 }}
          </span>
          分，本次修改为：
        </span>
        <el-input-number
          v-model="subjectiveScore"
          :step="0.1"
          placeholder="请输入主观题得分"
          style="width: 200px"
        ></el-input-number>
        <span style="padding-left: 5px">分</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getExamScorePage,
  exportExamScoreExcel,
  exportExamPaperZip,
  updateSubjectiveScore,
  getSubjectiveTotalScore,
} from "@/api/exam/paper";
import { Step } from "element-ui";

export default {
  name: "exam-score",
  data() {
    return {
      queryParams: {
        paperName: "",
        nickname: "",
        username: "",
        higherThanScore: undefined,
        belowScore: undefined,
        exameTypes: [],
        completeDates: [],
        pageNo: 1,
        pageSize: 10,
      },
      showSearch: true,
      loading: false,
      list: [],
      total: 0,
      selection: [],
      exportLoading: false,
      exportPaperLoading: false,
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      totalSubjectiveScore: 0,
      subjectiveScore: undefined,
    };
  },
  methods: {
    getList() {
      this.loading = true;
      getExamScorePage(this.queryParams).then((res) => {
        this.list = res.data.list;
        this.total = res.data.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.getList();
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.form = {};
      this.subjectiveScore = undefined;
    },
    handleEdit(row) {
      this.form = { ...row };
      getSubjectiveTotalScore({ id: this.form.id }).then((res) => {
        this.totalSubjectiveScore = res.data;
        this.open = true;
      });
    },
    handleView(id) {
      const url = this.$router.resolve({
        path: "/examResult",
        query: { id, from: "admin" },
      }).href;
      window.open(url, "_blank");
    },
    handleSelectionChange(value) {
      this.selection = value;
    },
    handleExport() {
      this.exportLoading = true;
      const ids = this.selection.map((item) => item.id);
      exportExamScoreExcel({ ...this.queryParams, ids }).then((response) => {
        this.$download.excel(response, "考试成绩.xlsx");
        this.exportLoading = false;
      });
    },
    handlePaperExport() {
      this.exportPaperLoading = true;
      const ids = this.selection.map((item) => item.id);
      exportExamPaperZip({ ...this.queryParams, ids }).then((response) => {
        this.$download.excel(response, "考试试卷.zip");
        this.exportPaperLoading = false;
      });
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }

        if (
          this.subjectiveScore === null ||
          this.subjectiveScore === "" ||
          this.subjectiveScore === undefined
        ) {
          this.$modal.msgError("请输入主观题得分");
          return;
        }

        updateSubjectiveScore({
          id: this.form.id,
          subjectiveScore: this.subjectiveScore,
        }).then((response) => {
          this.$modal.msgSuccess("修改成功");
          this.cancel();
          this.getList();
        });
      });
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.filter-form {
  ::v-deep .el-select,
  ::v-deep .el-input,
  ::v-deep .el-input-number {
    width: 200px;
  }

  ::v-deep .el-date-editor--daterange {
    width: 240px;
  }
}

.subjectiveScore-box {
  display: flex;
  align-items: center;

  .blue {
    color: #409eff;
  }
}
</style>
