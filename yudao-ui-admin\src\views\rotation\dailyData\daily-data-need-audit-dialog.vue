<template>
  <el-dialog title="待审核数据" width="1000px" :visible="visible" @close="$emit('close')">
    <el-form inline>
      <el-form-item class="mr" label="学员姓名">{{ studentInfo.nickname }}</el-form-item>
      <el-form-item class="mr" label="轮转科室">{{ studentInfo.rotationDepartmentName }}</el-form-item>
      <el-form-item class="mr" label="待审核数">{{ studentInfo.auditCnt }}</el-form-item>
      <el-form-item label="轮转数据项">
        <el-select v-model="auditQuery.rotationItem" size="small" @change="queryNeedAuditData">
          <el-option label="全部" value=""></el-option>
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTAION_ITEM)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
    </el-form>

    <el-button style="float: right; margin-bottom: 10px;" type="primary" @click="handlePassAll">一键审核</el-button>

    <el-table :data="auditData">
      <el-table-column label="轮转数据项" prop="rotaionItem" align="center">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.ROTAION_ITEM" :value="scope.row.rotaionItem" />
        </template>
      </el-table-column>
      <el-table-column label="病人姓名" prop="patientName" align="center"></el-table-column>
      <el-table-column label="病历号" prop="medicalCode" align="center"></el-table-column>
      <el-table-column label="填写日期" prop="createTime" align="center" width="150px">
        <template v-slot="scope">
          {{ scope.row.createTime && new Date(scope.row.createTime).toLocaleString() }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="scope">
          <el-button type="text" @click="viewDetail(scope.row)">查看详情</el-button>
          <el-button type="text" @click="handlePass(scope.row.id)">通过</el-button>
          <el-button type="text" @click="handleNoPass(scope.row.id)">不通过</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
import {
  allDailyDataAuditSuccess,
  dailyDataAuditFail,
  dailyDataAuditSuccess,
  getDailyDataStudentInfo,
  getNeedAuditDailyData
} from '@/api/rotation/dailyData'

export default {
  name: 'daily-data-need-audit-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    scheduleDetailsId: Number | String,
  },
  data() {
    return {
      auditQuery: {
        scheduleDetailsId: "",
        rotationItem: "",
      },
      studentInfo: {},
      auditData: [],
    };
  },
  methods: {
    queryStudentInfo() {
      getDailyDataStudentInfo(this.auditQuery).then(res => this.studentInfo = res?.data || {});
    },
    queryNeedAuditData() {
      getNeedAuditDailyData(this.auditQuery).then(res => {
        this.auditData = res.data;
      });
    },
    viewDetail(row) {
      this.$emit("show-detail", row);
    },
    handlePass(id) {
      this.$confirm("确定审核通过该数据吗？", "提示").then(() => {
        dailyDataAuditSuccess(id).then(() => {
          this.queryStudentInfo();
          this.queryNeedAuditData();
        });
      });
    },
    handleNoPass(id) {
      this.$confirm("确定审核不通过该数据吗？", "提示").then(() => {
        dailyDataAuditFail(id).then(() => {
          this.queryStudentInfo();
          this.queryNeedAuditData();
        });
      });
    },
    handlePassAll() {
      this.$confirm("确定全部审核通过吗？", "提示").then(() => {
        const { scheduleDetailsId, rotationItem } = this.auditQuery
        allDailyDataAuditSuccess(scheduleDetailsId, rotationItem).then(() => {
          this.queryStudentInfo();
          this.queryNeedAuditData();
        });
      });
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.auditQuery.scheduleDetailsId = this.scheduleDetailsId;
        this.queryStudentInfo();
        this.queryNeedAuditData();
      }
    },
  },
}
</script>

<style scoped lang="scss">

</style>
