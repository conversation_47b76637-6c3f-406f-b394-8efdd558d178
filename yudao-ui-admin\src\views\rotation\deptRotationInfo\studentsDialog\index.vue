<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="900px"
    v-dialogDrag
    append-to-body
    @close="cancel"
  >
    <div class="studentsDialog">
      <el-table :data="studentList" v-loading="loading">
        <el-table-column
          label="姓名"
          prop="nickname"
          align="center"
        ></el-table-column>

        <el-table-column
          label="工号"
          prop="username"
          align="center"
        ></el-table-column>

        <el-table-column
          label="电话"
          prop="mobile"
          align="center"
        ></el-table-column>

        <el-table-column
          label="年级"
          prop="grade"
          align="center"
        ></el-table-column>

        <el-table-column
          label="专业"
          prop="majorName"
          align="center"
        ></el-table-column>

        <el-table-column
          label="轮转时间"
          prop="nickname"
          align="center"
          width="200px"
        >
          <template slot-scope="scope">
            {{ scope.row.rotationBeginTime }} ~ {{ scope.row.rotationEndTime }}
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryStudentParams.pageNo"
        :limit.sync="queryStudentParams.pageSize"
        @pagination="getList"
      />
    </div>
  </el-dialog>
</template>

<script>
import { getJoinUserList } from "@/api/rotation/deptRotationInfo";

export default {
  name: "DeptRotationInfoDialog",
  props: {
    title: {
      type: String,
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    queryParams: {
      type: Object,
      default: () => {},
    },
    curRow: {
      type: Object,
      default: () => {},
    },
    period: {
      type: String,
    },
  },
  data() {
    return {
      open: this.dialogVisible,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 列表
      studentList: [],
      // 查询参数
      queryStudentParams: {
        pageNo: 1,
        pageSize: 10,
      },
    };
  },
  watch: {
    // 当 prop 值变化时，更新本地副本
    dialogVisible(newVal) {
      this.open = newVal;
      if (newVal) {
        this.getList();
      }
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:dialogVisible", false);
    },
    /** 查询列表 */
    getList() {
      this.loading = true;

      const params = {
        ...this.queryParams,
        rotationDepartmentId: this.curRow.rotationDepartmentId,
        rotationDepartmentName: this.curRow.rotationDepartmentName,
        departmentId: this.curRow.departmentId,
        departmentName: this.curRow.departmentName,
        date: this.period,
        ...this.queryStudentParams,
      };
      // 执行查询
      getJoinUserList(params).then((response) => {
        this.studentList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
