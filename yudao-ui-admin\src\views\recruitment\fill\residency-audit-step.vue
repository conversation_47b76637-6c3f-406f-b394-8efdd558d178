<template>
  <div>
    <div class="result-info">
      <span v-if="writtenResult">笔试成绩：{{ writtenResult }}</span>
      <span v-if="interviewResult">面试成绩：{{ interviewResult }}</span>
    </div>

    <el-steps class="residency-audit-step" direction="vertical" :active="stepActive">
      <el-step title="申请材料提交">
        <template slot="description">
          <div class="audit-content">
            <div class="flex-between">
              <span>报名申请：{{ reportInfo.reported ? "已提交" : "未提交" }}</span>
              <span>{{ reportInfo.commitTime }}</span>
            </div>
            <div class="flex-end" v-if="reportInfo.isEnableRevokeReport">
              <el-button type="primary" size="mini" @click="revertApply">撤回报名</el-button>
            </div>
          </div>
        </template>
      </el-step>
      <el-step title="报名信息审核">
        <template slot="description">
          <div class="audit-content">
            <div class="flex-between">
              <span>报名信息审核：{{ reportExamineInfo.result }}</span>
              <span>{{ reportExamineInfo.examineTime }}</span>
            </div>
            <div v-if="reportExamineInfo.recommend">审批意见：{{ reportExamineInfo.recommend }}</div>
            <div class="flex-end" v-if="reportExamineInfo.isEnableBackToUpdate">
              <el-button type="primary" size="mini" @click="modifyApplyInfo">修改报名信息</el-button>
            </div>
          </div>
        </template>
      </el-step>
      <el-step title="专业基地审核">
        <template slot="description">
          <div class="audit-content">
            <div class="flex-between">
              <span>专业基地审核：{{ professionalBaseExamineInfo.isEnable ? professionalBaseExamineInfo.result : '未进行' }}</span>
              <span>{{ professionalBaseExamineInfo.examineTime }}</span>
            </div>
            <div v-if="professionalBaseExamineInfo.recommend">审批意见：{{ professionalBaseExamineInfo.recommend }}</div>
            <div class="flex-end" v-if="professionalBaseExamineInfo.isEnableBackToUpdate">
              <el-button type="primary" size="mini" @click="modifyApplyInfo">修改报名信息</el-button>
            </div>
          </div>
        </template>
      </el-step>
      <el-step title="报名录取确认" finish-status="success">
        <template slot="description">
          <div class="audit-content">
            <div class="flex-between">
              <span>报到确认：{{ enrollExamineInfo.isEnable ? enrollExamineInfo.result : '未进行' }}</span>
              <span v-if="enrollExamineInfo.examineTime">{{ enrollExamineInfo.examineTime }}</span>
            </div>
            <div class="flex-between" v-if="enrollExamineInfo.result === '已发放通知书' && enrollExamineInfo.noticeContent">
              <span>您已通过录取审核，请携带录取通知书前来我院报到。</span>
              <el-button type="primary" size="mini" @click="checkNoticeSend(enrollExamineInfo.noticeContent)">立刻查看录取通知书</el-button>
            </div>
          </div>
        </template>
      </el-step>
      <el-step class="end-step" title="结束"></el-step>
    </el-steps>

    <el-dialog title="" :visible.sync="noticeOpen" width="1000px" v-dialogDrag append-to-body>
      <div ref="noticeContent" id="noticeContent-box" v-html="noticeContent" style="padding: 0 50px;"></div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleDown">下载</el-button>
        <el-button @click="noticeOpen = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getResidencyExamineInfo, revokeRegistration } from '@/api/recruitment/registration'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export default {
  name: 'residency-audit-step',
  props: {
    recruitmentRegistrationId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      planName: "", // 招生计划名称
      reportInfo: {}, // 申请材料步骤
      reportExamineInfo: {}, // 报名信息审核
      professionalBaseExamineInfo: {}, // 专业基地审核
      enrollExamineInfo: {},// 报名录取确认
      isEnd: false,// 结束
      stepActive: 0, // 当前在第几步
      noticeOpen: false,
      noticeContent: "",
      writtenResult: "",
      interviewResult: "",
    }
  },
  methods: {
    checkNoticeSend(html) {
      this.noticeOpen = true;
      this.noticeContent = html;
      this.$nextTick(() => {
        const editableElements = this.$refs.noticeContent.querySelectorAll("[contenteditable='true']");
        editableElements.forEach(element => {
          element.contentEditable = "false";
        });
      });
    },
    handleDown(){
      this.exportPDF('noticeContent-box', '进修人员报道通知书')
    },
    exportPDF(tableId, fileName){
      const table = document.getElementById(tableId);
      html2canvas(table).then(canvas => {
        const contentWidth = canvas .width;
        const contentHeight = canvas.height;
        const pageHeight = contentWidth / 592.28 * 841.89;
        let leftHeight = contentHeight;
        let position = 0;
        const imgWidth = 595.28;
        const imgHeight = 592.28 / contentWidth * contentHeight;
        const pageData = canvas .toDataURL('image/jpeg', 1.0);
        const pdf = new jsPDF( '', 'pt','a4');
        if (leftHeight < pageHeight) {
          pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;

            if (leftHeight > 0) {
              pdf.addPage();
            }
          }
        }
        pdf.save(`${fileName}.pdf`);
      })
    },
    revertApply() {
      revokeRegistration(this.recruitmentRegistrationId).then(() => {
        this.$modal.msgSuccess("撤回报名成功！");
        this.$router.push("/recruitment/registration");
      });
    },
    modifyApplyInfo() {
      this.$emit("modify");
    },
  },
  watch: {
    recruitmentRegistrationId: {
      immediate: true,
      handler(val) {
        getResidencyExamineInfo(val).then(res => {
          this.planName = res.data.planName || "";
          this.reportInfo = res.data.reportInfo || {};
          this.reportExamineInfo = res.data.reportExamineInfo || {};
          this.professionalBaseExamineInfo = res.data.professionalBaseExamineInfo || {};
          this.enrollExamineInfo = res.data.enrollExamineInfo || {};
          this.isEnd = res.data?.isEnd || false;
          this.writtenResult = res.data?.writtenResult || "";
          this.interviewResult = res.data?.interviewResult || "";
          if (res.data.isEnd) {
            this.stepActive = 5;
          } else if (res.data.enrollExamineInfo.isEnable) {
            this.stepActive = 3
          } else if (res.data.professionalBaseExamineInfo.isEnable) {
            this.stepActive = 2;
          } else if (res.data.reportExamineInfo.isEnable) {
            this.stepActive = 1;
          } else {
            this.stepActive = 0;
          }
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.result-info {
  font-size: 14px;
  span {
    display: inline-block;
    padding: 20px 20px 0 0;
  }
}

.residency-audit-step  {
  padding-top: 20px;

  ::v-deep .el-step__description {
    position: relative;
    left: 300px;
    top: -26px;
    width: 600px;
    min-height: 80px;
    border: 1px solid;
    font-size: 14px;
    padding-right: 10px;
    color: #333;
  }

  ::v-deep .el-step__description.is-wait {
    display: none;
  }

  .audit-content {
    padding: 10px;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .flex-end {
    display: flex;
    justify-content: flex-end;
  }
}

.end-step {
  ::v-deep .el-step__description {
    display: none;
  }
}
</style>
