<template>
    <el-dialog :title="title" :visible="open" width="800px" v-dialogDrag append-to-body @close="cancel">
        <div class="appraise-conts">
          <div v-for="(item, index) in list" :key="index" class="edit-item">
            <div class="item-title">{{`${index + 1}、${item.appraiseKpi}`}}</div>
            <div class="item-cont">
              <el-input-number 
                v-if="item.appraise360ItemId" 
                v-model="item.score" 
                :min="0" 
                :max="item.appraise360ItemScore" 
              ></el-input-number>
              <!-- <el-rate
                v-if="item.appraise360ItemId"
                v-model="item.score"
                allow-half
                text-color="#ff9900"
                :max="5"
                :disabled="disabled"
                score-template="{value}">
              </el-rate> -->
              <el-input v-else v-model="item.comments" :disabled="disabled" type="textarea" :placeholder="`请输入${item.appraiseKpi}`" />
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button v-if="!disabled" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">{{!disabled ? '取 消' : '关 闭'}}</el-button>
        </div>
    </el-dialog>
  </template>

  <script>
  import { createAppraise } from "@/api/rotation/studentappraiseteacher";

  export default {
    name: "AppraiseDialog",
    props: {
        title: {
          type: String
        },
        open: {
          type: Boolean,
          default: false
        },
        data: {
          type: Object,
          default: {}
        },
        appraiseSourceId: {
          type: Number
        },
        appraiseTargetId: {
          type: Number
        },
        disabled: {
          type: Boolean,
          default: false
        }
    },
    computed: {
      list() {
        if (this.disabled) {
          const _list = this.data.resultItemRespVOS
          return _list
        }
        return this.data.appraise360ResultItemCreateReqVOS
      },
    },
    data() {
      return {

      };
    },
    created() {
    },
    methods: {
      cancel() {
        this.$emit("setOpen", false)
      },
      submitForm() {
        const list = JSON.parse(JSON.stringify(this.list))
        let scoreFlag = true
        let commentsFlag = true
        list.forEach(item => {
          if (item.appraise360ItemId) {
            if (item.score === null || item.score === undefined) {
              scoreFlag = false
            }
          } else {
            if (!item.comments) {
              commentsFlag = false
            }
          }
        })
        if (!scoreFlag) {
          return this.$modal.msgError("请完成评分");
        }
        if (!commentsFlag) {
          return this.$modal.msgError("请填写意见或反馈");
        }
        const params = JSON.parse(JSON.stringify(this.data))
        params.appraiseSourceId = this.appraiseSourceId
        params.appraiseTargetId = this.appraiseTargetId
        params.appraise360ResultItemCreateReqVOS = list

        this.$modal.confirm('评价后不可修改，是否确认评价?').then(() => {
          createAppraise(params).then(response => {
            this.$modal.msgSuccess("评价成功");
            this.$emit("setOpen", false)
            this.$emit("refreshList")
          });
        }).catch(() => {});
      }

    }
  };
  </script>

  <style lang="scss" scoped>
  .appraise-conts{
    border: 1px #ddd solid;
    border-bottom: none;

    .item-title{
      padding: 10px 15px 10px 10px;
      border-bottom: 1px #ddd solid;

      &::before{
        content: '*';
        display: inline-block;
        color: red;
        padding-right: 5px;
      }
    }

    .item-cont{
      padding: 10px 15px;
      border-bottom: 1px #ddd solid;
    }
  }
  </style>
