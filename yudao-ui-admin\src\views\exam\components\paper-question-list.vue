<template>
  <div class="question-list">
    <div :class="['question-header', { 'is-view': isView }]">
      <h2>试题列表</h2>
      <template v-if="!isView">
        <el-button type="primary" :loading="replaceLoading" @click="handleBatchReplace">批量替换题目</el-button>
        <el-button type="primary" plain @click="handleKeywordReplace">关键字替换题目</el-button>
      </template>
    </div>

    <div class="question-section" v-for="(item, index) in questionTypes" :key="index">
      <div class="question-type">
        {{ zhOrder[index] }}、
        {{ getDictDataLabel(DICT_TYPE.EXAM_QUESTION_TYPE, item.questionType) }}
        （共{{ item.fixedQuestionIds ? item.fixedQuestionIds.split(",").length : 0 }}题，每题{{ item.score }}分）
      </div>
      <el-table :data="tableDataArr[index]" @selection-change="handleFromSelectionChange(index, $event)">
        <el-table-column type="selection" label="选择"></el-table-column>
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column label="标题内容" prop="title"></el-table-column>
        <el-table-column label="试题难度" prop="difficulty" width="120" align="center">
          <template #default="scope">
            {{ getDictDataLabel(DICT_TYPE.EXAM_DIFFICULTY_TYPE, scope.row.difficulty) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog title="关键字替换题目" :visible.sync="replaceOpen" append-to-body width="800px">
      <div class="mb20">
        <el-input class="w-50 mr10" placeholder="请输入关键字" v-model="replaceParams.content"></el-input>
        <el-button type="primary" @click="handleReplaceQuery">搜索</el-button>
      </div>

      <div class="mb10">
        需替换 <span class="text-danger">{{ selectFromQuestions.length }}</span> 题，
        当前已选择 <span class="text-danger">{{ selectToQuestions.length }}</span> 题
      </div>
      <el-table :data="replaceTableData" @selection-change="handleReplaceSelectionChange">
        <el-table-column type="selection" label="选择"></el-table-column>
        <el-table-column label="标题内容" prop="title"></el-table-column>
        <el-table-column label="试题难度" prop="difficulty" width="120" align="center">
          <template #default="scope">
            {{ getDictDataLabel(DICT_TYPE.EXAM_DIFFICULTY_TYPE, scope.row.difficulty) }}
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="replaceTotal > 0" :total="replaceTotal" :page.sync="replaceParams.pageNo"
                  :limit.sync="replaceParams.pageSize" @pagination="handleReplaceQuery"/>

      <template #footer>
        <div class="text-center">
          <el-button type="primary" @click="dealReplace">替换</el-button>
          <el-button @click="cancelReplace">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getQuestionList, getBatchReplaceQuestions, getQuestionPageFixPaper } from '@/api/exam/question';

export default {
  name: 'question-list',
  props: {
    questionTypes: Array,
    isView: Boolean,
  },
  data() {
    return {
      zhOrder: ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"],
      tableDataArr: [],
      replaceOpen: false,
      replaceParams: {
        paperId: "",
        questionType: "",
        content: "",
        pageNo: 1,
        pageSize: 10,
      },
      replaceTableData: [],
      replaceTotal: 0,
      selectFromQuestionsArr: [],
      selectToQuestions: [],
      replaceLoading: false,
    }
  },
  computed: {
    paperId() {
      return this.questionTypes?.[0]?.paperId;
    },
    questionTypeIndex() {
      let questionTypeIndex = -1; // 试题类型序号，-1为没选择或选中多个试题类型
      for (let i = 0; i < this.selectFromQuestionsArr.length; i++) {
        if (this.selectFromQuestionsArr[i]?.length > 0) {
          if (questionTypeIndex === -1) {
            questionTypeIndex = i;
          } else {
            questionTypeIndex = -1;
            break;
          }
        }
      }
      return questionTypeIndex;
    },
    // 选中题目类型
    questionType() {
      return this.questionTypes[this.questionTypeIndex]?.questionType || "";
    },
    // 选中题目列表
    selectFromQuestions() {
      return this.selectFromQuestionsArr[this.questionTypeIndex] || [];
    },
  },
  methods: {
    /** 试题列表 */
    queryTableData() {
      this.tableDataArr = new Array(this.questionTypes.length).fill([]);
      this.questionTypes.forEach((item, index) => {
        getQuestionList(item.fixedQuestionIds).then(res => {
          this.tableDataArr.splice(index, 1, res.data);
        });
      });
    },
    /** 批量替换题目 */
    handleBatchReplace() {
      if (this.questionTypeIndex > -1) {
        this.replaceLoading = true;
        const selectQuestionIds = this.selectFromQuestions.map(item => item.id);
        const currentQuestionIds = this.questionTypes[this.questionTypeIndex].fixedQuestionIds.split(",").filter(id => id !== "");
        const excludeQuestionIds = currentQuestionIds.filter(id => selectQuestionIds.indexOf(id) < 0);
        getBatchReplaceQuestions({
          paperId: this.paperId,
          questionType: this.questionType,
          replaceQuestionIds: selectQuestionIds.join(","),
          excludeQuestionIds: excludeQuestionIds.join(","),
        }).then(res => {
          this.replaceQuestions(res.data);
          this.replaceLoading = false;
        });
      } else {
        this.$message.warning("请选择同一试题类型需要替换的试题");
      }
    },
    /** 关键字替换题目 */
    handleFromSelectionChange(index, value) {
      let arr = this.selectFromQuestionsArr.slice();
      arr[index] = value;
      this.selectFromQuestionsArr = arr;
      console.log(this.selectFromQuestionsArr);
    },
    handleReplaceQuery() {
      getQuestionPageFixPaper(this.replaceParams).then(res => {
        this.replaceTableData = res.data.list;
        this.replaceTotal = res.data.total;
      });
    },
    handleKeywordReplace() {
      if (this.questionTypeIndex > -1) {
        this.replaceParams.paperId = this.paperId;
        this.replaceParams.questionType = this.questionType;
        this.replaceParams.pageNo = 1;
        this.replaceParams.pageSize = 10;
        this.replaceParams.content = "";
        this.replaceTableData = [];
        this.replaceTotal = [];
        this.selectToQuestions = [];
        this.replaceOpen = true;
        this.handleReplaceQuery();
      } else {
        this.$message.warning("请选择同一试题类型需要替换的试题");
      }
    },
    handleReplaceSelectionChange(value) {
      this.selectToQuestions = value;
    },
    dealReplace() {
      if (this.selectFromQuestions.length !== this.selectToQuestions.length) {
        this.$message.warning("请选择相同数量替换的试题");
        return;
      }
      this.replaceQuestions(this.selectToQuestions);
      this.cancelReplace();
    },
    cancelReplace() {
      this.replaceOpen = false;
      this.selectToQuestions = [];
    },
    replaceQuestions(questions) {
      const typeQuestionsMap = {};
      questions.forEach(q => {
        if (typeQuestionsMap[q.questionType]) {
          typeQuestionsMap[q.questionType].push(q);
        } else {
          typeQuestionsMap[q.questionType] = [q];
        }
      });
      this.questionTypes.forEach((t, i) => {
        const typeQuestions = typeQuestionsMap[t.questionType] || [];
        if (t.questionType === this.questionType) {
          const fromIndexes = this.selectFromQuestions.map(fq => {
            return this.tableDataArr[i].findIndex(item => item.id === fq.id);
          });
          const fixedQuestionIdArr = t.fixedQuestionIds.split(",");
          typeQuestions.forEach((tq, j) => {
            const fromIndex = fromIndexes[j];
            fixedQuestionIdArr.splice(fromIndex, 1, tq.id);
            this.tableDataArr[i].splice(fromIndex, 1, tq);
          });
          t.fixedQuestionIds = fixedQuestionIdArr.join(",");
        }
      });
      this.selectFromQuestionsArr = [];
      this.selectToQuestions = [];
    },
  },
  created() {
    this.queryTableData();
  },
}
</script>

<style scoped lang="scss">
.question-list {
  .question-header {
    display: flex;
    align-items: center;
    background: #fff;
    padding: 5px 0 10px;
    position: sticky;
    top: -30px;
    z-index: 999;

    &.is-view {
      position: relative;
      top: 0;
    }

    h2 {
      margin: 0;
      font-size: 18px;
      color: #000;
      flex-grow: 1;
    }
  }

  .question-section {
    margin-bottom: 16px;
  }

  .question-type {
    font-size: 15px;
    margin-bottom: 8px;
  }
}
</style>
