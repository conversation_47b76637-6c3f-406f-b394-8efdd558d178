import request from '@/utils/request'

// 获得招录报到分页
export function getCheckPage(query) {
    return request({
      url: '/recruitment/check/page',
      method: 'get',
      params: query
    })
}

// 通过任务
export function approve(data) {
    return request({
      url: `/recruitment/check/confirm`,
      method: 'put',
      data: data
    })
}

// 不通过任务
export function reject(data) {
    return request({
      url: `/recruitment/check/cancel?recruitmentRegistrationId=${data.recruitmentRegistrationId}`,
      method: 'put',
      // data: data
    })
}

// 修改报名信息
export function updateRegistrationinfo(data) {
    return request({
      url: '/recruitment/check/update-registration-info',
      method: 'post',
      data: data
    })
  }

// 获得报道二维码
export function getQrcode(query) {
  return request({
    url: '/recruitment/check/get-qrcode',
    method: 'get',
    params: query
  })
}

// 刷新二维码
export function updateQrcode(query) {
  return request({
    url: '/recruitment/check/update-qrcode',
    method: 'put',
    params: query
  })
}
