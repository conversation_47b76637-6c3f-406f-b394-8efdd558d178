<template>
  <div class="info-fill">
    <el-card shadow="hover">
      <div slot="header">报名信息</div>
      <el-form ref="registrationForm" inline label-position="top" :model="registrationVO" :rules="registrationRules" :disabled="readonly">
        <el-form-item label="报名项目：" prop="recruitmentProjectId">
          <el-select v-model="registrationVO.recruitmentProjectId" @change="handleProjectChange">
            <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择时长：" prop="recruitmentProjectDetailed">
          <el-select v-model="registrationVO.recruitmentProjectDetailed">
            <el-option
              v-for="item in projectDetailedList"
              :key="item.id"
              :label="`${item.recruitMonths}个月${item.remarks ? `(${item.remarks})` : ''}`"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学费：">
          <el-input-number :controls="false" disabled :value="detailedObject.tuition"></el-input-number>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            class="remark-input"
            type="textarea"
            :value="detailedObject.remarks"
            :rows="1"
            :controls="false"
            autosize
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item label="培训方案：">
          <el-link
            style="line-height: 1.5"
            :href="`${detailedObject.trainingProgramUrl}?token=${getAccessToken()}`"
            target="_blank"
          >{{ detailedObject.trainingProgramName }}</el-link>
          <span v-if="!detailedObject.trainingProgramName">暂无</span>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">基本信息</div>
      <el-form ref="baseInfoForm" inline label-position="top" :model="baseinfoVO" :rules="baseInfoRules" :disabled="readonly">
        <el-form-item label="姓名：" prop="name">
          <el-input v-model="baseinfoVO.name"></el-input>
        </el-form-item>
        <el-form-item label="性别：" prop="sex">
          <el-radio-group v-model="baseinfoVO.sex">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                      :key="dict.value" :label="+dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="民族：" prop="nation">
          <el-select v-model="baseinfoVO.nation" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_NATION)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item class="avatar-form-item" prop="avatar">
          <div class="avatar-upload">
            <image-upload
              v-model="baseinfoVO.avatar"
              :limit="1"
              :is-show-tip="false"
            ></image-upload>
            <p>
              使用1寸白底彩色免冠照片 <br/>
              请谨慎认真上传!
            </p>
          </div>
        </el-form-item>
        <el-form-item label="外语水平：" prop="foreignLanguageLevel">
          <el-select v-model="baseinfoVO.foreignLanguageLevel">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_FOREIGN_LANGUAGE_ABILITY)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="证件类型：" prop="idType">
          <el-select v-model="baseinfoVO.idType">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_CERTIFICATE_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="证件号码：" prop="idNo">
          <el-input v-model="baseinfoVO.idNo" @change="handleIdNoChange"></el-input>
        </el-form-item>
        <el-form-item label="出生日期：" prop="birthday">
          <el-date-picker v-model="baseinfoVO.birthday" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="年龄：" prop="age">
          <el-input-number v-model="baseinfoVO.age" controls-position="right" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="移动电话：" prop="mobilePhone">
          <el-input v-model="baseinfoVO.mobilePhone"></el-input>
        </el-form-item>
        <el-form-item label="邮箱地址：" prop="email">
          <el-input v-model="baseinfoVO.email"></el-input>
        </el-form-item>
        <el-form-item label="QQ：" prop="qq">
          <el-input v-model="baseinfoVO.qq"></el-input>
        </el-form-item>
        <el-form-item label="微信：" prop="weixin">
          <el-input v-model="baseinfoVO.weixin"></el-input>
        </el-form-item>
        <el-form-item label="籍贯：" prop="nativePlace">
          <el-select v-model="baseinfoVO.nativePlace" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.NATIVE_PLACE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="最高学历：" prop="highestAcademic">
          <el-select v-model="baseinfoVO.highestAcademic">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="最高学位：" prop="highestDegree">
          <el-select v-model="baseinfoVO.highestDegree">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_DEGREE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="开户银行：" prop="bank">
          <el-input v-model="baseinfoVO.bank" placeholder="某某银行某某支行"></el-input>
        </el-form-item>
        <el-form-item label="银行卡号：" prop="bankCardNumber">
          <el-input v-model="baseinfoVO.bankCardNumber"></el-input>
        </el-form-item>
        <el-form-item label="执业背景：" prop="relatedProfessionalBackground">
          <el-select v-model="baseinfoVO.relatedProfessionalBackground">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.PROFESSIONAL_BACKGROUND)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="岗位类型：" prop="positionType">
          <el-select v-model="baseinfoVO.positionType">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.POSITION_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="现住地址：" prop="currentAddress">
          <el-input v-model="baseinfoVO.currentAddress"></el-input>
        </el-form-item>
        <el-form-item label="政治面貌：" prop="politicalOutlook">
          <el-input v-model="baseinfoVO.politicalOutlook"></el-input>
        </el-form-item>
        <el-form-item label="健康状况：" prop="healthCondition">
          <el-select v-model="baseinfoVO.healthCondition">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_HEALTH_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="固定电话：" prop="fixedPhoneNumber">
          <el-input style="width: 65px" v-model="baseinfoVO.fixedPhoneAreaCode"></el-input>
          -
          <el-input style="width: 125px" v-model="baseinfoVO.fixedPhoneNumber"></el-input>
        </el-form-item>
        <el-form-item label="是否住宿：" prop="isStay">
          <el-radio-group v-model="baseinfoVO.isStay" @change="changeIsStay">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
                      :key="dict.value" :label="dict.value === 'true'">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否需要大白褂" prop="isNeedWhiteCoat">
          <el-radio-group v-model="baseinfoVO.isNeedWhiteCoat">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
                      :key="dict.value" :label="dict.value === 'true'">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="工龄：" prop="seniority">
          <el-input-number v-model="baseinfoVO.seniority" controls-position="right" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="紧急联系人：" prop="emergencyContact">
          <el-input v-model="baseinfoVO.emergencyContact"></el-input>
        </el-form-item>
        <el-form-item label="与本人关系：" prop="emergencyRelationship">
          <el-input v-model="baseinfoVO.emergencyRelationship"></el-input>
        </el-form-item>
        <el-form-item label="紧急联系人电话：" prop="emergencyTelephone">
          <el-input v-model="baseinfoVO.emergencyTelephone"></el-input>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="hover">
      <div class="add-header" slot="header">学历信息【自中专/高中起填写】
        <el-button v-if="!readonly" icon="el-icon-plus" type="primary" size="small" @click="addEduInfo">添加</el-button>
      </div>
      <el-table :data="eduInfoVOS">
        <el-table-column label="起止时间" prop="startDate" width="320px">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.startDate }} - {{ scope.row.endDate || "至今" }}</span>
            <template v-else>
              <el-date-picker class="short-item" v-model="scope.row.startDate" value-format="yyyy-MM-dd" placeholder="起始时间"></el-date-picker> -
              <el-date-picker class="short-item" v-model="scope.row.endDate" value-format="yyyy-MM-dd" placeholder="至今" clearable></el-date-picker>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="学校名称" prop="schoolName">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.schoolName }}</span>
            <el-input v-else v-model="scope.row.schoolName"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="专业" prop="major">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.major }}</span>
            <el-input v-else v-model="scope.row.major"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="学历" prop="education">
          <template v-slot="scope">
            <span v-if="readonly">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_EDUCATION, scope.row.education) }}</span>
            <el-select v-else v-model="scope.row.education">
              <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="学位" prop="degree">
          <template v-slot="scope">
            <span v-if="readonly">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_DEGREE, scope.row.degree) }}</span>
            <el-select v-else v-model="scope.row.degree">
              <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_DEGREE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="60px" v-if="!readonly">
          <template v-slot="scope">
            <el-link type="danger" @click="eduInfoVOS.splice(scope.row.$index, 1)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">资格证书信息</div>
      <el-form ref="certificateInfoForm" inline label-position="top" :model="certificateInfoVO" :rules="certificateInfoRules" :disabled="readonly">
        <el-form-item label="职称类别:" prop="positionalTitlesCategory">
          <el-select v-model="certificateInfoVO.positionalTitlesCategory">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.PROFESSIONAL_TITLE_CATEGORY)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="职称:" prop="positionalTitles">
          <el-select v-model="certificateInfoVO.positionalTitles">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="是否具有专业资格证书：" prop="isHavePhysicianQualificationCertificate">
          <el-select v-model="certificateInfoVO.isHavePhysicianQualificationCertificate">
            <el-option :value="true" label="是"></el-option>
            <el-option :value="false" label="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="专业技术资格证书编号:" prop="physicianQualificationCertificateNum" v-if="certificateInfoVO.isHavePhysicianQualificationCertificate">
          <el-input v-model="certificateInfoVO.physicianQualificationCertificateNum"></el-input>
        </el-form-item>
        <el-form-item label="获取时间:" prop="physicianQualificationCertificateTime" v-if="certificateInfoVO.isHavePhysicianQualificationCertificate">
          <el-date-picker v-model="certificateInfoVO.physicianQualificationCertificateTime" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="是否具有执业证书：" prop="isHaveMedicalPracticingCertificate">
          <el-select v-model="certificateInfoVO.isHaveMedicalPracticingCertificate">
            <el-option :value="true" label="是"></el-option>
            <el-option :value="false" label="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执业证书编号:" prop="medicalPracticingCertificateNum" v-if="certificateInfoVO.isHaveMedicalPracticingCertificate">
          <el-input v-model="certificateInfoVO.medicalPracticingCertificateNum"></el-input>
        </el-form-item>
        <el-form-item label="获取时间:" prop="medicalPracticingCertificateTime" v-if="certificateInfoVO.isHaveMedicalPracticingCertificate">
          <el-date-picker v-model="certificateInfoVO.medicalPracticingCertificateTime" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="执业范围" prop="medicalPracticingScope" v-if="certificateInfoVO.isHaveMedicalPracticingCertificate">
          <el-input v-model="certificateInfoVO.medicalPracticingScope"></el-input>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="hover">
      <div class="add-header" slot="header">工作经历
        <el-button v-if="!readonly" icon="el-icon-plus" type="primary" size="small" @click="addWorkHistory">添加</el-button>
      </div>
      <el-table :data="workHistoryInfoVOS">
        <el-table-column label="起止时间" prop="startDate" width="320px">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.startDate }} - {{ scope.row.endDate || "至今" }}</span>
            <template v-else>
              <el-date-picker class="short-item" v-model="scope.row.startDate" value-format="yyyy-MM-dd" placeholder="起始时间"></el-date-picker> -
              <el-date-picker class="short-item" v-model="scope.row.endDate" value-format="yyyy-MM-dd" placeholder="至今" clearable></el-date-picker>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="单位名称" prop="unitName">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.unitName }}</span>
            <el-input v-else v-model="scope.row.unitName"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="所在部门/科室" prop="department">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.department }}</span>
            <el-input v-else v-model="scope.row.department"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="职称" prop="positionalTitles">
          <template v-slot="scope">
            <span v-if="readonly">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES, scope.row.positionalTitles) }}</span>
            <el-select v-else v-model="scope.row.positionalTitles">
              <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="担任行政职务" prop="administrativPosition">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.administrativPosition }}</span>
            <el-input v-else v-model="scope.row.administrativPosition"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="60px" v-if="!readonly">
          <template v-slot="scope">
            <el-link type="danger" @click="workHistoryInfoVOS.splice(scope.row.$index, 1)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">选送单位信息</div>
      <el-form ref="unitInfoForm" inline label-position="top" :model="unitInfoVO" :rules="unitInfoRules" :disabled="readonly">
        <el-form-item label="选送单位:" prop="selectedUnit">
          <el-input v-model="unitInfoVO.selectedUnit"></el-input>
        </el-form-item>
        <el-form-item label="医院等级:" prop="hospitalLevel">
          <el-select v-model="unitInfoVO.hospitalLevel">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.HOSPITAL_LEVEL)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="单位所在省份:" prop="competentProvince">
          <el-select v-model="unitInfoVO.competentProvince" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.NATIVE_PLACE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="单位地址:" prop="competentAddress">
          <el-input v-model="unitInfoVO.competentAddress"></el-input>
        </el-form-item>
        <el-form-item label="选送科室:" prop="selectedDepartment">
          <el-input v-model="unitInfoVO.selectedDepartment"></el-input>
        </el-form-item>
        <el-form-item label="科室联系电话:" prop="departmentTelephone">
          <el-input v-model="unitInfoVO.departmentTelephone"></el-input>
        </el-form-item>
        <el-form-item label="单位主管部门:" prop="competentDepartment">
          <el-input v-model="unitInfoVO.competentDepartment"></el-input>
        </el-form-item>
        <el-form-item label="主管部门联系人:" prop="competentDepartmentContactPerson">
          <el-input v-model="unitInfoVO.competentDepartmentContactPerson"></el-input>
        </el-form-item>
        <el-form-item label="主管部门联系电话:" prop="competentDepartmentTelephone">
          <el-input v-model="unitInfoVO.competentDepartmentTelephone"></el-input>
        </el-form-item>
        <el-form-item label="是否需要对公付款：" prop="isCorporateRemittance" v-if="showCorporateRemittance">
          <el-select v-model="unitInfoVO.isCorporateRemittance">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
                       :key="dict.value" :label="dict.label" :value="dict.value === 'true'"/>
          </el-select>
        </el-form-item>
        <el-form-item label="选送单位开户行" prop="competentBank" v-if="showCorporateRemittance && unitInfoVO.isCorporateRemittance">
          <el-input v-model="unitInfoVO.competentBank"></el-input>
        </el-form-item>
        <el-form-item label="银行卡号" prop="competentBankCardNumber" v-if="showCorporateRemittance && unitInfoVO.isCorporateRemittance">
          <el-input v-model="unitInfoVO.competentBankCardNumber"></el-input>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">其他信息</div>
      <el-form ref="otherInfoForm" inline label-position="top" :model="otherInfoVO" :rules="otherInfoRules" :disabled="readonly">
        <el-form-item class="full-item" label="培训目标:" prop="trainingObjectives">
          <el-input type="textarea" autosize v-model="otherInfoVO.trainingObjectives"></el-input>
        </el-form-item>
        <el-form-item class="full-item" label="本人从事专业现有业务水平:" prop="currentBusinessLevel">
          <el-input type="textarea" autosize v-model="otherInfoVO.currentBusinessLevel"></el-input>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">资料上传</div>
      <el-form ref="fileInfoForm" inline label-position="top" :model="fileInfoVO" :rules="fileInfoRules" :disabled="readonly">
        <el-form-item label="身份证国徽面" prop="frontOfIdCard">
          <image-upload v-model="fileInfoVO.frontOfIdCard" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="身份证头像面" prop="reverseOfIdCard">
          <image-upload v-model="fileInfoVO.reverseOfIdCard" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="最高学历证书" prop="highestAcademicCertificate">
          <image-upload v-model="fileInfoVO.highestAcademicCertificate" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="最高学位证书" prop="highestDegreeCertificate">
          <image-upload v-model="fileInfoVO.highestDegreeCertificate" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="专业资格证书" prop="physicianQualificationCertificate">
          <image-upload v-model="fileInfoVO.physicianQualificationCertificate" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="执业证书" prop="medicalPracticingCertificate">
          <image-upload v-model="fileInfoVO.medicalPracticingCertificate" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="申请表查看" v-if="readonly && (isImageApplication || isPdfApplication)">
          <el-image
            v-if="isImageApplication"
            style="width: 148px; height: 148px"
            :src="applicationFormAddress"
            :preview-src-list="[applicationFormAddress]">
          </el-image>
          <svg-icon
            v-if="isPdfApplication"
            style="width: 148px; height: 148px; color: #1890ff; cursor: pointer"
            icon-class="pdf"
            @click.native="openPdf(applicationFormAddress)"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {
  getApplicationInfo,
  getApplicationInfoByPlanId,
  getPlanProjectDetailedList,
  getProjectsByPlanId,
  getRegistration
} from '@/api/recruitment/registration'
import { getConfigKey } from '@/api/infra/config'
import ImageUpload from '@/components/ImageUpload'
import { getAccessToken } from '@/utils/auth'

export default {
  name: "recruitment-form",
  components: { ImageUpload },
  props: {
    planId: String,
    recruitmentRegistrationId: String,
    readonly: Boolean,
  },
  data() {
    return {
      // 项目列表
      projectList: [],
      // 项目详情列表
      projectDetailedList: [],
      // 报名信息
      registrationVO: {
        id: undefined,
        planId: this.planId,
        recruitmentProjectDetailed: undefined,
        recruitmentProjectId: undefined,
      },
      registrationRules: {
        recruitmentProjectId: [{ required: true, message: "报名项目必选", trigger: "change" }],
        recruitmentProjectDetailed: [{ required: true, message: "时长必选", trigger: "change" }],
      },
      // 基本信息
      baseinfoVO: {
        age: null,
        bank: "",
        bankCardNumber: "",
        relatedProfessionalBackground: "",
        positionType: "",
        birthday: "",
        currentAddress: "",
        email: "",
        emergencyContact: "",
        emergencyRelationship: "",
        emergencyTelephone: "",
        fixedPhoneAreaCode: null,
        fixedPhoneNumber: null,
        foreignLanguageLevel: "",
        healthCondition: "",
        highestAcademic: "",
        highestDegree: "",
        idNo: "",
        idType: "",
        isStay: null,
        isNeedWhiteCoat: false,
        mobilePhone: "",
        name: "",
        nation: "",
        nativePlace: "",
        politicalOutlook: "",
        qq: "",
        recruitmentRegistrationId: null,
        seniority: null,
        sex: null,
        weixin: ""
      },
      baseInfoRules: {
        name: [{ required: true, message: "姓名必填", trigger: "blur" }],
        relatedProfessionalBackground: [{ required: true, message: "执业背景必填", trigger: "blur" }],
        positionType: [{ required: true, message: "岗位类型必填", trigger: "blur" }],
        sex: [{ required: true, message: "性别必选", trigger: "change" }],
        nation: [{ required: true, message: "民族必选", trigger: "change" }],
        foreignLanguageLevel: [{ required: true, message: "外语水平必选", trigger: "change" }],
        idType: [{ required: true, message: "证件类型必选", trigger: "change" }],
        idNo: [{ required: true, message: "证件号码必填", trigger: "blur" }],
        birthday: [{ required: true, message: "出生日期必选", trigger: "change" }],
        age: [{ required: true, message: "年龄必填", trigger: "blur" }],
        mobilePhone: [{ required: true, message: "移动电话必填", trigger: "blur" }, { type: "regexp", pattern: /^\d{11}$/, message: "请输入正确的移动电话号码", trigger: "blur" }],
        email: [{ required: true, message: "邮箱必填", trigger: "blur" }, { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }],
        weixin: [{ required: true, message: "微信必填", trigger: "blur" }],
        nativePlace: [{ required: true, message: "籍贯必选", trigger: "change" }],
        highestAcademic: [{ required: true, message: "最高学历必选", trigger: "change" }],
        highestDegree: [{ required: true, message: "最高学位必选", trigger: "change" }],
        currentAddress: [{ required: true, message: "现住地址必填", trigger: "blur" }],
        politicalOutlook: [{ required: true, message: "政治面貌必填", trigger: "blur" }],
        healthCondition: [{ required: true, message: "健康状况必选", trigger: "change" }],
        isStay: [{ required: true, message: "是否住宿必选", trigger: "change" }],
        isNeedWhiteCoat: [{ required: true, message: "是否需要白大褂必选", trigger: "change" }],
        seniority: [{ required: true, message: "工龄必填", trigger: "blur" }],
        emergencyContact: [{ required: true, message: "紧急联系人必填", trigger: "change" }],
        emergencyRelationship: [{ required: true, message: "与本人关系必填", trigger: "blur" }],
        emergencyTelephone: [{ required: true, message: "联系电话必填", trigger: "blur" }],
      },
      // 学历信息
      eduInfoVOS: [],
      // 资格证书信息
      certificateInfoVO: {
        positionalTitles: "",
        positionalTitlesCategory: "",
        isHavePhysicianQualificationCertificate: false,
        physicianQualificationCertificateNum: "",
        physicianQualificationCertificateTime: "",
        isHaveMedicalPracticingCertificate: false,
        medicalPracticingCertificateNum: "",
        medicalPracticingCertificateTime: "",
        medicalPracticingScope: "",
      },
      certificateInfoRules: {
        positionalTitlesCategory: [{ required: true, message: "职称类别必选", trigger: "change" }],
        positionalTitles: [{ required: true, message: "职称必选", trigger: "change" }],
        isHavePhysicianQualificationCertificate: [{ required: true, message: "是否具有专业技术资格证书必选", trigger: "change" }],
        physicianQualificationCertificateNum: [{ required: true, message: "专业技术资格证书编号必填", trigger: "blur" }],
        physicianQualificationCertificateTime: [{ required: true, message: "专业技术资格证书获取时间必选", trigger: "change" }],
        isHaveMedicalPracticingCertificate: [{ required: true, message: "是否具有执业证书必选", trigger: "change" }],
        medicalPracticingCertificateNum: [{ required: true, message: "执业证书编号必填", trigger: "blur" }],
        medicalPracticingCertificateTime: [{ required: true, message: "执业证书获取时间必选", trigger: "change" }],
        medicalPracticingScope: [{ required: true, message: "执业范围必填", trigger: "blur" }],
      },
      // 工作经历
      workHistoryInfoVOS: [],
      // 选送单位信息
      unitInfoVO: {
        competentDepartment: "",
        competentDepartmentTelephone: "",
        departmentTelephone: "",
        hospitalLevel: "",
        selectedDepartment: "",
        selectedUnit: "",
        competentProvince: "",
        competentAddress: "",
        competentDepartmentContactPerson: "",
        isCorporateRemittance: "",
        competentBank: "",
        competentBankCardNumber: ""
      },
      unitInfoRules: {
        selectedUnit: [{ required: true, message: "选送单位必填", trigger: "blur" }],
        hospitalLevel: [{ required: true, message: "医院等级必选", trigger: "change" }],
        selectedDepartment: [{ required: true, message: "选送科室必填", trigger: "blur" }],
        departmentTelephone: [{ required: true, message: "科室联系电话必填", trigger: "blur" }],
        competentDepartment: [{ required: true, message: "单位主管部门必填", trigger: "blur" }],
        competentDepartmentTelephone: [{ required: true, message: "主管部门联系电话必填", trigger: "blur" }],
        competentProvince: [{ required: true, message: "单位所在省份必填", trigger: "blur" }],
        competentAddress: [{ required: true, message: "单单位地址必填", trigger: "blur" }],
        competentDepartmentContactPerson: [{ required: true, message: "主管部门联系人必填", trigger: "blur" }],
        isCorporateRemittance: [{ required: true, message: "是否需要对公付款必填", trigger: "blur" }],
        competentBank: [{ required: true, message: "选送单位开户行必填", trigger: "blur" }],
        competentBankCardNumber: [{ required: true, message: "银行卡号必填", trigger: "blur" }],
      },
      // 其他信息
      otherInfoVO: {
        trainingObjectives: "",
        currentBusinessLevel: "",
      },
      otherInfoRules: {
        trainingObjectives: [{ required: true, message: "培训目标必填", trigger: "blur" }],
        currentBusinessLevel: [{ required: true, message: "现有业务水平必填", trigger: "blur" }],
      },
      // 资料上传
      fileInfoVO: {
        frontOfIdCard: "",
        highestAcademicCertificate: "",
        highestDegreeCertificate: "",
        medicalPracticingCertificate: "",
        physicianQualificationCertificate: "",
        reverseOfIdCard: ""
      },
      fileInfoRules: {
        frontOfIdCard: [{ required: true, message: "身份证国徽面必填", trigger: "change" }],
        reverseOfIdCard: [{ required: true, message: "身份证头像面必填", trigger: "change" }],
        highestAcademicCertificate: [{ required: true, message: "最高学历证书必填", trigger: "change" }],
      },
      // 用户上传的申请表
      applicationFormAddress: "",
      // 住宿信息提示
      stayInfo: "",
      // 显示是否需要对公付款
      showCorporateRemittance: true,
    }
  },
  computed: {
    detailedObject() {
      return this.projectDetailedList.find(item => item.id === this.registrationVO.recruitmentProjectDetailed) || {};
    },
    isImageApplication() {
      return /\.(jpg|jpeg|png|gif)$/i.test(this.applicationFormAddress);
    },
    isPdfApplication() {
      return /\.(pdf)$/i.test(this.applicationFormAddress);
    },
  },
  methods: {
    getAccessToken,
    getEduInfo() {
      const queryPromise = this.recruitmentRegistrationId ?
        getApplicationInfo(this.recruitmentRegistrationId) :
        getApplicationInfoByPlanId(this.planId);

      queryPromise.then(res => {
        if (res.data.registrationVO) {
          this.registrationVO = res.data.registrationVO;
          this.registrationVO.planId = this.planId;
        }
        if (res.data.baseinfoVO) this.baseinfoVO = res.data.baseinfoVO;
        if (res.data.eduInfoVOS) this.eduInfoVOS = res.data.eduInfoVOS;
        if (res.data.certificateInfoVO) this.certificateInfoVO = res.data.certificateInfoVO;
        if (res.data.workHistoryInfoVOS) this.workHistoryInfoVOS = res.data.workHistoryInfoVOS;
        if (res.data.unitInfoVO) this.unitInfoVO = res.data.unitInfoVO;
        if (res.data.otherInfoVO) this.otherInfoVO = res.data.otherInfoVO;
        if (res.data.fileInfoVO) this.fileInfoVO = res.data.fileInfoVO;
        const recruitmentProjectId = this.registrationVO?.recruitmentProjectId;
        if (recruitmentProjectId) {
          this.handleProjectChange(recruitmentProjectId);
        }
        this.$nextTick(() => this.$refs.baseInfoForm.clearValidate());
      });
    },
    handleProjectChange(value) {
      getPlanProjectDetailedList(this.planId, value).then(res => {
        this.projectDetailedList = res.data;
      });
    },
    addEduInfo() {
      this.eduInfoVOS.push({
        degree: "",
        education: "",
        endDate: "",
        major: "",
        recruitmentRegistrationId: 0,
        schoolName: "",
        startDate: "",
      });
    },
    addWorkHistory() {
      this.workHistoryInfoVOS.push({
        administrativPosition: "",
        department: "",
        endDate: "",
        positionalTitles: "",
        recruitmentRegistrationId: 0,
        startDate: "",
        unitName: "",
      });
    },
    openPdf(address) {
      window.open(`${address.replace("/get/", "/view/")}?token=${getAccessToken()}`, "_blank");
    },
    validForm() {
      const { registrationVO, baseinfoVO, eduInfoVOS, certificateInfoVO, workHistoryInfoVOS, unitInfoVO, otherInfoVO, fileInfoVO } = this;
      const registrationPromise = this.$refs.registrationForm.validate();
      const baseInfoPromise = this.$refs.baseInfoForm.validate();
      const certificateInfoPromise = this.$refs.certificateInfoForm.validate();
      const unitInfoPromise = this.$refs.unitInfoForm.validate();
      const otherInfoPromise = this.$refs.otherInfoForm.validate();
      return Promise.all([registrationPromise, baseInfoPromise, certificateInfoPromise, unitInfoPromise, otherInfoPromise]).then(() => ({
        registrationVO, baseinfoVO, eduInfoVOS, certificateInfoVO, workHistoryInfoVOS, unitInfoVO, otherInfoVO, fileInfoVO
      })).catch(() => {
        this.$message.warning("请检查数据是否都填写正确～");
        return Promise.reject();
      });
    },
    changeIsStay(value) {
      if (value) {
        this.$confirm(this.stayInfo, "提示", {
          confirmButtonText: "住宿",
          cancelButtonText: "不住宿",
          type: "warning"
        }).catch(() => {
          this.baseinfoVO.isStay = false;
        })
      }
    },
    handleIdNoChange(value) {
      const idCardNoPattern = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}[\dXx]$/;

      if (this.baseinfoVO.idType === "1" && idCardNoPattern.test(value)) {
        const birthYear = value.slice(6, 10);
        const birthMonth = value.slice(10, 12);
        const birthDate = value.slice(12, 14);
        const now = new Date();
        let age = now.getFullYear() - birthYear - 1;
        if ((now.getMonth() + 1) > +birthMonth || ((now.getMonth() + 1) === +birthMonth && now.getDate() > +birthDate)) {
          age += 1;
        }
        this.baseinfoVO.birthday = `${birthYear}-${birthMonth}-${birthDate}`;
        this.baseinfoVO.age = age;
      }
    },
  },
  created() {
    getConfigKey('recruitment.stay.info').then(res => {
      this.stayInfo = res.data || '';
    });
    getConfigKey('rotation.recruitment.corporate-remittance').then(res => {
      this.showCorporateRemittance = res.data !== 'false';
    });
    getProjectsByPlanId(this.planId).then(res => {
      this.projectList = res.data;
    });
    this.getEduInfo();
    if (this.readonly && this.recruitmentRegistrationId) {
      getRegistration(this.recruitmentRegistrationId).then(res => {
        this.applicationFormAddress = res.data.applicationFormAddress;
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.info-fill ::v-deep .el-card {
  margin-bottom: 10px;
}

.info-fill ::v-deep .el-card__header {
  font-size: 15px;
  font-weight: 600;
}

.info-fill ::v-deep .el-form-item__label {
  padding-bottom: 0!important;
}

.info-fill ::v-deep .el-form-item {
  width: 25%;
  margin-right: 0;
  padding-right: 20px;
  margin-bottom: 10px;
}

.info-fill .avatar-form-item {
  float: right;
  height: 280px;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  >p {
    line-height: 1.8;
  }
}

.avatar-upload ::v-deep .el-upload {
  height: 207px!important;
  background: #eee url(~@/assets/images/avatar.png) no-repeat center 56px;
  background-size: auto 80%;
  .el-icon-plus {
    display: none;
  }
}

.avatar-upload ::v-deep .el-upload-list__item {
  height: 207px!important;
}

.info-fill .full-item {
  width: 100%!important;
}

.info-fill .short-item {
  width: 142px!important;
}

.add-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remark-input {
  position: absolute;
  ::v-deep .el-textarea__inner {
    min-height: 36px!important;
  }
}
</style>
