import request from '@/utils/request'

// 获得教学活动分页
export function getDeptappraisestudentPage(query) {
  return request({
    url: '/rotation/deptappraisestudent/page',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/deptappraisestudent/index'}
  })
}

// 获得学员评价科室表单
export function getAppraiseForm(id) {
    return request({
      url: '/rotation/deptappraisestudent/get-form?scheduleDetailsId=' + id,
      method: 'get'
    })
}



