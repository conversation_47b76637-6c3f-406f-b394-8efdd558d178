<template>
  <div class="app-container">
    <el-radio-group style="margin-bottom: 20px;" size="medium" v-model="queryParams.appraiseActiveType" @change="handleAppraiseActiveTypeChange">
      <el-radio-button v-for="item in appraiseActiveTypeList" :label="item.value">{{ item.label }}</el-radio-button>
    </el-radio-group>

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="评价指标" prop="appraiseKpi">
        <el-input v-model="queryParams.appraiseKpi" placeholder="请输入评价指标" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="培训类型" prop="activeType" v-show="requireType" v-if="queryParams.appraiseActiveType === '2'">
        <el-select v-model="queryParams.activeType" placeholder="请选择培训类型" size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="活动类型" prop="activeType" v-show="requireType" v-else>
        <el-select v-model="queryParams.activeType" placeholder="请选择活动类型" size="small" @change="handleActiveTypeChange">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:appraise-active:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5" v-if="queryParams.appraiseActiveType === '1' && !activeNote">
        <el-button type="success" plain icon="el-icon-plus" size="mini" @click="handleSaveNote"
                   v-hasPermi="['rotation:appraise-active-notice:update']">新增活动须知</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:appraise-active:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <p class="active-note" v-if="queryParams.appraiseActiveType === '1' && activeNote">
      <span style="color: #666">活动须知：</span>{{ activeNote }}
      <i class="el-icon-edit" @click="handleSaveNote" v-hasPermi="['rotation:appraise-active-notice:update']"></i>
    </p>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="序号" type="index" align="center"></el-table-column>
      <el-table-column :label="queryParams.appraiseActiveType === '2' ? '培训类型' : '活动类型'" align="center" prop="activeType" v-if="requireType">
        <template slot-scope="scope">
          <dict-tag :type="queryParams.appraiseActiveType === '2' ? DICT_TYPE.TEACHING_TRAINING_TYPE : DICT_TYPE.ROTATION_ACTIVE_TYPE" :value="scope.row.activeType" />
        </template>
      </el-table-column>
      <el-table-column label="评价指标" align="center" prop="appraiseKpi" />
      <el-table-column label="指标分值" align="center" prop="score" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['rotation:appraise-active:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['rotation:appraise-active:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="评价指标" prop="appraiseKpi">
          <el-input v-model="form.appraiseKpi" placeholder="请输入评价指标" />
        </el-form-item>
        <el-form-item label="培训类型" prop="activeType" v-if="queryParams.appraiseActiveType === '2'">
          <el-select v-model="form.activeType" placeholder="请选择培训类型">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="活动类型" prop="activeType" v-if="requireType && queryParams.appraiseActiveType !== '2'">
          <el-select v-model="form.activeType" placeholder="请选择活动类型">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="指标分值" prop="score">
          <el-input-number v-model="form.score" :min="0" controls-position="right" placeholder="请输入指标分值" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createAppraiseActiveItem,
  updateAppraiseActiveItem,
  deleteAppraiseActiveItem,
  getAppraiseActiveItem,
  getAppraiseActiveItemPage,
  exportAppraiseActiveItemExcel,
  updateAppraiseActiveNote,
  getAppraiseActiveNote,
} from "@/api/rotation/appraiseActive";

export default {
  name: "AppraiseActiveItem",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 活动评价项列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 活动指标类型
      appraiseActiveTypeList: [],
      // 活动类型
      rotationActiveTypeList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        appraiseActiveType: null,
        activeType: null,
        appraiseKpi: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        appraiseKpi: [{ required: true, message: "评价指标不能为空", trigger: "blur" }],
        activeType: [{ required: true, message: "活动类型不能为空", trigger: "change" }],
        score: [{ required: true, message: "指标分值不能为空", trigger: "blur" }],
      },
      // 活动须知
      activeNote: "",
    };
  },
  computed: {
    requireType() {
      return ["4", "5"].indexOf(this.queryParams.appraiseActiveType) < 0;
    }
  },
  created() {
    this.appraiseActiveTypeList = this.getDictDatas(this.DICT_TYPE.APPRAISE_ACTIVE_TYPE);
    this.queryParams.appraiseActiveType = this.appraiseActiveTypeList[0]?.value;
    this.rotationActiveTypeList = this.getDictDatas(this.DICT_TYPE.ROTATION_ACTIVE_TYPE);
    this.queryParams.activeType = this.rotationActiveTypeList[0]?.value;
    this.getList();
    this.getActiveNote();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getAppraiseActiveItemPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        appraiseActiveId: undefined,
        activeType: undefined,
        appraiseKpi: undefined,
        score: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.appraiseActiveType = this.appraiseActiveTypeList[0]?.value;
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加活动评价项";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getAppraiseActiveItem(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改活动评价项";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateAppraiseActiveItem(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createAppraiseActiveItem({
          appraiseActiveType: this.queryParams.appraiseActiveType,
          activeType: this.form.activeType,
          appraiseActiveItemCreateReqVO: this.form
        }).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除' + row.appraiseKpi + '评价指标?').then(function() {
          return deleteAppraiseActiveItem(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有活动评价项数据项?').then(() => {
          this.exportLoading = true;
          return exportAppraiseActiveItemExcel(params);
        }).then(response => {
          this.$download.excel(response, '活动评价项.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    },
    /** 指标类型改变 */
    handleAppraiseActiveTypeChange() {
      if (this.requireType) {
        this.rules.activeType[0].required = true;
        this.queryParams.activeType = this.rotationActiveTypeList[0]?.value;
        this.getList();
      } else {
        this.rules.activeType[0].required = false;
        this.queryParams.activeType = undefined;
        this.getList();
      }
      if (this.queryParams.appraiseActiveType === "1") {
        this.getActiveNote();
      }
    },
    /** 编辑活动须知 */
    handleSaveNote() {
      this.$prompt("", "维护活动须知", {
        inputType: "textarea",
        inputPlaceholder: "请输入活动须知",
        inputValue: this.activeNote,
      }).then(({ value }) => {
        updateAppraiseActiveNote({
          activeType: this.queryParams.activeType,
          notice: value,
        }).then(() => {
          this.getActiveNote();
          this.$message.success("活动须知保存成功！");
        })
      })
    },
    /** 获取活动须知 */
    getActiveNote() {
      getAppraiseActiveNote(this.queryParams.activeType).then(res => {
        this.activeNote = res.data?.notice || "";
      });
    },
    /** 活动类型改变 */
    handleActiveTypeChange() {
      this.getActiveNote();
      this.getList();
    },
  }
};
</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}

.active-note {
  font-size: 15px;
  margin-bottom: 8px;

  .el-icon-edit {
    color: #999999;
    cursor: pointer;
    &:hover {
      color: #46a6ff;
    }
  }
}
</style>
