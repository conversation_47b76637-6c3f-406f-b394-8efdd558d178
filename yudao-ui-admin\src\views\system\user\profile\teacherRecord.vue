<template>
  <div>
    <div class="section-title">聘任记录</div>
    <el-table class="mb50" :data="appointmentRecordList">
      <el-table-column label="序号" type="index" align="center"></el-table-column>
      <el-table-column label="聘任岗位" prop="applyPosition" align="center">
        <template slot-scope="scope">{{ getMatchedLabel(roleOptions, scope.row.applyPosition, "id", "name") }}</template>
      </el-table-column>
      <el-table-column label="聘任开始日期" prop="teachBeginDate" align="center"></el-table-column>
      <el-table-column label="聘任截止日期" prop="teachEndDate" align="center"></el-table-column>
      <el-table-column label="聘任时长" prop="appointmentYears" align="center"></el-table-column>
      <el-table-column label="解聘时间" prop="dismissalTime" align="center"></el-table-column>
    </el-table>
    <div class="section-title">评优记录</div>
    <el-table class="mb50" :data="evaluationRecordList">
      <el-table-column label="序号" type="index"  align="center"></el-table-column>
      <el-table-column label="评选年度" prop="year" align="center"></el-table-column>
      <el-table-column label="评优项目" prop="teachersEvaluationProject" align="center"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { pageCurrentAppointment, pageCurrentEvaluation } from '@/api/teachers/archives'
import { listSimpleRoles } from "@/api/system/role";

export default {
  name: 'teacherRecord',
  data() {
    return {
      appointmentRecordList: [],
      evaluationRecordList: [],
      roleOptions: [],
    }
  },
  created() {
    listSimpleRoles().then(response => {
      const excludeRoleCodes = ["super_admin", "admin", "student", "hospital_admin", "recruitment_user"];
      this.roleOptions = response.data.filter(item => !excludeRoleCodes.includes(item.code));
    });
    pageCurrentAppointment({ pageNo: 1, pageSize: 999 }).then(res => this.appointmentRecordList = res.data.list)
    pageCurrentEvaluation({ pageNo: 1, pageSize: 999 }).then(res => this.evaluationRecordList = res.data.list)
  },
}
</script>

<style scoped lang="scss">
.section-title {
  line-height: 1;
  font-size: 15px;
  border-left: 4px solid #409eff;
  padding-left: 10px;
  margin-bottom: 10px;
}

.mb50 {
  margin-bottom: 50px;
}
</style>
