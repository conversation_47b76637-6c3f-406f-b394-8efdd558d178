<template>
  <!-- 对话框(添加 / 修改) -->
  <el-dialog
    custom-class="user-student-dialog"
    :title="dialogTitle"
    :visible.sync="open"
    width="80%"
    v-dialogDrag
    append-to-body
    @close="cancel"
  >
    <div class="steps-box">
      <el-radio-group v-model="activeStep" size="medium" @input="changeStep">
        <el-radio-button :label="0">基础信息</el-radio-button>
        <el-radio-button :label="1">培训信息</el-radio-button>
      </el-radio-group>
    </div>
    <div class="form-box">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="110px"
        :disabled="opt === 'view'"
      >
        <div v-if="activeStep == 0">
          <el-row :gutter="10">
            <el-col :md="12" :lg="12" :xl="12">
              <el-row :gutter="10">
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="姓名" prop="nickname">
                    <el-input
                      v-model="form.nickname"
                      placeholder="请输入姓名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="用户名" prop="username">
                    <el-input
                      v-model="form.username"
                      placeholder="请输入用户名"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10">
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="证件类型" prop="certificateType">
                    <el-select
                      v-model="form.certificateType"
                      filterable
                      placeholder="请选择证件类型"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_USER_CERTIFICATE_TYPE
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="证件号码" prop="certificateNumber">
                    <el-input
                      v-model="form.certificateNumber"
                      placeholder="请输入证件号码"
                      @blur="getIdcardInfoHandler"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10">
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="用户性别" prop="sex">
                    <el-select
                      v-model="form.sex"
                      filterable
                      placeholder="请选择用户性别"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_USER_SEX
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="parseInt(dict.value)"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="出生日期" prop="birthday">
                    <el-date-picker
                      clearable
                      v-model="form.birthday"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="选择出生日期"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10">
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="婚姻状况" prop="maritalStatus">
                    <el-select
                      v-model="form.maritalStatus"
                      filterable
                      placeholder="请选择婚姻状况"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_USER_MARITAL_STATUS
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="健康状况" prop="healthStatus">
                    <el-select
                      v-model="form.healthStatus"
                      filterable
                      placeholder="请选择健康状况"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_USER_HEALTH_STATUS
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10">
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="民族" prop="nation">
                    <el-select
                      v-model="form.nation"
                      filterable
                      placeholder="请选择民族"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_NATION
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="学历：" prop="education">
                    <el-select
                      v-model="form.education"
                      filterable
                      placeholder="请选择学历"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_USER_EDUCATION
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10">
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="是否党员" prop="isPartyMember">
                    <el-select
                      v-model="form.isPartyMember"
                      filterable
                      placeholder="请选择是否党员"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.INFRA_BOOLEAN_STRING
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="手机号码" prop="mobile">
                    <el-input
                      v-model="form.mobile"
                      placeholder="请输入手机号码"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :md="12" :lg="12" :xl="12">
              <userAvatar :user="form" @info="getAvatar" :opt="opt" />
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :md="12" :lg="12" :xl="12">
              <el-row :gutter="10">
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="用户邮箱" prop="email">
                    <el-input
                      type="email"
                      v-model="form.email"
                      placeholder="请输入用户邮箱"
                      maxlength="100"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="毕业时间：" prop="graduationSchoolDate">
                    <el-date-picker
                      clearable
                      v-model="form.graduationSchoolDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="选择毕业时间"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="学位：" prop="degree">
                    <el-select
                      v-model="form.degree"
                      filterable
                      placeholder="请选择学位"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_USER_DEGREE
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="毕业院校：" prop="graduationSchool">
                    <el-input
                      v-model="form.graduationSchool"
                      placeholder="请输入毕业院校"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="开户银行" prop="depositBank">
                    <el-input
                      v-model="form.depositBank"
                      placeholder="请输入开户银行"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="银行卡号" prop="bankCardNo">
                    <el-input
                      v-model="form.bankCardNo"
                      placeholder="请输入银行卡号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="户口省份" prop="residenceProvince">
                    <el-select
                      v-model="form.residenceProvince"
                      filterable
                      placeholder="请选择户口省份"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_PROVINCE
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="计算机能力" prop="computerAbility">
                    <el-select
                      v-model="form.computerAbility"
                      filterable
                      placeholder="请选择计算机能力"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_USER_COMPUTER_ABILITY
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="外语能力" prop="foreignLanguageAbility">
                    <el-select
                      v-model="form.foreignLanguageAbility"
                      filterable
                      placeholder="请选择外语能力"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_USER_FOREIGN_LANGUAGE_ABILITY
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="兴趣爱好" prop="hobby">
                    <el-input
                      v-model="form.hobby"
                      placeholder="请输入兴趣爱好"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="通讯地址" prop="postalAddress">
                    <el-input
                      v-model="form.postalAddress"
                      placeholder="请输入通讯地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="邮政编码" prop="postalCode">
                    <el-input
                      v-model="form.postalCode"
                      placeholder="请输入邮政编码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="紧急联系人" prop="emergencyContact">
                    <el-input
                      v-model="form.emergencyContact"
                      placeholder="请输入紧急联系人"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="与本人关系" prop="relationship">
                    <el-input
                      v-model="form.relationship"
                      placeholder="请输入与本人关系"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item
                    label="紧急联系人手机"
                    prop="emergencyContactPhone"
                  >
                    <el-input
                      v-model="form.emergencyContactPhone"
                      placeholder="请输入紧急联系人手机"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item
                    label="紧急联系人地址"
                    prop="emergencyContactAddres"
                  >
                    <el-input
                      v-model="form.emergencyContactAddres"
                      placeholder="请输入紧急联系人地址"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item label="培训状态" prop="trainingStatus">
                    <el-select
                      v-model="form.trainingStatus"
                      filterable
                      placeholder="请选择培训状态"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.SYSTEM_USER_TRAINING_STATUS
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :lg="12" :xl="12">
                  <el-form-item
                    label="是否获得处方权："
                    prop="obtainedPrescription"
                    label-width="280"
                  >
                    <el-radio-group v-model="form.obtainedPrescription">
                      <el-radio
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.INFRA_BOOLEAN_STRING
                        )"
                        :key="dict.value"
                        :label="dict.value"
                        >{{ dict.label }}</el-radio
                      >
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :md="12" :lg="12" :xl="12">
              <el-row :gutter="10">
                <el-col :md="21" :lg="21" :xl="21" :offset="3">
                  <div style="margin-bottom: 5px"><b>学历证书：</b></div>
                  <el-form-item
                    label=""
                    label-width="0"
                    prop="educationCertificate"
                  >
                    <imageUpload
                      v-model="form.educationCertificate"
                      :limit="1"
                      :isShowTip="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="21" :lg="21" :xl="21" :offset="3">
                  <div style="margin-bottom: 5px"><b>学位证书：</b></div>
                  <el-form-item
                    label=""
                    label-width="0"
                    prop="degreeCertificate"
                  >
                    <imageUpload
                      v-model="form.degreeCertificate"
                      :limit="1"
                      :isShowTip="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :md="18" :lg="18" :xl="18">
              <el-row :gutter="10">
                <el-col :md="24" :lg="24" :xl="24">
                  <el-form-item
                    label="是否获得医师资格证书："
                    prop="hasMedicalLicense"
                    label-width="280"
                  >
                    <el-radio-group v-model="form.hasMedicalLicense">
                      <el-radio
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.INFRA_BOOLEAN_STRING
                        )"
                        :key="dict.value"
                        :label="dict.value"
                        >{{ dict.label }}</el-radio
                      >
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="form.hasMedicalLicense === 'true'"
                  :md="12"
                  :lg="12"
                  :xl="12"
                >
                  <el-form-item
                    label="取得医师资格证书时间："
                    prop="medicalLicenseDate"
                    label-width="280"
                  >
                    <el-date-picker
                      clearable
                      v-model="form.medicalLicenseDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="选择日期"
                    />
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="form.hasMedicalLicense === 'true'"
                  :md="12"
                  :lg="12"
                  :xl="12"
                >
                  <el-form-item
                    label="医师资格级别："
                    prop="medicalLicenseLevel"
                    label-width="280"
                  >
                    <el-select
                      v-model="form.medicalLicenseLevel"
                      filterable
                      placeholder="请选择医师资格级别"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.MEDICAL_LICENSE_LEVEL
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="form.hasMedicalLicense === 'true'"
                  :md="12"
                  :lg="12"
                  :xl="12"
                >
                  <el-form-item
                    label="医师资格证书编码："
                    prop="medicalLicenseNumber"
                    label-width="280"
                  >
                    <el-input
                      v-model="form.medicalLicenseNumber"
                      placeholder="请输入医师资格证书编码"
                      style="width: 250px"
                    />
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="form.hasMedicalLicense === 'true'"
                  :md="12"
                  :lg="12"
                  :xl="12"
                >
                  <el-form-item
                    label="医师资格类别："
                    prop="medicalLicenseCategory"
                    label-width="280"
                  >
                    <el-select
                      v-model="form.medicalLicenseCategory"
                      filterable
                      placeholder="请选择医师资格类别"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.MEDICAL_LICENSE_CATEGORY
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :md="24" :lg="24" :xl="24">
                  <el-form-item
                    label="是否获得医师执业证书："
                    prop="hasPracticingLicense"
                    label-width="280"
                  >
                    <el-radio-group v-model="form.hasPracticingLicense">
                      <el-radio
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.INFRA_BOOLEAN_STRING
                        )"
                        :key="dict.value"
                        :label="dict.value"
                        >{{ dict.label }}</el-radio
                      >
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="form.hasPracticingLicense === 'true'"
                  :md="12"
                  :lg="12"
                  :xl="12"
                >
                  <el-form-item
                    label="取得医师执业证书时间："
                    prop="practicingLicenseDate"
                    label-width="280"
                  >
                    <el-date-picker
                      clearable
                      v-model="form.practicingLicenseDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="选择日期"
                    />
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="form.hasPracticingLicense === 'true'"
                  :md="12"
                  :lg="12"
                  :xl="12"
                >
                  <el-form-item
                    label="执业类型："
                    prop="practicingType"
                    label-width="280"
                  >
                    <el-select
                      v-model="form.practicingType"
                      filterable
                      placeholder="请选择执业类型"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.PRACTICING_TYPE
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="form.hasPracticingLicense === 'true'"
                  :md="12"
                  :lg="12"
                  :xl="12"
                >
                  <el-form-item
                    label="医师执业证书编码："
                    prop="practicingLicenseNumber"
                    label-width="280"
                  >
                    <el-input
                      v-model="form.practicingLicenseNumber"
                      placeholder="请输入医师执业证书编码"
                      style="width: 250px"
                    />
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="form.hasPracticingLicense === 'true'"
                  :md="12"
                  :lg="12"
                  :xl="12"
                >
                  <el-form-item
                    label="执业范围："
                    prop="practicingScope"
                    label-width="280"
                  >
                    <el-select
                      v-model="form.practicingScope"
                      filterable
                      placeholder="请选择执业范围"
                    >
                      <el-option
                        v-for="dict in this.getDictDatas(
                          DICT_TYPE.PRACTICING_SCOPE
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :md="6" :lg="6" :xl="6">
              <el-row :gutter="10" style="padding-left: 20px">
                <el-col
                  v-if="form.hasMedicalLicense === 'true'"
                  :md="24"
                  :lg="24"
                  :xl="24"
                >
                  <div style="margin-bottom: 5px"><b>医师资格证书：</b></div>
                  <el-form-item
                    label=""
                    label-width="0"
                    prop="medicalLicenseImage"
                  >
                    <imageUpload
                      v-model="form.medicalLicenseImage"
                      :limit="1"
                      :disabled="opt === 'view' ? true : false"
                      :isShowTip="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="form.hasPracticingLicense === 'true'"
                  :md="24"
                  :lg="24"
                  :xl="24"
                >
                  <div style="margin-bottom: 5px"><b>医师执业证书：</b></div>
                  <el-form-item
                    label=""
                    label-width="0"
                    prop="practicingLicenseImage"
                  >
                    <imageUpload
                      v-model="form.practicingLicenseImage"
                      :limit="1"
                      :disabled="opt === 'view' ? true : false"
                      :isShowTip="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>

        <div v-if="activeStep == 1">
          <el-row>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="学员类型：" prop="studentType">
                <el-select
                  v-model="form.studentType"
                  filterable
                  placeholder="请选择学员类型"
                  @change="getStandardSchemeListHandler"
                  :disabled="Boolean(form.id)"
                >
                  <el-option
                    v-for="dict in this.getDictDatas(
                      DICT_TYPE.SYSTEM_STUDENT_TYPE
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="16" :lg="16" :xl="16">
              <el-form-item
                v-if="form.studentType == 1"
                label="人员类型："
                prop="personnelType"
              >
                <el-radio-group
                  v-model="form.personnelType"
                  @input="personnelTypeChange"
                >
                  <el-radio
                    v-for="dict in this.getDictDatas(
                      DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE
                    )"
                    :label="dict.value"
                  >
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :md="16" :lg="16" :xl="16">
              <el-form-item label="派送单位：" prop="dispatchingUnit">
                <el-input
                  v-model="form.dispatchingUnit"
                  placeholder="请输入派送单位"
                />
              </el-form-item>
            </el-col>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="分组：" prop="studentGroup">
                <el-input
                  v-model="form.studentGroup"
                  placeholder="请输入分组"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="培训开始日期：" prop="trainingStartDate">
                <el-date-picker
                  clearable
                  v-model="form.trainingStartDate"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="选择培训开始日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="年级：" prop="grade">
                <el-date-picker
                  v-model="form.grade"
                  type="year"
                  format="yyyy"
                  value-format="yyyy"
                  placeholder="选择年级"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="培训专业：" prop="major">
                <el-select
                  v-model="form.major"
                  filterable
                  placeholder="请选择培训专业"
                  @change="getStandardSchemeListHandler"
                  :disabled="majorDisabled"
                  style="width: 100%"
                >
                  <el-option
                    v-for="major in majorList"
                    :key="major.code"
                    :label="major.name"
                    :value="major.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="培训方案：" prop="standardSchemeId">
                <el-select
                  v-model="form.standardSchemeId"
                  filterable
                  placeholder="请选择培训方案"
                  :disabled="standardSchemeDisabled"
                  style="width: 100%"
                >
                  <el-option
                    v-for="standardScheme in standardSchemeList"
                    :key="standardScheme.id"
                    :label="standardScheme.name"
                    :value="standardScheme.id"
                  />
                </el-select>
                <!-- <el-input v-model="form.standardSchemeId" placeholder="请输入标准方案id" /> -->
              </el-form-item>
            </el-col>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="培训时长：" prop="rotationTime">
                <el-input
                  v-model="form.rotationTime"
                  placeholder="请输入培训时长"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="结业年份：" prop="graduationYear">
                <el-date-picker
                  v-model="form.graduationYear"
                  type="year"
                  format="yyyy"
                  value-format="yyyy"
                  placeholder="选择结业年份"
                  style="width: 100%"
                >
                </el-date-picker>
                <!-- <el-input v-model="form.graduationYear" placeholder="请输入结业年份" /> -->
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="结业时间：" prop="graduationDate">
                <el-date-picker
                  clearable
                  v-model="form.graduationDate"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="选择结业时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :md="8" :lg="8" :xl="8">
              <el-form-item label="责任导师：" prop="responsibleMentorUserId">
                <el-select
                  v-model="form.responsibleMentorUserId"
                  filterable
                  placeholder="请选择责任导师"
                  style="width: 100%"
                >
                  <el-option
                    v-for="mentor in mentorList"
                    :key="mentor.id"
                    :label="mentor.nickname"
                    :value="mentor.id"
                  />
                </el-select>
                <!-- <el-input v-model="form.responsibleMentorUserId" placeholder="请输入责任导师" /> -->
              </el-form-item>
            </el-col>
            <el-col :md="8" :lg="8" :xl="8"></el-col>
          </el-row>

          <el-row
            v-if="
              form.studentType == '12' ||
              (form.studentType == '1' && form.personnelType == '4')
            "
          >
            <p>注意：以下内容请上传大小不超过50M的PDF文件！</p>
            <el-col :md="24" :lg="24" :xl="24">
              <el-form-item label="培养计划：" prop="cultivationPlanFile">
                <FileUpload
                  v-model="form.cultivationPlanFile"
                  :fileSize="50"
                  :fileType="['pdf']"
                  :isShowTip="false"
                />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="24" :xl="24">
              <el-form-item label="开题报告：" prop="proposalFile">
                <FileUpload
                  v-model="form.proposalFile"
                  :fileSize="50"
                  :fileType="['pdf']"
                  :isShowTip="false"
                />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="24" :xl="24">
              <el-form-item label="中期考核：" prop="midTermAssessmentFile">
                <FileUpload
                  v-model="form.midTermAssessmentFile"
                  :fileSize="50"
                  :fileType="['pdf']"
                  :isShowTip="false"
                />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="24" :xl="24">
              <el-form-item label="毕业论文：" prop="graduationThesisFile">
                <FileUpload
                  v-model="form.graduationThesisFile"
                  :fileSize="50"
                  :fileType="['pdf']"
                  :isShowTip="false"
                />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="24" :xl="24">
              <el-form-item label="学位成绩单：" prop="degreeTranscriptFile">
                <FileUpload
                  v-model="form.degreeTranscriptFile"
                  :fileSize="50"
                  :fileType="['pdf']"
                  :isShowTip="false"
                />
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="24" :xl="24">
              <el-form-item label="其他材料：" prop="otherFile">
                <FileUpload
                  v-model="form.otherFile"
                  :fileSize="50"
                  :fileType="['pdf']"
                  :isShowTip="false"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer" v-if="opt !== 'view'">
      <el-button v-if="activeStep != 0" type="primary" @click="handlePreStep">
        上一步
      </el-button>
      <el-button v-if="activeStep != 1" type="primary" @click="handleNextStep">
        下一步
      </el-button>
      <el-button type="primary" @click="submitForm"> 确 定 </el-button>
      <el-button @click="cancel"> 取 消 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import userAvatar from "../userAvatar.vue";
import ImageUpload from "@/components/ImageUpload";
import FileUpload from "@/components/FileUploadInfo";
import { getSimpleMajorList } from "@/api/system/major";
import { getMentorSimpleList } from "@/api/rotation/mentorLibrary";
import {
  createUserStudent,
  updateUserStudent,
  getUserStudent,
  getStandardSchemeList,
  getIdcardInfo,
} from "@/api/system/userStudent";

export default {
  props: {
    dialogTitle: {
      type: String,
    },
    dialogOpen: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: [String, Number],
    },
    opt: {
      type: String,
      default: "",
    },
  },
  components: { userAvatar, ImageUpload, FileUpload },
  data() {
    const checkCertificateNumber = (rule, value, callback) => {
      if (this.form.certificateType == 1 && value) {
        const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的证件号码"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const checkMobile = (rule, value, callback) => {
      if (value) {
        const reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的手机号码"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const checkEmail = (rule, value, callback) => {
      if (value) {
        const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的邮箱地址"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
          {
            min: 4,
            max: 16,
            message: "长度在 4 到 16 个字符",
            trigger: "blur",
          },
          // {
          //   pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,16}$/,
          //   message: "用户名由数字和字母组成",
          //   trigger: "blur"
          // }
        ],
        nickname: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        studentType: [
          { required: true, message: "学员类型不能为空", trigger: "blur" },
        ],
        grade: [{ required: true, message: "年级不能为空", trigger: "blur" }],
        standardSchemeId: [
          { required: true, message: "培训方案不能为空", trigger: "blur" },
        ],
        rotationTime: [
          { required: true, message: "培训时间不能为空", trigger: "blur" },
        ],
        certificateNumber: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkCertificateNumber, trigger: "blur" },
        ],
        mobile: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkMobile, trigger: "blur" },
        ],
        email: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkEmail, trigger: "blur" },
        ],
        emergencyContactPhone: [{ validator: checkMobile, trigger: "blur" }],
        major: [
          { required: true, message: "培训专业不能为空", trigger: "blur" },
        ],
        personnelType: [
          { required: false, message: "人员类型不能为空", trigger: "blur" },
        ],
        dispatchingUnit: [
          { required: false, message: "派送单位不能为空", trigger: "blur" },
        ],
      },
      activeStep: 0,
      open: false,
      majorList: [],
      standardSchemeList: [],
      mentorList: [],
    };
  },
  watch: {
    dialogOpen(newVal) {
      if (newVal) {
        getMentorSimpleList().then((response) => {
          this.mentorList = response.data;
        });
        this.reset();
        if (this.opt !== "add") {
          this.rules.personnelType[0].required = false;
          this.rules.dispatchingUnit[0].required = false;
          const id = this.userId;

          getUserStudent(id).then(async (response) => {
            const row = response.data;
            await this.getSimpleMajorListHandler(row);
            await this.getStandardScheme(row);

            this.form = response.data;
            if (this.form.studentType == 1) {
              this.rules.personnelType[0].required = true;
            }
            if (this.form.personnelType == 2 || this.form.personnelType == 4) {
              this.rules.dispatchingUnit[0].required = true;
            }

            if (response.data.hasMedicalLicense !== null) {
              this.form.hasMedicalLicense =
                response.data.hasMedicalLicense.toString();
            }

            if (response.data.hasPracticingLicense !== null) {
              this.form.hasPracticingLicense =
                response.data.hasPracticingLicense.toString();
            }
            if (response.data.obtainedPrescription !== null) {
              this.form.obtainedPrescription =
                response.data.obtainedPrescription.toString();
            }

            if (response.data.cultivationPlanFile) {
              this.form.cultivationPlanFile = JSON.parse(
                response.data.cultivationPlanFile
              );
            }

            if (response.data.proposalFile) {
              this.form.proposalFile = JSON.parse(response.data.proposalFile);
            }

            if (response.data.midTermAssessmentFile) {
              this.form.midTermAssessmentFile = JSON.parse(
                response.data.midTermAssessmentFile
              );
            }

            if (response.data.graduationThesisFile) {
              this.form.graduationThesisFile = JSON.parse(
                response.data.graduationThesisFile
              );
            }

            if (response.data.degreeTranscriptFile) {
              this.form.degreeTranscriptFile = JSON.parse(
                response.data.degreeTranscriptFile
              );
            }

            if (response.data.otherFile) {
              this.form.otherFile = JSON.parse(response.data.otherFile);
            }

            this.open = newVal;
          });
        } else {
          this.open = newVal;
        }
      } else {
        this.open = newVal;
      }
    },
  },
  computed: {
    majorDisabled() {
      return (
        this.form.id &&
        this.form.id.toString() !== "" &&
        this.form.major !== null &&
        this.form.major.toString() !== ""
      );
    },
    standardSchemeDisabled() {
      return (
        this.form.id &&
        this.form.id.toString() !== "" &&
        this.form.standardSchemeId !== null &&
        this.form.standardSchemeId.toString() !== ""
      );
    },
  },
  methods: {
    cancel() {
      this.$emit("update:dialogOpen", false);
      this.reset();
    },
    /** 上一步*/
    handlePreStep() {
      this.activeStep = this.activeStep - 1;
    },
    /** 下一步*/
    handleNextStep() {
      this.activeStep = this.activeStep + 1;
    },
    /** 切换步骤条*/
    changeStep(val) {
      this.activeStep = val;
    },
    /** 表单重置 */
    reset() {
      this.activeStep = 0;
      this.form = {
        id: undefined,
        username: undefined,
        nickname: undefined,
        studentGroup: undefined,
        avatar: undefined,
        sex: undefined,
        education: undefined,
        email: undefined,
        mobile: undefined,
        certificateType: undefined,
        certificateNumber: undefined,
        birthday: undefined,
        maritalStatus: undefined,
        nation: undefined,
        isPartyMember: undefined,
        healthStatus: undefined,
        depositBank: undefined,
        bankCardNo: undefined,
        residenceProvince: undefined,
        computerAbility: undefined,
        foreignLanguageAbility: undefined,
        hobby: undefined,
        postalAddress: undefined,
        postalCode: undefined,
        emergencyContact: undefined,
        relationship: undefined,
        emergencyContactPhone: undefined,
        emergencyContactAddres: undefined,
        studentType: undefined,
        personnelType: undefined,
        trainingStartDate: undefined,
        grade: undefined,
        major: undefined,
        standardSchemeId: undefined,
        rotationTime: undefined,
        graduationYear: undefined,
        graduationDate: undefined,
        responsibleMentorUserId: undefined,
        dispatchingUnit: undefined,
        obtainedPrescription: "false",
        hasMedicalLicense: "false",
        medicalLicenseDate: undefined,
        medicalLicenseLevel: undefined,
        medicalLicenseNumber: undefined,
        medicalLicenseCategory: undefined,
        hasPracticingLicense: "false",
        practicingLicenseDate: undefined,
        practicingType: undefined,
        practicingLicenseNumber: undefined,
        practicingScope: undefined,
        medicalLicenseImage: "",
        practicingLicenseImage: "",
        educationCertificate: undefined,
        degreeCertificate: undefined,
        degree: undefined,
        graduationSchool: undefined,
        graduationSchoolDate: undefined,
        trainingStatus: undefined,
        cultivationPlanFile: "",
        proposalFile: "",
        midTermAssessmentFile: "",
        graduationThesisFile: "",
        degreeTranscriptFile: "",
        otherFile: "",
      };
      this.resetForm("form");
    },

    /** 设置头像*/
    getAvatar(picUrl) {
      this.form.avatar = picUrl;
    },

    /** 获取身份证相关信息*/
    getIdcardInfoHandler(e) {
      if (this.form.certificateType == 1) {
        const params = {
          certificateNumber: this.form.certificateNumber,
          certificateType: "1",
        };
        getIdcardInfo(params).then((response) => {
          let birthday = response.data.birthday;
          let tempARR = birthday.split("");
          tempARR.splice(4, 0, "-");
          tempARR.splice(7, 0, "-");
          const _birthday = tempARR.join("");

          this.form.sex = response.data.sex;
          this.form.birthday = _birthday;
        });
      }
    },

    async getSimpleMajorListHandler(row) {
      const params = {
        studentType: row.studentType,
      };
      try {
        const res = await getSimpleMajorList(params);
        this.majorList = res.data;
      } catch (error) {
        this.majorList = [];
      }
    },

    async getStandardScheme(row) {
      const { studentType, major } = row;
      if (studentType && major) {
        const params = {
          studentType,
          major,
        };
        try {
          const res = await getStandardSchemeList(params);
          this.standardSchemeList = res.data;
        } catch (error) {
          this.standardSchemeList = [];
        }
      }
    },

    /** 获取方案列表*/
    getStandardSchemeListHandler() {
      this.standardSchemeList = [];
      this.form.standardSchemeId = "";
      this.rules.personnelType[0].required = false;
      if (this.form.studentType == 1) {
        this.rules.personnelType[0].required = true;
      }
      this.getSimpleMajorListHandler(this.form);
      this.getStandardScheme(this.form);
    },

    personnelTypeChange(val) {
      this.rules.dispatchingUnit[0].required = false;
      if (val == 2 || val == 4) {
        this.rules.dispatchingUnit[0].required = true;
      }
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = {
          ...this.form,
        };
        params.cultivationPlanFile = JSON.stringify(
          this.form.cultivationPlanFile || []
        );
        params.proposalFile = JSON.stringify(this.form.proposalFile || []);
        params.midTermAssessmentFile = JSON.stringify(
          this.form.midTermAssessmentFile || []
        );
        params.graduationThesisFile = JSON.stringify(
          this.form.graduationThesisFile || []
        );
        params.degreeTranscriptFile = JSON.stringify(
          this.form.degreeTranscriptFile || []
        );
        params.otherFile = JSON.stringify(this.form.otherFile || []);

        // 修改的提交
        if (this.form.id != null) {
          updateUserStudent(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.cancel();
            this.$emit("refresh");
          });
          return;
        }
        // 添加的提交
        this.$confirm("保存后学员类型、培训方案、培训专业等不能再修改")
          .then((_) => {
            createUserStudent(params).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.cancel();
              this.$emit("refresh");
            });
          })
          .catch((_) => {});
      });
    },
  },
};
</script>

<style lang="scss"></style>
