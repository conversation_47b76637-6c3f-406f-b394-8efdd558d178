<template>
  <div class="container">
    <div class="logo">{{ loginTitle }}</div>
    <!-- 登录区域 -->
    <div class="content">
      <!-- 配图 -->
      <div class="pic"></div>
      <!-- 表单 -->
      <div class="field">
        <!-- [移动端]标题 -->
        <h2 class="mobile-title">
          <h3 class="title">后台管理系统</h3>
        </h2>

        <!-- 表单 -->
        <div class="form-cont">
          <div class="form-logo">
            <img
              v-if="formlogo"
              height="60"
              :src="require(`../assets/images/${formlogo}.png`)"
            />
          </div>
          <el-tabs
            class="form"
            v-model="loginForm.loginType"
            @tab-click="handleTabsClick"
            style="float: none"
          >
            <el-tab-pane label="账号密码登录" name="uname"> </el-tab-pane>
            <el-tab-pane
              v-if="showWeixinLogin"
              label="微信扫码登录"
              name="weixin"
            >
            </el-tab-pane>
            <!-- <el-tab-pane label="短信验证码登录" name="sms">
            </el-tab-pane> -->
          </el-tabs>
          <div>
            <el-form
              ref="loginForm"
              :model="loginForm"
              :rules="LoginRules"
              class="login-form"
            >
              <el-form-item prop="tenantName" v-if="tenantEnable">
                <el-input
                  v-model="loginForm.tenantName"
                  type="text"
                  auto-complete="off"
                  placeholder="租户"
                >
                  <svg-icon
                    slot="prefix"
                    icon-class="tree"
                    class="el-input__icon input-icon"
                  />
                </el-input>
              </el-form-item>
              <!-- 账号密码登录 -->
              <div v-if="loginForm.loginType === 'uname'">
                <div
                  v-if="scanStatus === 'scanned'"
                  style="margin-bottom: 10px"
                >
                  <el-alert
                    title="还未绑定账号密码，请绑定！"
                    :closable="false"
                    type="warning"
                  >
                  </el-alert>
                </div>
                <el-form-item prop="username">
                  <el-input
                    v-model="loginForm.username"
                    type="text"
                    auto-complete="off"
                    placeholder="账号"
                  >
                    <svg-icon
                      slot="prefix"
                      icon-class="user"
                      class="el-input__icon input-icon"
                    />
                  </el-input>
                </el-form-item>
                <el-form-item prop="password">
                  <el-input
                    v-model="loginForm.password"
                    type="password"
                    auto-complete="off"
                    show-password
                    placeholder="密码"
                    @keyup.enter.native="getCode"
                  >
                    <svg-icon
                      slot="prefix"
                      icon-class="password"
                      class="el-input__icon input-icon"
                    />
                  </el-input>
                </el-form-item>
                <el-checkbox
                  v-model="loginForm.rememberMe"
                  style="margin: 0 0 25px 0"
                  >记住密码</el-checkbox
                >
              </div>

              <!-- 微信扫码登录 -->
              <div v-if="loginForm.loginType === 'weixin'" class="weixin-box">
                <img :src="weixinQrCode" />
                <div class="weixin-tips">打开微信扫一扫</div>
              </div>

              <!-- 短信验证码登录 -->
              <div v-if="loginForm.loginType === 'sms'">
                <el-form-item prop="mobile">
                  <el-input
                    v-model="loginForm.mobile"
                    type="text"
                    auto-complete="off"
                    placeholder="请输入手机号"
                  >
                    <svg-icon
                      slot="prefix"
                      icon-class="phone"
                      class="el-input__icon input-icon"
                    />
                  </el-input>
                </el-form-item>
                <el-form-item prop="mobileCode">
                  <el-input
                    v-model="loginForm.mobileCode"
                    type="text"
                    auto-complete="off"
                    placeholder="短信验证码"
                    @keyup.enter.native="handleLogin"
                  >
                    <template slot="icon">
                      <svg-icon
                        slot="prefix"
                        icon-class="password"
                        class="el-input__icon input-icon"
                      />
                    </template>
                    <template slot="append">
                      <span
                        v-if="mobileCodeTimer <= 0"
                        class="getMobileCode"
                        @click="getSmsCode"
                        style="cursor: pointer"
                        >获取验证码</span
                      >
                      <span v-if="mobileCodeTimer > 0" class="getMobileCode"
                        >{{ mobileCodeTimer }}秒后可重新获取</span
                      >
                    </template>
                  </el-input>
                </el-form-item>
              </div>

              <!-- 下方的登录按钮 -->
              <el-form-item
                v-if="loginForm.loginType !== 'weixin'"
                style="width: 100%"
              >
                <div class="login-register-buttons">
                  <el-button
                    :loading="loading"
                    size="medium"
                    type="primary"
                    @click.native.prevent="getCode"
                  >
                    <span v-if="!loading">登 录</span>
                    <span v-else>登 录 中...</span>
                  </el-button>
                  <el-button
                    v-if="showRegister"
                    size="medium"
                    type="primary"
                    plain
                    @click="$router.push('register')"
                    >注册</el-button
                  >
                </div>
              </el-form-item>

              <!--  社交登录 -->
              <!-- <el-form-item style="width:100%;">
                  <div class="oauth-login" style="display:flex">
                    <div class="oauth-login-item" v-for="item in SysUserSocialTypeEnum" :key="item.type" @click="doSocialLogin(item)">
                      <img :src="item.img" height="25px" width="25px" alt="登录" >
                      <span>{{item.title}}</span>
                    </div>
                </div>
              </el-form-item> -->
            </el-form>
          </div>
        </div>
      </div>
      <!-- 公告 -->
      <div class="notice" v-if="notices.length > 0 && showNotice">
        <div class="notice-title">
          <strong>通知公告</strong>
          <el-link @click="handleToMore">查看更多</el-link>
        </div>
        <div class="notice-item" v-for="(item, index) in notices" :key="index">
          <el-link :underline="false" @click="handleToView(item.id)">{{
            item.title
          }}</el-link>
          <span>{{ item.createTime }}</span>
        </div>
      </div>
    </div>

    <!-- 图形验证码 -->
    <Verify
      ref="verify"
      :captcha-type="'blockPuzzle'"
      :img-size="{ width: '400px', height: '200px' }"
      @success="handleLogin"
    />

    <!-- footer -->
    <div class="footer" style="color: white">
      {{ footerText }}
      <!-- 主办单位: 龙岗区人民医院 技术支持: 佛山锦延科技有限公司 联系电话: 0757-85689139 -->
      <!-- Copyright © 2020-2022 iocoder.cn All Rights Reserved.-->
    </div>
  </div>
</template>

<script>
// import formlogo from '@/assets/images/logo.png'
import {
  sendSmsCode,
  socialAuthRedirect,
  loginQrCode,
  wxLogin,
} from "@/api/login";
import { getTenantIdByName } from "@/api/system/tenant";
import { SystemUserSocialTypeEnum } from "@/utils/constants";
import { getCaptchaEnable, getTenantEnable } from "@/utils/ruoyi";
import {
  getPassword,
  getRememberMe,
  getTenantName,
  getUsername,
  removePassword,
  removeRememberMe,
  removeTenantName,
  removeUsername,
  setPassword,
  setRememberMe,
  setTenantId,
  setTenantName,
  setUsername,
  setToken,
} from "@/utils/auth";
// import {setToken, removeToken, getAccessToken} from '@/utils/auth'
import { getForeignNoticeInfoPage } from "@/api/rotation/noticeInfo";
import Verify from "@/components/Verifition/Verify";

export default {
  name: "Login",
  components: {
    Verify,
  },
  data() {
    return {
      formlogo: process.env.VUE_APP_LOGIN_LOGO,
      loginTitle: process.env.VUE_APP_LOGIN_TITLE,
      footerText: process.env.VUE_APP_FOOTER,
      showWeixinLogin: false,
      codeUrl: "",
      captchaEnable: true,
      tenantEnable: false,
      mobileCodeTimer: 0,
      loginForm: {
        loginType: "uname",
        username: "",
        password: "",
        captchaVerification: "",
        mobile: "",
        mobileCode: "",
        rememberMe: false,
        tenantName: "芋道源码",
        openid: "",
      },
      scene: 21,

      LoginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" },
        ],
        mobile: [
          { required: true, trigger: "blur", message: "手机号不能为空" },
          {
            validator: function (rule, value, callback) {
              if (
                /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/.test(
                  value
                ) === false
              ) {
                callback(new Error("手机号格式错误"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        tenantName: [
          { required: true, trigger: "blur", message: "租户不能为空" },
          {
            validator: (rule, value, callback) => {
              // debugger
              getTenantIdByName(value).then((res) => {
                const tenantId = res.data;
                if (tenantId && tenantId >= 0) {
                  // 设置租户
                  setTenantId(tenantId);
                  callback();
                } else {
                  callback("租户不存在");
                }
              });
            },
            trigger: "blur",
          },
        ],
      },
      loading: false,
      redirect: undefined,
      // 枚举
      SysUserSocialTypeEnum: SystemUserSocialTypeEnum,

      weixinQrCode: localStorage.getItem("weixinLoginQrCode") || "", //微信二维码
      timer: null,
      scanStatus: "", // 微信扫码登录状态 notscanned没有进行扫码,scanned 表示用户已扫码, loggedin 表示用户已绑定用户
      notices: [],

      showNotice: false,
      showRegister: false,
    };
  },
  async created() {
    console.log(
      "process.env.VUE_APP_WEIXINLOGIN",
      process.env.VUE_APP_WEIXINLOGIN,
      typeof process.env.VUE_APP_WEIXINLOGIN
    );
    try {
      await this.getConfigKey("login-notice-show").then((res) => {
        this.showNotice = res.data === "true";
      });
      await this.getConfigKey("yudao.wx.enable").then((res) => {
        this.showWeixinLogin = res.data === "true";
      });
      await this.getConfigKey("yudao.pc.register").then((res) => {
        this.showRegister = res.data === "true";
      });
    } catch (err) {
      console.log(err);
    }
    // 租户开关
    this.tenantEnable = getTenantEnable();
    // 验证码开关
    this.captchaEnable = getCaptchaEnable();
    // 重定向地址
    this.redirect = this.$route.query.redirect
      ? decodeURIComponent(this.$route.query.redirect)
      : undefined;
    this.getCookie();
    // 院外通知
    getForeignNoticeInfoPage({ pageNo: 1, pageSize: 2 }).then(
      (res) => (this.notices = res.data?.list || [])
    );
  },
  methods: {
    handleTabsClick(tab) {
      const tabName = tab.name;
      if (tabName === "weixin") {
        loginQrCode().then((res) => {
          const { code, data, msg } = res;
          if (code === 0) {
            this.weixinQrCode = data.url;
            localStorage.setItem("weixinLoginQrCode", this.weixinQrCode);

            this.createSetInterval(data.scene);
          } else {
            this.$modal.msgError(msg);
          }
        });
      } else {
        this.stopSetInterval();
      }
    },
    handleWxLogin(scene) {
      wxLogin({ scene }).then((res) => {
        const { code, data, msg } = res;
        if (code === 0) {
          const { scanStatus, openid, authLoginRespVO } = data;
          this.scanStatus = scanStatus;
          if (scanStatus === "scanned") {
            this.loginForm.openid = openid;
            this.loginForm.loginType = "uname";
            this.stopSetInterval();
          }
          if (scanStatus === "loggedin") {
            setToken(authLoginRespVO);
            this.stopSetInterval();
            this.$router.push({ path: this.redirect || "/" }).catch(() => {});
          }
        }
      });
    },
    createSetInterval(scene) {
      this.stopSetInterval();
      let _this = this;
      this.timer = setInterval(() => {
        _this.handleWxLogin(scene);
      }, 5000);
    },
    handleToMore() {
      const { href } = this.$router.resolve("/notices");
      window.open(href, "_blank");
    },
    handleToView(id) {
      const { href } = this.$router.resolve(`/notices?id=${id}`);
      window.open(href, "_blank");
    },
    // 关闭轮询
    stopSetInterval() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    getCode() {
      // 情况一，未开启：则直接登录
      if (!this.captchaEnable) {
        this.handleLogin({});
        return;
      }

      // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
      // 弹出验证码
      this.$refs.verify.show();
    },
    getCookie() {
      const username = getUsername();
      const password = getPassword();
      const rememberMe = getRememberMe();
      const tenantName = getTenantName();
      this.loginForm = {
        ...this.loginForm,
        username: username ? username : this.loginForm.username,
        password: password ? password : this.loginForm.password,
        rememberMe: rememberMe ? getRememberMe() : false,
        tenantName: tenantName ? tenantName : this.loginForm.tenantName,
      };
    },
    handleLogin(captchaParams) {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          // 设置 Cookie
          if (this.loginForm.rememberMe) {
            setUsername(this.loginForm.username);
            setPassword(this.loginForm.password);
            setRememberMe(this.loginForm.rememberMe);
            setTenantName(this.loginForm.tenantName);
          } else {
            removeUsername();
            removePassword();
            removeRememberMe();
            removeTenantName();
          }
          this.loginForm.captchaVerification =
            captchaParams.captchaVerification;
          // 发起登陆
          // console.log("发起登录", this.loginForm);
          this.$store
            .dispatch(
              this.loginForm.loginType === "sms" ? "SmsLogin" : "Login",
              this.loginForm
            )
            .then(() => {
              this.$router.push({ path: this.redirect || "/" }).catch(() => {});
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    doSocialLogin(socialTypeEnum) {
      // 设置登录中
      this.loading = true;
      // 计算 redirectUri
      const redirectUri =
        location.origin +
        "/social-login?type=" +
        socialTypeEnum.type +
        "&redirect=" +
        (this.redirect || "/"); // 重定向不能丢
      // const redirectUri = 'http://127.0.0.1:48080/api/gitee/callback';
      // const redirectUri = 'http://127.0.0.1:48080/api/dingtalk/callback';
      // 进行跳转
      socialAuthRedirect(
        socialTypeEnum.type,
        encodeURIComponent(redirectUri)
      ).then((res) => {
        // console.log(res.url);
        window.location.href = res.data;
      });
    },
    /** ========== 以下为升级短信登录 ========== */
    getSmsCode() {
      if (this.mobileCodeTimer > 0) return;
      this.$refs.loginForm.validate((valid) => {
        if (!valid) return;
        sendSmsCode(
          this.loginForm.mobile,
          this.scene,
          this.loginForm.uuid,
          this.loginForm.code
        ).then((res) => {
          this.$modal.msgSuccess("获取验证码成功");
          this.mobileCodeTimer = 60;
          let msgTimer = setInterval(() => {
            this.mobileCodeTimer = this.mobileCodeTimer - 1;
            if (this.mobileCodeTimer <= 0) {
              clearInterval(msgTimer);
            }
          }, 1000);
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/login.scss";

.oauth-login {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.oauth-login-item {
  display: flex;
  align-items: center;
  margin-right: 10px;
}
.oauth-login-item img {
  height: 25px;
  width: 25px;
}
.oauth-login-item span:hover {
  text-decoration: underline red;
  color: red;
}
.login-register-buttons {
  display: flex;
  ::v-deep .el-button {
    flex-grow: 1;
    width: 1px;
  }
}

.weixin-box {
  height: 218px;
  text-align: center;

  img {
    width: 188px;
    height: 188px;
    margin: 0 auto;
  }

  .weixin-tips {
    text-align: center;
    font-size: 12px;
  }
}

.notice {
  position: absolute;
  bottom: -80px;
  background: #fff;
  width: 100%;
  height: 100px;
  padding: 15px 20px;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  border-top: 1px solid #dcdfe6;

  .notice-title {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    align-items: center;
    a {
      font-size: 14px;
      font-weight: 400;
      color: #1f8ffb;
    }
  }

  .notice-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;
    .el-link {
      overflow: hidden;
      text-overflow: ellipsis;
      display: initial;
      white-space: nowrap;
    }
    span {
      color: #777;
      flex-shrink: 0;
    }
  }
}
</style>
