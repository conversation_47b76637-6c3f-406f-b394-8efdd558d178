<template>
  <div class="notice-view">
    <template v-if="loading">
      <el-skeleton style="margin-bottom: 40px">
        <template v-slot:template>
          <div class="notice-title">
            <el-skeleton-item variant="h3" style="width: 60%" />
          </div>
          <div class="notice-info">
            <el-skeleton-item variant="text" style="width: 30%" />
          </div>
        </template>
      </el-skeleton>
      <el-skeleton :rows="6"></el-skeleton>
    </template>

    <template v-else>
      <h2 class="notice-title">{{ title }}</h2>
      <div class="notice-info">
        发布人：{{ creatorNickname }}
        <el-divider direction="vertical"></el-divider>
        发布日期：{{ formatDate(createTime).slice(0, 10) }}
        <el-divider direction="vertical"></el-divider>
        浏览次数：{{ readCount }}
      </div>
      <div class="notice-content ql-editor" v-html="content"></div>
      <div class="notice-files" v-if="files.length > 0">
        <strong>附件：</strong>
        <file-upload-info v-model="files" disabled/>
      </div>
    </template>
  </div>
</template>

<script>
import { viewNoticeInfo } from "@/api/rotation/noticeInfo";
import FileUploadInfo from '@/components/FileUploadInfo';
import { formatDate } from '@/utils';
import "quill/dist/quill.core.css";

export default {
  name: "noticeView",
  components: { FileUploadInfo },
  data() {
    return {
      title: "",
      content: "",
      files: [],
      creatorNickname: "",
      createTime: "",
      readCount: 0,
      loading: false,
    }
  },
  created() {
    this.loading = true;
    viewNoticeInfo(this.$route.query.id).then(res => {
      this.title = res.data.title;
      this.content = res.data.content;
      this.creatorNickname = res.data.creatorNickname;
      this.createTime = res.data.createTime;
      this.readCount = res.data.readCount;
      this.files = res.data.files ? JSON.parse(res.data.files) : [];
    }).finally(() => this.loading = false);
  },
  methods: {
    formatDate
  },
}
</script>

<style lang="scss" scoped>
.notice-view {
  padding: 20px 50px 40px 50px;
}

.notice-title {
  font-size: 22px;
  line-height: 1.5;
  text-align: center;
  margin-bottom: 20px;
}

.notice-info {
  font-size: 14px;
  color: #666;
  text-align: right;
}

.notice-content {
  font-size: 15px;
  padding: 20px 0;
}

.notice-files {
  font-size: 14px;
}
</style>
