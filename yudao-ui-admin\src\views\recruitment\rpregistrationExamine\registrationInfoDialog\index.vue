<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="1200px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <recruitment-form-residency
      :planId="planId"
      :recruitmentRegistrationId="recruitmentRegistrationId"
      :key="recruitmentRegistrationId"
      readonly
    />
    <div slot="footer" class="dialog-footer">
      <el-button
        v-if="formData.reportExamineStatus === 'to_be_examine'"
        type="primary"
        @click="handleAudit(formData)"
        v-hasPermi="['recruitment:rpregistration-examine:update']"
      >
        审批
      </el-button>
      <el-button
        v-if="
          formData.reportExamineStatus === 'to_be_examine' &&
          formData.isObeyAdjustment
        "
        type="primary"
        @click="handleEdit(formData)"
        v-hasPermi="['recruitment:rpregistration-examine:update']"
      >
        修改专业
      </el-button>
      <el-button @click="cancel">关闭</el-button>
    </div>

    <audit-dialog
      :title="auditDialogTitle"
      :openAudit="openAudit"
      :formData="formData"
      @update:openAudit="(value) => (openAudit = value)"
      @refresh="refresh"
    />

    <update-dialog
      :title="updateDialogTitle"
      :openUpdate="openUpdate"
      :formData="formData"
      :majorList="planMajorList"
      @update:openUpdate="(value) => (openUpdate = value)"
      @refresh="refresh"
    />
  </el-dialog>
</template>

<script>
import AuditDialog from "../auditDialog";
import UpdateDialog from "../updateDialog";
import RecruitmentFormResidency from "../../fill/recruitment-form-residency.vue";
import { getPlanMajorList } from "@/api/recruitment/rpregistrationExamine";

export default {
  name: "RegistrationInfoDialog",
  components: { RecruitmentFormResidency, AuditDialog, UpdateDialog },
  props: {
    title: {
      type: String,
    },
    openRegistrationInfo: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      open: this.openRegistrationInfo,
      openAudit: false,
      auditDialogTitle: "审核信息",
      openUpdate: false,
      updateDialogTitle: "专业修改",
      planMajorList: [],
    };
  },
  computed: {
    recruitmentRegistrationId() {
      return this.formData?.recruitmentRegistrationId?.toString();
    },
    planId() {
      return this.formData?.planId?.toString();
    },
  },
  watch: {
    openRegistrationInfo(newVal) {
      this.open = newVal;
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:openRegistrationInfo", false);
    },

    refresh() {
      this.$emit("refresh");
      this.cancel();
    },

    handleAudit(row) {
      this.auditDialogTitle = `审核确认-${row.name}`;
      this.openAudit = true;
    },

    handleEdit(row) {
      this.updateDialogTitle = `专业修改-${row.name}`;
      getPlanMajorList(row.recruitmentRegistrationId).then((response) => {
        this.planMajorList = response.data;
        this.openUpdate = true;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
