<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="医院科室" prop="departmentName">
        <el-input
          v-model="queryParams.departmentName"
          placeholder="请输入医院科室"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="轮转科室" prop="rotationDepartmentName">
        <el-input
          v-model="queryParams.rotationDepartmentName"
          placeholder="请输入轮转科室"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-date-picker
          v-model="queryParams.grade"
          type="year"
          format="yyyy"
          value-format="yyyy"
          placeholder="选择年级"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          filterable
          placeholder="请选择学员类型"
          @change="handleQueryStudentTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          filterable
          clearable
          size="small"
          @change="handleQuery"
        >
          <!-- <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_MAJOR)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/> -->
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="派送单位" prop="dispatchingUnit">
        <el-input
          v-model="queryParams.dispatchingUnit"
          placeholder="请输入派送单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="轮转时间" prop="rotationDates">
        <el-date-picker
          v-model="queryParams.rotationDates"
          style="width: 240px"
          value-format="yyyy-MM"
          type="monthrange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :clearable="false"
          @change="rotationDatesChange"
        />
      </el-form-item>
      <el-form-item label="人员类型" prop="personnelTypes">
        <el-select
          v-model="queryParams.personnelTypes"
          multiple
          filterable
          placeholder="请选择人员类型"
          clearable
          size="small"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-radio-group
          v-model="queryParams.statisticType"
          size="small"
          style="margin-right: 10px"
          @change="rotationDatesChange"
        >
          <el-radio-button label="0">按月统计</el-radio-button>
          <el-radio-button label="1">按周统计</el-radio-button>
        </el-radio-group>
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="small"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:deptplan:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="医院科室"
        align="center"
        prop="departmentName"
        fixed
      />
      <el-table-column
        label="轮转科室"
        align="center"
        prop="rotationDepartmentName"
        fixed
      />
      <el-table-column
        v-for="period in queryParams.statisticType === '0'
          ? monthList
          : weekList"
        :key="period"
        :label="period"
        :prop="period"
        align="center"
        min-width="100px"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleClick(scope.row, period)">{{
            scope.row[period]
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <students-dialog
      :dialogVisible="dialogVisible"
      :title="title"
      :queryParams="queryParams"
      :curRow="curRow"
      :period="period"
      @update:dialogVisible="(value) => (dialogVisible = value)"
    />
  </div>
</template>

<script>
import {
  getDeptplanList,
  exportDeptplanExcel,
} from "@/api/rotation/deptRotationInfo";
import { getSimpleMajorList } from "@/api/system/major";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import StudentsDialog from "./studentsDialog";

dayjs.extend(isoWeek);

export default {
  name: "DeptRotationInfo",
  components: { StudentsDialog },
  data() {
    const start = dayjs().format("YYYY-MM");
    const end = dayjs().add(6, "month").format("YYYY-MM");
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 职工用户列表
      list: [],
      queryMajorList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        departmentName: null,
        rotationDepartmentName: null,
        dispatchingUnit: null,
        grade: null,
        major: null,
        personnelTypes: null,
        studentType: null,
        rotationDates: [start, end],
        statisticType: "0",
      },
      monthList: [],
      weekList: [],
      dialogVisible: false,
      title: "",
      curRow: "",
      period: "",
    };
  },
  created() {
    this.rotationDatesChange();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getDeptplanList(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryMajorList = [];
      this.resetForm("queryForm");
      this.rotationDatesChange();
    },
    /** 改变轮转时间，计算时间表头 */
    rotationDatesChange() {
      const { statisticType, rotationDates } = this.queryParams;
      const [start, end] = rotationDates;
      if (statisticType === "0") {
        const monthDiff = dayjs(end).diff(start, "month");
        let monthList = [];
        for (let i = 0; i <= monthDiff; i++) {
          const month = dayjs(start).add(i, "month").format("YYYY-MM");
          monthList.push(month);
        }
        this.monthList = monthList;
      } else {
        const weekDiff = dayjs(end).endOf("month").diff(start, "week");
        let weekList = [];
        for (let i = 0; i <= weekDiff; i++) {
          const day = dayjs(start).add(i, "week");
          const week = `${day.startOf("isoWeek").format("YYYYMMDD")}-${day
            .endOf("isoWeek")
            .format("YYYYMMDD")}`;
          weekList.push(week);
        }
        this.weekList = weekList;
      }
      this.handleQuery();
    },
    /** 查询学员类型改变 */
    handleQueryStudentTypeChange(value) {
      this.queryParams.major = null;
      this.queryMajorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.queryMajorList = res.data;
        this.handleQuery();
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有科室轮转信息?")
        .then(() => {
          this.exportLoading = true;
          return exportDeptplanExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "科室轮转信息.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    handleClick(row, period) {
      if (row[period] === 0) {
        return;
      }
      this.title = `${period}[${row.rotationDepartmentName}]学员信息`;
      this.curRow = row;
      this.period = period;
      this.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss"></style>
