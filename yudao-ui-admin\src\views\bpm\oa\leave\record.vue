<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="请假类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择请假类型"
          clearable
        >
          <el-option
            v-for="dict in leaveTypeDictData"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="请假时间" prop="leaveTime">
        <el-date-picker
          v-model="queryParams.leaveTime"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="结果" prop="result">
        <el-select
          v-model="queryParams.result"
          placeholder="请选择流结果"
          clearable
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="原因" prop="reason">
        <el-input
          v-model="queryParams.reason"
          placeholder="请输入原因"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请假人" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入请假人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否销假" prop="cancellation">
        <el-select v-model="queryParams.cancellation" placeholder="请选择是否销假" clearable>
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          clearable
        >
          <el-option
            v-for="item in gradeList"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          clearable
          filterable
        >
          <el-option
            v-for="item in majorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="轮转科室" prop="rotationDepartmentId">
        <el-select
          v-model="queryParams.rotationDepartmentId"
          placeholder="请选择轮转科室"
          clearable
          filterable
        >
          <el-option
            v-for="item in rotationDepartmentList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          icon="el-icon-plus"
          v-hasPermi="['bpm:oa-leave-record:create']"
          @click="handleAdd"
          >添加请假记录</el-button
        >
        <el-button
          plain
          size="mini"
          icon="el-icon-download"
          :loading="exportLoading"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="请假人"
        align="center"
        prop="nickname"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column
        label="登录账号"
        align="center"
        prop="username"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column label="学员类型" align="center" prop="studentTypeName" />
      <el-table-column label="人员类型" width="100" align="center" prop="personnelType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE"
            :value="scope.row.personnelType"
          />
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center" prop="result">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT"
            :value="scope.row.result"
          />
        </template>
      </el-table-column>
      <el-table-column label="审批节点" width="120" align="center" prop="bpmTaskName" />
      <el-table-column label="是否销假" align="center" prop="cancellation">
        <template slot-scope="scope">
          <span :style="{ color: scope.row.cancellation ? 'green' : 'red' }">{{ scope.row.cancellation ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column
        label="培训专业"
        align="center"
        prop="majorName"
        show-overflow-tooltip
      />
      <el-table-column
        label="轮转科室"
        align="center"
        prop="rotationDepartmentName"
        show-overflow-tooltip
      />
      <el-table-column
        label="请假开始时间"
        align="center"
        prop="startTime"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请假天数" align="center" prop="day" />
      <el-table-column label="请假类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.BPM_OA_LEAVE_TYPE"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="原因"
        align="center"
        prop="reason"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="申请时间"
        align="center"
        prop="applyTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="100"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleProcessDetail(scope.row)"
            >审批进度</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加请假记录 -->
    <el-dialog
      title="添加请假记录"
      :visible.sync="open"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        class="oa-record-dialog-form"
      >
        <el-form-item label="请假人" prop="studentUserId">
          <el-select
            style="width: 100%"
            v-model="form.studentUserId"
            filterable
            @change="handleStudentChange"
          >
            <el-option
              v-for="item in userStudentList"
              :key="item.id"
              :label="`${item.nickname}(${item.username})`"
              :value="item.id"
            ></el-option>
          </el-select>
          <div class="student-user-info" v-if="curStudent">
            <span
              >学员类型：{{
                getDictDataLabel(
                  DICT_TYPE.SYSTEM_STUDENT_TYPE,
                  curStudent.studentType
                )
              }}</span
            >
            <span>年级：{{ curStudent.grade }}</span>
            <span>培训专业：{{ curStudent.majorName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="请假类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择"
            style="width: 100%"
            @change="leaveTypeChange"
          >
            <el-option
              v-for="dict in leaveTypeList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="请假时间" prop="timeArr">
          <el-date-picker
            style="width: 100%"
            v-model="form.timeArr"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            popper-class="oa-record-dialog-dataPicker"
            @change="onDatePickerChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="请假时长" prop="day">
          <el-input v-model="form.day">
            <template slot="prepend">共</template>
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <el-form-item label="原因" prop="reason">
          <el-input
            type="textarea"
            :rows="3"
            v-model="form.reason"
            placeholder="请输入原因"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="附件上传" prop="pictures">
          <file-upload
            v-model="form.pictures"
            :limit="999"
            :fileSize="50"
            :fileType="null"
          ></file-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitForm"
          >提 交</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getLeaveManagePage,
  manageCreateLeave,
  getLeaveStudentUserConfig,
  exportLeaveRecordExcel,
} from "@/api/bpm/leave";
import { getDictDatas, DICT_TYPE } from "@/utils/dict";
import FileUpload from "@/components/FileUploadInfo";
import { getScheduleByRotationDatesStudent } from "@/api/rotation/schedule";
import {
  getUserStudentList,
  getStudentGradeList,
} from "@/api/system/userStudent";
import { getRotationDepartmentSimpleList } from "@/api/system/department";
import { getSimpleMajorList } from "@/api/system/major";
import { getConfigKey } from "@/api/infra/config";

export default {
  name: "LeaveRecord",
  components: { FileUpload },
  data() {
    return {
      leaveTypeDictData: getDictDatas(DICT_TYPE.BPM_OA_LEAVE_TYPE),
      leaveResultData: getDictDatas(DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT),
      typeDictData: getDictDatas(DICT_TYPE.BPM_OA_LEAVE_TYPE),
      gradeList: [],
      majorList: [],
      rotationDepartmentList: [],
      userStudentList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        result: null,
        type: null,
        reason: null,
        leaveTime: [],
        nickname: null,
        grade: null,
        major: null,
        rotationDepartmentId: null,
        username: null,
        cancellation: null,
      },
      // 显示搜索条件
      showSearch: true,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 请假申请列表
      list: [],
      // 是否显示弹出层
      open: false,
      // 表单数据
      form: {},
      // 表单规则
      rules: {
        studentUserId: [
          { required: true, message: "请选择请请假人", trigger: "change" },
        ],
        type: [
          { required: true, message: "请选择请假类型", trigger: "change" },
        ],
        timeArr: [
          {
            type: "array",
            required: true,
            message: "请选择请假时间",
            trigger: "change",
          },
        ],
        day: [{ required: true, message: "请输入请假时长", trigger: "change" }],
        rotationDepartmentId: [
          { required: true, message: "请选择轮转科室", trigger: "change" },
        ],
        reason: [{ required: true, message: "请填写原因", trigger: "blur" }],
        pictures: [{ required: true, message: "请上传附件", trigger: "blur" }],
        scheduleDetailsId: [
          { required: true, message: this.leaveTip, trigger: "change" },
        ],
      },
      // 提交进度
      submitLoading: false,
      // 请假配置
      leaveConfig: null,
      // 未入科请假提示语
      leaveTip: "",
      // 导出loading
      exportLoading: false,
    };
  },
  computed: {
    curStudent() {
      return this.userStudentList.find(
        (item) => item.id === this.form.studentUserId
      );
    },
    leaveTypeList() {
      const list = this.getDictDatas(DICT_TYPE.BPM_OA_LEAVE_TYPE);
      return this.leaveConfig?.leaveType
        ? list.filter(
            (item) =>
              this.leaveConfig.leaveType.split(",").indexOf(item.value) > -1
          )
        : list;
    },
  },
  created() {
    this.getList();
    getStudentGradeList().then((res) => {
      this.gradeList = res.data;
    });
    getRotationDepartmentSimpleList().then((res) => {
      this.rotationDepartmentList = res.data;
    });
    getSimpleMajorList().then((res) => {
      this.majorList = res.data;
    });
    getUserStudentList().then((res) => {
      this.userStudentList = res.data;
    });
    getConfigKey("oa_leave_tip").then((res) => {
      this.leaveTip = res.data || "您选择的请假时间暂无排班计划，请重新选择";
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getLeaveManagePage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    leaveTypeChange(val) {
      if (val === "4") {
        this.rules.pictures[0].required = false;
      } else {
        this.rules.pictures[0].required = true;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看审批进度 */
    handleProcessDetail(row) {
      if (!row.processInstanceId) {
        this.$message.info("该请假记录无审批流程");
        return;
      }
      this.$router.push({
        path: "/bpm/process-instance/detail",
        query: { id: row.processInstanceId },
      });
    },
    /** 新增请假记录 */
    handleAdd() {
      this.form = {
        studentUserId: undefined,
        rotationDepartmentId: undefined,
        scheduleDetailsId: undefined,
        startTime: undefined,
        endTime: undefined,
        type: undefined,
        reason: undefined,
        timeArr: undefined,
        day: undefined,
        pictures: undefined,
      };
      this.open = true;
      this.$nextTick(() => this.resetForm("form"));
    },
    /** 切换请假人 */
    handleStudentChange(value) {
      getLeaveStudentUserConfig(value).then((res) => {
        this.leaveConfig = res.data;
      });
    },
    /** 请假时间校验 */
    onDatePickerChange(val) {
      if (!val || val.length === 0) {
        return;
      }
      const diffDays = this.getDiffDay(val[0], val[1]);
      this.form.day = diffDays + 1;
      const params = {
        rotationBeginTime: val[0],
        rotationEndTime: val[1],
        studentId: this.form.studentUserId,
      };
      getScheduleByRotationDatesStudent(params).then((res) => {
        const { code, data } = res;
        if (code === 0 && data) {
          if (data.length === 0) {
            this.$message.warning(this.leaveTip);
          } else if (data.length === 1) {
            this.form.rotationDepartmentId = data[0].rotationDepartmentId;
            this.form.scheduleDetailsId = data[0].id;
          } else {
            let str = "";
            data.forEach((item) => {
              str =
                str +
                item.rotationDepartmentName +
                "(" +
                item.rotationBeginTime +
                "~" +
                item.rotationEndTime +
                ")" +
                ";";
            });
            this.$message.warning(
              `请重新选择时间段，您选的时间段跨了以下几个轮转计划：${str}`
            );
          }
        }
      });
    },
    getDiffDay(date_1, date_2) {
      // 计算两个日期之间的差值
      let totalDays, diffDate;
      let myDate_1 = Date.parse(date_1);
      let myDate_2 = Date.parse(date_2);
      // 将两个日期都转换为毫秒格式，然后做差
      diffDate = Math.abs(myDate_1 - myDate_2); // 取相差毫秒数的绝对值
      totalDays = Math.floor(diffDate / (1000 * 3600 * 24)); // 向下取整
      return totalDays; // 相差的天数
    },
    /** 提交取消按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        if (!this.form.scheduleDetailsId) {
          this.$message.warning(this.leaveTip);
          return;
        }
        this.submitLoading = true;
        const params = {
          studentUserId: this.form.studentUserId,
          day: this.form.day,
          endTime: this.form.timeArr[1],
          pictures: this.form.pictures
            ? JSON.stringify(this.form.pictures)
            : "",
          reason: this.form.reason,
          rotationDepartmentId: this.form.rotationDepartmentId,
          scheduleDetailsId: this.form.scheduleDetailsId,
          startTime: this.form.timeArr[0],
          type: this.form.type,
        };

        manageCreateLeave(params)
          .then((response) => {
            this.$modal.msgSuccess("发起成功");
            this.open = false;
            this.submitLoading = false;
            this.getList();
          })
          .catch((err) => {
            this.submitLoading = false;
          });
      });
    },
    cancel() {
      this.open = false;
    },
    /** 导出考勤 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有请假记录数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportLeaveRecordExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "请假记录.xlsx");
          this.exportLoading = false;
        })
        .catch(() => (this.exportLoading = false));
    },
  },
};
</script>

<style lang="scss">
.oa-record-dialog-form {
  .student-user-info {
    //margin-left: -80px;
    display: flex;
    justify-content: space-between;
    gap: 4px;
    padding-top: 15px;
    line-height: 1;
  }
}

.oa-record-dialog-dataPicker {
  .el-picker-panel__sidebar {
    width: 200px !important;
  }
  .el-picker-panel__sidebar + .el-picker-panel__body {
    margin-left: 210px !important;
  }
}
</style>
