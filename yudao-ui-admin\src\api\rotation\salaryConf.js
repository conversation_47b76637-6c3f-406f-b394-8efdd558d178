import request from '@/utils/request'

// 创建薪资配置
export function createSalaryConf(data) {
  return request({
    url: '/rotation/salary-conf/create',
    method: 'post',
    data: data
  })
}

// 更新薪资配置
export function updateSalaryConf(data) {
  return request({
    url: '/rotation/salary-conf/update',
    method: 'put',
    data: data
  })
}

// 删除薪资配置
export function deleteSalaryConf(id) {
  return request({
    url: '/rotation/salary-conf/delete?id=' + id,
    method: 'delete'
  })
}

// 获得薪资配置
export function getSalaryConf(id) {
  return request({
    url: '/rotation/salary-conf/get?id=' + id,
    method: 'get'
  })
}

// 获得薪资配置分页
export function getSalaryConfPage(query) {
  return request({
    url: '/rotation/salary-conf/page',
    method: 'get',
    params: query
  })
}

// 导出薪资配置 Excel
export function exportSalaryConfExcel(query) {
  return request({
    url: '/rotation/salary-conf/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
