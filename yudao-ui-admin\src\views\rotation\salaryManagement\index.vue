<template>
  <div class="app-container salaryManagement">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="月份" prop="salaryMonth">
        <el-date-picker
          v-model="queryParams.salaryMonth"
          type="month"
          format="yyyy-MM"
          value-format="yyyy-MM"
          placeholder="选择月"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          filterable
          clearable
          placeholder="请选择学员类型"
          @change="handleQueryStudentTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-date-picker
          v-model="queryParams.grade"
          type="year"
          format="yyyy"
          value-format="yyyy"
          placeholder="选择年级"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click.native="handleImport"
          v-hasPermi="['rotation:salary-management:create']"
        >
          导入
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:salary-management:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        v-for="(item, index) in headColumns"
        :key="item.id"
        :label="item.fieldName"
        align="center"
        :prop="item.fieldCode"
        width="100"
        class="scroll-columm"
        :fixed="
          item.systemFlag &&
          (index === headColumns.length - 1 ? 'right' : 'left')
        "
        >)
        <template slot-scope="scope">
          <span>{{ scope.row[item.fieldCode] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的数据
          <p>
            仅允许导入xls、xlsx格式文件。
            <el-link
              style="font-size: 12px"
              type="primary"
              :underline="false"
              @click="handleExportTemplate"
              >下载模板</el-link
            >
          </p>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBaseHeader } from "@/utils/request";
import { getSimpleMajorList } from "@/api/system/major";
import {
  getSalaryPage,
  getTableHeads,
  exportSalaryTemplate,
  exportSalaryExcel,
} from "@/api/rotation/salaryManagement";

export default {
  name: "SalaryManagement",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      headColumns: [],
      // 薪资管理列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: null,
        username: null,
        salaryMonth: null,
        studentType: null,
        major: null,
        grade: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        studentId: [
          { required: true, message: "学员id不能为空", trigger: "blur" },
        ],
        salaryMonth: [
          { required: true, message: "薪资年月不能为空", trigger: "blur" },
        ],
        salaryData: [
          { required: true, message: "薪资数据不能为空", trigger: "blur" },
        ],
      },
      queryMajorList: [],
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的科室数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/admin-api/rotation/salary-management/import-salary",
      },
    };
  },
  created() {
    this.getTableHeadsFun();
  },
  methods: {
    getTableHeadsFun() {
      getTableHeads().then((response) => {
        this.headColumns = response.data;
        this.getList();
      });
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getSalaryPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 查询学员类型改变 */
    handleQueryStudentTypeChange(value) {
      this.queryParams.major = null;
      this.queryMajorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.queryMajorList = res.data;
      });
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        studentId: undefined,
        salaryMonth: undefined,
        salaryData: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "薪资导入";
      this.upload.open = true;
      this.upload.url =
        process.env.VUE_APP_BASE_API +
        "/admin-api/rotation/salary-management/import-salary";
    },
    /** 导入模版 */
    handleExportTemplate() {
      exportSalaryTemplate()
        .then((response) => {
          this.$download.excel(response, "薪资导入模版.xlsx");
        })
        .catch(() => {});
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      let text = "创建成功数量：" + data.successDetails.length;
      for (const name of data.successDetails) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      }
      // text += "<br />更新成功数量：" + data.updateUsernames.length;
      // for (const name of data.updateUsernames) {
      //   text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      // }
      text += "<br />更新失败数量：" + Object.keys(data.failureDetails).length;
      for (const name in data.failureDetails) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          name +
          "：" +
          data.failureUsernames[name];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有薪资管理数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportSalaryExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "薪资管理.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.salaryManagement {
  .el-table__header-wrapper {
    th.el-table__cell > .cell {
      color: #064c8d;
    }
  }

  .el-table__body-wrapper {
    .cell {
      color: #064c8d;
    }
  }
}
</style>
