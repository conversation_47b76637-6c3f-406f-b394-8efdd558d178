<template>
  <div class="app-container">
    <el-form class="config-form" ref="form" :model="form" :rules="rules" label-width="80px">
      <div class="date-row">
        <el-form-item label="师资遴选开始时间" prop="selectionBeginTime" label-width="135px">
          <el-date-picker v-model="form.selectionBeginTime" type="date" value-format="yyyy-MM-dd" placeholder="选择师资遴选开始时间" />
        </el-form-item>
        <el-form-item label="师资遴选结束时间" prop="selectionEndTime" label-width="135px">
          <el-date-picker v-model="form.selectionEndTime" type="date" value-format="yyyy-MM-dd" placeholder="选择师资遴选结束时间" />
        </el-form-item>
      </div>
      <el-form-item label="遴选岗位" prop="selectionPositions">
        <el-select class="full-width" v-model="form.selectionPositions" placeholder="请选择遴选岗位" multiple filterable clearable>
          <el-option v-for="option in roleOptions" :label="option.name" :value="option.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="遴选说明" prop="remarks">
        <el-input v-model="form.remarks" type="textarea" :autosize="{ minRows: 3, maxRows: 10 }" placeholder="请输入遴选说明" />
      </el-form-item>
    </el-form>
    <div class="controls-bar">
      <el-button type="primary" @click="submitForm">更 新</el-button>
    </div>
  </div>
</template>

<script>
import { updateSelectionConfig, getSelectionConfig } from "@/api/teachers/selectionConfig";
import { listSimpleRoles } from "@/api/system/role";

export default {
  name: "SelectionConfig",
  components: {
  },
  data() {
    return {
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        selectionBeginTime: [{ required: true, message: "师资遴选开始时间不能为空", trigger: "blur" }],
        selectionEndTime: [{ required: true, message: "师资遴选结束时间不能为空", trigger: "blur" }],
        selectionPositions: [{ required: true, message: "遴选岗位集不能为空", trigger: "change" }],
        remarks: [{ required: true, message: "遴选说明不能为空", trigger: "blur" }],
      },
      roleOptions: [],
    };
  },
  created() {
    getSelectionConfig().then(response => {
      this.form = response.data;
      this.form.selectionPositions = this.form.selectionPositions ? this.form.selectionPositions.split(",").map(n => +n) : [];
    });
    listSimpleRoles().then(response => {
      const excludeRoleCodes = ["super_admin", "admin", "student", "hospital_admin", "recruitment_user"];
      this.roleOptions = response.data.filter(item => !excludeRoleCodes.includes(item.code));
    });
  },
  methods: {
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        updateSelectionConfig({
          ...this.form,
          selectionPositions: this.form.selectionPositions.join(",")
        }).then(() => {
          this.$modal.msgSuccess("修改成功");
        });
      });
    },
  }
};
</script>

<style lang="scss">
.config-form {
  width: 800px;
}

.date-row {
  display: flex;
  justify-content: space-between;
}

.full-width {
  width: 100%;
}

.controls-bar {
  width: 800px;
  padding-left: 80px;
  margin-top: 50px;
}
</style>
