<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="70px">
      <el-form-item label="方案名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入培训方案名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" clearable size="small" @change="handleQueryStudentTypeChange">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select v-model="queryParams.major" placeholder="请选择培训专业" clearable size="small">
          <el-option v-for="item in queryMajorList" :key="item.code" :label="item.name" :value="item.code"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="培训方案名称" align="center" prop="name" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType" />
        </template>
      </el-table-column>
      <el-table-column label="培训专业" align="center" prop="majorName"></el-table-column>
      <el-table-column label="轮转时长（总时间）" align="center" prop="rotationTime">
        <template v-slot="scope">
          {{ scope.row.rotationTime }}
          <dict-tag :type="DICT_TYPE.ROTATION_CYCLE" :value="scope.row.rotationCycle" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-attract" @click="handleRelationship(scope.row)">轮转规则</el-button>
          <el-button size="mini" type="text" icon="el-icon-document" @click="handleRemark(scope.row)">方案说明</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog title="对应科室" :visible.sync="relationshipVisible">
      <el-form inline>
        <el-form-item label="年级">
          <el-select v-model="selectGrade" @change="queryRelationships(standardSchemeId, selectGrade)">
            <el-option v-for="item in gradeList" :key="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <el-table :data="relationshipList">
        <el-table-column label="标准科室">
          <template v-slot="scope">{{ standardDepartmentName(scope.row.standardDepartmentId) }}</template>
        </el-table-column>
        <el-table-column label="轮转科室">
          <template v-slot="scope">
            <el-select multiple v-model="scope.row.rotationDepartmentIds">
              <el-option
                v-for="item in filterRotationDeptList(scope.row.rotationDepartmentIds)"
                :key="item.rotationDepartmentId"
                :label="item.rotationDepartmentName"
                :value="item.rotationDepartmentId"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer">
        <el-button type="primary" @click="handleSureRelationship">确定</el-button>
        <el-button @click="relationshipVisible = false">取消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="方案说明" :visible.sync="remarkVisible">
      <div v-html="remark"></div>
    </el-dialog>
  </div>
</template>

<script>
import { getHospitalSchemePage, getDepartmentRelationships, saveDepartmentRelationships } from '@/api/rotation/hospitalScheme'
import { getSimpleMajorList } from '@/api/system/major'
import { getRotationDepartments, getScheduleGradeList } from '@/api/rotation/schedule'
import { getStandardSchemeDepartments } from '@/api/rotation/rule'
import { getStandardScheme } from '@/api/rotation/standardScheme'
import Editor from '@/components/Editor';

export default {
  name: "HospitalScheme",
  components: {
    Editor,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 标准方案列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        studentType: null,
        major: null,
        publishStatus: "1",
      },
      // 查询培训专业列表
      queryMajorList: [],
      // 对应轮转科室和标准科室
      relationshipVisible: false,
      standardSchemeId: "",
      standardDepartments: [],
      gradeList: [],
      selectGrade: "",
      rotationDeptList: [],
      relationshipList: [],
      // 方案说明
      remarkVisible: false,
      remark: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getHospitalSchemePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        studentType: undefined,
        major: undefined,
        schemeType: undefined,
        rotationCycle: 1,
        rotationItemsConfigStatus: 1,
        rotationItems: [],
        activeConfigStatus: 1,
        activeItems: [],
        appraiseConfigStatus: 1,
        appraiseItems: [],
        examineConfigStatus: 1,
        examineItems: [],
        publishStatus: 1,
        description: undefined,
      };
      this.resetForm("form");
    },
    /** 查询学员类型改变 */
    handleQueryStudentTypeChange(value) {
      this.queryParams.major = null;
      this.queryMajorList = [];
      getSimpleMajorList({ studentType: value }).then(res => {
        this.queryMajorList = res.data;
      });
    },
    /** 表单学员类型改变 */
    handleFormStudentTypeChange(value) {
      this.form.major = undefined;
      this.formMajorList = [];
      getSimpleMajorList({ studentType: value }).then(res => {
        this.formMajorList = res.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 点击轮转规则 */
    handleRelationship(item) {
      this.relationshipVisible = true;
      this.standardSchemeId = item.id;
      getScheduleGradeList(item.id).then(res => {
        this.gradeList = res.data;
        this.selectGrade = this.gradeList[0];
        if (!this.selectGrade) {
          this.$message.warning("请先导入排班计划！");
          this.relationshipVisible = false;
          return;
        }
        this.queryRelationships(item.id, this.selectGrade);
      });
    },
    /** 获取对应关系 */
    queryRelationships(standardSchemeId, grade) {
      getRotationDepartments(standardSchemeId, grade).then(res => {
        this.rotationDeptList = res.data;
      });
      getDepartmentRelationships({ standardSchemeId, grade }).then(res => {
        const relationships = res.data;
        getStandardSchemeDepartments(standardSchemeId).then(res => {
          this.standardDepartments = res.data;
          this.relationshipList = res.data.map(item => {
            const matched = relationships.find(r => r.standardDepartmentId === item.standardDepartmentId);
            return {
              standardDepartmentId: item.standardDepartmentId,
              standardDepartmentName: item.standardDepartmentName,
              rotationDepartmentIds: matched ? matched.rotationDepartmentIds : [],
            };
          });
        })
      });
    },
    /** 保存对应关系 */
    handleSureRelationship() {
      if (this.filterRotationDeptList([]).length > 0) {
        this.$message.warning("还有轮转科室尚未对应");
        return;
      }
      saveDepartmentRelationships({
        departmentRelationships: this.relationshipList,
        grade: this.selectGrade,
        standardSchemeId: this.standardSchemeId
      }).then(() => {
        this.$message.success("保存成功");
        this.relationshipVisible = false;
      });
    },
    /** 轮转科室列表 */
    filterRotationDeptList(ids) {
      const selected = [];
      this.relationshipList.forEach(item => selected.push(...item.rotationDepartmentIds));
      return this.rotationDeptList.filter(item => {
        return selected.indexOf(item.rotationDepartmentId) < 0 || ids.includes(item.rotationDepartmentId);
      });
    },
    /** 标准方案名 */
    standardDepartmentName(departmentId) {
      return this.standardDepartments.find(item => item.standardDepartmentId === departmentId)?.standardDepartmentName;
    },
    /** 方案说明 */
    handleRemark(item) {
      getStandardScheme(item.id).then(res => {
        this.remark = res.data.description;
        this.remarkVisible = true;
      });
    },
  }
};
</script>
