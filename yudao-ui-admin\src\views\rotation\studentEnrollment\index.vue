<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="轮转科室" prop="rotationDepartmentName">
        <el-input
          v-model="queryParams.rotationDepartmentName"
          placeholder="请输入轮转科室"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="派送单位" prop="dispatchingUnit">
        <el-input
          v-model="queryParams.dispatchingUnit"
          placeholder="请输入派送单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择学员类型"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="grade in gradeOptions"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="入科时间" prop="enrollmentDate">
        <el-date-picker
          clearable
          v-model="queryParams.enrollmentDate"
          type="month"
          format="yyyy-MM"
          value-format="yyyy-MM"
          placeholder="选择月"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="入科时间" prop="rotationBeginDates">
        <el-date-picker
          v-model="queryParams.rotationBeginDates"
          clearable
          style="width: 240px"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          filterable
          clearable
          size="small"
          @change="queryStudentTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="major in majorOptions"
            :key="major.code"
            :label="major.name"
            :value="major.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="轮转状态" prop="rotationStatus">
        <el-select
          v-model="queryParams.rotationStatus"
          placeholder="请选择轮转状态"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:student-enrollment:create']">新增</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:student-enrollment:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="学员姓名" align="center" prop="nickname" /> -->
      <el-table-column
        label="学员姓名"
        align="left"
        prop="nickname"
        width="180"
      >
        <template v-slot="scope">
          {{ scope.row.nickname }}
          <el-tag
            v-if="scope.row.hasMedicalLicense"
            size="small"
            style="margin-right: 6px"
            >资</el-tag
          >
          <el-tag
            v-if="scope.row.hasPracticingLicense"
            size="small"
            style="margin-right: 6px"
            >执</el-tag
          >
          <el-tag
            v-if="scope.row.obtainedPrescription"
            size="small"
            style="margin-right: 6px"
            >处</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="手机号" align="center" prop="mobile" />
      <el-table-column label="派送单位" align="center" prop="dispatchingUnit" />
      <el-table-column label="状态" align="center" prop="rotationStatus">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_STATUS"
            :value="scope.row.rotationStatus"
          />
        </template>
      </el-table-column>
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          />
        </template>
      </el-table-column>
      <el-table-column label="培训专业" align="center" prop="majorName" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column
        label="轮转科室"
        align="center"
        prop="rotationDeptName"
      />
      <el-table-column label="轮转次数" align="center" prop="studentType">
        <template slot-scope="scope">
          {{ scope.row.rotationIndex }}/{{ scope.row.rotationSize }}
        </template>
      </el-table-column>
      <el-table-column label="轮转时间" align="center" prop="rotationDate" />
      <el-table-column
        label="入科时间"
        align="center"
        prop="enrollmentDate"
        width="180"
      >
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rotation:student-enrollment:save']"
          >
            {{
              ["0", "4"].indexOf(scope.row.rotationStatus) > -1
                ? "入科"
                : "修改"
            }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      custom-class="student-enrollment-dialog"
      :title="title"
      :visible.sync="open"
      width="600px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="10">
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="标准科室" prop="standardDepartmentName">
              <el-input v-model="form.standardDepartmentName" disabled />
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="轮转科室" prop="rotationDepartmentName">
              <el-input
                v-model="form.rotationDepartmentName"
                placeholder="轮转科室"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="时间范围" prop="dateRange">
              <el-date-picker
                v-model="form.dateRange"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                disabled
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="技能考官" prop="skillsExaminerUserids">
              <el-select
                v-model="form.skillsExaminerUserids"
                multiple
                filterable
                placeholder="请选择技能考官"
              >
                <el-option
                  v-for="user in skillsExaminerList"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="教学主任" prop="userId">
              <el-select
                v-model="form.userId"
                filterable
                placeholder="请选择教学主任"
              >
                <el-option
                  v-for="user in archiaterList"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="10"
          v-for="(item, index) in form.studentEnrollmentTeacherList"
          :key="index"
        >
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item
              label="带教老师"
              :prop="'studentEnrollmentTeacherList.' + index + '.userId'"
              :rules="{
                required: true,
                message: '请选择带教老师',
                trigger: 'change',
              }"
            >
              <el-select
                v-model="item.userId"
                filterable
                placeholder="请选择带教老师"
              >
                <el-option
                  v-for="user in teacherListMap[index]"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                >
                  <span style="float: left">{{ user.nickname }}</span>
                  <span
                    style="
                      float: right;
                      color: #8492a6;
                      font-size: 13px;
                      margin-left: 15px;
                    "
                  >
                    {{ user.teacherStudentCount }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="11" :lg="11" :xl="11">
            <el-form-item
              label-width="0"
              :prop="'studentEnrollmentTeacherList.' + index + '.daterange'"
              :rules="{
                required: true,
                message: '请选择日期',
                trigger: 'change',
              }"
            >
              <el-date-picker
                v-model="item.daterange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
                @change="(vals) => daterangeChange(vals, index)"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="3" :lg="3" :xl="3">
            <i
              class="el-icon-circle-plus-outline"
              @click="addTeacher(index)"
            ></i>
            <i
              v-if="index !== 0"
              class="el-icon-remove-outline"
              @click="delTeacher(index)"
            ></i>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-popconfirm
          v-if="!isNotInTime"
          title="该学员还未到入科时间，是否确认入科！"
          @confirm="submitForm"
        >
          <el-button slot="reference" type="primary" style="margin-right: 10px"
            >确 定</el-button
          >
        </el-popconfirm>
        <el-button v-else type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSimpleMajorList } from "@/api/system/major";
import { getStudentGradeList } from "@/api/system/userStudent";
import {
  getSkillsExaminerList,
  getTeaterList,
  getTeachingDirectorList,
} from "@/api/system/userWorker";
import {
  saveStudentEnrollment,
  deleteStudentEnrollment,
  getStudentEnrollment,
  getStudentEnrollmentPage,
  exportStudentEnrollmentExcel,
} from "@/api/rotation/studentEnrollment";
import { getConfigKey } from "@/api/infra/config";

export default {
  name: "StudentEnrollment",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员入科列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: "",
        rotationDepartmentName: "",
        grade: "",
        rotationBeginDates: [],
        studentType: "",
        major: "",
        rotationStatus: "0",
        dispatchingUnit: "",
      },
      // 表单参数
      form: {},
      gradeOptions: [],
      majorOptions: [],
      skillsExaminerList: [],
      archiaterList: [],
      teacherList: [],
      teacherListMap: { 0: [] },
      currentRow: {},
      isNotInTime: true,
      // 表单校验
      rules: {
        skillsExaminerUserids: [
          {
            type: "array",
            required: true,
            message: "请选择技能考官",
            trigger: "change",
          },
        ],
        // studentId: [{ required: true, message: "学员id不能为空", trigger: "blur" }],
        userId: [
          {
            required: true,
            message: "教学主任userid不能为空",
            trigger: "blur",
          },
        ],
        // enrollmentDate: [{ required: true, message: "入科时间不能为空", trigger: "blur" }],
      },
      pickerOptions: {
        disabledDate: (time) => {
          return (
            time.getTime() > new Date(this.form.rotationEndTime).getTime() ||
            time.getTime() <
              new Date(this.form.rotationBeginTime).getTime() -
                1 * 24 * 60 * 60 * 1000
          );
        },
      },
      examinerMinNumber: "",
    };
  },
  created() {
    getConfigKey("rotation.resident.physician.skills.examiner.min.number").then(
      (res) => {
        console.log("getConfigKey===", res);
        this.examinerMinNumber = res.data || "";
      }
    );
    this.getList();
    this.getGradeList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getStudentEnrollmentPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /**查询年级 */
    getGradeList() {
      this.gradeOptions = [];
      getStudentGradeList().then((res) => {
        this.gradeOptions = res.data;
      });
    },
    /**根据学员类型获取培训专业 */
    queryStudentTypeChange(val) {
      this.majorOptions = [];
      getSimpleMajorList({ studentType: val }).then((res) => {
        this.majorOptions = res.data;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        scheduleDetailsId: undefined,
        rotationDepartmentName: undefined,
        standardDepartmentName: undefined,
        dateRange: [],
        userId: undefined,
        skillsExaminerUserids: [],
        studentEnrollmentTeacherList: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加学员入科";
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      const id = row.scheduleDetailsId;
      this.currentRow = row;

      if (this.examinerMinNumber === "0" && row.studentType === 1) {
        this.rules.skillsExaminerUserids[0].required = false;
      }

      const params = {
        departmentId: row.departmentId,
        studentType: row.studentType,
        majorCode: row.majorCode,
      };
      const { data: skillsExaminerList } = await this.getSkillsExaminer(params);
      this.skillsExaminerList = skillsExaminerList;
      // const { data: teacherList } = await this.getTeater(params);
      // this.teacherList = teacherList;
      const { data: archiaterList } = await this.getTeachingDirector(params);
      this.archiaterList = archiaterList;

      getStudentEnrollment(id).then(async (response) => {
        const data = response.data;
        this.form = data;
        this.isNotInTime = this.compareDate(new Date(), data.rotationBeginTime);
        this.form.dateRange = [data.rotationBeginTime, data.rotationEndTime];

        if (!data.studentEnrollmentTeacherList) {
          params.startDate = data.rotationBeginTime;
          const { data: teacherList } = await this.getTeater(params);
          this.teacherListMap[0] = teacherList;

          this.form.studentEnrollmentTeacherList = [
            {
              userId: "",
              daterange: this.form.dateRange,
              startDate: data.rotationBeginTime,
              endDate: data.rotationEndTime,
            },
          ];
        } else {
          const { studentEnrollmentTeacherList } = this.form;
          let _studentEnrollmentTeacherList = JSON.parse(
            JSON.stringify(studentEnrollmentTeacherList)
          );

          for (let i = 0; i < _studentEnrollmentTeacherList.length; i++) {
            let arr = [];
            const item = _studentEnrollmentTeacherList[i];
            arr.push(item.startDate);
            arr.push(item.endDate);
            item.daterange = arr;

            params.startDate = item.startDate;
            const { data: teacherList } = await this.getTeater(params);
            this.$set(this.teacherListMap, i, teacherList);
          }

          this.form.studentEnrollmentTeacherList =
            _studentEnrollmentTeacherList;
        }
        this.open = true;
        this.title = "修改学员入科";
      });
    },
    compareDate(date1, date2) {
      const oDate1 = new Date(date1);
      const oDate2 = new Date(date2);
      if (oDate1.getTime() > oDate2.getTime()) {
        return true; //第一个大
      } else {
        return false; //第二个大
      }
    },
    getSkillsExaminer(params) {
      return getSkillsExaminerList(params);
    },
    getTeater(params) {
      return getTeaterList(params);
    },
    getTeachingDirector(params) {
      return getTeachingDirectorList(params);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }

        let studentEnrollmentTeacherList = [];
        // console.log(
        //   "this.form.studentEnrollmentTeacherList====",
        //   this.form.studentEnrollmentTeacherList
        // );
        this.form.studentEnrollmentTeacherList.forEach((item) => {
          studentEnrollmentTeacherList.push({
            userId: item.userId,
            startDate: item.daterange[0],
            endDate: item.daterange[1],
          });
        });
        const params = {
          scheduleDetailsId: this.form.scheduleDetailsId,
          skillsExaminerUserids: this.form.skillsExaminerUserids,
          studentId: this.currentRow.studentId,
          userId: this.form.userId,
          studentEnrollmentTeacherList,
        };
        saveStudentEnrollment(params).then((res) => {
          this.$modal.msgSuccess("入科成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除学员入科编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteStudentEnrollment(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有学员入科数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportStudentEnrollmentExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "学员入科.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    async daterangeChange(values, index) {
      if (values) {
        const params = {
          departmentId: this.currentRow.departmentId,
          studentType: this.currentRow.studentType,
          majorCode: this.currentRow.majorCode,
          startDate: values[0],
        };
        const { data: teacherList } = await this.getTeater(params);
        this.$set(this.teacherListMap, index, teacherList);
      }
    },
    async addTeacher() {
      this.form.studentEnrollmentTeacherList.push({
        userId: "",
        daterange: this.form.dateRange,
        startDate: this.form.dateRange[0],
        endDate: this.form.dateRange[1],
      });

      const params = {
        departmentId: this.currentRow.departmentId,
        studentType: this.currentRow.studentType,
        majorCode: this.currentRow.majorCode,
        startDate: this.form.dateRange[0],
      };
      const { data: teacherList } = await this.getTeater(params);
      const len = this.form.studentEnrollmentTeacherList.length;
      this.$set(this.teacherListMap, len - 1, teacherList);
    },
    delTeacher(index) {
      this.form.studentEnrollmentTeacherList.splice(index, 1);
      delete this.teacherListMap[index];
    },
  },
};
</script>
<style lang="scss">
.student-enrollment-dialog {
  .el-form-item__content {
    position: relative;
  }
  .el-icon-circle-plus-outline,
  .el-icon-remove-outline {
    font-weight: bold;
    font-size: 24px;
    position: absolute;
    top: 5px;
    right: 6px;
    cursor: pointer;
  }
  .el-icon-circle-plus-outline {
    right: 36px;
  }
}
</style>
