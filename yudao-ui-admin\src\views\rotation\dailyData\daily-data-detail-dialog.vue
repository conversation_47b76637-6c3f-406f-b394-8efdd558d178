<template>
  <el-dialog title="查看详情" :visible.sync="detailVisible">
    <el-form label-width="120px" v-if="[1, 5, 6, 7, 8].includes(rotationItem)">
      <el-form-item label="病人姓名：" class="half-form-item">
        {{ detailData.patientName }}
      </el-form-item>
      <el-form-item label="病历号：" class="half-form-item">
        {{ detailData.medicalCode }}
      </el-form-item>
      <el-form-item label="疾病名称：" class="half-form-item">
        {{ detailData.diseaseName }}
      </el-form-item>
      <el-form-item label="诊断类型：" class="half-form-item">
        <dict-tag :type="DICT_TYPE.ROTATION_DIAGNOSE_TYPE" :value="detailData.diagnoseType" />
      </el-form-item>
      <el-form-item label="图片附件：">
        <template v-if="detailData.pictures">
          <el-image
            v-for="(imgUrl, i) in detailData.pictures"
            class="attachment-picture"
            :key="i"
            :src="imgUrl"
            :preview-src-list="detailData.pictures"
          ></el-image>
        </template>
        <span v-else>无</span>
      </el-form-item>
    </el-form>

    <el-form label-width="120px" v-if="rotationItem === 2">
      <el-form-item label="病种名称：">
        {{ detailData.ruleDepartmentRotationItemName }}
      </el-form-item>
      <el-form-item label="病人姓名：" class="half-form-item">
        {{ detailData.patientName }}
      </el-form-item>
      <el-form-item label="日期：" class="half-form-item">
        {{ detailData.recordDate }}
      </el-form-item>
      <el-form-item label="病历号/病理号：" prop="medicalCode" class="half-form-item">
        {{ detailData.medicalCode }}
      </el-form-item>
      <el-form-item label="诊断类型：" class="half-form-item">
        <dict-tag :type="DICT_TYPE.ROTATION_DIAGNOSE_TYPE" :value="detailData.diagnoseType" />
      </el-form-item>
      <el-form-item label="是否主管：" class="half-form-item">
        {{ detailData.isManager ? "是" : "否" }}
      </el-form-item>
      <el-form-item label="是否抢救：" prop="isRescue" class="half-form-item">
        {{ detailData.isRescue ? "是" : "否" }}
      </el-form-item>
      <el-form-item label="备注：" prop="remarks">
        {{ detailData.remarks || "无" }}
      </el-form-item>
    </el-form>

    <el-form label-width="120px" v-if="rotationItem === 4">
      <el-form-item label="手术名称：">
        {{ detailData.ruleDepartmentRotationItemName }}
      </el-form-item>
      <el-form-item label="病人姓名：" class="half-form-item">
        {{ detailData.patientName }}
      </el-form-item>
      <el-form-item label="手术日期：" class="half-form-item">
        {{ detailData.recordDate }}
      </el-form-item>
      <el-form-item label="病历号：" class="half-form-item">
        {{ detailData.medicalCode }}
      </el-form-item>
      <el-form-item label="术中职务：" class="half-form-item">
        <dict-tag :type="DICT_TYPE.ROTATION_DIAGNOSE_TYPE" :value="detailData.intraoperativePosition" />
      </el-form-item>
      <el-form-item label="备注：" prop="remarks">
        {{ detailData.remarks || "无" }}
      </el-form-item>
    </el-form>

    <el-form label-width="120px" v-if="rotationItem === 3">
      <el-form-item label="操作名称：">
        {{ detailData.ruleDepartmentRotationItemName }}
      </el-form-item>
      <el-form-item label="病人姓名：" class="half-form-item">
        {{ detailData.patientName }}
      </el-form-item>
      <el-form-item label="操作日期：" class="half-form-item">
        {{ detailData.recordDate }}
      </el-form-item>
      <el-form-item label="病历号：" class="half-form-item">
        {{ detailData.medicalCode }}
      </el-form-item>
      <el-form-item label="是否成功：" class="half-form-item">
        {{ detailData.isSuccess ? "是" : "否" }}
      </el-form-item>
      <el-form-item label="失败原因：" v-show="!detailData.isSuccess">
        {{ detailData.failReason || "无" }}
      </el-form-item>
      <el-form-item label="备注">
        {{ detailData.remarks || "无" }}
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { getRecordData } from '@/api/rotation/manual'
import { getAccessToken } from '@/utils/auth'

export default {
  name: 'daily-data-detail-dialog',
  data() {
    return {
      detailVisible: false,
      rotationItem: "",
      detailData: {},
    }
  },
  methods: {
    viewDetail(row) {
      getRecordData(row.id).then(res => {
        this.detailData = res.data;
        this.detailData.pictures = this.detailData.pictures ? this.detailData.pictures.split(',').map(url => `${url.split('?')[0]}?token=${getAccessToken()}`) : null;
      });
      this.rotationItem = row.rotaionItem;
      this.detailVisible = true;
    },
  },
}
</script>

<style scoped lang="scss">
.half-form-item {
  display: inline-block;
  width: 50%;
}

.attachment-picture {
  width: 150px;
  height: 150px;
  margin-right: 20px;
}
</style>
