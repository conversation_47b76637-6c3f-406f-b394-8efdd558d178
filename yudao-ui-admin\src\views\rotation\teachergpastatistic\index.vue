<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入姓名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item label="专业基地" prop="professionalBaseValue">
          <el-select v-model="queryParams.professionalBaseValue" filterable clearable placeholder="请选择专业基地">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.PROFESSIONAL_BASE)"
            :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
      </el-form-item>

      <el-form-item label="科室" prop="deptId">
        <el-select v-model="queryParams.deptId" filterable clearable placeholder="请选择医院科室" size="small">
          <el-option
              v-for="item in departmentOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="统计时间" prop="statisticDates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.statisticDates"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:teachergpastatistic:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="姓名" align="center" width="130" prop="nickname" />
      <el-table-column label="登录名" align="center" width="130" prop="username" />
      <el-table-column label="专业基地" align="center" width="150" prop="professionalBaseLabel" show-overflow-tooltip />
      <el-table-column label="科室" align="center" min-width="220" prop="deptName" show-overflow-tooltip />
      <el-table-column label="导师评价得分" align="center" min-width="130" prop="appraiseProcessScore">
        <template slot-scope="scope">
          <span>{{ scope.row.appraiseProcessScore === null ? '--' : scope.row.appraiseProcessScore }}</span>
        </template>
      </el-table-column>
      <el-table-column label="带教评价得分" align="center" min-width="130" prop="appraise360Result" >
        <template slot-scope="scope">
          <span>{{ scope.row.appraise360Result === null ? '--' : scope.row.appraise360Result }}</span>
        </template>
      </el-table-column>
      <el-table-column label="带教人数" align="center" min-width="130" prop="studentNum" >
        <template slot-scope="scope">
          <span>{{ scope.row.studentNum === null ? '--' : scope.row.studentNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="带教天数" align="center" width="80" prop="teacherDays" />
      <el-table-column label="小讲课量" align="center" min-width="130" prop="active1Num" >
        <template slot-scope="scope">
          <span>{{ scope.row.active1Num === null ? '--' : scope.row.active1Num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="小讲课评价得分" align="center" min-width="130" prop="active1Score" >
        <template slot-scope="scope">
          <span>{{ scope.row.active1Score === null ? '--' : scope.row.active1Score }}</span>
        </template>
      </el-table-column>
      <el-table-column label="教学查房量" align="center" min-width="130" prop="active2Num" >
        <template slot-scope="scope">
          <span>{{ scope.row.active2Num === null ? '--' : scope.row.active2Num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="教学查房评价得分" align="center" min-width="130" prop="active2Score" >
        <template slot-scope="scope">
          <span>{{ scope.row.active2Score === null ? '--' : scope.row.active2Score }}</span>
        </template>
      </el-table-column>
      <el-table-column label="病例讨论量" align="center" min-width="130" prop="active3Num" >
        <template slot-scope="scope">
          <span>{{ scope.row.active3Num === null ? '--' : scope.row.active3Num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="病例讨论评价得分" align="center" min-width="130" prop="active3Score" >
        <template slot-scope="scope">
          <span>{{ scope.row.active3Score === null ? '--' : scope.row.active3Score }}</span>
        </template>
      </el-table-column>
      <el-table-column label="公共课量" align="center" min-width="130" prop="training3Num" >
        <template slot-scope="scope">
          <span>{{ scope.row.training3Num === null ? '--' : scope.row.training3Num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="公共课评价得分" align="center" min-width="130" prop="training3Score" >
        <template slot-scope="scope">
          <span>{{ scope.row.training3Score === null ? '--' : scope.row.training3Score }}</span>
        </template>
      </el-table-column>
      <el-table-column label="技能指导量" align="center" min-width="130" prop="training9Num" >
        <template slot-scope="scope">
          <span>{{ scope.row.training9Num === null ? '--' : scope.row.training9Num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="技能指导评价得分" align="center" min-width="130" prop="training9Score" >
        <template slot-scope="scope">
          <span>{{ scope.row.training9Score === null ? '--' : scope.row.training9Score }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
  </div>
</template>

<script>
import { getDepartmentSimpleList } from "@/api/system/department";
import { getTeachergpastatisticPage, exportTeachergpastatistic } from "@/api/rotation/teachergpastatistic";

export default {
  name: "TeacherGpaStatistic",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员入科列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 50,
        nickname: '',
        deptId: '',
        statisticDates: [],
        professionalBaseValue: ''
      },
      // 表单参数
      departmentOptions: [],
      currentRow: {}
    };
  },
  created() {
    this.getList();
    this.getDepartment();
  },
methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getTeachergpastatisticPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then(res => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有数据项?').then(() => {
          this.exportLoading = true;
          return exportTeachergpastatistic(params);
        }).then(response => {
          this.$download.excel(response, '带教绩效统计.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
<style lang="scss">
</style>
