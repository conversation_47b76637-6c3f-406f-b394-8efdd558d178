<template>
  <div class="app-container salaryView">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="月份" prop="salaryMonth">
        <el-date-picker
          v-model="queryParams.salaryMonth"
          type="month"
          format="yyyy-MM"
          value-format="yyyy-MM"
          placeholder="选择月"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:salary-management:export']"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        v-for="(item, index) in headColumns"
        :key="item.id"
        :label="item.fieldName"
        align="center"
        :prop="item.fieldCode"
        width="100"
        class="scroll-columm"
        :fixed="
          item.systemFlag &&
          (index === headColumns.length - 1 ? 'right' : 'left')
        "
      >
        <template slot-scope="scope">
          <span>{{ scope.row[item.fieldCode] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getStudentSalaryPage,
  getStudentTableHeads,
  exportSalaryManagementExcel,
} from "@/api/rotation/salaryManagement";

export default {
  name: "SalaryView",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      headColumns: [],
      // 薪资管理列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        salaryMonth: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        studentId: [
          { required: true, message: "学员id不能为空", trigger: "blur" },
        ],
        salaryMonth: [
          { required: true, message: "薪资年月不能为空", trigger: "blur" },
        ],
        salaryData: [
          { required: true, message: "薪资数据不能为空", trigger: "blur" },
        ],
      },
      queryMajorList: [],
    };
  },
  created() {
    this.getMonthTableHeads();
  },
  methods: {
    getMonthTableHeads() {
      getStudentTableHeads().then((response) => {
        this.headColumns = response.data;
        this.getList();
      });
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getStudentSalaryPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        studentId: undefined,
        salaryMonth: undefined,
        salaryData: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有月度薪资数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportSalaryManagementExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "月度薪资.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.salaryView {
  .el-table__header-wrapper {
    th.el-table__cell > .cell {
      color: #064c8d;
    }
  }
  .el-table__body-wrapper {
    .cell {
      color: #064c8d;
    }
  }
}
</style>
