import request from '@/utils/request'

// 获得活动评价反馈结果
export function getFeedbackResult(id) {
  return request({
    url: '/rotation/appraise-active-feedback-result/get',
    method: 'get',
    params: { id }
  })
}

// 获得活动评价综合结果根据参数
export function getComprehensiveByParam(query) {
  return request({
    url: '/rotation/appraise-active-feedback-result/get-comprehensive-by-param',
    method: 'get',
    params: query
  })
}

// 获得活动评价明细结果分页
export function getAppraiseDetails(query) {
  return request({
    url: '/rotation/appraise-active-feedback-result/get-details-page',
    method: 'get',
    params: query
  })
}

// 保存活动评价综合反馈结果
export function saveFeedbackResult(data) {
  return request({
    url: '/rotation/appraise-active-feedback-result/save',
    method: 'post',
    data
  })
}

// 导出 Excel
export function exportAppraiseDetailsExcel(query) {
  return request({
    url: '/rotation/appraise-active-feedback-result/export-details',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 导出 Excel
export function exportHospitalTrainingDetails(query) {
  return request({
    url: '/rotation/appraise-active-feedback-result/export-hospital-training-details',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

