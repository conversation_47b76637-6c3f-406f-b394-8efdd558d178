<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="申请岗位" prop="applyPositionName">
        <el-select
          v-model="queryParams.applyPositionName"
          placeholder="请选择聘任结果"
          clearable
          size="small"
        >
          <el-option
            v-for="item in roleOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="聘任结果" prop="selectionResult">
        <el-select
          v-model="queryParams.selectionResult"
          placeholder="请选择聘任结果"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.TEACHERS_APPOINTMENT_SELECTION_RESULT
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请人" prop="applyNickname">
        <el-input
          v-model="queryParams.applyNickname"
          placeholder="请输入申请人"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="申请人"
        align="center"
        prop="applyNickname"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-link type="primary" @click="handleNicknameClick(scope.row)">{{
            scope.row.applyNickname
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="用户名"
        align="center"
        prop="applyUsername"
        min-width="100"
      />
      <el-table-column label="申请岗位" align="center" prop="applyPosition">
        <template slot-scope="scope">{{
          getMatchedLabel(roleOptions, scope.row.applyPosition, "id", "name")
        }}</template>
      </el-table-column>
      <el-table-column
        label="任教学员类型"
        align="center"
        prop="studentTypeNames"
        min-width="180"
      />
      <el-table-column
        label="任职科室"
        align="center"
        prop="teachDepartmentNames"
        min-width="180"
      />
      <el-table-column
        label="任职开始日期"
        align="center"
        prop="teachBeginDate"
        width="100"
      />
      <el-table-column
        label="任职结束日期"
        align="center"
        prop="teachEndDate"
        width="100"
      />
      <el-table-column
        label="申报材料"
        align="center"
        prop="applicationDocument"
        min-width="230"
      >
        <template slot-scope="scope">
          <file-upload-info
            :value="JSON.parse(scope.row.applicationDocument)"
            disabled
          ></file-upload-info>
        </template>
      </el-table-column>
      <el-table-column label="聘任结果" align="center" prop="selectionResult">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHERS_APPOINTMENT_SELECTION_RESULT"
            :value="scope.row.selectionResult"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="200"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            v-hasPermi="['teachers:appointment-apply-confirm:update']"
            v-if="scope.row.selectionResult === 'to_be_confirmed'"
            @click="handleSure(scope.row)"
            >遴选确认</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            v-hasPermi="['teachers:appointment-apply-confirm:delete']"
            v-else
            @click="handleView(scope.row)"
            >查看详情</el-button
          >
          <el-button
            v-if="
              scope.row.generateProof && scope.row.selectionResult === 'success'
            "
            size="mini"
            type="text"
            icon="el-icon-postcard"
            @click="handleViewProof(scope.row)"
          >
            查看证书
          </el-button>
          <el-button
            v-if="
              !scope.row.generateProof &&
              scope.row.selectionResult === 'success'
            "
            size="mini"
            type="text"
            icon="el-icon-postcard"
            @click="handleGenerateProof(scope.row)"
          >
            生成证书
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-width="120"
      >
        <div class="form-title">申报内容</div>
        <el-row>
          <el-col :span="12"
            ><el-form-item label="申请人">{{
              row.applyNickname
            }}</el-form-item></el-col
          >
          <el-col :span="12"
            ><el-form-item label="用户名">{{
              row.applyUsername
            }}</el-form-item></el-col
          >
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="申请岗位">{{
              getMatchedLabel(roleOptions, row.applyPosition, "id", "name")
            }}</el-form-item>
          </el-col>
          <el-col :span="12"
            ><el-form-item label="任教学员类型">{{
              row.studentTypeNames
            }}</el-form-item></el-col
          >
        </el-row>
        <el-row>
          <el-col :span="12"
            ><el-form-item label="任职科室">{{
              row.teachDepartmentNames
            }}</el-form-item></el-col
          >
          <el-col :span="12"
            ><el-form-item label="任职开始日期">{{
              row.teachBeginDate
            }}</el-form-item></el-col
          >
        </el-row>
        <el-row>
          <el-col :span="12"
            ><el-form-item label="任职结束日期">{{
              row.teachEndDate
            }}</el-form-item></el-col
          >
        </el-row>
        <div class="form-title">申报材料</div>
        <el-form-item label-width="60">
          <file-upload-info
            :value="JSON.parse(row.applicationDocument || null)"
            disabled
          ></file-upload-info>
        </el-form-item>
        <div class="form-title">申报确认</div>
        <el-form-item label="聘任状态" prop="selectionResult">
          <el-radio-group v-model="form.selectionResult" :disabled="isView">
            <el-radio label="success">应聘成功</el-radio>
            <el-radio label="fail">应聘失败</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          class="half-item-right"
          label="任职开始日期"
          prop="teachBeginDate"
          required
          v-if="form.selectionResult === 'success'"
        >
          <el-date-picker
            v-model="form.teachBeginDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择任职开始日期"
            disabled
          />
        </el-form-item>
        <el-form-item
          class="half-item-left"
          label="任职结束日期"
          prop="teachEndDate"
          required
          v-if="form.selectionResult === 'success'"
        >
          <el-date-picker
            v-model="form.teachEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择任职结束日期"
            :disabled="isView"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!isView">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <user-worker-dialog
      opt="view"
      dialogTitle="查看职工用户"
      :dialogOpen="archiveOpen"
      :userId="archiveUserId"
      @update:dialogOpen="(value) => (archiveOpen = value)"
    />
  </div>
</template>

<script>
import {
  updateAppointmentApplyConfirm,
  getAppointmentApplyConfirm,
  getAppointmentApplyConfirmPage,
  generateProof,
} from "@/api/teachers/appointmentApplyConfirm";
import { listSimpleRoles } from "@/api/system/role";
import FileUploadInfo from "@/components/FileUploadInfo";
import userWorkerDialog from "@/views/system/userWorker/useWorderDialog";
import {
  getTemplateByType,
  previewDocTemplateUrl,
} from "@/api/system/template";

export default {
  name: "AppointmentApplyConfirm",
  components: { FileUploadInfo, userWorkerDialog },
  data() {
    const validBeginDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error("任职开始日期不能为空"));
      } else {
        if (this.form.teachEndDate) {
          this.$refs.form.validateField("teachEndDate");
        }
        callback();
      }
    };

    const validEndDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error("任职结束日期不能为空"));
      } else if (
        new Date(value).getTime() < new Date(this.form.teachBeginDate).getTime()
      ) {
        callback(new Error("任职结束日期不能小于开始日期"));
      } else {
        callback();
      }
    };

    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 师资聘用申请确认列表
      list: [],
      // 角色列表
      roleOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        applyPositionName: null,
        selectionResult: null,
        applyNickname: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        selectionResult: [
          { required: true, message: "聘任结果不能为空", trigger: "change" },
        ],
        teachBeginDate: [{ validator: validBeginDate, trigger: "change" }],
        teachEndDate: [{ validator: validEndDate, trigger: "change" }],
      },
      // 当前操作行
      row: {},
      // 是否为查看
      isView: false,
      // 查看档案弹窗
      archiveOpen: false,
      // 档案用户id
      archiveUserId: "",
    };
  },
  created() {
    this.getList();
    listSimpleRoles().then((response) => {
      const excludeRoleCodes = [
        "super_admin",
        "admin",
        "student",
        "hospital_admin",
        "recruitment_user",
      ];
      this.roleOptions = response.data.filter(
        (item) => !excludeRoleCodes.includes(item.code)
      );
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getAppointmentApplyConfirmPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.resetForm("form");
      getAppointmentApplyConfirm(this.row.id).then((response) => {
        const { id, selectionResult } = response.data.applyConfirmRespVO;
        const { teachBeginDate, teachEndDate } = response.data.applyRespVO;
        this.form = { id, selectionResult, teachBeginDate, teachEndDate };
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 遴选确认 */
    handleSure(row) {
      this.row = row;
      this.reset();
      this.title = "遴选确认";
      this.open = true;
      this.isView = false;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        updateAppointmentApplyConfirm(this.form).then((response) => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 查看详情 */
    handleView(row) {
      this.row = row;
      this.reset();
      this.title = "查看详情";
      this.open = true;
      this.isView = true;
    },
    /** 查看职工档案 */
    handleNicknameClick(row) {
      this.archiveUserId = row.applyUserId;
      this.archiveOpen = true;
    },
    handleGenerateProof(row) {
      this.$modal
        .confirm(
          `正在给 ${row.applyNickname}  生成培训证明，确认生成吗？`,
          "证明生成确认",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "info",
          }
        )
        .then(() => {
          generateProof(row.id).then((response) => {
            this.$modal.msgSuccess("生成证书成功");
            this.getList();
          });
        });
    },
    handleViewProof(row) {
      getTemplateByType({
        templateType: "training_certificate",
        studentType: "1",
      }).then((res) => {
        const id = row.id;
        const url = previewDocTemplateUrl(
          res.data.id,
          id,
          `trainingCertificate${id}`
        );
        window.open(url);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.form-title {
  font-size: 14px;
  color: #000;
  font-weight: 500;
  margin-bottom: 10px;
  border-left: 4px solid #1890ff;
  padding-left: 6px;
}
</style>
