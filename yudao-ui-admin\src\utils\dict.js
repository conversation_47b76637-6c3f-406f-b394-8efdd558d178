/**
 * Created by 芋道源码
 *
 * 数据字典工具类
 */
import store from "@/store";

export const DICT_TYPE = {
  USER_TYPE: "user_type",
  COMMON_STATUS: "common_status",

  // ========== ROTATION 模块 ==========
  ROTATION_ENROLLMENT_EDU_DOC_STATUS: "rotation_enrollment_edu_doc_status",

  // ========== SYSTEM 模块 ==========
  SYSTEM_USER_SEX: "system_user_sex",
  SYSTEM_MENU_TYPE: "system_menu_type",
  SYSTEM_ROLE_TYPE: "system_role_type",
  SYSTEM_DATA_SCOPE: "system_data_scope",
  SYSTEM_NOTICE_TYPE: "system_notice_type",
  SYSTEM_OPERATE_TYPE: "system_operate_type",
  SYSTEM_LOGIN_TYPE: "system_login_type",
  SYSTEM_LOGIN_RESULT: "system_login_result",
  SYSTEM_SMS_CHANNEL_CODE: "system_sms_channel_code",
  SYSTEM_SMS_TEMPLATE_TYPE: "system_sms_template_type",
  SYSTEM_SMS_SEND_STATUS: "system_sms_send_status",
  SYSTEM_SMS_RECEIVE_STATUS: "system_sms_receive_status",
  SYSTEM_ERROR_CODE_TYPE: "system_error_code_type",
  SYSTEM_OAUTH2_GRANT_TYPE: "system_oauth2_grant_type",
  SYSTEM_USER_POST: "system_user_post",
  SYSTEM_NATION: "system_nation",

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING: "infra_boolean_string",
  INFRA_REDIS_TIMEOUT_TYPE: "infra_redis_timeout_type",
  INFRA_JOB_STATUS: "infra_job_status",
  INFRA_JOB_LOG_STATUS: "infra_job_log_status",
  INFRA_API_ERROR_LOG_PROCESS_STATUS: "infra_api_error_log_process_status",
  INFRA_CONFIG_TYPE: "infra_config_type",
  INFRA_CODEGEN_TEMPLATE_TYPE: "infra_codegen_template_type",
  INFRA_CODEGEN_SCENE: "infra_codegen_scene",
  INFRA_FILE_STORAGE: "infra_file_storage",

  // ========== BPM 模块 ==========
  BPM_MODEL_CATEGORY: "bpm_model_category",
  BPM_MODEL_FORM_TYPE: "bpm_model_form_type",
  BPM_TASK_ASSIGN_RULE_TYPE: "bpm_task_assign_rule_type",
  BPM_PROCESS_INSTANCE_STATUS: "bpm_process_instance_status",
  BPM_PROCESS_INSTANCE_RESULT: "bpm_process_instance_result",
  BPM_TASK_ASSIGN_SCRIPT: "bpm_task_assign_script",
  BPM_OA_LEAVE_TYPE: "bpm_oa_leave_type",

  // ========== PAY 模块 ==========
  PAY_CHANNEL_WECHAT_VERSION: "pay_channel_wechat_version", // 微信渠道版本
  PAY_CHANNEL_ALIPAY_SIGN_TYPE: "pay_channel_alipay_sign_type", // 支付渠道支付宝算法类型
  PAY_CHANNEL_ALIPAY_MODE: "pay_channel_alipay_mode", // 支付宝公钥类型
  PAY_CHANNEL_ALIPAY_SERVER_TYPE: "pay_channel_alipay_server_type", // 支付宝网关地址
  PAY_CHANNEL_CODE_TYPE: "pay_channel_code_type", // 支付渠道编码类型
  PAY_ORDER_NOTIFY_STATUS: "pay_order_notify_status", // 商户支付订单回调状态
  PAY_ORDER_STATUS: "pay_order_status", // 商户支付订单状态
  PAY_ORDER_REFUND_STATUS: "pay_order_refund_status", // 商户支付订单退款状态
  PAY_REFUND_ORDER_STATUS: "pay_refund_order_status", // 退款订单状态
  PAY_REFUND_ORDER_TYPE: "pay_refund_order_type", // 退款订单类别

  // ========== 新增 模块 ==========
  SYSTEM_STUDENT_TYPE: "system_student_type", //学员类型
  SYSTEM_MAJOR: "system_major", //培训专业
  SYSTEM_TRAIN_YEARS: "system_train_years", //培养年限
  ROTATION_SCHEME_TYPE: "rotation_scheme_type", //方案类型
  ROTATION_CYCLE: "rotation_cycle", //轮转周期
  ROTAION_ITEM: "rotaion_item", //轮转数据项
  ROTATION_STATUS: "rotation_status", //轮转状态
  ROTATION_PUBLISH_STATUS: "rotation_publish_status", //发布状态
  SYSTEM_DEPARTMENT_TYPE: "system_department_type", //科室类型
  EXAMINE_ITEM: "examine_item", //考核数据项
  APPRAISE_ITEM: "appraise_item", //评价数据项
  ACTIVE_ITEM: "active_item", //活动数据项
  ACTIVE_DEVOLEP_STATUS: "active_devolep_status", // 活动开展状态
  ROTATION_ADMISSION_MANAGEMENT: "rotation_admission_management", // 入院管理配置
  ROTATION_ENROLLMENT_EDU: "rotation_enrollment_edu", // 入科教育配置
  ROTATION_MID_TERM_EXAMINE: "rotation_mid_term_examine", // 中期考核配置
  ROTATION_GRAD: "rotation_grad", // 结业配置

  SYSTEM_USER_HEALTH_STATUS: "system_user_health_status", //健康状况
  SYSTEM_PROVINCE: "system_province", //户口省份
  SYSTEM_USER_CERTIFICATE_TYPE: "system_user_certificate_type", //证件类型
  SYSTEM_USER_MARITAL_STATUS: "system_user_marital_status", //婚姻状况
  SYSTEM_USER_NATION: "system_user_nation", //民族
  SYSTEM_USER_COMPUTER_ABILITY: "system_user_computer_ability", //计算机能力
  SYSTEM_USER_FOREIGN_LANGUAGE_ABILITY: "system_user_foreign_language_ability", //外语能力
  SYSTEM_USER_PERSONNEL_TYPE: "system_user_personnel_type", //人员类型

  SYSTEM_USER_EDUCATION: "system_user_education", //学历
  SYSTEM_USER_DEGREE: "system_user_degree", //学位
  SYSTEM_USER_POSITIONAL_TITLES: "system_user_positional_titles", //职称
  SYSTEM_USER_WORK_TYPE: "system_user_work_type", //职工类型
  SYSTEM_USER_TYPE: "system_user_type", //用户类型

  ROTATION_DEVELOP_WAY: "rotation_develop_way", //开展方式
  ROTATION_EXAMINE_STATUS: "rotation_examine_status", //考核状态
  ROTATION_EVALUATION_STATUS: "rotation_evaluation_status", //考核状态
  APPRAISE_ACTIVE_TYPE: "appraise_active_type", //活动评价类型
  ROTATION_ACTIVE_TYPE: "rotation_active_type", //活动类型
  APPRAISE_360_TYPE: "appraise_360_type", //360评价类型

  ROTATION_DIAGNOSE_TYPE: "rotation_diagnose_type", //诊断类型
  ROTATION_INTRAOPERATIVE_POSITION: "rotation_intraoperative_position", //术中职务
  ROTATION_AUDIT_STATUS: "rotation_audit_status", //审核状态
  ROTATION_STUDY_STATUS: "rotation_study_status", // 学习状态
  ROTATION_JOIN_TYPE: "rotation_join_type", // 参与类型

  GRADUATION_AUDIT_STATUS: "graduation_audit_status", // 出科审核状态
  GRADUATION_TOTALITY_APPRAISE_STATUS: "graduation_totality_appraise_status", // 出科总体评价状态

  STAFF_ROOM: "staff_room", // 教研室
  PROFESSIONAL_BASE: "professional_base", // 专业基地
  EXAME_PAPER_TYPE: "exame_paper_type", // 试卷类型
  EXAM_TYPE: "exam_type", // 试卷类型
  EXAM_QUESTION_TYPE: "exam_question_type", // 试题类型
  ROTATION_EXAM_TYPE: "rotation_exam_type", // 轮转考试类型
  IMITATE_EXAM_TYPE: "imitate_exam_type", // 模拟考试类型
  HOSPITAL_EXAM_TYPE: "hospital_exam_type", // 院内考核考试类型
  EXAM_PAPER_OPEN_SETTING: "exam_paper_open_setting", // 试卷开放

  ATTENDANCE_STATUS: "attendance_status", // 考勤状态
  BPM_OA_DUTY_TYPE: "bpm_oa_duty_type", //OA 值班类型

  SYSTEM_USER_TRAINING_STATUS: "system_user_training_status", // 系统学员培训状态

  TEACHING_TRAINING_USER_TYPE: "teaching_training_user_type", // 培训人类型
  TEACHING_TRAINING_TYPE: "teaching_training_type", // 培训类型
  TEACHING_TRAINING_LEVEL: "teaching_training_level", // 培训级别
  HOSPITAL_TRAINING_JOIN_STATE: "hospital_training_join_state", // 院级培训参与状态

  SUPERVISE_TYPE: "supervise_type", // 督导类型
  SUPERVISE_TEACHER_FORM_TYPE: "supervise_teacher_form_type", // 督导教学表单类型
  SUPERVISE_HOSPITAL_FORM_TYPE: "supervise_hospital_form_type", // 督导院级表单类型
  SUPERVISE_STATUS: "supervise_status", // 督导状态
  HOSPITAL_SUPERVISE_STATUS: "hospital_supervise_status", //院级督导状态
  APPRAISE_PROCESS_TYPE: "appraise_process_type", //过程评价类型
  SYSTEM_CERTIFICATE_LEVEL: "system_certificate_level", // 证书级别
  SYSTEM_CERTIFICATE_TYPE: "system_certificate_type", // 证书类型
  SYSTEM_TEACHER_LEVEL: "system_teacher_level", // 师资级别

  MENTOR_LEVEL: "mentor_level", // 导师层次
  MENTOR_TYPE: "mentor_type", // 导师类型
  JOB_TITLE: "job_title", // 导师类型
  MENTOR_COMMUNICATION_WAY: "mentor_communication_way", // 导师交流方式
  NATIVE_PLACE: "native_place", //籍贯
  PROFESSIONAL_TITLE_CATEGORY: "professional_title_category", // 职称类别
  HOSPITAL_LEVEL: "hospital_level", // 医院等级
  PHYSICIAN_QUALIFICATION_CERTIFICATE_TYPE:
    "physician_qualification_certificate_type", // 医师资格证书类型
  TYPE_OF_PRACTICING_CERTIFICATE: "type_of_practicing_certificate", // 医师执业证书类型

  REGISTRATION_NOTICE_SEND_STATUS: "registration_notice_send_status", // 医师执业证书类型
  ROTATION_GRADUATION_FORM_TYPE: "rotation_graduation_form_type", // 出科考核评分表类型
  MEDICAL_LICENSE_LEVEL: "medical_license_level", // 医师资格级别
  MEDICAL_LICENSE_CATEGORY: "medical_license_category", // 医师资格类别
  PRACTICING_TYPE: "practicing_type", // 执业类型
  PRACTICING_SCOPE: "practicing_scope", // 执业范围

  REGISTRATION_CHECK_STATUS: "registration_check_status", // 招录报到状态
  RECRUITMENT_RESULT: "recruitment_result", // 招录审核结果
  REGISTRATION_EXAMINE_STATUS: "registration_examine_status", // 招录审核状态
  REGISTRATION_RESIDENTIAL_TRAINING_EXAMINE_RESULT:
    "registration_residential_training_examine_result", // 招录审核结果
  REGISTRATION_PROFESSIONAL_BASE_EXAMINE_RESULT:
    "registration_professional_base_examine_result", // 招录专业基地审核结果
  REGISTRATION_RESIDENTIAL_TRAINING_ENROLL_EXAMINE_STATUS:
    "registration_residential_training_enroll_examine_status", // 招录住培录取确认状态
  REGISTRATION_RESIDENTIAL_TRAINING_SCORE_STATUS:
    "registration_residential_training_score_status", // 招录住培成绩状态

  APPRAISE_360_DIMENSION: "appraise_360_dimension", // 360评价维度
  PROFESSIONAL_BACKGROUND: "professional_background", // 执业背景
  POSITION_TYPE: "position_type", // 岗位类型
  ROTATION_NOTICE_TYPE: "rotation_notice_type", // 通知分类
  ROTATION_NOTICE_READ_STATUS: "rotation_notice_read_status", // 通知已阅状态
  EXAM_DIFFICULTY_TYPE: "exam_difficulty_type", // 试题难度

  CERTIFICATE_METHOD: "certificate_method", // 结业证书下发方式

  SYSTEM_TEMPLATE: "system_template", // 模板类型
  SYSTEM_TEMPLATE_CONTENT: "system_template_content", // 模板类型内容

  EXAM_GENERATION_METHOD: "exam_generation_method", // 组卷方式
  EXAM_QUESTION_SETTING_METHOD: "exam_question_setting_method", // 出题类型
  EXAM_ACTIVE_TYPE: "exam_active_type", // 活动分类
  EXAM_ERROR_CONFIRM_STATUS: "exam_error_confirm_status", // 错题确认状态

  SUMMARY_STATUS: "summary_status", //年度总结状态
  ASSESSMENT_RESULT: "assessment_result", //年度总结考评结果

  TEACHING_DEPT_TRAINING_TYPE: "teaching_dept_training_type", // 教学科室培训类型
  TEACHING_SEMINAR_TYPE: "teaching_seminar_type", // 教研室活动类型

  //=====师资管理========
  TEACHERS_EVALUATION_PROJECT: "teachers_evaluation_project", // 师资评优项目
  TEACHERS_EVALUATION_APPLY_RESULT: "teachers_evaluation_apply_result", // 师资评优申请状态
  TEACHERS_EVALUATION_SELECTION_RESULT: "teachers_evaluation_selection_result", // 师资评优遴选结果
  TEACHERS_APPOINTMENT_STATUS: "teachers_appointment_status", // 师资聘任状态
  TEACHERS_APPOINTMENT_APPLY_RESULT: "teachers_appointment_apply_result", // 师资聘任申请状态
  TEACHERS_APPOINTMENT_SELECTION_RESULT:
    "teachers_appointment_selection_result", // 师资聘任遴选结果

  //===== 见习管理 ======
  TEACHING_METHODS: "teaching_methods", // 教学方式
  INTERN_CASE_EXAMINE_STATUS: "intern_case_examine_status", // 见习病例审核状态
};

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export function getDictDatas(dictType) {
  return store.getters.dict_datas[dictType] || [];
}

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @param values 数组、单个元素
 * @returns {*|Array} 数据字典数组
 */
export function getDictDatas2(dictType, values) {
  if (values === undefined) {
    return [];
  }
  // 如果是单个元素，则转换成数组
  if (!Array.isArray(values)) {
    values = [this.value];
  }
  // 获得字典数据
  const results = [];
  for (const value of values) {
    const dict = getDictData(dictType, value);
    if (dict) {
      results.push(dict);
    }
  }
  return results;
}

export function getDictData(dictType, value) {
  // 获取 dictType 对应的数据字典数组
  const dictDatas = getDictDatas(dictType);
  if (!dictDatas || dictDatas.length === 0) {
    return "";
  }
  // 获取 value 对应的展示名
  value = value + ""; // 强制转换成字符串，因为 DictData 小类数值，是字符串
  for (const dictData of dictDatas) {
    if (dictData.value === value) {
      return dictData;
    }
  }
  return undefined;
}

export function getDictDataLabel(dictType, value) {
  const dict = getDictData(dictType, value);
  return dict ? dict.label : "";
}

export class getDictDataL {}
