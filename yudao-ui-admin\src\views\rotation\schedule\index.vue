<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          clearable
          filterable
        >
          <el-option
            v-for="grade in studentGradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          clearable
          @change="handleStudentTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          clearable
          filterable
        >
          <el-option
            v-for="item in majorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          clearable
          @keyup.enter.native="getStudentList"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          clearable
          @keyup.enter.native="getStudentList"
        />
      </el-form-item>
      <el-form-item label="派送单位" prop="dispatchingUnit">
        <el-input
          v-model="queryParams.dispatchingUnit"
          clearable
          @keyup.enter.native="getStudentList"
        />
      </el-form-item>
      <el-form-item label="人员类型" prop="personnelType" v-if="false">
        <el-checkbox-group v-model="queryParams.personnelType" size="small">
          <el-checkbox
            v-for="dict in this.getDictDatas(
              DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE
            )"
            :key="dict.value"
            :label="dict.value"
            >{{ dict.label }}</el-checkbox
          >
        </el-checkbox-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getStudentList"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button icon="el-icon-upload2" @click="handleImport('month')"
          >按月导入</el-button
        >
        <el-button icon="el-icon-upload2" @click="handleImport('week')"
          >按周导入</el-button
        >
        <el-button icon="el-icon-upload2" @click="handleImport('day')"
          >按日导入</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="20" type="flex" style="font-size: 14px">
      <el-col :span="8">
        <el-card shadow="none">
          <div slot="header">学员信息</div>
          <ul class="student-list" style="max-height: 690px; overflow-y: auto">
            <li
              v-for="item in studentList"
              :class="{ 'is-active': activeStudent.id === item.id }"
              :key="item.id"
              @click="handleStudentClick(item)"
            >
              {{ item.nickname }}
              （
              {{ item.grade }}级
              <dict-tag
                :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                :value="item.studentType"
              />
              <span v-if="item.majorName">-</span> {{ item.majorName }}
              <span v-if="item.dispatchingUnit">-</span>
              {{ item.dispatchingUnit }}
              ）
            </li>
          </ul>
        </el-card>
      </el-col>
      <el-col :span="16" style="min-width: 820px">
        <el-card shadow="none" v-loading="scheduleLoading">
          <div slot="header">排班信息</div>
          <div class="base-info" v-if="activeStudent">
            <span>姓名：{{ activeStudent.nickname }}</span>
            <span>年级：{{ activeStudent.grade }}</span>
            <span>
              轮转时长：{{ scheduleInfo.rotationTime }}
              <dict-tag
                :type="DICT_TYPE.ROTATION_CYCLE"
                :value="scheduleInfo.rotationCycle"
              />
            </span>
            <span>培训方案：{{ scheduleInfo.standardSchemeName }}</span>
          </div>
          <el-form class="control-bar" inline size="small">
            <el-form-item>
              <span slot="label">
                轮转总时长(<dict-tag
                  :type="DICT_TYPE.ROTATION_CYCLE"
                  :value="scheduleInfo.rotationCycle"
                />):
              </span>
              {{ totalTime }}
            </el-form-item>
            <el-form-item label="开始时间：">
              <el-date-picker
                style="width: 138px"
                :clearable="false"
                placeholder="选择日期"
                v-model="scheduleInfo.beginDate"
                :picker-options="{ disabledDate }"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="updateScheduleDate"
                >更新排班</el-button
              >
              <el-button type="primary" @click="addRotationDepartment"
                >添加轮转科室</el-button
              >
              <el-button type="default" @click="handleClearSchedule"
                >清空排班</el-button
              >
              <el-tooltip
                manual
                placement="top"
                effect="light"
                :value="visible"
              >
                <div slot="content">
                  调整排班后<strong style="color: red">一定要点击</strong
                  >该按钮进行<br />结果保存，否则将丢失修改信息！
                </div>
                <el-button type="primary" @click="handleSaveSchedule"
                  >保存排班</el-button
                >
              </el-tooltip>
            </el-form-item>
          </el-form>
          <div style="width: 100%; max-height: 610px; overflow-y: auto">
            <table class="schedule-table">
              <thead>
                <tr>
                  <th>标准科室</th>
                  <th>轮转科室</th>
                  <th>轮转时长</th>
                  <th>轮转时间</th>
                  <th>轮转状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <vue-draggable
                element="tbody"
                draggable=".draggable-tr"
                v-model="scheduleInfo.scheduleDetailses"
              >
                <tr
                  v-for="(item, index) in scheduleInfo.scheduleDetailses"
                  :key="index"
                  :class="{ 'draggable-tr': item.rotationStatus === 0 }"
                >
                  <td>{{ item.standardDepartmentName }}</td>
                  <td>{{ item.rotationDepartmentName }}</td>
                  <td>{{ item.rotationTime }}</td>
                  <td>
                    {{ item.rotationBeginTime }} ~ {{ item.rotationEndTime }}
                  </td>
                  <td>
                    <dict-tag
                      :type="DICT_TYPE.ROTATION_STATUS"
                      :value="item.rotationStatus"
                    />
                  </td>
                  <td>
                    <el-button
                      v-if="[0, 1, 3, 4].includes(item.rotationStatus)"
                      type="text"
                      @click="updateRotationDepartment(item, index)"
                      >编辑</el-button
                    >
                    <el-button
                      v-if="[0, 4].includes(item.rotationStatus)"
                      type="text"
                      @click="deleteRotationDepartment(index)"
                      >删除</el-button
                    >
                    <el-button
                      v-if="[0].includes(item.rotationStatus)"
                      type="text"
                      @click="toTopRotationDepartment(index)"
                      >置顶</el-button
                    >
                  </td>
                </tr>
              </vue-draggable>
            </table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="rotationTitle"
      :visible.sync="rotationOpen"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <el-form
        ref="departmentForm"
        :model="rotationForm"
        :rules="rotationRules"
        label-width="80px"
      >
        <el-form-item label="组合名称" prop="ruleId">
          <el-select v-model="rotationForm.ruleId" @change="handleRuleChange">
            <el-option
              v-for="g in groupList"
              :key="g.id"
              :label="g.name"
              :value="g.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标准科室" prop="standardDepartmentId">
          <el-select v-model="rotationForm.standardDepartmentId">
            <el-option
              v-for="d in standardDepartmentList"
              :key="d.standardDepartmentId"
              :label="d.standardDepartmentName"
              :value="d.standardDepartmentId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="轮转科室" prop="rotationDepartmentId">
          <el-select v-model="rotationForm.rotationDepartmentId" filterable>
            <el-option
              v-for="d in departmentList"
              :key="d.id"
              :label="d.name"
              :value="d.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="轮转时长" prop="rotationTime">
          <el-input-number
            controls-position="right"
            :min="0"
            :step="scheduleInfo.rotationCycle === '1' ? 0.5 : 1"
            v-model="rotationForm.rotationTime"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRotationDepartment"
          >确 定</el-button
        >
        <el-button @click="cancelRotationDepartment">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-popover placement="top" width="380px" v-model="downloadPopover">
            <el-input-number
              v-if="templateType === 'day'"
              v-model="departmentCount"
              style="margin-bottom: 10px"
              placeholder="请输入轮转科室数量"
              :min="1"
              :step="1"
              step-strictly
              controls-position="right"
            ></el-input-number>
            <el-date-picker
              v-else
              v-model="dateRange"
              style="margin-bottom: 10px"
              :type="{ month: 'monthrange', week: 'daterange' }[templateType]"
              :value-format="
                { month: 'yyyy-MM', week: 'yyyy-MM-dd' }[templateType]
              "
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
            <div style="text-align: right; margin: 0">
              <el-button
                size="mini"
                type="text"
                @click="downloadPopover = false"
                >取消</el-button
              >
              <el-button type="primary" size="mini" @click="importTemplate"
                >确定</el-button
              >
            </div>
            <el-link
              slot="reference"
              style="font-size: 12px; vertical-align: baseline"
              type="primary"
              :underline="false"
              >下载模板</el-link
            >
          </el-popover>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getUserStudentList,
  getStudentGradeList,
} from "@/api/system/userStudent";
import {
  getSchedule,
  importScheduleMonthTemplate,
  importScheduleWeekTemplate,
  importScheduleDayTemplate,
  saveSchedule,
} from "@/api/rotation/schedule";
import {
  getRotationRuleSimpleList,
  getStandardSchemeDepartments,
} from "@/api/rotation/rule";
import { getRotationDepartmentSimpleList } from "@/api/system/department";
import { getSimpleMajorList } from "@/api/system/major";
import { getBaseHeader } from "@/utils/request";
import dayjs from "dayjs";
import VueDraggable from "vuedraggable";

export default {
  name: "Schedule",
  components: {
    VueDraggable,
  },
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      studentGradeList: [],
      majorList: [],
      queryParams: {
        grade: undefined,
        studentType: undefined,
        major: undefined,
        nickname: undefined,
        username: undefined,
        personnelType: undefined,
        dispatchingUnit: undefined,
      },
      // 学员列表
      studentList: [],
      activeStudent: null,
      // 排班信息
      scheduleInfo: {
        id: 0,
        studentId: 0,
        standardSchemeId: 0,
        beginDate: "",
        scheduleDetailses: [],
        rotationTime: 0,
      },
      // 排班遮罩层
      scheduleLoading: true,
      // 轮转科室弹出层标题
      rotationTitle: "",
      // 是否显示轮转科室弹出层
      rotationOpen: false,
      // 编辑的轮转科室index
      editIndex: null,
      // 轮转科室表单参数
      rotationForm: {},
      // 轮转科室表单校验
      rotationRules: {
        ruleId: [
          { required: true, message: "组合名称不能为空", trigger: "blur" },
        ],
        standardDepartmentId: [
          { required: true, message: "标准科室不能为空", trigger: "blur" },
        ],
        rotationDepartmentId: [
          { required: true, message: "轮转科室不能为空", trigger: "blur" },
        ],
        rotationTime: [
          { required: true, message: "轮转时长不能为空", trigger: "blur" },
        ],
      },
      // 保存排班提示
      visible: false,
      // 是否管理员
      isManager: false,
      // 标准科室列表
      standardDepartmentList: [],
      // 科室列表
      departmentList: [],
      // 组合名称列表
      groupList: [],
      // 排班导入
      upload: {
        open: false,
        title: "",
        isUploading: false,
        headers: getBaseHeader(),
        url: "",
      },
      // 下载模版
      downloadPopover: false,
      dateRange: [],
      departmentCount: undefined,
      templateType: "month",
    };
  },
  computed: {
    totalTime() {
      let total = 0;
      const scheduleDetailses = this.scheduleInfo.scheduleDetailses || [];
      scheduleDetailses.forEach((s) => (total += s.rotationTime));
      return total;
    },
  },
  mounted() {
    getRotationDepartmentSimpleList().then(
      (res) => (this.departmentList = res.data)
    );
    getStudentGradeList().then((res) => {
      this.studentGradeList = res.data;
      // this.queryParams.grade = res.data[0] || undefined;
      this.getStudentList();
    });
    this.interval = setTimeout(() => {
      this.visible = true;
    }, 1000);
  },
  beforeDestroy() {
    this.visible = false;
    clearTimeout(this.interval);
  },
  methods: {
    /** 学员类型改变 */
    handleStudentTypeChange(value) {
      this.queryParams.major = null;
      this.majorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.majorList = res.data;
      });
    },
    /** 查询学员信息 */
    getStudentList() {
      getUserStudentList(this.queryParams, "rotation/schedule/index").then(
        (res) => {
          this.studentList = res.data;
          this.handleStudentClick(this.studentList[0]);
        }
      );
    },
    handleStudentClick(item) {
      this.activeStudent = item;
      if (this.activeStudent) {
        this.getStudentScheduleInfo(this.activeStudent.id);
      }
    },
    /** 根据学员信息查排班信息 */
    getStudentScheduleInfo(id) {
      this.scheduleLoading = true;
      getSchedule(id)
        .then((res) => {
          const scheduleInfo = res.data;
          if (!scheduleInfo.scheduleDetailses)
            scheduleInfo.scheduleDetailses = [];
          this.scheduleInfo = scheduleInfo;
          const { standardSchemeId, grade } = this.scheduleInfo;
          getRotationRuleSimpleList(standardSchemeId).then(
            (res) => (this.groupList = res.data)
          );
        })
        .finally(() => (this.scheduleLoading = false));
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.getStudentList();
    },
    /** 开始时间禁止选择日期 */
    disabledDate(date) {
      const d = date.getDate();
      if (this.scheduleInfo.rotationCycle === 1) {
        return [1, 16].indexOf(d) < 0;
      } else {
        return false;
      }
    },
    /** 更新排班 */
    updateScheduleDate() {
      if (!this.scheduleInfo.beginDate) return;
      let beginDate = dayjs(this.scheduleInfo.beginDate);
      this.scheduleInfo.scheduleDetailses.forEach((s) => {
        const int = parseInt(s.rotationTime);
        const half = s.rotationTime % 1 === 0.5;
        const isMid = beginDate.date() === 16;
        let endDate;
        if (this.scheduleInfo.rotationCycle === 1) {
          // 月
          endDate = beginDate.add(int, "month");
          if (half) {
            if (isMid) {
              endDate = endDate.endOf("month");
            } else {
              endDate = endDate.date(15);
            }
          } else {
            endDate = endDate.subtract(1, "day");
          }
        }
        if (this.scheduleInfo.rotationCycle === 2) {
          // 日
          endDate = beginDate.add(int, "day").subtract(1, "day");
        }
        if (this.scheduleInfo.rotationCycle === 3) {
          // 周
          endDate = beginDate.add(int, "week").subtract(1, "day");
        }
        s.rotationBeginTime = beginDate.format("YYYY-MM-DD");
        s.rotationEndTime = endDate.format("YYYY-MM-DD");
        beginDate = endDate.add(1, "day");
      });
    },
    /** 重置轮转科室表单 */
    resetRotationDepartment() {
      this.rotationForm = {
        ruleId: undefined,
        standardDepartmentId: undefined,
        rotationDepartmentId: undefined,
        rotationTime: undefined,
        rotationStatus: undefined,
      };
    },
    /** 新增轮转科室操作 */
    addRotationDepartment() {
      this.resetRotationDepartment();
      this.editIndex = null;
      this.rotationOpen = true;
      this.rotationTitle = "添加轮转科室";
    },
    /** 修改轮转科室操作 */
    async updateRotationDepartment(row, index) {
      await this.getStandardSchemeDepartmentsList(row.ruleId);
      this.rotationForm = { ...row };
      this.editIndex = index;
      this.rotationOpen = true;
      this.rotationTitle = "修改轮转科室";
    },
    /* 标准科室选择 */
    handleRuleChange(value) {
      this.getStandardSchemeDepartmentsList(value);
      this.rotationForm.rotationDepartmentId = "";
    },
    /** 获取标准科室列表 */
    async getStandardSchemeDepartmentsList(value) {
      const { data } = await getStandardSchemeDepartments(
        this.scheduleInfo.standardSchemeId,
        value
      );
      this.standardDepartmentList = data || [];
    },
    /** 提交轮转科室按钮 */
    submitRotationDepartment() {
      this.$refs.departmentForm.validate((valid) => {
        const { standardDepartmentId, rotationDepartmentId } =
          this.rotationForm;
        const standardDepartmentName = this.standardDepartmentList.find(
          (item) => item.standardDepartmentId === standardDepartmentId
        ).standardDepartmentName;
        const rotationDepartmentName = this.departmentList.find(
          (item) => item.id === rotationDepartmentId
        ).name;
        if (valid) {
          if (this.editIndex === null) {
            this.scheduleInfo.scheduleDetailses.push({
              ...this.rotationForm,
              standardDepartmentName,
              rotationDepartmentName,
              rotationBeginTime: "",
              rotationEndTime: "",
              rotationStatus: 0,
            });
          } else {
            this.scheduleInfo.scheduleDetailses.splice(this.editIndex, 1, {
              ...this.rotationForm,
              standardDepartmentName,
              rotationDepartmentName,
            });
          }
          this.cancelRotationDepartment();
        }
      });
    },
    /** 取消轮转科室按钮 */
    cancelRotationDepartment() {
      this.$refs.departmentForm.clearValidate();
      this.rotationOpen = false;
    },
    /** 删除轮转科室 */
    deleteRotationDepartment(index) {
      this.scheduleInfo.scheduleDetailses.splice(index, 1);
    },
    /** 置顶轮转科室 */
    toTopRotationDepartment(index) {
      const target = this.scheduleInfo.scheduleDetailses.splice(index, 1);
      const topIndex = this.scheduleInfo.scheduleDetailses.findIndex(
        (item) => item.rotationStatus === 0
      );
      this.scheduleInfo.scheduleDetailses.splice(topIndex, 0, ...target);
    },
    /** 清空排班 */
    handleClearSchedule() {
      this.scheduleInfo.scheduleDetailses =
        this.scheduleInfo.scheduleDetailses.filter((item) =>
          [1, 2, 3].includes(item.rotationStatus)
        );
    },
    /** 保存排班 */
    handleSaveSchedule() {
      saveSchedule(this.scheduleInfo).then(() =>
        this.$message.success("保存成功")
      );
    },
    /** 排班导入 */
    handleImport(type) {
      const typeTitle = {
        month: "按月导入排班",
        week: "按周导入排班",
        day: "按日导入排班",
      }[type];
      const typeUrl = {
        month: "/admin-api/rotation/schedule/import-month",
        week: "/admin-api/rotation/schedule/import-week",
        day: "/admin-api/rotation/schedule/import-day",
      }[type];
      this.templateType = type;
      this.upload.title = typeTitle;
      this.upload.url = process.env.VUE_APP_BASE_API + typeUrl;
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      const [beginDate, endDate] = this.dateRange;
      const importPromise = {
        month: importScheduleMonthTemplate(beginDate, endDate),
        week: importScheduleWeekTemplate(beginDate, endDate),
        day: importScheduleDayTemplate(this.departmentCount),
      }[this.templateType];

      importPromise.then((response) => {
        const typeFileName = {
          month: "按月排班导入模板.xlsx",
          week: "按周排班导入模板.xlsx",
          day: "按日排班导入模板.xlsx",
        }[this.templateType];
        this.$download.excel(response, typeFileName);
        this.downloadPopover = false;
      });
    },
    // 文件上传中处理
    handleFileUploadProgress() {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      let text = "创建成功数量：" + data.createScheduleDetails.length;
      for (const name of data.createScheduleDetails) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      }

      text +=
        "<br />导入失败数量：" + Object.keys(data.failureUsernames).length;
      for (const name in data.failureUsernames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          name +
          "：" +
          data.failureUsernames[name];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getStudentScheduleInfo(this.activeStudent.id);
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="scss" scoped>
.student-list {
  margin: 0;
  padding: 0;
  max-height: 700px;
  overflow-y: auto;

  li {
    list-style-type: none;
    padding: 5px 10px;
    line-height: 1.5;
    cursor: pointer;

    &.is-active {
      background: #f6f7ff;
    }
  }
}

.base-info {
  margin-bottom: 20px;

  span {
    margin-right: 20px;
  }
}

.control-bar {
  float: right;

  .el-form-item {
    margin-bottom: 10px;
  }

  .el-form-item:last-child {
    margin-right: 0;
  }
}

.schedule-table {
  width: 100%;
  text-align: center;
  border-collapse: collapse;
  border: 1px solid #eee;

  thead {
    background: #eee;
  }

  th,
  td {
    height: 47px;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
  }

  tr.draggable-tr {
    cursor: move;
  }
}
</style>
