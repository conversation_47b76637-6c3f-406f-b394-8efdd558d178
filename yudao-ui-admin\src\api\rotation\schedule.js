import request from '@/utils/request'

// 创建排班
export function createSchedule(data) {
  return request({
    url: '/rotation/schedule/create',
    method: 'post',
    data: data
  })
}

// 更新排班
export function updateSchedule(data) {
  return request({
    url: '/rotation/schedule/update',
    method: 'put',
    data: data
  })
}

// 删除排班
export function deleteSchedule(id) {
  return request({
    url: '/rotation/schedule/delete?id=' + id,
    method: 'delete'
  })
}

// 获得排班
export function getSchedule(studentId) {
  return request({
    url: '/rotation/schedule/get-by-studentid?studentId=' + studentId,
    method: 'get'
  })
}

// 获得排班分页
export function getSchedulePage(query) {
  return request({
    url: '/rotation/schedule/page',
    method: 'get',
    params: query
  })
}

// 导出排班 Excel
export function exportScheduleExcel(query) {
  return request({
    url: '/rotation/schedule/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 保存排班
export function saveSchedule(data) {
  return request({
    url: '/rotation/schedule/save',
    method: 'put',
    data
  })
}

// 获得排班轮转科室
export function getRotationDepartments(standardSchemeId, grade) {
  return request({
    url: '/rotation/schedule/get-rotation-departments',
    method: 'get',
    params: { standardSchemeId, grade }
  })
}

// 获得导入月排班模板
export function importScheduleMonthTemplate(beginDate, endDate) {
  return request({
    url: `/rotation/schedule/get-import-month-template?beginDate=${beginDate}&endDate=${endDate}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 获得导入周排班模板
export function importScheduleWeekTemplate(beginDate, endDate) {
  return request({
    url: `/rotation/schedule/get-import-week-template?beginDate=${beginDate}&endDate=${endDate}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 获得导入日排班模板
export function importScheduleDayTemplate(rotationDepartmentSize) {
  return request({
    url: `/rotation/schedule/get-import-day-template?rotationDepartmentSize=${rotationDepartmentSize}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取排班学员用户年级列表
export function getScheduleGradeList(standardSchemeId) {
  return request({
    url: `/rotation/schedule/list-all-grade?standardSchemeId=${standardSchemeId}`,
    method: 'get'
  })
}

// 根据轮转科室获得当前用户排班详情
export function getScheduleByRotationDeptId(rotationDepartmentId) {
  return request({
    url: '/rotation/schedule-details/get-by-rotation-department-id?rotationDepartmentId=' + rotationDepartmentId,
    method: 'get'
  })
}

// 根据时间获得当前用户排班详情
export function getScheduleByRotationDates(query) {
  return request({
    url: '/rotation/schedule-details/get-by-rotation-dates',
    method: 'get',
    params: query
  })
}

// 根据时间获得学员用户排班详情
export function getScheduleByRotationDatesStudent(query) {
  return request({
    url: '/rotation/schedule-details/get-by-rotation-dates-student',
    method: 'get',
    params: query
  })
}
