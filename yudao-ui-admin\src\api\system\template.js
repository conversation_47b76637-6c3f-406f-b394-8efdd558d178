import request from '@/utils/request'
import { Base64 } from "js-base64";
import { getAccessToken } from '@/utils/auth';

// 创建模板
export function createTemplate(data) {
  return request({
    url: '/system/template/create',
    method: 'post',
    data: data
  })
}

// 更新模板
export function updateTemplate(data) {
  return request({
    url: '/system/template/update',
    method: 'put',
    data: data
  })
}

// 删除模板
export function deleteTemplate(id) {
  return request({
    url: '/system/template/delete?id=' + id,
    method: 'delete'
  })
}

// 获得模板
export function getTemplate(id) {
  return request({
    url: '/system/template/get?id=' + id,
    method: 'get'
  })
}

// 获得模板分页
export function getTemplatePage(query) {
  return request({
    url: '/system/template/page',
    method: 'get',
    params: query
  })
}

// 导出模板 Excel
export function exportTemplateExcel(query) {
  return request({
    url: '/system/template/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 通过模板类型与学员类型获得模板
export function getTemplateByType(query) {
  return request({
    url: '/system/template/get-by-type',
    method: 'get',
    params: query
  })
}

// 获得doc模板内容
export function getDocTemplateContent(query) {
  return request({
    url: '/system/template/get-doc-content',
    method: 'get',
    params: query
  })
}

// 获得html模板内容
export function getHtmlTemplateContent(query) {
  return request({
    url: '/system/template/get-html-content',
    method: 'get',
    params: query
  })
}

/**
 * 数据开放服务： kkFile预览doc模板内容
 * @param id 编号
 * @param objectId 业务系统id
 * @param name 文件名称
 * @returns {string}
 */
export const previewDocTemplateUrl = (id, objectId, name) => {
  const baseUrl = process.env.VUE_APP_BASE_API;
  const kkfileUrl = process.env.VUE_APP_KKFILE_API;
  const previewUrl = `${kkfileUrl}/kkfileview/onlinePreview?url=`;
  const url = `${baseUrl}/admin-api/system/template/get-doc-content?id=${id}&objectId=${
    objectId}&fullfilename=${name}${Date.now()}.docx&token=${getAccessToken()}`;
  return previewUrl + encodeURIComponent(Base64.encode(url));
};
