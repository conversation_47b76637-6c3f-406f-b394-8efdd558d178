import request from '@/utils/request'

// 获得排班分页
export function getMentorPage(query) {
  return request({
    url: '/rotation/user-worker-mentor/page',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/mentorLibrary/index'}
  })
}

// 获得职工用户导师详情(根据id)
export function getMentorDetail(id) {
  return request({
    url: `/rotation/user-worker-mentor/get?id=${id}`,
    method: 'get',
  })
}

// 获得职工用户导师详情(登录用户本身)
export function getMentorOwn(query) {
  return request({
    url: '/rotation/user-worker-mentor/getOwn',
    method: 'get',
    params: query
  })
}

// 获得职工用户导师详情(基于学员)
export function getMentorByStudent(query) {
  return request({
    url: '/rotation/user-worker-mentor/getByStudent',
    method: 'get',
    params: query
  })
}

// 新职工用户导师
export function updateMentorInfo(data) {
  return request({
    url: '/rotation/user-worker-mentor/update',
    method: 'put',
    data: data
  })
}

// 获得职工用户导师精简列表
export function getMentorSimpleList(query) {
  return request({
    url: '/rotation/user-worker-mentor/simpleList',
    method: 'get',
    params: query
  })
}