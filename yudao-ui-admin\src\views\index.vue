<template>
  <!-- <div class="dashboard-editor-container">

    <panel-group @handleSetLineChartData="handleSetLineChartData" />

    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <line-chart :chart-data="lineChartData" />
    </el-row>

    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <raddar-chart />
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <pie-chart />
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <bar-chart />
        </div>
      </el-col>
    </el-row>


  </div> -->
  <div class="welcome-page">
    <h3>欢迎使用教学管理系统！</h3>

    <el-tabs class="notice-tabs" v-if="showNotice">
      <el-tab-pane label="院内通知">
        <el-table :data="internalNotices">
          <el-table-column label="标题" prop="title">
            <template v-slot="scope">
              <el-link class="notice-title" @click="handleTitleClick(scope.row.id)">{{ scope.row.title }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="发布日期" prop="createTime" width="110px" align="center"></el-table-column>
          <el-table-column label="阅读状态" prop="readed" width="100px" align="center">
            <template v-slot="scope">{{ scope.row.readed ? "已阅" : "未阅" }}</template>
          </el-table-column>
        </el-table>
        <pagination v-show="internalTotal > 0" :total="internalTotal" :page.sync="internalParams.pageNo" :limit.sync="internalParams.pageSize"
                    @pagination="queryInternalNotice"/>
      </el-tab-pane>
      <el-tab-pane label="院外通知">
        <el-table :data="foreignNotices">
          <el-table-column label="标题" prop="title">
            <template v-slot="scope">
              <el-link class="notice-title" @click="handleTitleClick(scope.row.id)">{{ scope.row.title }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="发布日期" prop="createTime" width="110px" align="center"></el-table-column>
          <el-table-column label="浏览次数" prop="readCount" width="100px" align="center"></el-table-column>
        </el-table>
        <pagination v-show="foreignTotal > 0" :total="foreignTotal" :page.sync="foreignParams.pageNo" :limit.sync="foreignParams.pageSize"
                    @pagination="queryForeignNotice"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import PanelGroup from './dashboard/PanelGroup'
import LineChart from './dashboard/LineChart'
import RaddarChart from './dashboard/RaddarChart'
import PieChart from './dashboard/PieChart'
import BarChart from './dashboard/BarChart'
import { getForeignNoticeInfoPage, getInternalNoticeInfoPage } from '@/api/rotation/noticeInfo'

const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }
}

export default {
  name: 'Index',
  components: {
    PanelGroup,
    LineChart,
    RaddarChart,
    PieChart,
    BarChart
  },
  data() {
    return {
      lineChartData: lineChartData.newVisitis,
      foreignParams: {
        pageNo: 1,
        pageSize: 10,
      },
      foreignTotal: 0,
      internalParams: {
        pageNo: 1,
        pageSize: 10,
      },
      internalTotal: 0,
      foreignNotices: [],
      internalNotices: [],
      showNotice: false,
    }
  },
  methods: {
    handleSetLineChartData(type) {
      this.lineChartData = lineChartData[type]
    },
    queryForeignNotice() {
      getForeignNoticeInfoPage(this.foreignParams).then(res => {
        this.foreignNotices = res.data.list;
        this.foreignTotal = res.data.total;
      });
    },
    queryInternalNotice() {
      getInternalNoticeInfoPage(this.internalParams).then(res => {
        this.internalNotices = res.data.list;
        this.internalTotal = res.data.total;
      });
    },
    handleTitleClick(id) {
      this.$router.push(`/notice/notice-view?id=${id}`);
    },
  },
  created() {
    this.getConfigKey("index-notice-show").then(res => {
      this.showNotice = res.data === "true";
    });
    this.queryForeignNotice();
    this.queryInternalNotice();
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}

.welcome-page{
  position: absolute;
  width: 100%;
  height: 100%;
  background: #fff url('../assets/images/welcome.jpg') no-repeat right top;
  background-position: 100%;
  overflow: auto;
  padding-bottom: 20px;

  h3{
    font-size: 26px;
    margin-top: 70px;
    margin-left: 50px;
  }

  .notice-tabs {
    width: 500px;
    margin-left: 50px;

    ::v-deep .el-tabs__nav-wrap::after {
      display: none;
    }
  }
}

.notice-title {
  display: initial;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
