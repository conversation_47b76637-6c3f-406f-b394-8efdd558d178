<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学员类型" prop="studentTypes">
        <el-select
          v-model="queryParams.studentTypes"
          placeholder="请选择学员类型"
          multiple
          filterable
          clearable
          size="small"
          @change="handleQueryStudentTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="majors">
        <el-select
          v-model="queryParams.majors"
          placeholder="请选择培训专业"
          multiple
          filterable
          clearable
          size="small"
        >
          <!-- <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_MAJOR)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/> -->
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训状态" prop="trainingStatus">
        <el-select
          v-model="queryParams.trainingStatus"
          filterable
          placeholder="请选择培训状态"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.SYSTEM_USER_TRAINING_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grades">
        <el-select
          v-model="queryParams.grades"
          placeholder="请选择年级"
          multiple
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="grade in studentGradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员类型" prop="personnelTypes">
        <el-select
          v-model="queryParams.personnelTypes"
          multiple
          filterable
          placeholder="请选择人员类型"
          clearable
          size="small"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="派送单位" prop="dispatchingUnit">
        <el-input
          v-model="queryParams.dispatchingUnit"
          placeholder="请输入派送单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户性别" prop="sex">
        <el-select
          v-model="queryParams.sex"
          placeholder="请选择用户性别"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:user-student:create']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:user-student:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click.native="handleImport"
          v-hasPermi="['system:user-student:import']"
        >
          导入
        </el-button>
        <!-- <el-dropdown v-hasPermi="['system:user-student:import']">
          <el-button type="warning" plain icon="el-icon-upload2" size="mini">
            导入<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="handleImport">选择文件导入</el-dropdown-item>
            <el-dropdown-item @click.native="handleExportTemplate">下载导入模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="用户ID" align="center" prop="id" /> -->
      <el-table-column label="姓名" align="left" prop="nickname" width="180">
        <template v-slot="scope">
          {{ scope.row.nickname }}
          <el-tag
            v-if="scope.row.hasMedicalLicense"
            size="small"
            style="margin-right: 6px"
            >资</el-tag
          >
          <el-tag
            v-if="scope.row.hasPracticingLicense"
            size="small"
            style="margin-right: 6px"
            >执</el-tag
          >
          <el-tag
            v-if="scope.row.obtainedPrescription"
            size="small"
            style="margin-right: 6px"
            >处</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          />
        </template>
      </el-table-column>

      <el-table-column label="培训专业" align="center" prop="majorName">
        <!-- <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_MAJOR" :value="parseInt(scope.row.major)" />
        </template> -->
      </el-table-column>
      <el-table-column label="培训时长" align="center" prop="rotationTime" />
      <el-table-column label="手机号码" align="center" prop="mobile" />
      <el-table-column label="派送单位" align="center" prop="dispatchingUnit" />
      <el-table-column label="用户性别" align="center" prop="sex">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="用户状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <!-- <el-table-column label="人员类型" align="center" prop="personnelType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE" :value="scope.row.personnelType" />
        </template>
      </el-table-column>
      <el-table-column label="培训开始日期" align="center" prop="trainingStartDate" />
      <el-table-column label="责任导师" align="center" prop="responsibleMentorUserId" /> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row, 'edit')"
            v-hasPermi="['system:user-student:update']"
          >
            修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:user-student:delete']"
          >
            删除
          </el-button>
          <el-dropdown
            @command="
              (command) => handleCommand(command, scope.$index, scope.row)
            "
            v-hasPermi="[
              'system:user:delete',
              'system:user:update-password',
              'system:permission:assign-user-role',
            ]"
          >
            <span class="el-dropdown-link">
              <i class="el-icon-d-arrow-right el-icon--right"></i>更多
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="handleResetPwd"
                size="mini"
                type="text"
                icon="el-icon-key"
                v-hasPermi="['system:user:update-password']"
              >
                重置密码
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />
          是否更新已经存在的学员档案数据
          <p>
            仅允许导入xls、xlsx格式文件。
            <el-link
              style="font-size: 12px"
              type="primary"
              :underline="false"
              @click="handleExportTemplate"
              >下载模板</el-link
            >
          </p>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <user-student-dialog
      :dialogTitle="title"
      :dialogOpen="open"
      :userId="curRow.id"
      :opt="opt"
      @update:dialogOpen="(value) => (open = value)"
      @refresh="getList"
    />
  </div>
</template>

<script>
import userStudentDialog from "./userStudentDialog";
import {
  deleteUserStudent,
  getUserStudentPage,
  exportUserStudentExcel,
  exportTemplate,
  getStudentGradeList,
} from "@/api/system/userStudent";
import { resetUserPwd } from "@/api/system/user";
import { updateUserStatus } from "@/api/system/userStudent";
import { getSimpleMajorList } from "@/api/system/major";
import { getBaseHeader } from "@/utils/request";
import { CommonStatusEnum } from "@/utils/constants";

export default {
  name: "UserStudent",
  components: { userStudentDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员用户列表
      list: [],
      queryMajorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        username: null,
        nickname: null,
        sex: null,
        studentTypes: null,
        majors: null,
        grades: null,
        personnelTypes: null,
        dispatchingUnit: null,
        trainingStatus: null,
      },

      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的科室数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/admin-api/system/user-student/import",
      },

      studentGradeList: [],
      opt: "",
      curRow: {},
    };
  },
  created() {
    this.getList();
    getStudentGradeList().then((res) => {
      this.studentGradeList = res.data;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getUserStudentPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryMajorList = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查询学员类型改变 */
    handleQueryStudentTypeChange(value) {
      this.queryParams.major = null;
      this.queryMajorList = [];
      getSimpleMajorList({ studentTypes: value?.join(",") }).then((res) => {
        this.queryMajorList = res.data;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      this.opt = "add";
      this.curRow = {};
      this.open = true;
      this.title = "添加学员用户";
    },
    /** 修改按钮操作 */
    handleUpdate(row, type) {
      // this.reset();
      this.opt = type;
      this.curRow = row;
      this.open = true;
      this.title = "修改学员用户";
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === CommonStatusEnum.ENABLE ? "启用" : "锁定";
      this.$modal
        .confirm('确认要"' + text + '""' + row.username + '"用户吗?')
        .then(function () {
          return updateUserStatus(row.id, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status =
            row.status === CommonStatusEnum.ENABLE
              ? CommonStatusEnum.DISABLE
              : CommonStatusEnum.ENABLE;
        });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除学员用户编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteUserStudent(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有学员用户数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportUserStudentExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "学员用户.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "学员档案导入";
      this.upload.open = true;
    },
    /** 导出按钮操作 */
    handleExportTemplate() {
      exportTemplate()
        .then((response) => {
          this.$download.excel(response, "学员档案导入模版.xlsx");
        })
        .catch(() => {});
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      let text = "创建成功数量：" + data.createUsernames.length;
      // for (const name of data.createUsernames) {
      //   text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + name;
      // }
      text += "<br />更新成功数量：" + data.updateUsernames.length;
      // for (const name of data.updateUsernames) {
      //   text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + name;
      // }
      text +=
        "<br />更新失败数量：" + Object.keys(data.failureUsernames).length;
      for (const name in data.failureUsernames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          name +
          "：" +
          data.failureUsernames[name];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 更多操作
    handleCommand(command, index, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        default:
          break;
      }
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.username + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,16}$/,
        inputErrorMessage:
          "必须包含大小写字母和数字的组合，可以使用特殊字符，长度8到16位",
      })
        .then(({ value }) => {
          resetUserPwd(row.id, value).then((response) => {
            this.$modal.msgSuccess("修改成功，新密码是：" + value);
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss">
.user-student-dialog {
  .el-dialog__body {
    flex: auto;
    padding-top: 5px;
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .steps-box {
      border-radius: 4px;
      background: #f5f7fa;

      .el-radio-button__inner {
        background: transparent;
        border: none;
        border-radius: 0;
      }

      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        color: #1890ff;
        background-color: transparent;
        border-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
        position: relative;

        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 100%;
          height: 2px;
          background: #1890ff;
          bottom: 0;
          left: 0;
        }
      }
    }

    .form-box {
      flex: auto;
      overflow-y: auto;
      padding: 15px 10px 0 10px;
    }

    .el-date-editor.el-input {
      width: auto;
    }
  }
}
</style>
