import request from "@/utils/request";

// 获得专业基地科室关系,专业基地配置
export function getProfessionalBaseDepartment(query) {
  return request({
    url: "/system/professional-base-department/get",
    method: "get",
    params: query,
  });
}

// 获得专业基地科室关系,专业基地配置分页
export function getProfessionalBaseDepartmentPage(query) {
  return request({
    url: "/system/professional-base-department/page",
    method: "get",
    params: query,
  });
}

// 更新专业基地科室关系,专业基地配置
export function updateProfessionalBaseDepartment(data) {
  return request({
    url: "/system/professional-base-department/update",
    method: "put",
    data,
  });
}

// 获取专业基地精简信息列表
export function getBaseDepartmentSimpleList(query) {
  return request({
    url: "/system/professional-base-department/list-all-simple",
    method: "get",
    params: query,
  });
}
