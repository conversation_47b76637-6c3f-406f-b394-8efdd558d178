<template>
  <div class="app-container">
    <el-calendar class="active-calendar" v-model="activeMonth">
      <template v-slot:dateCell="scope">
        <div class="cell-date">{{ getFormatDate(scope.date) }}</div>
        <div class="active-list">
          <div class="active-item" v-for="item in getCurrentDayActives(scope.date)">{{ getActiveDesc(item) }}</div>
        </div>
      </template>
    </el-calendar>
  </div>
</template>

<script>
import { getTeachingActiveCalendar } from '@/api/rotation/teachingActiveCalendar'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'Calendar',
  data() {
    return {
      list: [],
      activeMonth: parseTime(new Date(), '{y}-{m}'),
    }
  },
  methods: {
    queryCalendarData() {
      const activeMonth = parseTime(this.activeMonth, '{y}-{m}')
      getTeachingActiveCalendar({ activeMonth }).then(res => {
        this.list = res.data
      });
    },
    getFormatDate(date) {
      return parseTime(date, '{m}-{d}')
    },
    getCurrentDayActives(date) {
      return this.list.filter(item => item.startTime && parseTime(date, '{y}-{m}-{d}') === parseTime(new Date(item.startTime), '{y}-{m}-{d}'))
    },
    getActiveDesc(item) {
      return parseTime(new Date(item.startTime), '{h}:{i}') + ' '
        + item.departmentName + '-'
        + item.speakerUsername + '-'
        + item.name
    },
  },
  created() {
    this.queryCalendarData()
  },
  watch: {
    activeMonth() {
      this.queryCalendarData()
    }
  },
}
</script>

<style scoped lang="scss">
.active-calendar {
  font-size: 15px;

  ::v-deep .el-calendar-day {
    height: 110px;
    position: relative;
  }

  ::v-deep .el-calendar-day:hover .active-list {
    height: auto;
    background: #f1f8ff;
    position: absolute;
    left: 0;
    right: 0;
    z-index: 1;
    padding: 0 8px;

    .active-item {
      max-height: initial;
      -webkit-line-clamp: initial;
    }
  }

  .cell-date {
    font-weight: 600;
    margin-bottom: 4px;
  }

  .active-list {
    height: 80px;
    overflow: hidden;
  }

  .active-item {
    max-height: 42px;
    font-size: 14px;
    color: #333;
    line-height: 1.3;
    margin-bottom: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    padding-left: 8px;
    position: relative;
  }

  .active-item::before {
    content: '';
    display: block;
    width: 4px;
    height: 4px;
    border-radius: 2px;
    background: #1c84c6;
    position: absolute;
    left: 0;
    top: 7px;
  }
}
</style>
