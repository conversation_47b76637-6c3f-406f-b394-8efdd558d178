import request from "@/utils/request";

// 创建职工用户
export function createUserWorker(data) {
  return request({
    url: "/system/user-worker/create",
    method: "post",
    data: data,
  });
}

// 更新职工用户
export function updateUserWorker(data) {
  return request({
    url: "/system/user-worker/update",
    method: "put",
    data: data,
  });
}

// 删除职工用户
export function deleteUserWorker(id) {
  return request({
    url: "/system/user-worker/delete?id=" + id,
    method: "delete",
  });
}

// 获得职工用户
export function getUserWorker(id) {
  return request({
    url: "/system/user-worker/get?id=" + id,
    method: "get",
  });
}

// 获得当前职工用户
export function getCurrentUserWorker() {
  return request({
    url: "/system/user-worker/get-current",
    method: "get",
  });
}

// 获得职工用户分页
export function getUserWorkerPage(query) {
  return request({
    url: "/system/user-worker/page",
    method: "get",
    params: query,
    headers: { component: "system/userWorker/index" },
  });
}

// 获取职工精简信息列表
export function getUserWorkerSimpleList(query) {
  return request({
    url: "/system/user-worker/list-all-simple",
    method: "get",
    params: query,
  });
}

// 获取职工精简信息列表
export function getUserWorkerPermissionList(query) {
  return request({
    url: "/system/user-worker/list-dept-permission-user",
    method: "get",
    params: query,
  });
}

// 获取当前职工精简信息列表
export function getUserWorkerCurrentList(query) {
  return request({
    url: "/system/user-worker/list-current-user",
    method: "get",
    params: query,
  });
}

// 导出职工用户 Excel
export function exportUserWorkerExcel(query) {
  return request({
    url: "/system/user-worker/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获取技能考官职工精简信息列表
export function getSkillsExaminerList(query) {
  return request({
    url: "/system/user-worker/list-skills-examiner",
    method: "get",
    params: query,
  });
}

// 获取技能考官职工精简信息列表
export function getTeachingDirectorList(query) {
  return request({
    url: "/system/user-worker/list-teaching-director",
    method: "get",
    params: query,
  });
}

// 获取带教老师精简信息列表
export function getTeaterList(query) {
  return request({
    url: "/system/user-worker/list-teacher",
    method: "get",
    params: query,
  });
}

// 导入模版
export function exportTemplate() {
  return request({
    url: "/system/user-worker/get-import-template",
    method: "get",
    responseType: "blob",
  });
}

// 修改用户状态
export function updateUserStatus(id, status) {
  const data = {
    id,
    status,
  };
  return request({
    url: "/system/user-worker/update-status",
    method: "put",
    data: data,
  });
}
