<template>
  <el-drawer custom-class="custom-drawer" size="88%" :title="`参考人员(${paperName})`" :visible.sync="open" :wrapper-closable="false" >
    <div class="drawer-content-wrapper">
      <el-tabs v-model="candidateType">
        <el-tab-pane label="学员" name="2">
          <el-form class="filter-form" inline label-width="72px" size="small">
            <el-form-item label="姓名">
              <el-input v-model="studentQueryParams.nickname" placeholder="请输入" clearable></el-input>
            </el-form-item>
            <el-form-item label="用户名">
              <el-input v-model="studentQueryParams.username" placeholder="请输入" clearable></el-input>
            </el-form-item>
            <el-form-item label="性别">
              <el-select v-model="studentQueryParams.sex" clearable>
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)" :key="dict.value"
                           :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学员类型">
              <el-select v-model="studentQueryParams.studentType" clearable filterable @change="handleStudentTypeChange">
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)" :key="dict.value"
                           :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="培训专业">
              <el-select v-model="studentQueryParams.major" clearable filterable>
                <el-option v-for="item in majorList" :key="item.code" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="年级">
              <el-select v-model="studentQueryParams.grade" clearable filterable>
                <el-option v-for="item in gradeList" :key="item" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleStudentQuery">查询</el-button>
            </el-form-item>
          </el-form>

          <div class="mb10">
            <el-button type="primary" size="small" @click="handleAdd">新增考生</el-button>
            <el-button size="small" @click="handleImport">Excel文件导入</el-button>
            <el-button size="small" @click="handleDeleteStudents">删除考生</el-button>
          </div>

          <el-table v-loading="studentLoading" :data="studentList" @selection-change="handleStudentSelection">
            <el-table-column type="selection"></el-table-column>
            <el-table-column label="姓名" prop="nickname"></el-table-column>
            <el-table-column label="用户名" prop="username"></el-table-column>
            <el-table-column label="性别" prop="sex">
              <template v-slot="{ row }">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_SEX, row.sex) }}</template>
            </el-table-column>
            <el-table-column label="学员类型" prop="studentType">
              <template v-slot="{ row }">{{ getDictDataLabel(DICT_TYPE.SYSTEM_STUDENT_TYPE, row.studentType) }}</template>
            </el-table-column>
            <el-table-column label="培训专业" prop="majorName"></el-table-column>
            <el-table-column label="年级" prop="grade"></el-table-column>
          </el-table>
          <pagination v-show="studentTotal > 0" :total="studentTotal" :page.sync="studentQueryParams.pageNo"
                      :limit.sync="studentQueryParams.pageSize" @pagination="getStudentList"/>
        </el-tab-pane>

        <el-tab-pane label="职工" name="1">
          <el-form class="filter-form" inline label-width="72px" size="small">
            <el-form-item label="姓名">
              <el-input v-model="workQueryParams.nickname" placeholder="请输入" clearable></el-input>
            </el-form-item>
            <el-form-item label="用户名">
              <el-input v-model="workQueryParams.username" placeholder="请输入" clearable></el-input>
            </el-form-item>
            <el-form-item label="性别">
              <el-select v-model="workQueryParams.sex" clearable>
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)" :key="dict.value"
                           :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="角色">
              <el-select v-model="workQueryParams.roleId" clearable filterable>
                <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="职称">
              <el-select v-model="workQueryParams.positionalTitles" clearable filterable>
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)" :key="dict.value"
                           :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="科室">
              <el-select v-model="workQueryParams.deptId" clearable filterable>
                <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleWorkQuery">查询</el-button>
            </el-form-item>
          </el-form>

          <div class="mb10">
            <el-button type="primary" size="small" @click="handleAdd">新增考生</el-button>
            <el-button size="small" @click="handleImport">Excel文件导入</el-button>
            <el-button size="small" @click="handleDeleteStudents">删除考生</el-button>
          </div>

          <el-table v-loading="workLoading" :data="workList" @selection-change="handleWorkSelection">
            <el-table-column type="selection"></el-table-column>
            <el-table-column label="姓名" prop="nickname"></el-table-column>
            <el-table-column label="用户名" prop="username"></el-table-column>
            <el-table-column label="性别" prop="sex">
              <template v-slot="{row}">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_SEX, row.sex) }}</template>
            </el-table-column>
            <el-table-column label="角色" prop="roleNames" min-width="150px"></el-table-column>
            <el-table-column label="职称" prop="positionalTitles">
              <template v-slot="{row}">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES, row.positionalTitles) }}</template>
            </el-table-column>
            <el-table-column label="科室" prop="deptNames" min-width="150px" show-overflow-tooltip></el-table-column>
          </el-table>
          <pagination v-show="workTotal > 0" :total="workTotal" :page.sync="workQueryParams.pageNo"
                      :limit.sync="workQueryParams.pageSize" @pagination="getWorkList"/>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-dialog title="新增参考人员" width="1000px" :visible.sync="addOpen" append-to-body>
      <el-tabs v-model="candidateType" v-if="addOpen">
        <el-tab-pane label="学员" name="2">
          <el-form class="filter-form" inline label-width="72px" size="small">
            <el-form-item label="姓名">
              <el-input v-model="studentQueryParams.nickname" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="用户名">
              <el-input v-model="studentQueryParams.username" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="性别">
              <el-select v-model="studentQueryParams.sex">
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)" :key="dict.value"
                           :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学员类型">
              <el-select v-model="studentQueryParams.studentType" @change="handleStudentTypeChange">
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)" :key="dict.value"
                           :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="培训专业">
              <el-select v-model="studentQueryParams.major">
                <el-option v-for="item in majorList" :key="item.code" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="年级">
              <el-select v-model="studentQueryParams.grade">
                <el-option v-for="item in gradeList" :key="item" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleAddStudentQuery">查询</el-button>
            </el-form-item>
          </el-form>

          <el-table v-loading="addStudentLoading" :data="addStudentList" @selection-change="handleAddStudentSelection">
            <el-table-column type="selection"></el-table-column>
            <el-table-column label="姓名" prop="nickname"></el-table-column>
            <el-table-column label="用户名" prop="username"></el-table-column>
            <el-table-column label="性别" prop="sex">
              <template v-slot="{row}">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_SEX, row.sex) }}</template>
            </el-table-column>
            <el-table-column label="学员类型" prop="studentType">
              <template v-slot="{row}">{{ getDictDataLabel(DICT_TYPE.SYSTEM_STUDENT_TYPE, row.studentType) }}</template>
            </el-table-column>
            <el-table-column label="培训专业" prop="majorName"></el-table-column>
            <el-table-column label="年级" prop="grade"></el-table-column>
          </el-table>
          <pagination v-show="addStudentTotal > 0" :total="addStudentTotal" :page.sync="studentQueryParams.pageNo"
                      :limit.sync="studentQueryParams.pageSize" @pagination="getAddStudentList"/>
        </el-tab-pane>

        <el-tab-pane label="职工" name="1">
          <el-form class="filter-form" inline label-width="72px" size="small">
            <el-form-item label="姓名">
              <el-input v-model="workQueryParams.nickname" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="用户名">
              <el-input v-model="workQueryParams.username" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="性别">
              <el-select v-model="workQueryParams.sex">
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)" :key="dict.value"
                           :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="角色">
              <el-select v-model="workQueryParams.roleId">
                <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="职称">
              <el-select v-model="workQueryParams.positionalTitles">
                <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)" :key="dict.value"
                           :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="科室">
              <el-select v-model="workQueryParams.deptId">
                <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleAddWorkQuery">查询</el-button>
            </el-form-item>
          </el-form>

          <el-table v-loading="addWorkLoading" :data="addWorkList" @selection-change="handleAddWorkSelection">
            <el-table-column type="selection"></el-table-column>
            <el-table-column label="姓名" prop="nickname"></el-table-column>
            <el-table-column label="用户名" prop="username"></el-table-column>
            <el-table-column label="性别" prop="sex">
              <template v-slot="{row}">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_SEX, row.sex) }}</template>
            </el-table-column>
            <el-table-column label="角色" prop="roleNames" min-width="150px"></el-table-column>
            <el-table-column label="职称" prop="positionalTitles">
              <template v-slot="{row}">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES, row.positionalTitles) }}</template>
            </el-table-column>
            <el-table-column label="科室" prop="deptNames" min-width="150px" show-overflow-tooltip></el-table-column>
          </el-table>
          <pagination v-show="addWorkTotal > 0" :total="addWorkTotal" :page.sync="workQueryParams.pageNo"
                      :limit.sync="workQueryParams.pageSize" @pagination="getAddWorkList"/>
        </el-tab-pane>
      </el-tabs>

      <template v-slot:footer>
        <el-button type="primary" @click="handleAddSure">添加选中考生</el-button>
        <el-button @click="handleAddCancel">取消</el-button>
      </template>
    </el-dialog>

    <import-dialog ref="importDialog"></import-dialog>
  </el-drawer>
</template>

<script>
import { getExamStudentPage, getExamWorkPage, getExamStudentNotJoinedPage, getExamWorkNotJoinedPage,
  addExamUsers, deleteExamUsers, getImportExamStudentTemplate, getImportExamWorkTemplate,
  importExamStudentUrl, importExamWorkUrl} from "@/api/exam/paper";
import { getSimpleMajorList } from '@/api/system/major';
import { getStudentGradeList } from '@/api/system/userStudent';
import { listSimpleRoles } from '@/api/system/role';
import { getCurrentUserDepts } from '@/api/system/permission'
import ImportDialog from '@/views/components/importDialog'

export default {
  name: 'paper-candidate-drawer',
  components: { ImportDialog },
  props: {
    type: {
      type: String,
      required: true,
      default: "imitate",
    }
  },
  data() {
    return {
      paperId: "",
      paperName: "",
      majorList: [],
      gradeList: [],
      roleList: [],
      deptList: [],
      /** 已参考学员抽屉 */
      open: false,
      candidateType: "2",
      studentQueryParams: {
        nickname: "",
        username: "",
        sex: "",
        studentType: "",
        major: "",
        grade: "",
        pageNo: 1,
        pageSize: 10,
      },
      studentLoading: false,
      studentTotal: 0,
      studentList: [],
      studentSelection: [],
      workQueryParams: {
        nickname: "",
        username: "",
        sex: "",
        roleId: "",
        positionalTitles: "",
        deptId: "",
        pageNo: 1,
        pageSize: 10,
      },
      workLoading: false,
      workTotal: 0,
      workList: [],
      workSelection: [],
      /** 导入学员弹窗 */
      addOpen: false,
      addStudentLoading: false,
      addStudentTotal: 0,
      addStudentList: [],
      addStudentSelection: [],
      addWorkLoading: false,
      addWorkTotal: 0,
      addWorkList: [],
      addWorkSelection: [],
    }
  },
  methods: {
    /** 参考人员抽屉 */
    handleOpen(paperId, paperName) {
      this.paperId = paperId;
      this.paperName = paperName;
      this.open = true;
      this.getStudentList();
      this.getWorkList();
    },
    /** 切换学员类型 */
    handleStudentTypeChange(val)   {
      getSimpleMajorList({ studentTypes: val }).then(res => {
        this.majorList = res.data;
        this.studentQueryParams.major = "";
      });
    },
    // 请求列表
    getStudentList() {
      this.studentLoading = true;
      const params = {
        ...this.studentQueryParams,
        paperId: this.paperId,
      };
      getExamStudentPage(params, this.type).then((res) => {
        this.studentList = res.data.list;
        this.studentTotal = res.data.total;
      }).finally(() => this.studentLoading = false);
    },
    handleStudentQuery() {
      this.studentQueryParams.pageNo = 1;
      this.getStudentList();
    },
    getWorkList() {
      this.workLoading = true;
      const params = {
        ...this.workQueryParams,
        paperId: this.paperId,
      };
      getExamWorkPage(params, this.type).then((res) => {
        this.workList = res.data.list;
        this.workTotal = res.data.total;
      }).finally(() => this.workLoading = false);
    },
    handleWorkQuery() {
      this.workQueryParams.pageNo = 1;
      this.getWorkList();
    },
    // 新增考生
    handleAdd() {
      this.addOpen = true;
    },
    getAddStudentList() {
      this.addStudentLoading = true;
      const params = {
        ...this.studentQueryParams,
        paperId: this.paperId,
      };
      getExamStudentNotJoinedPage(params, this.type).then((res) => {
        this.addStudentList = res.data.list;
        this.addStudentTotal = res.data.total;
      }).finally(() => this.addStudentLoading = false);
    },
    handleAddStudentQuery() {
      this.studentQueryParams.pageNo = 1;
      this.getAddStudentList();
    },
    handleAddStudentSelection(value) {
      this.addStudentSelection = value;
    },
    getAddWorkList() {
      this.addWorkLoading = true;
      const params = {
        ...this.workQueryParams,
        paperId: this.paperId,
      };
      getExamWorkNotJoinedPage(params, this.type).then((res) => {
        this.addWorkList = res.data.list;
        this.addWorkTotal = res.data.total;
      }).finally(() => this.addWorkLoading = false);
    },
    handleAddWorkQuery() {
      this.studentQueryParams.pageNo = 1;
      this.getAddWorkList();
    },
    handleAddWorkSelection(value) {
      this.addWorkSelection = value;
    },
    handleAddSure() {
      const data = {
        paperId: this.paperId,
        userType: this.candidateType,
        userIds: this.candidateType === "2" ? this.addStudentSelection.map(item => item.userId) :
          this.addWorkSelection.map(item => item.userId),
      };
      addExamUsers(data, this.type).then(() => {
        this.$message.success("添加考生成功！");
        if (this.candidateType === "2") {
          this.getStudentList();
        } else {
          this.getWorkList();
        }
        this.handleAddCancel();
      });
    },
    handleAddCancel() {
      this.addOpen = false;
    },
    // 文件导入
    handleImport() {
      const url = this.candidateType === '2' ? importExamStudentUrl(this.type) : importExamWorkUrl(this.type);
      const title = this.candidateType === '2' ? '导入学员' : '导入职工';
      const templateApi = this.candidateType === '2' ? () => getImportExamStudentTemplate(this.type) : () => getImportExamWorkTemplate(this.type);
      const templateName = this.candidateType === '2' ? '学员模板.xlsx' : '职工模板.xlsx';
      this.$refs.importDialog.handleImport({ url, title, templateApi, templateName });
    },
    // 删除考生
    handleStudentSelection(value) {
      this.studentSelection = value;
    },
    handleWorkSelection(value) {
      this.workSelection = value;
    },
    handleDeleteStudents() {
      this.$confirm("确定删除选中的考生？", "提示").then(() => {
        const paperUserIds = this.candidateType === "2" ? this.studentSelection.map(item => item.id).join(",") :
          this.workSelection.map(item => item.id).join(",");
        deleteExamUsers(paperUserIds, this.type).then(() => {
          this.$message.success("选中考生删除成功！");
          if (this.candidateType === "2") {
            this.getStudentList();
          } else {
            this.getWorkList();
          }
        });
      })
    },
  },
  created() {
    getStudentGradeList().then(res => this.gradeList = res.data);
    listSimpleRoles().then(res => this.roleList = res.data);
    getCurrentUserDepts({ 'component': `exam/${this.type}Paper/index` }).then(res => this.deptList = res.data);
  },
}
</script>

<style lang="scss" scoped>
.drawer-content-wrapper {
  padding: 0 20px;
}

.filter-form {
  ::v-deep .el-select,
  ::v-deep .el-input {
    width: 200px;
  }
}
</style>
