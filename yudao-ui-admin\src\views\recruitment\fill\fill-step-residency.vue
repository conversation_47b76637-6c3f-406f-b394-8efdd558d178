<template>
  <div class="fill-step-residency" v-loading="loading">
    <el-steps class="fill-step" :active="active" finish-status="success" simple>
      <el-step title="申请信息填写" ></el-step>
      <el-step title="审核结果" ></el-step>
    </el-steps>

    <div class="plan-name">名称：{{ $route.query.planName }}</div>

    <div class="step-first" v-if="active === 1">
      <recruitment-form-residency
        ref="recruitmentForm"
        :plan-id="$route.query.planId"
        :recruitment-registration-id="recruitmentRegistrationId"
      ></recruitment-form-residency>

      <div class="bottom-bar">
        <el-checkbox v-model="realPromise">本人承诺以上信息真实可靠，如有不实之处,愿意承担相应责任。</el-checkbox>

        <div class="bottom-buttons">
          <el-button type="primary" @click="handleStaging">暂存</el-button>
          <el-button type="primary" :disabled="!realPromise" @click="handleNext">提交报名申请</el-button>
          <el-button type="default" @click="handleClose">关闭</el-button>
        </div>
      </div>
    </div>

    <div class="step-last" v-if="active === 2">
      <recruitment-audit-step
        :recruitment-registration-id="recruitmentRegistrationId"
        @modify="active = 1"
      ></recruitment-audit-step>
    </div>
  </div>
</template>

<script>
import { stagingResidencyApplicationInfo, saveResidencyApplicationForm, getResidencyRegistration } from '@/api/recruitment/registration'
import RecruitmentFormResidency from '@/views/recruitment/fill/recruitment-form-residency'
import RecruitmentAuditStep from './residency-audit-step'

export default {
  name: "fill-step-residency",
  components: { RecruitmentFormResidency, RecruitmentAuditStep },
  data() {
    return {
      recruitmentRegistrationId: this.$route.query.recruitmentRegistrationId,
      active: 0,
      realPromise: false, // 是否承诺真实性
      loading: false,
    }
  },
  methods: {
    handleStaging() {
      this.$refs.recruitmentForm.validForm().then(data => {
        stagingResidencyApplicationInfo(data).then((res) => {
          this.$message.success("暂存成功！");
          this.$router.push({
            path: this.$route.path,
            query: { ...this.$route.query, recruitmentRegistrationId: res.data },
          });
        });
      });
    },
    handleNext() {
      this.$refs.recruitmentForm.validForm().then(data => {
        saveResidencyApplicationForm(data).then((res) => {
          this.$message.success("保存成功！");
          this.recruitmentRegistrationId = res.data;
          this.active = 2;
        });
      });
    },
    handleClose() {
      this.$tab.closePage();
    },
    queryActive() {
      this.loading = true;
      if (!this.recruitmentRegistrationId) {
        this.active = 1;
        this.loading = false;
        return;
      }
      getResidencyRegistration(this.recruitmentRegistrationId).then(res => {
        switch (res.data.step) {
          case "finding_of_audit":
            this.active = 2;
            break;
          default:
            this.active = 1;
        }
      }).finally(() => this.loading = false);
    },
  },
  created() {
    this.queryActive();
  },
  activated() {
    const queryId = this.$route.query.recruitmentRegistrationId;
    if (queryId !== this.recruitmentRegistrationId) {
      this.recruitmentRegistrationId = queryId;
      this.queryActive();
    }
  }
}
</script>

<style lang="scss" scoped>
.fill-step {
  margin-bottom: 20px;
}

.plan-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

.bottom-bar {
  padding: 30px 0 20px 0;
  text-align: center;

  .bottom-buttons {
    display: flex;
    padding-top: 10px;
    justify-content: center;
  }
}
</style>
