import request from "@/utils/request";

// 创建考卷管理
export function createPaper(data, type) {
  return request({
    url: `/exam/${type}/paper/create`,
    method: "post",
    data: data,
  });
}

// 更新考卷管理
export function updatePaper(data, type) {
  return request({
    url: `/exam/${type}/paper/update`,
    method: "put",
    data: data,
  });
}

// 删除考卷管理
export function deletePaper(id, type) {
  return request({
    url: `/exam/${type}/paper/delete?id=${id}`,
    method: "delete",
  });
}

// 获得考卷管理
export function getPaper(id, type) {
  return request({
    url: `/exam/${type}/paper/get?id=${id}`,
    method: "get",
  });
}

// 获得考卷管理分页
export function getPaperPage(query, type) {
  return request({
    url: `/exam/${type}/paper/page`,
    method: "get",
    params: query,
    headers: { component: `/exam/${type}Paper/index` },
  });
}

// 导出考卷管理 Word
export function exportPaperWord(id, type) {
  return request({
    url: `/exam/${type}/paper/export-paper-word`,
    method: "get",
    params: { id },
    responseType: "blob",
  });
}

// 开启考卷管理
export function openPaper(id, type) {
  return request({
    url: `/exam/${type}/paper/open?id=${id}`,
    method: "get",
  });
}

// 关闭考卷管理
export function closePaper(id, type) {
  return request({
    url: `/exam/${type}/paper/close?id=${id}`,
    method: "get",
  });
}

// 根据考试对象获得考卷列表
export function getExamPaperList(query) {
  return request({
    url: "/exam/paper/get-by-exam-object-id",
    method: "get",
    params: query,
  });
}

// 关闭考卷列表
export function closePaperList(ids, type) {
  return request({
    url: `/exam/${type}/paper/close-list?ids=${ids}`,
    methods: "get",
  });
}

// 开启考卷列表
export function openPaperList(ids, type) {
  return request({
    url: `/exam/${type}/paper/open-list?ids=${ids}`,
    methods: "get",
  });
}

// 复制试卷
export function copyPaper(id, type) {
  return request({
    url: `/exam/${type}/paper/copy?id=${id}`,
    method: "put",
  });
}

// 轮转（季度quarterly、阶段stage、年度annual、模拟imitate、院内hospital）考核分页
export function getRotationAssessmentPage(query, type) {
  return request({
    url: `/exam/${type}/page`,
    method: "get",
    params: query,
  });
}

// 获得已参加学员情况分页 type: imitate、hospital
export function getExamStudentPage(query, type) {
  return request({
    url: `/exam/${type}/paper/page-student`,
    method: "get",
    params: query,
  });
}

// 获得已参加职工情况分页
export function getExamWorkPage(query, type) {
  return request({
    url: `/exam/${type}/paper/page-work`,
    method: "get",
    params: query,
  });
}

// 获得待参加学员分页
export function getExamStudentNotJoinedPage(query, type) {
  return request({
    url: `/exam/${type}/paper/page-user-students-not-joined`,
    method: "get",
    params: query,
  });
}

// 获得待参加职工分页
export function getExamWorkNotJoinedPage(query, type) {
  return request({
    url: `/exam/${type}/paper/page-user-workers-not-joined`,
    method: "get",
    params: query,
  });
}

// 添加参考人员
export function addExamUsers(data, type) {
  return request({
    url: `/exam/${type}/paper/addUsers`,
    method: "put",
    data: data,
  });
}

// 删除参考用户
export function deleteExamUsers(paperUserIds, type) {
  return request({
    url: `/exam/${type}/paper/deleteUsers`,
    method: "delete",
    params: { paperUserIds },
  });
}

// 获得导入学员模板
export function getImportExamStudentTemplate(type) {
  return request({
    url: `/exam/${type}/paper/get-import-user-student-template`,
    method: "get",
    responseType: "blob",
  });
}

// 获得导入职工模板
export function getImportExamWorkTemplate(type) {
  return request({
    url: `/exam/${type}/paper/get-import-user-work-template`,
    method: "get",
    responseType: "blob",
  });
}

// 导入学员用户
export function importExamStudentUrl(type) {
  return (
    process.env.VUE_APP_BASE_API + `/exam/${type}/paper/import-user-student`
  );
}

// 导入职工用户
export function importExamWorkUrl(type) {
  return process.env.VUE_APP_BASE_API + `/exam/${type}/paper/import-user-work`;
}

// 考试成绩查询分页
export function getExamScorePage(query) {
  return request({
    url: "/exam/result/page",
    method: "get",
    params: query,
  });
}

// 导出考试成绩
export function exportExamScoreExcel(query) {
  return request({
    url: "/exam/result/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 导出考试试卷
export function exportExamPaperZip(query) {
  return request({
    url: "/exam/result/export-exam-result-zip",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获取教学活动试卷
export function getTeachingActivityPaper(query) {
  return request({
    url: "/exam/active/paper/get-teaching-active-paper",
    method: "get",
    params: query,
  });
}

// 获取院级培训试卷
export function getHospitalTrainPaper(query) {
  return request({
    url: "/exam/active/paper/get-teaching-hospital_training-paper",
    method: "get",
    params: query,
  });
}

// 修改主观题得分
export function updateSubjectiveScore(data, type) {
  return request({
    url: `/exam/result/update-subjective-score`,
    method: "post",
    data: data,
  });
}

// 获取主观题得分
export function getSubjectiveTotalScore(query) {
  return request({
    url: "/exam/result/get-subjective-total-score",
    method: "get",
    params: query,
  });
}
