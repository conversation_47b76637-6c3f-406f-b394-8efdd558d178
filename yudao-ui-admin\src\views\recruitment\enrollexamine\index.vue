<template>
  <div class="app-container enrollexamine">

    <div class="top-radio-box">
      <el-radio-group v-model="radioVal" size="medium" class="top-radio-group" @input="handleRadioChange">
        <el-radio-button label="1">待录取</el-radio-button>
        <el-radio-button label="2">已录取</el-radio-button>
        <el-radio-button label="3">不录取</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" filterable placeholder="请选择学员类型">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                    :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item label="报名项目" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入报名项目" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="queryParams.sex" placeholder="请选择性别" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="学历" prop="highestAcademic">
        <el-select v-model="queryParams.highestAcademic" filterable placeholder="请选择学历">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
                    :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="职称" prop="positionalTitles">
        <el-select v-model="queryParams.positionalTitles" filterable placeholder="请选择职称">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)"
                    :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="时长" prop="recruitMonths">
        <el-input v-model="queryParams.recruitMonths" placeholder="请输入时长" clearable/>
      </el-form-item>
      <el-form-item label="工龄">
        <div class="seniority-box">
          <el-input v-model="queryParams.seniorities[0]" clearable/>
        </div>
        <div class="seniority-line">~</div>
        <div class="seniority-box">
          <el-input v-model="queryParams.seniorities[1]" clearable/>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-if="radioVal == 1" type="primary" plain icon="el-icon-finished" size="mini"
                   @click="handleBatchAudit" v-hasPermi="['recruitment:enrollexamine:update']">批量录取</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['recruitment:enrollexamine:update']">导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getData"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        width="55"
        fixed
      >
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="nickname" width="120" fixed>
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="viewUserInfo(scope.row)">{{ scope.row.nickname }}</el-button>
        </template>
      </el-table-column>
      <el-table-column v-if="radioVal != 2" label="操作" align="center" width="200" fixed class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="radioVal == 1" size="mini" type="text" @click="handleAudit(scope.row, 'pass')"
                     v-hasPermi="['recruitment:enrollexamine:update']">录取</el-button>
          <el-button v-if="radioVal == 1" size="mini" type="text" @click="handleAudit(scope.row, 'noPass')"
                     v-hasPermi="['recruitment:enrollexamine:update']">不录取</el-button>
          <el-button v-if="radioVal == 3" size="mini" type="text" @click="handleAudit(scope.row, 'back')"
                     v-hasPermi="['recruitment:enrollexamine:update']">退回修改</el-button>
        </template>
      </el-table-column>

      <el-table-column label="性别" align="center" prop="sex" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="学历" align="center" prop="highestAcademic" width="120">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_EDUCATION" :value="scope.row.highestAcademic" />
        </template>
      </el-table-column>
      <el-table-column label="职称" align="center" prop="positionalTitles" width="120">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES" :value="scope.row.positionalTitles" />
        </template>
      </el-table-column>
      <el-table-column label="工龄" align="center" prop="seniority" width="80" />
      <el-table-column label="工作单位" align="center" prop="selectedUnit" width="200" />
      <el-table-column label="报名项目" align="center" prop="projectName" width="180" />
      <el-table-column label="时长(月)" align="center" prop="recruitMonths" width="80" />
      <el-table-column label="提交时间" align="center" prop="commitTime" width="180" />
      <el-table-column label="学费(元)" align="center" prop="tuition" width="100" />
      <el-table-column label="备注" align="center" prop="remarks" />

    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getData"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog title="批量审核" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="录取结果" prop="batchExamineStatus">
          <el-radio-group v-model="form.batchExamineStatus" @change="handleBatchStatusChange">
            <el-radio label="approve">录取</el-radio>
            <el-radio label="reject">不录取</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="reason">
          <el-input v-model="form.reason" type="textarea" placeholder="请输入您的审核意见"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">再考虑一下，关闭</el-button>
        <el-button type="primary" :loading="submitting" @click="submitForm">确定批量审核，提交</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { DICT_TYPE } from '@/utils/dict';
import { exportSuperviseFormExcel } from "@/api/rotation/superviseForm";
import { getRegistration } from "@/api/recruitment/noticesend";
import {getRegistrationexaminePage, getRegistrationexaminePageDone, approve, reject, backRegistration, batchExamine} from "@/api/recruitment/enrollexamine";

export default {
  name: "Enrollexamine",
  data() {
    return {
      radioVal: '1',
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督导表单列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        studentType: '',
        projectName: '',
        sex: '',
        highestAcademic: '',
        positionalTitles: '',
        recruitMonths: '',
        seniorities: ['', '']
      },
      multipleSelection: [],

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        batchExamineStatus: [{ required: true, message: "请选择录取结果", trigger: "change" }],
        reason: [{ required: true, message: "请输入审批意见", trigger: "blur" }],
      },
      submitting: false,
    };
  },
  created() {
    this.getWaitingList();

  },
  methods: {
    getData(){
      if (this.radioVal == 1) {
        this.getWaitingList()
      } else {
        this.getDoneList()
      }
    },
    /** 查询列表 */
    getWaitingList() {
      this.loading = true;
      this.list = []
      // 执行查询
      getRegistrationexaminePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    getDoneList() {
      this.loading = true;
      this.list = []
      const params = {
        ...this.queryParams
      }
      params.result = this.radioVal
      // 执行查询
      getRegistrationexaminePageDone(params).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    handleRadioChange(val){
      this.resetForm("queryForm");
      this.radioVal = val
      this.getData()
    },

    handleBatchAudit(){
      if (this.multipleSelection.length === 0) {
        this.$modal.msgWarning("请选择至少一项");
        return;
      }
      this.reset();
      this.open = true;
    },

    viewUserInfo(row){
      getRegistration({id: row.recruitmentRegistrationId}).then(res => {
        const data = res.data
        const { planId } = data
        this.$router.push({
          path: 'personalInfo',
          query: { planId, recruitmentRegistrationId: row.recruitmentRegistrationId },
        });
      })
    },

    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        taskIds: this.multipleSelection.map(item => item.taskId),
        batchExamineStatus: '',
        reason: '',
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    handleAudit(row, type){
      if (this.radioVal == 1) {
        this.$prompt('请输入审核意见', '录取确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: type === "pass" ? "确认录取" : type === "noPass" ? "不予录取" : "",
          inputValidator: (value) => {
            console.log('inputValidator', value)
            if (!value) {
              return false
            } else if (value.trim() === '') {
              return false
            } else {
              return true
            }
          },
          inputErrorMessage: '请输入审核意见',
          beforeClose: (action, instance, done) => {
            const value = instance.inputValue
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;

              const api = type === 'noPass' ? reject : approve
              const params = {
                reason: value,
                taskId: row.taskId
              }
              api(params).then(res => {
                instance.confirmButtonLoading = false;
                this.$modal.msgSuccess("操作成功");
                done();
                this.handleQuery();
              }).catch(err => {
                instance.confirmButtonLoading = false;
              })

            } else {
              done();
            }
          }
        })

      } else {
        const typeName = '退回修改'
        this.$confirm(`确认${typeName}${row.nickname}的录取申请么?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          beforeClose: (action, instance, done) => {
            const value = instance.inputValue
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;

              const params = {
                recruitmentRegistrationId: row.recruitmentRegistrationId
              }
              backRegistration(params).then(res => {
                instance.confirmButtonLoading = false;
                this.$modal.msgSuccess("操作成功");
                done();
                this.handleQuery();
              }).catch(err => {
                instance.confirmButtonLoading = false;
              })

            } else {
              done();
            }
          }
        })
      }
    },

    handleBatchStatusChange(val) {
      if (val === "approve") {
        this.form.reason = "确认录取"
      }
      if (val === "reject") {
        this.form.reason = "不予录取"
      }
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        this.submitting = true;
        batchExamine(this.form).then(() => {
          this.$modal.msgSuccess("操作成功");
          this.cancel();
          this.handleQuery();
        }).finally(() => this.submitting = false)
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有督导表单数据项?').then(() => {
          this.exportLoading = true;
          return exportSuperviseFormExcel(params);
        }).then(response => {
          this.$download.excel(response, '督导表单.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>

<style lang="scss">
.enrollexamine {
  position: relative;

  .top-radio-box{
    position: relative;
    margin-bottom: 15px;
    &::before{
      position: absolute;
      content: ' ';
      display: block;
      width: 100%;
      height: 1px;
      background: #ddd;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .el-radio-button:first-child .el-radio-button__inner{
      border-radius: 4px 0 0 0;
    }
    .el-radio-button:last-child .el-radio-button__inner{
      border-radius: 0 4px 0 0;
    }
  }

  .seniority-box{
    display: inline-block;
    width: 80px;
  }
  .seniority-line{
    display: inline-block;
    width: 25px;
    text-align: center;
  }
}

.superviseForm-indicator-dialog{

.el-dialog__body{
  padding-top: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.indicators-wapper{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .indicators-wapper-head{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;
    border-bottom: 1px #ddd solid;
  }

  .indicators-wapper-tables{
    max-height: calc(100vh - 240px);
    flex: auto;
    padding-top: 13px;
    overflow-y: auto;
  }
}
}
</style>
