<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="70px">
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" filterable clearable size="small" @change="handleQueryStudentTypeChange">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select v-model="queryParams.major" placeholder="请选择培训专业" filterable clearable size="small">
          <!-- <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_MAJOR)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/> -->
          <el-option v-for="item in queryMajorList" :key="item.code" :label="item.name" :value="item.code"/>
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select v-model="queryParams.grade" placeholder="请选择年级" filterable clearable size="small">
          <el-option v-for="grade in studentGradeList" :key="grade" :label="grade" :value="grade"/>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="isAppraised">
        <el-checkbox v-model="queryParams.isAppraised">待评价</el-checkbox>
      </el-form-item>
      <el-form-item label="学员姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入学员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" >
      <el-table-column label="学员姓名" prop="nickname" align="center"></el-table-column>
      <el-table-column label="学员类型" prop="studentType" align="center">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType"></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="培训专业" prop="majorName" align="center"></el-table-column>
      <el-table-column label="年级" prop="grade" align="center"></el-table-column>
      <el-table-column label="带教开始时间" prop="rotationBeginTime" align="center"></el-table-column>
      <el-table-column label="带教结束时间" prop="rotationEndTime" align="center"></el-table-column>
      <el-table-column label="轮转科室" prop="rotationDepartmentName" align="center"></el-table-column>
      <el-table-column label="评价得分" align="center" prop="score" width="140">
        <template slot-scope="scope">
          <el-rate
            v-model="scope.row.score"
            disabled
            show-score
            text-color="#ff9900"
            :max="5"
            score-template="{value}">
          </el-rate>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.appraise360ResultId" size="mini" type="text" icon="el-icon-document" @click="handleView(scope.row)">
            查看评价
          </el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">
            进入评价
          </el-button>
        </template>
      </el-table-column>


    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <AppraiseDialog
      v-if="formData"
      :title="title"
      :open="open"
      :data="formData"
      :appraiseSourceId="curRow && curRow.appraiseSourceId"
      :appraiseTargetId="curRow && curRow.appraiseTargetId"
      :disabled="appraiseDisabled"
      @setOpen="setOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import AppraiseDialog from "@/views/components/appraiseDialog";
import { getStudentGradeList } from '@/api/system/userStudent';
import { getSimpleMajorList } from '@/api/system/major';
import { getDeptappraisestudentPage, getAppraiseForm } from "@/api/rotation/deptappraisestudent";
import { getAppraiseResult, createAppraise } from "@/api/rotation/studentappraiseteacher";

export default {
  name: "Deptappraisestudent",
  components: {
    AppraiseDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      list: [],
      queryMajorList: [],
      // 年级列表
      studentGradeList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        isAppraised: true,
        grade: "",
        major: "",
        nickname: "",
        studentType: "",
      },
      formData: null,
      open: false,
      title: '',
      curRow: null,
      appraiseDisabled: false
    };
  },
  created() {
    this.getList();
    getStudentGradeList().then(res => {
      this.studentGradeList = res.data;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getDeptappraisestudentPage(this.queryParams).then(response => {
        const list = response.data.list;
        list.forEach(item => {
          const _score = item.score / item.appraise360Score * 5
          item.score = _score
        })
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryMajorList = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查询学员类型改变 */
    handleQueryStudentTypeChange(value) {
      this.queryParams.major = null;
      this.queryMajorList = [];
      getSimpleMajorList({ studentType: value }).then(res => {
        this.queryMajorList = res.data;
      });
    },
    handleView(row) {
      const id = row.appraise360ResultId;
      this.appraiseDisabled = true
      getAppraiseResult(id).then(response => {
        this.formData = response.data;
        this.open = true;
        this.title = `查看评价-${row.rotationDepartmentName}`;
      });
    },
    handleEdit(row) {
      const id = row.scheduleDetailsId;
      this.curRow = row;
      this.appraiseDisabled = false
      getAppraiseForm(id).then(response => {
        this.formData = response.data;
        this.open = true;
        this.title = `正在评价-${row.rotationDepartmentName}`;
      });
    },
    setOpen(flag) {
      this.open = flag;
      this.editForm = null
      this.curRow = null
    }
  }
};
</script>
