<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="学员姓名" prop="studentNickname">
        <el-input v-model="queryParams.studentNickname" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="登记日期" prop="createTimes">
        <el-date-picker
          v-model="queryParams.createTimes"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="examineStatus">
        <el-select
          v-model="queryParams.examineStatus"
          filterable
          clearable
          placeholder="请选择审核状态"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.INTERN_CASE_EXAMINE_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <!-- <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row> -->

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      class="internCaseRecordExamine-table"
    >
      <el-table-column
        label="登记日期"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学员姓名" align="center" prop="studentNickname" />
      <el-table-column label="用户名" align="center" prop="studentUsername" />
      <el-table-column label="专业" align="center" prop="studentMajorName" />
      <el-table-column label="病例附件" align="center" prop="files">
        <template slot-scope="scope">
          <imageUpload :value="scope.row.files" disabled />
        </template>
      </el-table-column>
      <el-table-column
        label="审核人"
        align="center"
        prop="examineUserNickname"
      />
      <el-table-column label="审核状态" align="center" prop="examineStatus">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.INTERN_CASE_EXAMINE_STATUS"
            :value="scope.row.examineStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="100"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.examineStatus == 0"
            size="mini"
            type="text"
            @click="handleApprove(scope.row)"
            v-hasPermi="['rotation:intern-case-record-examine:examine']"
          >
            通过
          </el-button>
          <el-button
            v-if="scope.row.examineStatus == 0"
            size="mini"
            type="text"
            @click="handleReject(scope.row)"
            v-hasPermi="['rotation:intern-case-record-examine:examine']"
          >
            不通过
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  examineApprove,
  examineReject,
  getInternCaseRecordExaminePage,
} from "@/api/rotation/internCaseRecordExamine";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "InternCaseRecordExamine",
  components: { ImageUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 见习病例书写列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        studentNickname: "",
        examineStatus: "",
        createTimes: [],
      },
      mentorOptions: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getInternCaseRecordExaminePage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 通过操作 */
    handleApprove(row) {
      const id = row.id;
      this.$modal
        .confirm("是否确认审核通过该条记录?")
        .then(function () {
          return examineApprove({ id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("操作成功");
        })
        .catch(() => {});
    },

    /** 不通过操作 */
    handleReject(row) {
      const id = row.id;
      this.$modal
        .confirm("是否确认审核不通过该条记录?")
        .then(function () {
          return examineReject({ id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("操作成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.internCaseRecordExamine-table {
  ::v-deep .el-upload-list__item {
    width: 50px !important;
    height: 50px !important;
    margin-right: 10px;
  }
}
</style>
