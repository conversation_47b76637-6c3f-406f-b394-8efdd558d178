<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" clearable size="small" @change="handleQueryStudentTypeChange">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select v-model="queryParams.major" placeholder="请选择培训专业" clearable size="small">
          <!-- <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_MAJOR)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/> -->
          <el-option v-for="item in queryMajorList" :key="item.code" :label="item.name" :value="item.code"/>
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select v-model="queryParams.grade" placeholder="请选择年级" clearable size="small">
          <el-option v-for="grade in studentGradeList" :key="grade" :label="grade" :value="grade"/>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="isAudit">
        <el-checkbox v-model="queryParams.isAudit">待审核</el-checkbox>
      </el-form-item>
      <el-form-item label="学员姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入学员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" >
      <el-table-column label="姓名" prop="nickname" align="center"></el-table-column>
      <el-table-column label="学员类型" prop="studentType" align="center">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType"></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="培训专业" prop="majorName" align="center"></el-table-column>
      <el-table-column label="轮转科室" prop="rotationDepartmentName" align="center"></el-table-column>
      <el-table-column label="年级" prop="grade" align="center"></el-table-column>
      <el-table-column label="轮转时间" prop="rotationBeginEndTime" align="center" width="190px"></el-table-column>
      <el-table-column label="轮转状态" prop="rotationStatus" align="center">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.ROTATION_STATUS" :value="scope.row.rotationStatus"></dict-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据完成情况" prop="dataComplete" align="center">
        <template v-slot="scope">
          <el-link type="primary" @click="handleDataComplete(scope.row)">{{ scope.row.dataComplete }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="待审核数据" prop="auditCnt" align="center">
        <template v-slot="scope">
          <el-link type="primary" @click="handleDataAudit(scope.row)">{{ scope.row.auditCnt }}</el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <daily-data-complete-dialog
      :visible.sync="completeVisible"
      :schedule-details-id="scheduleDetailsId"
      @show-detail="handleShowDetail"
      @close="completeVisible = false"
    ></daily-data-complete-dialog>

    <daily-data-need-audit-dialog
      :visible.sync="auditVisible"
      :schedule-details-id="scheduleDetailsId"
      @show-detail="handleShowDetail"
      @close="handleNeedAuditClose"
    ></daily-data-need-audit-dialog>

    <daily-data-detail-dialog ref="detail"></daily-data-detail-dialog>
  </div>
</template>

<script>
import { getDailyDataPage } from "@/api/rotation/dailyData";
import { getStudentGradeList } from '@/api/system/userStudent';
import { getSimpleMajorList } from "@/api/system/major";
import DailyDataCompleteDialog from "./daily-data-complete-dialog";
import DailyDataNeedAuditDialog from "./daily-data-need-audit-dialog";
import DailyDataDetailDialog from "./daily-data-detail-dialog";

export default {
  name: "DailyData",
  components: {
    DailyDataCompleteDialog,
    DailyDataNeedAuditDialog,
    DailyDataDetailDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日常数据审核列表
      list: [],
      // 年级列表
      studentGradeList: [],
      queryMajorList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        isAudit: true,
        grade: "",
        major: "",
        nickname: "",
        studentType: "",
      },
      // 数据完成情况\审核
      scheduleDetailsId: "",
      completeVisible: false,
      auditVisible: false,
    };
  },
  created() {
    this.getList();
    getStudentGradeList().then(res => {
      this.studentGradeList = res.data;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getDailyDataPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryMajorList = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查询学员类型改变 */
    handleQueryStudentTypeChange(value) {
      this.queryParams.major = null;
      this.queryMajorList = [];
      getSimpleMajorList({ studentType: value }).then(res => {
        this.queryMajorList = res.data;
      });
    },
    /** 数据完成情况 */
    handleDataComplete(row) {
      this.scheduleDetailsId = row.scheduleDetailsId;
      this.completeVisible = true;
    },
    handleDataAudit(row) {
      this.scheduleDetailsId = row.scheduleDetailsId;
      this.auditVisible = true;
    },
    handleNeedAuditClose() {
      this.auditVisible = false;
      this.getList();
    },
    handleShowDetail(row) {
      this.$refs.detail.viewDetail(row);
    },
  }
};
</script>

<style lang="scss" scoped>
.mr {
  margin-right: 20px;
}
</style>
