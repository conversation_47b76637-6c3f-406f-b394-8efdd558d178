import request from '@/utils/request'

// 创建出科审核
export function createGraduationApply(data) {
  return request({
    url: '/rotation/graduation-apply/create',
    method: 'post',
    data: data
  })
}

// 暂存出科审核
export function updateGraduationApply(data) {
  return request({
    url: '/rotation/graduation-apply/update',
    method: 'put',
    data: data
  })
}

// 删除出科审核
export function deleteGraduationApply(id) {
  return request({
    url: '/rotation/graduation-apply/delete?id=' + id,
    method: 'delete'
  })
}

// 获得出科审核
export function getGraduationApply(id) {
  return request({
    url: '/rotation/graduation-apply/get?id=' + id,
    method: 'get'
  })
}

// 获得出科审核分页
export function getGraduationApplyPage(query) {
  return request({
    url: '/rotation/graduation-apply/page',
    method: 'get',
    params: query
  })
}

// 导出出科审核 Excel
export function exportGraduationApplyExcel(query) {
  return request({
    url: '/rotation/graduation-apply/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得出科审核流程
export function getGraduationApplySchedule(id) {
  return request({
    url: '/rotation/graduation-apply/get-audit',
    method: 'get',
    params: { id }
  })
}

// 验证是否能出科审核
export function graduationApplyValidate(scheduleDetailsId) {
  return request({
    url: '/rotation/graduation-apply/validate',
    method: 'get',
    params: { scheduleDetailsId }
  })
}
