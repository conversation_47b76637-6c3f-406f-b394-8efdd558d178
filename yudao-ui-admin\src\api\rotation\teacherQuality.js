import request from '@/utils/request'

// 获得统计分页
export function getTeacherQualityPage(query) {
  return request({
    url: '/rotation/teacher-quality/page',
    method: 'get',
    params: query,
    // headers: {'component': 'rotation/teachingActiveDevelopment/index'}
  })
}

// 导出 Excel
export function exportTeacherQuality(query) {
  return request({
    url: '/rotation/teacher-quality/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

