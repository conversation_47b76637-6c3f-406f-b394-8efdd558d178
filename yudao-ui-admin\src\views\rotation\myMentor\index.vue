<template>
  <div class="app-container mentorUpdate-page">
    <div class="mentorLibrary-detail-box">
          <div class="base-info">
            <img v-if="form.mentorPhoto" class="photo" :src="form.mentorPhoto" />
            <div v-else class="no-photo">
              <i class="el-icon-picture-outline" style="font-size: 28px"></i>
              <span>暂无图片</span>
            </div>
            <div class="info-box">
              <div class="time"> 最后更新时间：{{form.updateTime}}</div>
              <el-descriptions class="margin-top" :column="2" border>
                <el-descriptions-item>
                  <template slot="label">
                    姓名
                  </template>
                  {{ form.nickname }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    性别
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_SEX, form.sex) }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    名族
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_NATION, form.nation) }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    导师层次
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.MENTOR_LEVEL, form.mentorLevel) }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    职称
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES, form.positionalTitles) }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    导师类型
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.MENTOR_TYPE, form.mentorType) }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    最后学历
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_EDUCATION, form.education) }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    最后学位
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_DEGREE, form.degree) }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    行政职务
                  </template>
                  {{ getDictDataLabel(DICT_TYPE.JOB_TITLE, form.jobTitle) }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    个人邮箱
                  </template>
                  {{ form.email }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    工作单位
                  </template>
                  {{ form.organizationName }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label">
                    邮政编码
                  </template>
                  {{ form.postalCode }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>

          <div class="desc-box">
            <div class="title">个人简介</div>
            <div class="cont">
              <p>{{ form.personalProfile }}</p>
            </div>
          </div>

          <div class="desc-box">
            <div class="title">研究领域</div>
            <div class="cont">
              <p>{{ form.researchArea }}</p>
            </div>
          </div>

          <div class="desc-box">
            <div class="title">个人成果</div>
            <div class="cont">
              <p>{{ form.personalAchievements }}</p>
            </div>
          </div>
      </div>
      
  </div>
</template>

<script>
import { getAccessToken } from "@/utils/auth";
import { getMentorByStudent } from "@/api/rotation/mentorLibrary";


export default {
  name: "MyMentor",
  data() {
    return {
      form: {},
    };
  },
  created() {
    getMentorByStudent().then((response) => {
      const {data = {}} = response

      if (data.mentorPhoto) {
        if (data.mentorPhoto.indexOf('?') > -1) {
          data.mentorPhoto = data.mentorPhoto.split('?')[0]
        }
        data.mentorPhoto = data.mentorPhoto + '?token=' + getAccessToken()
      }

      this.form = data;
    });
  },
  methods: {
  }
};
</script>

<style lang="scss">
.mentorUpdate-page{
  .mentorLibrary-detail-box{
    width: 1000px;
    margin: 0 auto;

    .el-form-item{
      margin-bottom: 0;
    }

    .base-info{
      width: 100%;
      display: flex;

      .photo{
        width: 200px;
        height: 280px;
      }

      .no-photo{
        width: 200px;
        height: 280px;
        background: #f8f8f8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 14px;

        span{
          margin-top: 10px;
        }
      }

      .info-box{
        width: calc(100% - 220px);
        padding-left: 20px;

        .time{
          text-align: right;
          margin-bottom: 10px;
        }
      }
    }

    .desc-box{
      margin: 20px 0 30px 0;

      .title{
        position: relative;
        padding-left: 16px;
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;

        &::before{
          content: ' ';
          display: inline-block;
          width: 4px;
          height: 14px;
          background: #1890ff;
          position: absolute;
          left: 0;
          top: 3px;
        }
      }

      .cont{
        border: 1px #eee solid;
        padding: 10px;

        p{
          margin: 0;
          line-height: 25px;
        }
      }
    }

    .footer{
      text-align: right;
    }
  }
}

</style>
