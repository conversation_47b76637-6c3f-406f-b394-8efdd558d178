<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="培训名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入培训名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训人" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入姓名查询"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训类型" prop="trainingType">
        <el-select
          v-model="queryParams.trainingType"
          placeholder="请选择培训类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_SEMINAR_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训科室" prop="departmentId">
        <el-select
          v-model="queryParams.departmentId"
          filterable
          clearable
          placeholder="请选择培训科室"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="parseInt(item.id)"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:room-activity-record:create']"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:dept-training:export']"
          >导出</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="培训名称" align="center" prop="name" />
      <el-table-column label="培训类型" align="center" prop="trainingType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHING_SEMINAR_TYPE"
            :value="scope.row.trainingType"
          />
        </template>
      </el-table-column>
      <el-table-column label="培训科室" align="center" prop="departmentName" />
      <el-table-column label="培训人" align="center" prop="nickname" />
      <el-table-column label="培训地点" align="center" prop="trainingAddress" />
      <el-table-column
        label="开展时间"
        align="center"
        prop="startTime"
        width="300"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }} ~ {{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="附件" align="center" prop="filesCount">
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row, 'view')"
            v-hasPermi="['rotation:room-activity-record:query']"
          >
            查看
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row, 'edit')"
            v-hasPermi="['rotation:room-activity-record:update']"
          >
            修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:room-activity-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="750px"
      v-dialogDrag
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        :disabled="opt === 'view'"
      >
        <el-form-item label="培训名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入培训名称" />
        </el-form-item>
        <el-form-item label="培训类型" prop="trainingType">
          <el-select
            v-model="form.trainingType"
            placeholder="请选择培训类型"
            style="width: 100%"
          >
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.TEACHING_SEMINAR_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="培训科室" prop="departmentId">
          <el-select
            v-model="form.departmentId"
            filterable
            clearable
            placeholder="请选择培训科室"
            style="width: 100%"
          >
            <el-option
              v-for="item in departmentOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="parseInt(item.id)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="培训人" prop="trainingUserId">
          <el-select
            v-model="form.trainingUserId"
            filterable
            placeholder="请选择培训人"
            style="width: 100%"
          >
            <el-option
              v-for="user in userWorkerOptions"
              :key="user.id"
              :label="user.nickname"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="培训地点" prop="trainingAddress">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2 }"
            v-model="form.trainingAddress"
            placeholder="请输入培训地点"
          />
        </el-form-item>
        <el-form-item label="开展时间" prop="timeArr">
          <el-date-picker
            v-model="form.timeArr"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="附件" prop="files">
          <FileUpload
            v-model="form.files"
            :limit="999"
            :fileSize="50"
            :disabled="opt === 'view'"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="opt !== 'view'">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import {
  createRoomActivityRecord,
  updateRoomActivityRecord,
  deleteRoomActivityRecord,
  getRoomActivityRecordPage,
  exportRoomActivityRecordExcel,
} from "@/api/rotation/roomActivityRecord";
import { getDepartmentSimpleList } from "@/api/system/department";
import { getUserWorkerSimpleList } from "@/api/system/userWorker";

export default {
  name: "RoomActivityRecord",
  components: { FileUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 教研室活动记录列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        trainingType: null,
        departmentId: null,
        nickname: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "培训名称不能为空", trigger: "blur" },
        ],
        trainingType: [
          { required: true, message: "培训类型不能为空", trigger: "change" },
        ],
        departmentId: [
          { required: true, message: "培训科室不能为空", trigger: "change" },
        ],
        trainingUserId: [
          { required: true, message: "培训人不能为空", trigger: "change" },
        ],
        trainingAddress: [
          { required: true, message: "培训地点不能为空", trigger: "blur" },
        ],
        timeArr: [
          { required: true, message: "开展时间不能为空", trigger: "change" },
        ],
        files: [{ required: true, message: "附件不能为空", trigger: "change" }],
      },
      departmentOptions: [],
      userWorkerOptions: [],
      opt: "",
    };
  },
  created() {
    this.getDepartment();
    this.getUserworkData();
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getRoomActivityRecordPage(this.queryParams).then((response) => {
        const list = response.data.list;
        // 处理 list 数据
        list.forEach((item) => {
          item.filesCount = item.files ? JSON.parse(item.files).length : 0;
        });
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    getUserworkData() {
      getUserWorkerSimpleList().then((res) => {
        this.userWorkerOptions = [];
        this.userWorkerOptions.push(...res.data);
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        trainingType: undefined,
        departmentId: undefined,
        trainingUserId: undefined,
        trainingAddress: undefined,
        timeArr: undefined,
        // startTime: undefined,
        // endTime: undefined,
        files: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加继续教育活动";
    },
    /** 修改按钮操作 */
    handleUpdate(row, type) {
      const item = JSON.parse(JSON.stringify(row));
      this.reset();
      this.opt = type;
      this.form = item;
      this.form.timeArr = [this.form.startTime, this.form.endTime];
      this.form.files = item.files ? JSON.parse(item.files) : [];
      this.open = true;
      this.title = type === "edit" ? "修改继续教育活动" : "查看继续教育活动";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = {
          ...this.form,
          startTime: this.form.timeArr[0],
          endTime: this.form.timeArr[1],
          files: this.form.files ? JSON.stringify(this.form.files) : "",
        };

        delete params.timeArr;

        // 修改的提交
        if (this.form.id != null) {
          updateRoomActivityRecord(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createRoomActivityRecord(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const { id, name } = row;
      this.$modal
        .confirm('是否确认删除继续教育活动名称为"' + name + '"的数据项?')
        .then(function () {
          return deleteRoomActivityRecord(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有继续教育活动数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportRoomActivityRecordExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "继续教育活动登记.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>
