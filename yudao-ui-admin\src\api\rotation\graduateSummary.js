import request from "@/utils/request";

// 创建见习出科小结
export function createGraduateSummary(data) {
  return request({
    url: "/rotation/graduate-summary/create",
    method: "post",
    data: data,
  });
}

// 更新见习出科小结
export function updateGraduateSummary(data) {
  return request({
    url: "/rotation/graduate-summary/update",
    method: "put",
    data: data,
  });
}

// 删除见习出科小结
export function deleteGraduateSummary(id) {
  return request({
    url: "/rotation/graduate-summary/delete?id=" + id,
    method: "delete",
  });
}

// 获得见习出科小结
export function getGraduateSummary(id) {
  return request({
    url: "/rotation/graduate-summary/get?id=" + id,
    method: "get",
  });
}

// 获得见习出科小结分页
export function getGraduateSummaryPage(query) {
  return request({
    url: "/rotation/graduate-summary/page",
    method: "get",
    params: query,
    headers: { component: "rotation/graduateSummary/index" },
  });
}

// 导出见习出科小结 Excel
export function exportGraduateSummaryExcel(query) {
  return request({
    url: "/rotation/graduate-summary/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
