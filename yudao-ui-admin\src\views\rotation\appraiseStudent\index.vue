<template>
  <div class="app-container appraise-student-mentor">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="70px">
      <el-form-item label="学员姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入学员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select v-model="queryParams.major" placeholder="请选择培训专业" filterable clearable size="small">
          <el-option v-for="item in queryMajorList" :key="item.code" :label="item.name" :value="item.code"/>
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select v-model="queryParams.grade" placeholder="请选择年级" filterable clearable size="small">
          <el-option v-for="grade in studentGradeList" :key="grade" :label="grade" :value="grade"/>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="isToApprais">
        <el-checkbox v-model="queryParams.isToApprais">待评价</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" >
      <el-table-column label="学员姓名" prop="studentUserName" align="center"></el-table-column>
      <el-table-column label="培训专业" prop="majorName" align="center"></el-table-column>
      <el-table-column label="年级" prop="grade" align="center"></el-table-column>
      <el-table-column label="评价年月" prop="appraiseBeginDate" align="center"></el-table-column>
      <el-table-column label="评价得分" align="center" prop="score" width="140">
        <template slot-scope="scope">
          <el-rate
            v-model="scope.row.score"
            disabled
            show-score
            text-color="#ff9900"
            :max="5"
            score-template="{value}">
          </el-rate>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.appraised" size="mini" type="text" icon="el-icon-document" @click="handleView(scope.row)">
            查看评价
          </el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">
            进入评价
          </el-button>
        </template>
      </el-table-column>


    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <AppraiseDialog 
      v-if="formData"
      :title="title"
      :open="open"
      :data="formData"
      :appraiseSourceId="curRow && curRow.id"
      :disabled="appraiseDisabled"
      @setOpen="setOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import AppraiseDialog from "../appraiseMentor/appraiseDialog";
import { getMentorStudentPage, getAppraiseForm } from "@/api/rotation/appraiseMentor";
import { getStudentGradeList } from '@/api/system/userStudent';
import { getSimpleMajorList } from '@/api/system/major';

export default {
  name: "AppraiseStudent",
  components: {
    AppraiseDialog
  },
  data() {
    return {
      loading: true,
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        isToApprais: true,
        grade: "",
        major: "",
        nickname: "",
      },
      formData: null,
      open: false,
      title: '',
      curRow: null,
      appraiseDisabled: false,
      // 年级列表
      studentGradeList: [],
      queryMajorList: [],
    };
  },
  created() {
    this.getList();
    getStudentGradeList().then(res => {
      this.studentGradeList = res.data;
    });
    getSimpleMajorList().then(res => {
      this.queryMajorList = res.data;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getMentorStudentPage(this.queryParams).then(response => {
        const list = response.data.list;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleView(row) {
      const id = row.id;
      this.appraiseDisabled = true
      getAppraiseForm(id).then(response => {
        this.formData = response.data;
        this.open = true;
        this.title = `查看评价-${row.studentUserName}`;
      });
    },
    handleEdit(row) {
      const id = row.id;
      this.curRow = row;
      this.appraiseDisabled = false
      getAppraiseForm(id).then(response => {
        this.formData = response.data
        this.open = true;
        this.title = `正在评价-${row.studentUserName}`;
      });
    },
    setOpen(flag) {
      this.open = flag;
      this.editForm = null
      this.curRow = null
    }
    
  }
};
</script>

<style lang="scss" scoped>
.appraise-student-mentor{
  font-size: 14px;

  .list-item{
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    margin-bottom: 25px;
    
    .list-item-head{
      height: 42px;
      display: flex;
      align-items: center;
      background: #f8f8f8;
      border-bottom: 1px #ddd solid;
      padding: 0 15px;
      

      .head-item{
        margin-right: 25px;

        .fontBlur{
          color: #1890ff;
        }
        .fontRed{
          color: #f56c6c;
        }
      }
    }

    .list-item-cont{
      height: 52px;
      padding: 0 45px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .cont-item{
        
      }
    }
  }
}
</style>
