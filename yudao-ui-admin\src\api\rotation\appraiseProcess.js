import request from '@/utils/request'

// 创建过程评价指标管理
export function createAppraiseProcess(data) {
  return request({
    url: '/rotation/appraise-process/create',
    method: 'post',
    data: data
  })
}

// 更新过程评价指标管理
export function updateAppraiseProcess(data) {
  return request({
    url: '/rotation/appraise-process/update',
    method: 'put',
    data: data
  })
}

// 删除过程评价指标管理
export function deleteAppraiseProcess(id) {
  return request({
    url: '/rotation/appraise-process/delete?id=' + id,
    method: 'delete'
  })
}

// 获得过程评价指标管理
export function getAppraiseProcess(id) {
  return request({
    url: '/rotation/appraise-process/get?id=' + id,
    method: 'get'
  })
}

// 获得过程评价指标管理分页
export function getAppraiseProcessPage(query) {
  return request({
    url: '/rotation/appraise-process/page',
    method: 'get',
    params: query
  })
}

// 导出过程评价指标管理 Excel
export function exportAppraiseProcessExcel(query) {
  return request({
    url: '/rotation/appraise-process/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得过程评价配置
export function getProcessConfig(query) {
  return request({
    url: '/rotation/appraise-process/get-config',
    method: 'get',
    params: query
  })
}

// 更新过程评价配置
export function saveProcessConfig(data) {
  return request({
    url: '/rotation/appraise-process/save-config',
    method: 'put',
    data: data
  })
}