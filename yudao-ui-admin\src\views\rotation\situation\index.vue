<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          filterable
          clearable
          @change="handleStudentTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          filterable
          clearable
        >
          <el-option
            v-for="item in majorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          filterable
          clearable
        >
          <el-option
            v-for="grade in studentGradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          clearable
          @keyup.enter.native="getStudentList"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          clearable
          @keyup.enter.native="getStudentList"
        />
      </el-form-item>
      <el-form-item label="轮转状态" prop="rotationStatus">
        <el-select
          v-model="queryParams.rotationStatus"
          placeholder="请选择轮转状态"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="轮转科室" prop="rotationDepartmentId">
        <el-select
          v-model="queryParams.rotationDepartmentId"
          placeholder="请选择轮转科室"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dept in rotationDeptsList"
            :key="dept.id"
            :label="dept.name"
            :value="dept.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="出科申请状态" prop="graduationAuditStatus">
        <el-select
          v-model="queryParams.graduationAuditStatus"
          placeholder="请选择出科申请状态"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.GRADUATION_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="出科时间" prop="departureDates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.departureDates"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="轮转时间" prop="rotationDates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.rotationDates"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-upload2"
          size="mini"
          @click="() => handleImport('llcj')"
          v-hasPermi="['rotation:situation:export']"
        >
          理论成绩导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-upload2"
          size="mini"
          @click="() => handleImport('jnkh')"
          v-hasPermi="['rotation:situation:export']"
        >
          技能考核成绩导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:situation:export']"
        >
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-check"
          size="mini"
          @click="handleProfessionalBatchConfirm"
          v-hasPermi="['rotation:situation:professionalconfirm']"
        >
          专业基地管理员批量确认
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="45"
        fixed
        :selectable="(row) => row.graduationApplyId"
        v-hasPermi="['rotation:situation:professionalconfirm']"
      ></el-table-column>
      <el-table-column
        label="学员类型"
        align="center"
        prop="studentType"
        fixed
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="学员姓名"
        align="center"
        prop="nickname"
        fixed
        width="80"
      />
      <el-table-column
        label="用户名"
        align="center"
        prop="username"
        fixed
        width="80"
      />
      <el-table-column
        label="培训专业"
        align="center"
        prop="majorName"
        width="100"
      />
      <el-table-column label="年级" align="center" prop="grade" width="80" />
      <el-table-column
        label="处方权"
        align="center"
        prop="obtainedPrescription"
        width="80"
      >
        <template v-slot="scope">{{
          scope.row.obtainedPrescription ? "是" : "否"
        }}</template>
      </el-table-column>
      <el-table-column
        label="轮转科室"
        align="center"
        prop="rotationDeptName"
        width="150"
      />
      <el-table-column
        label="轮转次数"
        align="center"
        prop="rotationSize"
        width="80"
      >
        <template slot-scope="scope">
          {{ scope.row.rotationIndex }}/{{ scope.row.rotationSize }}
        </template>
      </el-table-column>
      <el-table-column
        label="轮转时间"
        align="center"
        prop="rotationDate"
        width="200"
      />
      <el-table-column
        label="轮转状态"
        align="center"
        prop="rotationStatus"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_STATUS"
            :value="scope.row.rotationStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="出科申请状态"
        align="center"
        prop="graduationAuditStatus"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.GRADUATION_AUDIT_STATUS"
            :value="scope.row.graduationAuditStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="带教老师"
        align="center"
        prop="teacherNames"
        width="100"
      >
        <template slot-scope="scope">
          <el-link
            v-if="
              scope.row.teacherNames && scope.row.teacherNames.indexOf(',') > -1
            "
            type="primary"
            @click="teacherNamesClick(scope.row)"
          >
            {{ scope.row.teacherNames }}
          </el-link>
          <span v-else>{{ scope.row.teacherNames }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="技能考官"
        align="center"
        prop="skillsExaminerNames"
        width="100"
      ></el-table-column>
      <el-table-column
        label="入科教育"
        align="center"
        prop="enrollmentEduCnt"
        width="80"
      />
      <el-table-column
        label="培训数据完成率"
        align="center"
        prop="completionRatio"
        width="120"
      >
        <template slot-scope="scope">
          <el-link type="primary" @click="handleShowComplete(scope.row)">
            {{ scope.row.completionRatio }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="参加教学活动数"
        align="center"
        prop="joinActiveCases"
        width="120"
      />
      <el-table-column
        label="形成性评价数"
        align="center"
        prop="formativeEvaluationResultCount"
        width="120"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            @click="showFormativeEvaluationList(scope.row)"
          >
            {{ scope.row.formativeEvaluationResultCount }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="学员评价带教"
        align="center"
        prop="studentAppraiseTeacherScore"
        width="100"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.studentAppraiseTeacherScore === null
                ? ""
                : scope.row.studentAppraiseTeacherScore
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="学员评价科室"
        align="center"
        prop="studentAppraiseDeptScore"
        width="100"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.studentAppraiseDeptScore === null
                ? ""
                : scope.row.studentAppraiseDeptScore
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="带教评价"
        align="center"
        prop="teacherAppraiseScore"
        width="100"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.teacherAppraiseScore === null
                ? ""
                : scope.row.teacherAppraiseScore
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="科室评价"
        align="center"
        prop="deptAppraiseScore"
        width="100"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.deptAppraiseScore === null
                ? ""
                : scope.row.deptAppraiseScore
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="护士评价"
        align="center"
        prop="nurseAppraiseScore"
        width="100"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.nurseAppraiseScore === null
                ? ""
                : scope.row.nurseAppraiseScore
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="患者评价"
        align="center"
        prop="patientAppraiseScore"
        width="100"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.patientAppraiseScore === null
                ? ""
                : scope.row.patientAppraiseScore
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="日常考核分数"
        align="center"
        prop="dailyAppraiseScore"
        width="120"
      >
        <template v-slot="scope">
          <el-link type="primary" @click="handleView(scope.row)">
            {{ scope.row.dailyAppraiseScore }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="出科理论成绩"
        align="center"
        prop="graduationTheoryScore"
        width="120"
      >
        <template v-slot="scope">
          <el-link
            type="primary"
            @click="handleResultPreview(scope.row.examAnswerResultId)"
          >
            {{ scope.row.graduationTheoryScore }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="出科技能成绩"
        align="center"
        prop="graduationAssessmentScore"
        width="120"
      >
        <template v-slot="scope">
          <el-link
            type="primary"
            @click="handleAssessmentScorePreview(scope.row)"
          >
            {{ scope.row.graduationAssessmentScore }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="出科综合成绩"
        align="center"
        prop="comprehensiveScore"
        width="100"
      />
      <el-table-column
        label="出科考核结果"
        align="center"
        prop="totalityAppraiseStatus"
        width="120"
      >
        <template slot-scope="scope">
          <dict-tag
            class="link-tag"
            :type="DICT_TYPE.GRADUATION_TOTALITY_APPRAISE_STATUS"
            :value="scope.row.totalityAppraiseStatus"
            @click.native="handleAppraiseStatusPreview(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="150"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-popconfirm
            title="是否确认？"
            @confirm="handleProfessionalConfirm(scope.row)"
            v-if="
              scope.row.totalityAppraiseStatus == 1 &&
              !scope.row.professionalBaseAdminUserId
            "
          >
            <el-button
              slot="reference"
              size="mini"
              type="text"
              v-hasPermi="['rotation:situation:professionalconfirm']"
            >
              专业基地管理员确认
            </el-button>
          </el-popconfirm>
          <el-button
            v-if="scope.row.professionalBaseAdminUserId"
            size="mini"
            type="text"
            v-hasPermi="['rotation:situation:professionalconfirm']"
            disabled
          >
            已确认
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <audit-dialog
      :visible.sync="auditVisible"
      :schedule-details-id="curRow.scheduleDetailsId || 0"
      :student-id="curRow.studentId || 0"
    ></audit-dialog>

    <dept-audit
      :visible.sync="deptAuditVisible"
      is-view
      :row="curRow"
    ></dept-audit>

    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="带教时间查看"
      :visible.sync="teachTimeVisible"
      width="450px"
      append-to-body
    >
      <el-table :data="teachTimeData">
        <el-table-column
          width="130"
          property="nickname"
          label="老师姓名"
        ></el-table-column>
        <el-table-column
          width="100"
          property="username"
          label="用户名"
        ></el-table-column>
        <el-table-column property="address" label="带教时间">
          <template slot-scope="scope">
            <span>{{ scope.row.startDate + "~" + scope.row.endDate }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      title="形成性评价列表"
      :visible.sync="formativeEvaluationListVisible"
    >
      <el-table :data="formativeEvaluationList">
        <el-table-column label="学员姓名" width="80">{{
          curRow.nickname
        }}</el-table-column>
        <el-table-column
          label="考核人"
          prop="teacherName"
          width="80"
        ></el-table-column>
        <el-table-column
          label="考核时间"
          prop="createTime"
          width="180"
        ></el-table-column>
        <el-table-column label="考核表单" prop="name"></el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-link
              type="primary"
              @click="showFormativeEvaluationDetail(scope.row)"
              >查看</el-link
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      title="查看形成行评价"
      :visible.sync="formativeEvaluationItemVisible"
      width="800px"
    >
      <div
        class="formative-evaluation-item-wrapper"
        id="formativeEvaluationItem"
      >
        <component
          v-if="formativeEvaluationFormId"
          :is="`form-${formativeEvaluationFormId}`"
          :form-data="formativeEvaluationItem"
          check
        ></component>
      </div>
      <div class="formative-evaluation-item-footer" slot="footer">
        <el-button
          type="primary"
          :loading="pdfExportLoading"
          @click="handleDownPDF"
          >导出为PDF</el-button
        >
      </div>
    </el-dialog>

    <AppraiseDialog
      v-if="formData"
      :title="title"
      :open="open"
      :data="formData"
      :disabled="appraiseDisabled"
      :pdfName="appraisPdfName"
      @setOpen="setOpen"
    />

    <DailyDataCompleteDialog
      :visible="completeVisible"
      :scheduleDetailsId="scheduleDetailsId"
      @show-detail="handleShowCompleteDetail"
      @close="completeVisible = false"
    />
    <DailyDataDetailDialog ref="detail" />
  </div>
</template>

<script>
import AppraiseDialog from "@/views/components/appraiseDialog";
import { getStudentGradeList } from "@/api/system/userStudent";
import { getSimpleMajorList } from "@/api/system/major";
import {
  getSituationPage,
  getCurrentUserRotationDepts,
  exportSituationExcel,
  importTemplate,
  getTeacherList,
  importGraduationTemplate,
  confirmProfessionalUser,
  confirmProfessionalUsers,
} from "@/api/rotation/situation";
import AuditDialog from "../manualItem/auditDialog";
import DeptAudit from "../graduationDeptAudit/deptAudit";
import { getBaseHeader } from "@/utils/request";
import { getFormativeEvaluationResultList } from "@/api/rotation/formativeEvaluationResult";
import { getAppraiseResult } from "@/api/rotation/studentappraiseteacher";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import DailyDataCompleteDialog from "@/views/rotation/dailyData/daily-data-complete-dialog";
import DailyDataDetailDialog from "@/views/rotation/dailyData/daily-data-detail-dialog";

const requireComponents = require.context(
  "@/views/rotation/common/formativeEvaluationForm",
  false,
  /\.vue$/
);
const componentsObj = {};
requireComponents.keys().forEach((filePath) => {
  const componentName = filePath.split("/")[1].replace(/\.vue$/, "");
  const componentConfig = requireComponents(filePath);
  componentsObj[componentName] = componentConfig.default || componentConfig;
});

export default {
  name: "Situation",
  components: {
    AuditDialog,
    DeptAudit,
    AppraiseDialog,
    DailyDataCompleteDialog,
    DailyDataDetailDialog,
    ...componentsObj,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      studentDetailLoading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 教学活动列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        studentType: null,
        major: null,
        grade: null,
        nickname: "",
        username: "",
        rotationStatus: "",
        rotationDepartmentId: "",
        departureDates: [],
        rotationDates: [],
        graduationAuditStatus: "",
      },
      studentGradeList: [],
      majorList: [],
      rotationDeptsList: [],
      auditVisible: false,
      curRow: {},
      deptAuditVisible: false,
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/admin-api/exam/answer-result/import-graduation-assessment",
      },
      uploadType: "",
      teachTimeVisible: false,
      teachTimeData: [],
      formativeEvaluationListVisible: false,
      formativeEvaluationList: [],
      formativeEvaluationItemVisible: false,
      formativeEvaluationFormId: null,
      formativeEvaluationItem: {},
      exportEvaluationPdfName: "",
      formData: null,
      open: false,
      title: "",
      appraiseDisabled: false,
      pdfExportLoading: false,
      scheduleDetailsId: "",
      completeVisible: false,
      multipleSelection: [],
    };
  },
  created() {
    getCurrentUserRotationDepts({ component: "rotation/situation/index" }).then(
      (res) => {
        this.rotationDeptsList = res.data;
      }
    );
    getStudentGradeList().then((res) => {
      this.studentGradeList = res.data;
      // this.queryParams.grade = res.data[0] || undefined;
      this.getList();
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getSituationPage(this.queryParams).then((response) => {
        const list = response.data.list;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 学员类型改变 */
    handleStudentTypeChange(value) {
      this.queryParams.major = null;
      this.majorList = [];
      getSimpleMajorList({ studentType: value }).then((res) => {
        this.majorList = res.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportSituationExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "轮转情况.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    handleSelectionChange(val) {
      console.log("handleSelectionChange===", val);
      this.multipleSelection = val;
    },

    handleProfessionalBatchConfirm() {
      if (this.multipleSelection.length === 0) {
        this.$modal.msgWarning("请选择需要确认的数据");
        return;
      }
      this.$modal
        .confirm("是否专业基地管理员批量确认?")
        .then(() => {
          return confirmProfessionalUsers({
            graduationApplyIds: this.multipleSelection.map(
              (item) => item.graduationApplyId
            ),
          });
        })
        .then(() => {
          this.$modal.msgSuccess("批量专业基地管理员确认成功");
          this.getList();
        });
    },

    handleProfessionalConfirm(row) {
      confirmProfessionalUser({
        graduationApplyId: row.graduationApplyId,
      }).then((res) => {
        this.$modal.msgSuccess("专业基地管理员确认成功");
        this.getList();
      });
    },

    handleResultPreview(id) {
      const url = this.$router.resolve({
        path: "/examResult",
        query: { id },
      }).href;
      window.open(url, "_blank");
    },
    handleAssessmentScorePreview(row) {
      this.curRow = row;
      this.auditVisible = true;
    },
    handleAppraiseStatusPreview(row) {
      this.curRow = row;
      this.deptAuditVisible = true;
    },
    /** 导入按钮操作 */
    handleImport(type) {
      this.uploadType = type;
      if (type === "llcj") {
        this.upload.title = "理论考试成绩导入";
        this.upload.url = `${process.env.VUE_APP_BASE_API}/admin-api/exam/answer-result/import-graduation-assessment`;
      }
      if (type === "jnkh") {
        this.upload.title = "技能考核成绩导入";
        this.upload.url = `${process.env.VUE_APP_BASE_API}/admin-api/rotation/graduation-assessment-result/import-graduation-assessment`;
      }
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      if (this.uploadType === "llcj") {
        importTemplate().then((response) => {
          this.$download.excel(response, "理论考试成绩模板.xlsx");
        });
      }
      if (this.uploadType === "jnkh") {
        importGraduationTemplate().then((response) => {
          this.$download.excel(response, "技能考核成绩模板.xlsx");
        });
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      console.log("handleFileSuccess=====", data);
      let text = "";
      // let text = '创建成功数量：' + data.createActiveNames.length;
      // for (const name of data.createActiveNames) {
      //   text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + name;
      // }
      text +=
        "<br />创建失败数量：" + Object.keys(data.failureUsernames).length;
      for (const name in data.failureUsernames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          name +
          "：" +
          data.failureUsernames[name];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    teacherNamesClick(item) {
      getTeacherList(item.scheduleDetailsId).then((res) => {
        this.teachTimeData = res.data;
        this.teachTimeVisible = true;
      });
    },
    showFormativeEvaluationList(row) {
      this.curRow = row;
      this.formativeEvaluationList = [];
      this.formativeEvaluationListVisible = true;
      getFormativeEvaluationResultList(row.scheduleDetailsId, true).then(
        (res) => {
          this.formativeEvaluationList = res.data;
        }
      );
    },
    showFormativeEvaluationDetail(row) {
      this.formativeEvaluationFormId = row.formId;
      this.formativeEvaluationItem = JSON.parse(row.result);
      this.exportEvaluationPdfName = `${this.curRow.nickname}-${this.curRow.rotationDeptName}-${row.name}`;
      this.formativeEvaluationItemVisible = true;
    },
    handleDownPDF() {
      this.exportPDF("formativeEvaluationItem", this.exportEvaluationPdfName);
    },
    exportPDF(targetId, fileName) {
      const targetEl = document.getElementById(targetId);
      this.pdfExportLoading = true;
      html2canvas(targetEl)
        .then((canvas) => {
          const contentWidth = canvas.width;
          const contentHeight = canvas.height;
          const pageHeight = (contentWidth / 592.28) * 841.89;
          let leftHeight = contentHeight;
          let position = 0;
          const imgWidth = 595.28;
          const imgHeight = (592.28 / contentWidth) * contentHeight;
          const pageData = canvas.toDataURL("image/jpeg", 1.0);
          const pdf = new jsPDF("", "pt", "a4");
          if (leftHeight < pageHeight) {
            pdf.addImage(pageData, "JPEG", 0, 0, imgWidth, imgHeight);
          } else {
            while (leftHeight > 0) {
              pdf.addImage(pageData, "JPEG", 0, position, imgWidth, imgHeight);
              leftHeight -= pageHeight;
              position -= 841.89;

              if (leftHeight > 0) {
                pdf.addPage();
              }
            }
          }
          pdf.save(`${fileName}.pdf`);
          this.pdfExportLoading = false;
        })
        .catch(() => (this.pdfExportLoading = false));
    },
    handleView(row) {
      const id = row.dailyAppraiseResultId;
      this.appraiseDisabled = true;
      this.appraisPdfName = `${row.nickname}+日常考核+${row.dailyAppraiseScore}分`;
      getAppraiseResult(id).then((response) => {
        this.formData = response.data;
        this.open = true;
        this.title = `查看评价-${row.rotationDeptName}`;
      });
    },
    setOpen(flag) {
      this.open = flag;
    },
    handleShowComplete(row) {
      if (row.completionRatio === "/") {
        return;
      }
      this.scheduleDetailsId = row.scheduleDetailsId;
      this.completeVisible = true;
    },
    handleShowCompleteDetail(row) {
      this.$refs.detail.viewDetail(row);
    },
  },
};
</script>

<style lang="scss" scoped>
.link-tag {
  color: #1890ff;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
    text-underline-offset: 5px;
  }
}

.formative-evaluation-item-wrapper {
  margin: -20px;
  padding: 20px;
}

.formative-evaluation-item-footer {
  text-align: center;
}
</style>
