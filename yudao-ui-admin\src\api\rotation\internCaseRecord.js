import request from '@/utils/request'

// 创建见习病例书写
export function createInternCaseRecord(data) {
  return request({
    url: '/rotation/intern-case-record/create',
    method: 'post',
    data: data
  })
}

// 更新见习病例书写
export function updateInternCaseRecord(data) {
  return request({
    url: '/rotation/intern-case-record/update',
    method: 'put',
    data: data
  })
}

// 删除见习病例书写
export function deleteInternCaseRecord(id) {
  return request({
    url: '/rotation/intern-case-record/delete?id=' + id,
    method: 'delete'
  })
}

// 获得见习病例书写
export function getInternCaseRecord(id) {
  return request({
    url: '/rotation/intern-case-record/get?id=' + id,
    method: 'get'
  })
}

// 获得见习病例书写分页
export function getInternCaseRecordPage(query) {
  return request({
    url: '/rotation/intern-case-record/page',
    method: 'get',
    params: query
  })
}

// 导出见习病例书写 Excel
export function exportInternCaseRecordExcel(query) {
  return request({
    url: '/rotation/intern-case-record/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
