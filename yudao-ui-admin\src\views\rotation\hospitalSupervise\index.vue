<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="督导类型" prop="formType">
        <el-select
          v-model="queryParams.formType"
          placeholder="请选择督导类型"
          clearable
          size="small"
          @change="handleTypeChange"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="督导对象" prop="developObject">
        <el-select
          v-model="queryParams.developObject"
          placeholder="请选择督导对象"
          clearable
          filterable
        >
          <el-option
            v-for="item in queryDevelopObjectList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="督导专家" prop="nickname">
        <el-select
          v-model="queryParams.nickname"
          placeholder="请输入督导专家"
          filterable
          clearable
        >
          <el-option
            v-for="item in supervisorList"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="督导时间" prop="superviseDates">
        <el-date-picker
          v-model="queryParams.superviseDates"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="督导状态" prop="hospitalSuperviseStatus">
        <el-select
          v-model="queryParams.hospitalSuperviseStatus"
          placeholder="请选择督导状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.HOSPITAL_SUPERVISE_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="hospitalSuperviseStatus">
        <el-input
          v-model="queryParams.hospitalSuperviseName"
          placeholder="请输入督导项目名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:hospital-supervise:create']"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['rotation:hospital-supervise:create']"
          >导入</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="项目名称"
        align="center"
        prop="hospitalSuperviseName"
        width="160px"
      ></el-table-column>
      <el-table-column label="督导类型" align="center" prop="formType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE"
            :value="scope.row.formType"
          />
        </template>
      </el-table-column>
      <el-table-column label="督导对象" align="center" prop="developObjectName">
        <template slot-scope="scope">
          <el-link type="primary" @click="viewObject(scope.row)">{{
            scope.row.developObjectName
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="督导专家"
        align="center"
        min-width="120"
        prop="nicknames"
      />
      <el-table-column
        label="督导时间"
        align="center"
        width="280"
        prop="beginEndTime"
      />
      <el-table-column
        label="综合表单合计得分"
        align="center"
        prop="comprehensiveScore"
      >
        <template v-slot="scope">
          {{ scope.row.comprehensiveScore || "--" }}
        </template>
      </el-table-column>
      <el-table-column label="非综合表单得分" align="center" prop="score">
        <template slot="header">
          督导得分
          <el-tooltip placement="top">
            <template slot="content">
              除共享表单以外的表单，取权重平均分；若表单不是一百分，则换算成100分后再计算平均值。<br />
              如有一张表总分200分，督导专家评分180分，那么按一百分换算可得到专家评分为90分。
            </template>
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template>
        <template v-slot="scope">
          {{ scope.row.score || "--" }}
        </template>
      </el-table-column>
      <el-table-column
        label="督导状态"
        align="center"
        prop="hospitalSuperviseStatus"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.HOSPITAL_SUPERVISE_STATUS"
            :value="scope.row.hospitalSuperviseStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="140px"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rotation:hospital-supervise:update']"
            v-if="scope.row.showUpdate"
          >
            修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleOrderOpinion(scope.row)"
            v-hasPermi="['rotation:hospital-supervise:update']"
            v-if="scope.row.showComprehensive"
          >
            下达意见
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleViewOpinion(scope.row)"
            v-hasPermi="['rotation:hospital-supervise:update']"
            v-if="scope.row.comprehensiveId"
          >
            查看详情
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:hospital-supervise:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="980px"
      v-dialogDrag
      append-to-body
      custom-class="hospital-supform-choice"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="hospitalSuperviseName">
          <el-input
            v-model="form.hospitalSuperviseName"
            placeholder="请输入督导项目名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="inline-block w-50"
          label="督导类型"
          prop="formType"
        >
          <el-select
            v-model="form.formType"
            placeholder="请选择督导类型"
            @change="handleFormTypeChange"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="inline-block w-50"
          label="督导对象"
          prop="developObject"
        >
          <el-select
            class="w-100"
            v-model="form.developObject"
            placeholder="请选择督导对象"
            filterable
            @change="handleObjectChange"
          >
            <el-option
              v-for="item in formDevelopObjectList"
              :key="item.value"
              :label="item.label"
              :value="item.value.toString()"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="督导专家" prop="superviseUserId">
          <el-select
            class="w-100"
            v-model="form.superviseUserId"
            placeholder="请选择督导专家"
            multiple
            filterable
            clearable
          >
            <el-option
              v-for="item in supervisorList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="督导时间" prop="startEndTime">
          <el-date-picker
            v-model="form.startEndTime"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
          />
        </el-form-item>
        <el-form-item
          label="是否指定督导表单"
          prop="appointed"
          label-width="135px"
        >
          <el-radio-group
            v-model="form.appointed"
            @change="handleAppointedChange"
          >
            <el-radio :label="false">否</el-radio>
            <el-radio :label="true">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="表单名称" v-if="form.appointed">
          <el-input
            style="width: 240px; margin-right: 10px"
            v-model="formKey"
            placeholder="输入表单名称快速检索表单"
            clearable
          ></el-input>
          <el-button type="primary" @click="querySimpleFormList(form, false)"
            >表单检索</el-button
          >
        </el-form-item>
        <el-form-item label-width="0px" v-if="form.appointed">
          <el-transfer
            class="custom-transfer"
            v-model="form.superviseFormIds"
            :data="formData"
            :titles="['表单列表', '已选表单']"
          >
            <span slot-scope="{ option }" :title="option.label">{{
              option.label
            }}</span>
          </el-transfer>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitloading">
          确 定
        </el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 下达意见 -->
    <el-dialog
      :title="`${isOpinionView ? '查看' : '下达'}督导意见`"
      :visible.sync="opinionOpen"
      width="700px"
      v-dialogDrag
      append-to-body
      custom-class="hospital-supervise-dialog"
    >
      <div id="hospital-supervise">
        <el-form class="mb5 export-item" inline>
          <el-form-item class="mb5" style="width: 40%" label="督导对象:">
            {{ headInfos.developObjectName }}
          </el-form-item>
          <el-form-item class="mb5" label="督导时间:">
            {{ headInfos.startTime }}~{{ headInfos.endTime }}
          </el-form-item>
        </el-form>

        <p
          class="mb10 export-item"
          v-if="
            headInfos.resultInfoRespVOList &&
            headInfos.resultInfoRespVOList.length > 0
          "
        >
          综合表单合计得分：{{ headInfos.comprehensiveScore }}
          <el-tooltip placement="top">
            <template slot="content">
              不同综合表单求和，如若其中一张综合表有多人评分，则求平均后再与其他共享表做求和计算。
            </template>
            <i class="el-icon-info"></i>
          </el-tooltip>
        </p>

        <el-table
          class="mb20 export-item"
          :data="headInfos.resultInfoRespVOList"
        >
          <el-table-column label="督导专家" prop="nickname"></el-table-column>
          <el-table-column
            label="综合表单评分"
            prop="comprehensiveScore"
            width="120px"
            align="center"
          >
            <template v-slot="scope">
              <el-link
                type="primary"
                @click="handleScoreClick(scope.row, true)"
                >{{
                  scope.row.comprehensiveScore === null
                    ? "--"
                    : scope.row.comprehensiveScore
                }}</el-link
              >
            </template>
          </el-table-column>
          <el-table-column
            label="非综合表单评分"
            prop="score"
            width="120px"
            align="center"
          >
            <template v-slot="scope">
              <el-link
                type="primary"
                @click="handleScoreClick(scope.row, false)"
                >{{
                  scope.row.score === null ? "--" : scope.row.score
                }}</el-link
              >
            </template>
          </el-table-column>
          <el-table-column
            label="督导意见"
            prop="opinions"
            min-width="240px"
          ></el-table-column>
        </el-table>

        <el-form class="export-item" label-width="160px" v-if="isOpinionView">
          <el-form-item label="综合督导意见">
            <div class="textarea-view">
              {{ headInfos.comprehensiveOpinion }}
            </div>
          </el-form-item>
          <el-form-item label="整改要求" v-if="headInfos.isNeedRectification">
            <div class="textarea-view">
              {{ headInfos.rectificationRequire }}
            </div>
          </el-form-item>
          <el-form-item label="督导反馈">
            <div class="textarea-view">
              {{ headInfos.rectificationFeedback }}
            </div>
          </el-form-item>
          <el-form-item label="整改附件">
            <template v-if="headInfos.files">
              <file-upload
                v-model="headInfos.files"
                :limit="9999"
                :file-size="50"
                :fileType="fileType"
                :disabled="true"
                :isShowTip="false"
              >
              </file-upload>
              <!-- <el-link
                style="line-height: 1.2"
                v-for="file in safeJsonParseFiles(headInfos.files)"
                :key="file.url"
                :href="file.url"
                type="primary"
                download
              >{{ file.name }}</el-link> -->
            </template>
            <span v-else>暂无</span>
          </el-form-item>
          <el-form-item label="已进行督导确认的专家">{{
            headInfos.confirmedNicknames || "暂无"
          }}</el-form-item>
        </el-form>

        <el-form
          :model="opinionForm"
          :rules="opinionRules"
          label-width="140px"
          ref="opinionForm"
          v-else
        >
          <el-form-item label="综合督导意见" prop="comprehensiveOpinion">
            <el-input
              type="textarea"
              v-model="opinionForm.comprehensiveOpinion"
              placeholder="请输入综合督导意见"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="本次督导是否需要整改"
            prop="isNeedRectification"
            label-width="180px"
          >
            <el-radio-group
              class="ml10"
              v-model="opinionForm.isNeedRectification"
            >
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="opinionForm.isNeedRectification">
            <el-form-item label="整改要求" prop="rectificationRequire">
              <el-input
                type="textarea"
                v-model="opinionForm.rectificationRequire"
                placeholder="请输入整改要求"
              ></el-input>
            </el-form-item>
            <el-form-item label="整改要求完成时间" prop="rectificationLastTime">
              <el-date-picker
                v-model="opinionForm.rectificationLastTime"
                value-format="yyyy-MM-dd hh:mm"
                placeholder="请选择完成时间"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="整改责任人" prop="rectificationUserIds">
              <el-select
                class="w-100"
                v-model="opinionForm.rectificationUserIds"
                multiple
                clearable
                filterable
              >
                <el-option
                  v-for="user in comprehensiveUserList"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-form>
      </div>

      <span slot="footer" v-if="!isOpinionView">
        <el-button type="primary" @click="submitOpinion">确定</el-button>
        <el-button @click="cancelOpinion">取消</el-button>
      </span>
      <span slot="footer" v-else>
        <el-button type="primary" :loading="exportloading" @click="handleDown()"
          >导出</el-button
        >
      </span>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <!-- <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的用户数据
          </div> -->
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <hospital-supervise-evaluate-dialog
      :visible.sync="scoreOpen"
      :supervise-object="superviseObject"
    ></hospital-supervise-evaluate-dialog>

    <supervise-object-dialog
      :visible.sync="superviseObjectDialog"
      :supervise-object="superviseObject"
    ></supervise-object-dialog>
  </div>
</template>

<script>
import { getDepartmentSimpleList } from "@/api/system/department";
import {
  getSupervisionExpertList,
  getSupervisionComprehensiveUser,
  createHospitalSupervise,
  updateHospitalSupervise,
  deleteHospitalSupervise,
  getHospitalSupervisePage,
  getHospitalSupervise,
  exportHospitalSuperviseExcel,
  getHospitalSuperviseHeadInfos,
  getHospitalSuperviseInfos,
  saveHospitalSuperviseComprehensive,
  importTemplate,
} from "@/api/rotation/hospitalSupervise";
import { getSimpleFormList } from "@/api/rotation/teachingActiveSupervise";
import FileUpload from "@/components/FileUploadInfo";
import HospitalSuperviseEvaluateDialog from "../hospitalSuperviseDevelop/hospital-supervise-evaluate-dialog";
import SuperviseObjectDialog from "../hospitalSuperviseDevelop/superviseObjectDialog";
import { exportPDF } from "@/utils/exportUtils";
import { getBaseHeader } from "@/utils/request";

export default {
  name: "HospitalSupervise",
  components: {
    FileUpload,
    HospitalSuperviseEvaluateDialog,
    SuperviseObjectDialog,
  },
  data() {
    return {
      fileType: [
        "doc",
        "xls",
        "ppt",
        "pptx",
        "txt",
        "pdf",
        "png",
        "jpg",
        "jpeg",
      ],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级督导列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        hospitalSuperviseName: "",
        formType: null,
        developObject: null,
        nickname: null,
        superviseDates: [],
        hospitalSuperviseStatus: null,
      },
      // 查询条件督导对象列表
      queryDevelopObjectList: [],
      // 专业基地列表
      professionalBaseList: this.getDictDatas(this.DICT_TYPE.PROFESSIONAL_BASE),
      // 科室列表
      departmentOptions: [],
      // 督导专家列表
      supervisorList: [],
      // 责任人列表
      comprehensiveUserList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        hospitalSuperviseName: [
          { required: true, message: "督导项目名称不能为空", trigger: "blur" },
        ],
        formType: [
          { required: true, message: "督导类型不能为空", trigger: "change" },
        ],
        developObject: [
          { required: true, message: "督导对象不能为空", trigger: "blur" },
        ],
        superviseUserId: [
          { required: true, message: "督导人员不能为空", trigger: "blur" },
        ],
        startEndTime: [
          { required: true, message: "督导时间不能为空", trigger: "blur" },
        ],
        appointed: [
          {
            required: true,
            message: "是否指定督导表单不能为空",
            trigger: "blur",
          },
        ],
      },
      // 表单督导对象列表
      formDevelopObjectList: [],
      // 表单数据
      formData: [],
      // 表单过滤词
      formKey: "",
      // 督导意见弹窗
      opinionOpen: false,
      // 查看督导意见
      isOpinionView: false,
      // 当前行
      currentRow: null,
      // 督导意见头部信息
      headInfos: {},
      // 打分情况
      scoreList: [],
      // 意见表单
      opinionForm: {},
      // 表单校验
      opinionRules: {
        comprehensiveOpinion: [
          { required: true, message: "综合督导意见必填", trigger: "blur" },
        ],
        isNeedRectification: [
          { required: true, message: "是否需要整改必填", trigger: "change" },
        ],
        rectificationRequire: [
          { required: true, message: "整改要求必填", trigger: "blur" },
        ],
        rectificationLastTime: [
          {
            required: true,
            message: "整改要求完成时间必填",
            trigger: "change",
          },
        ],
        rectificationUserIds: [
          { required: true, message: "整改责任人", trigger: "change" },
        ],
      },
      submitloading: false,
      // 评分详情弹窗
      scoreOpen: false,
      // 督导对象
      superviseObject: {},
      superviseObjectDialog: false,
      exportloading: false,
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        // updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/admin-api/rotation/hospital-supervise/import-hospital-supervise",
      },
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    this.getSupervisorList();
  },
  methods: {
    safeJsonParseFiles(str) {
      // debugger
      try {
        const rs = JSON.parse(str);
        if (typeof rs === "string") {
          return rs
            .split(",")
            .map((url) => ({ url, name: url.split("/").pop() }));
        } else {
          return rs;
        }
      } catch (e) {
        console.log(e);
        return str
          .split(",")
          .map((url) => ({ url, name: url.split("/").pop() }));
      }
    },
    // 获得科室列表
    getDepartment() {
      getDepartmentSimpleList(0).then((res) => {
        const { data = [] } = res;
        // 处理 roleOptions 参数
        const list = [];
        data.forEach((item) => {
          list.push({
            label: item.name,
            value: item.id,
          });
        });
        this.departmentOptions = list;
      });
    },
    // 获取督导专家列表
    getSupervisorList() {
      getSupervisionExpertList().then((res) => {
        this.supervisorList = (res.data || []).map((item) => ({
          label: item.nickname,
          value: item.id,
        }));
        this.form.superviseUserId = [];
      });
    },
    // 获取整改责任人列表
    getComprehensiveUserList(formType, developObject) {
      const params = { formType };
      if (formType === "3") {
        params.departmentId = developObject;
      }
      getSupervisionComprehensiveUser(params).then((res) => {
        this.comprehensiveUserList = res.data;
      });
    },
    /** 请求表单 */
    querySimpleFormList(row, clear = false) {
      return getSimpleFormList({
        superviseType: "2",
        formType: row.formType,
        developObject: row.developObject,
        name: this.formKey,
      }).then((res) => {
        this.formData = res.data.map((item) => ({
          key: item.id.toString(),
          label: item.name,
        }));
        if (clear) {
          this.superviseFormIds = [];
        }
      });
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getHospitalSupervisePage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        formType: undefined,
        developObject: undefined,
        superviseUserId: [],
        startEndTime: [],
        appointed: false,
        superviseFormIds: [],
      };
      this.resetForm("form");
    },
    /** 查询督导类型改变 */
    handleTypeChange(val) {
      switch (val) {
        case "1":
          this.queryDevelopObjectList = this.professionalBaseList;
          break;
        case "2":
          this.queryDevelopObjectList = this.getDictDatas(
            this.DICT_TYPE.STAFF_ROOM
          );
          break;
        case "3":
          this.queryDevelopObjectList = this.departmentOptions;
          break;
        default:
          this.queryDevelopObjectList = [];
      }
      this.queryParams.developObject = undefined;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加院级督导";
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      const id = row.id;
      this.handleFormTypeChange(row.formType);
      await this.querySimpleFormList(row, true);
      getHospitalSupervise(id).then((response) => {
        this.form = response.data;
        this.form.startEndTime = [
          response.data.startTime,
          response.data.endTime,
        ];
        this.form.superviseFormIds = response.data.superviseFormIds
          ? response.data.superviseFormIds.split(",")
          : [];

        this.open = true;
        this.title = "修改院级督导";
      });
    },
    /** 弹窗督导类型改变 */
    handleFormTypeChange(val) {
      switch (val) {
        case "1":
          this.formDevelopObjectList = this.professionalBaseList;
          break;
        case "2":
          this.formDevelopObjectList = this.getDictDatas(
            this.DICT_TYPE.STAFF_ROOM
          );
          break;
        case "3":
          this.formDevelopObjectList = this.departmentOptions;
          break;
        default:
          this.formDevelopObjectList = [];
      }
      this.form.developObject = undefined;
      if (this.form.appointed) {
        this.querySimpleFormList(this.form, true);
      }
    },
    /** 督导对象改变 */
    handleObjectChange() {
      this.getSupervisorList();
      if (this.form.appointed) {
        this.querySimpleFormList(this.form, true);
      }
    },
    /** 是否指定督导表单切换 */
    handleAppointedChange(val) {
      if (val) {
        if (!this.form.formType || !this.form.developObject) {
          this.$message.warning("请先选择督导类型和督导对象。");
          this.form.appointed = false;
          return;
        }
        this.querySimpleFormList(this.form, true);
      } else {
        this.form.superviseFormIds = [];
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        this.submitloading = true;
        const {
          hospitalSuperviseName,
          formType,
          developObject,
          superviseUserId,
          startEndTime,
          appointed,
          superviseFormIds,
        } = this.form;
        const form = {
          hospitalSuperviseName,
          formType,
          developObject,
          superviseUserId,
          startTime: startEndTime[0],
          endTime: startEndTime[1],
          appointed,
          superviseFormIds:
            superviseFormIds && superviseFormIds.length
              ? superviseFormIds.join(",")
              : "",
        };
        if (this.form.id) {
          form.id = this.form.id;
          // 修改的提交
          updateHospitalSupervise(form)
            .then((response) => {
              this.submitloading = false;
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            })
            .catch(() => {
              this.submitloading = false;
            });
        } else {
          // 添加的提交
          createHospitalSupervise(form)
            .then((response) => {
              this.submitloading = false;
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            })
            .catch(() => {
              this.submitloading = false;
            });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除该院级督导?")
        .then(function () {
          return deleteHospitalSupervise(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有院级督导数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportHospitalSuperviseExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "院级督导.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    /** 重置下达意见 */
    resetOpinion(id) {
      this.opinionForm = {
        id,
        comprehensiveOpinion: "",
        isNeedRectification: false,
        rectificationRequire: "",
        rectificationLastTime: "",
        rectificationUserIds: [],
      };
    },
    /** 下达意见 */
    handleOrderOpinion(row) {
      this.resetOpinion(row.id);
      this.isOpinionView = false;
      this.opinionOpen = true;
      getHospitalSuperviseHeadInfos(row.id).then((res) => {
        this.headInfos = res.data;
        this.getComprehensiveUserList(row.formType, row.developObject);
      });
    },
    submitOpinion() {
      this.$confirm("是否确认本次督导意见下达?", "提示").then(() => {
        const data = { ...this.opinionForm };
        data.rectificationUserIds = data.rectificationUserIds.join(",");
        saveHospitalSuperviseComprehensive(data).then(() => {
          this.$message.success("下达督导意见成功！");
          this.cancelOpinion();
          this.getList();
        });
      });
    },
    cancelOpinion() {
      this.$refs.opinionForm.clearValidate();
      this.opinionOpen = false;
    },
    /** 查看详情 */
    handleViewOpinion(row) {
      this.resetOpinion();
      this.isOpinionView = true;
      this.opinionOpen = true;
      this.currentRow = row;
      getHospitalSuperviseInfos(row.id).then((res) => {
        this.headInfos = res.data;
        this.headInfos.files = this.safeJsonParseFiles(this.headInfos.files);
      });
    },
    /** 点击分数查看评分详情 */
    handleScoreClick(row, isComprehensive) {
      if (isComprehensive) {
        if (row.comprehensiveScore === null) {
          return;
        }
      } else {
        if (row.score === null) {
          return;
        }
      }
      this.scoreOpen = true;
      this.superviseObject = {
        ...this.currentRow,
        superviseResultId: row.superviseResult,
        isComprehensive,
        superviseNickname: row.nickname,
      };
    },
    viewObject(row) {
      this.superviseObject = row;
      this.superviseObjectDialog = true;
    },
    handleDown() {
      this.exportloading = true;
      exportPDF(
        `hospital-supervise`,
        `${this.currentRow.hospitalSuperviseName}`,
        () => {
          this.exportloading = false;
        }
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "院级督导导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.$download.excel(response, "院级督导导入模板.xlsx");
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      const {
        createHospitalSuperviseNames = [],
        updateHospitalSuperviseNames = [],
        failureHospitalSuperviseNames = {},
      } = data;
      let text = "创建成功数量：" + createHospitalSuperviseNames.length;
      for (const username of createHospitalSuperviseNames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + username;
      }
      text += "<br />更新成功数量：" + updateHospitalSuperviseNames.length;
      for (const username of updateHospitalSuperviseNames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + username;
      }
      text +=
        "<br />更新失败数量：" +
        Object.keys(failureHospitalSuperviseNames).length;
      for (const username in failureHospitalSuperviseNames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          username +
          "：" +
          failureHospitalSuperviseNames[username];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-transfer {
  ::v-deep .el-transfer-panel {
    // width: 266px;
    overflow: visible;
    .el-checkbox__label:hover {
      overflow: visible;
    }
  }

  ::v-deep .el-transfer__buttons {
    width: 116px;
    .el-button {
      margin-left: 0;
    }
  }
}
</style>

<style lang="scss">
.hospital-supform-choice {
  .el-transfer-panel {
    width: 43%;
  }

  .el-transfer__buttons {
    display: inline-flex;
    flex-direction: column;

    .el-button + .el-button {
      margin-left: 0;
    }
  }
}

.hospital-supervise-dialog {
  .textarea-view {
    background-color: #f5f7fa;
    border-color: #dfe4ed;
    color: #999;
    padding: 5px 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    min-height: 42px;
    word-break: break-all;
  }
}
</style>
