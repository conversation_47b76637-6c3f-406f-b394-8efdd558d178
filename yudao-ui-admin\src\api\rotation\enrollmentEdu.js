import request from "@/utils/request";

// 创建入科教育
export function createEnrollmentEdu(data) {
  return request({
    url: "/rotation/enrollment-edu/create",
    method: "post",
    data: data,
  });
}

// 更新入科教育
export function updateEnrollmentEdu(data) {
  return request({
    url: "/rotation/enrollment-edu/update",
    method: "put",
    data: data,
  });
}

// 删除入科教育
export function deleteEnrollmentEdu(id) {
  return request({
    url: "/rotation/enrollment-edu/delete?id=" + id,
    method: "delete",
  });
}

// 获得入科教育
export function getEnrollmentEdu(id) {
  return request({
    url: "/rotation/enrollment-edu/get?id=" + id,
    method: "get",
  });
}

// 获得入科教育分页
export function getEnrollmentEduPage(query) {
  return request({
    url: "/rotation/enrollment-edu/page",
    method: "get",
    params: query,
    headers: { component: "rotation/enrollmentEdu/index" },
  });
}

// 导出入科教育 Excel
export function exportEnrollmentEduExcel(query) {
  return request({
    url: "/rotation/enrollment-edu/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获得参加学员列表
export function getStudentsList(query) {
  return request({
    url: "/rotation/enrollment-edu/list-students",
    method: "get",
    params: query,
  });
}

// 确认参加
export function confirmJoin(data) {
  return request({
    url: "/rotation/enrollment-edu-student/confirm-join",
    method: "put",
    data: data,
  });
}

// 撤销参加
export function revokeJoin(data) {
  return request({
    url: "/rotation/enrollment-edu-student/revoke-join",
    method: "put",
    data: data,
  });
}

// 删除活动用户
export function deleteTeachingUser(params) {
  return request({
    url: "/rotation/enrollment-edu-student/deleteUser",
    method: "delete",
    params: params,
  });
}
