<template>
  <div class="app-container">
    <el-tabs type="card">
      <el-tab-pane label="考勤管理">
        <!-- 搜索工作栏 -->
        <el-form
          v-if="queryParamsInit"
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="70px"
        >
          <el-form-item label="考勤月份" prop="attendanceMonthDate">
            <el-date-picker
              style="width: 193px"
              v-model="queryParams.attendanceMonthDate"
              type="month"
              placeholder="选择月份"
              value-format="yyyy-MM"
              :clearable="false"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="轮转科室"
            prop="rotationDepartmentId"
            v-if="role === 'dept'"
          >
            <el-select
              v-model="queryParams.rotationDepartmentId"
              placeholder="请选择轮转科室"
              filterable
              size="small"
            >
              <el-option
                v-for="dict in rotationDepartmentList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="学员类型" prop="studentType">
            <el-select
              v-model="queryParams.studentType"
              placeholder="请选择学员类型"
              clearable
              size="small"
            >
              <el-option
                v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="培训专业" prop="major">
            <el-select
              v-model="queryParams.major"
              placeholder="请选择培训专业"
              clearable
              filterable
              size="small"
            >
              <el-option
                v-for="dict in majorList"
                :key="dict.code"
                :label="dict.name"
                :value="dict.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="学员姓名" prop="nickname">
            <el-input
              v-model="queryParams.nickname"
              placeholder="请输入学员姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="queryParams.username"
              placeholder="请输入用户名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="年级" prop="grade">
            <el-input
              v-model="queryParams.grade"
              placeholder="请输入年级"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8" style="padding: 0 5px">
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="[
                'attendance:teacherrecord:export',
                'attendance:deptrecord:export',
              ]"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <!-- 列表 -->
        <el-table v-loading="loading" :data="loading ? [] : list" border>
          <el-table-column type="index" align="center" fixed="left" />
          <el-table-column
            label="姓名"
            prop="nickname"
            align="center"
            width="100px"
            fixed="left"
          ></el-table-column>
          <el-table-column
            label="工号"
            prop="username"
            align="center"
            width="100px"
            fixed="left"
          ></el-table-column>
          <el-table-column label="年月" align="center" fixed="left">{{
            queryParams.attendanceMonthDate
          }}</el-table-column>
          <el-table-column
            label="轮转科室"
            prop="rotationDepartmentName"
            align="center"
            fixed="left"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-for="day in getDays(list[0])"
            :key="day"
            align="center"
            width="100px"
          >
            <template v-slot:header>
              {{ day.substring(8, 10) }} <br />
              {{
                {
                  0: "日",
                  1: "一",
                  2: "二",
                  3: "三",
                  4: "四",
                  5: "五",
                  6: "六",
                }[new Date(day).getDay()]
              }}
            </template>
            <template v-slot="scope">
              <el-dropdown
                class="attendance-status-cell"
                :disabled="disabledAttendance(scope.row[day])"
                trigger="click"
              >
                <div
                  :class="[
                    'attendance-status-content',
                    getAttendanceStatus(scope.row[day]).cssClass,
                  ]"
                >
                  {{ getAttendanceStatus(scope.row[day]).label }}
                </div>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="dict in getDictDatas(DICT_TYPE.ATTENDANCE_STATUS)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                    @click.native="
                      changeAttendanceStatus(scope.row, day, dict.value)
                    "
                    >{{ dict.label }}</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
        <div style="padding: 5px"></div>
      </el-tab-pane>

      <el-tab-pane label="考勤统计" v-if="role === 'dept'">
        <!-- 搜索工作栏 -->
        <el-form
          :model="statisticsQueryParams"
          ref="statisticsQueryForm"
          size="small"
          :inline="true"
          v-show="statisticsShowSearch"
          label-width="70px"
        >
          <el-form-item label="考勤范围" prop="attendanceMonthDate">
            <el-date-picker
              style="width: 250px"
              v-model="statisticsQueryParams.startEndDate"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="轮转科室" prop="rotationDepartmentId">
            <el-select
              v-model="statisticsQueryParams.rotationDepartmentId"
              placeholder="请选择轮转科室"
              filterable
              clearable
              size="small"
            >
              <el-option
                v-for="dict in rotationDepartmentList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="学员类型" prop="studentType">
            <el-select
              v-model="statisticsQueryParams.studentType"
              placeholder="请选择学员类型"
              clearable
              size="small"
            >
              <el-option
                v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="培训专业" prop="major">
            <el-select
              v-model="statisticsQueryParams.major"
              placeholder="请选择培训专业"
              clearable
              filterable
              size="small"
            >
              <el-option
                v-for="dict in majorList"
                :key="dict.code"
                :label="dict.name"
                :value="dict.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="学员姓名" prop="nickname">
            <el-input
              v-model="statisticsQueryParams.nickname"
              placeholder="请输入学员姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="statisticsQueryParams.username"
              placeholder="请输入用户名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="年级" prop="grade">
            <el-input
              v-model="statisticsQueryParams.grade"
              placeholder="请输入年级"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="考勤状态" prop="attendanceStatusList">
            <el-select
              v-model="statisticsQueryParams.attendanceStatusList"
              multiple
              placeholder="请选择考勤状态"
              collapse-tags
            >
              <el-option
                v-for="dict in this.getDictDatas(DICT_TYPE.ATTENDANCE_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleStatisticsQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetStatisticsQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8" style="padding: 0 5px">
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleStatisticsExport"
              :loading="statisticsExportLoading"
              v-hasPermi="[
                'attendance:teacherrecord:export',
                'attendance:deptrecord:export',
              ]"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="statisticsShowSearch"
            @queryTable="getStatisticsList"
          ></right-toolbar>
        </el-row>

        <!-- 列表 -->
        <el-table
          v-loading="statisticsLoading"
          :data="statisticsLoading ? [] : statisticsList"
          border
        >
          <el-table-column
            label="学员姓名"
            prop="nickname"
            align="center"
            width="100px"
            fixed="left"
          ></el-table-column>
          <el-table-column label="用户名" prop="username"></el-table-column>
          <el-table-column
            label="轮转科室"
            prop="rotationDepartmentName"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column label="出勤" prop="attendance"></el-table-column>
          <el-table-column label="缺勤" prop="absence"></el-table-column>
          <el-table-column label="休息" prop="rest"></el-table-column>
          <el-table-column label="请假" prop="leave"></el-table-column>
          <el-table-column label="迟到" prop="late"></el-table-column>
          <el-table-column label="早退" prop="early"></el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination
          v-show="statisticsTotal > 0"
          :total="statisticsTotal"
          :page.sync="statisticsQueryParams.pageNo"
          :limit.sync="statisticsQueryParams.pageSize"
          @pagination="getStatisticsList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import dayjs from "dayjs";
import {
  updateRecord,
  getRecordPage,
  exportRecordExcel,
  getStatisticsPage,
  exportStatisticsExcel,
} from "@/api/attendance/record";
import { getSimpleMajorList } from "@/api/system/major";
import { getRotationDepartmentPermissionList } from "@/api/system/department";
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "Record",
  components: {},
  props: {
    role: {
      type: String,
      default: "teacher",
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考勤记录列表
      list: [],
      // 查询参数
      queryParams: {
        rotationDepartmentId: "",
        attendanceMonthDate: parseTime(new Date(), "{y}-{m}"),
        studentType: "",
        major: "",
        nickname: "",
        username: "",
        grade: "",
        pageNo: 1,
        pageSize: 10,
      },
      // 查询参数是否已初始化；用于异步请求后初始化时，用于保证重置初始化值正确
      queryParamsInit: false,
      // 轮转科室列表
      rotationDepartmentList: [],
      // 专业列表
      majorList: [],
      /* 考勤统计 */
      // 遮罩层
      statisticsLoading: true,
      // 导出遮罩层
      statisticsExportLoading: false,
      // 显示搜索条件
      statisticsShowSearch: true,
      // 总条数
      statisticsTotal: 0,
      // 考勤统计列表
      statisticsList: [],
      // 查询参数
      statisticsQueryParams: {
        rotationDepartmentId: "",
        startEndDate: [
          dayjs().startOf("month").format("YYYY-MM-DD"),
          dayjs().endOf("month").format("YYYY-MM-DD"),
        ],
        studentType: "",
        major: "",
        nickname: "",
        username: "",
        grade: "",
        attendanceStatusList: this.getDictDatas(
          this.DICT_TYPE.ATTENDANCE_STATUS
        ).map((dict) => dict.value),
        pageNo: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    getSimpleMajorList().then((res) => (this.majorList = res.data));
    getRotationDepartmentPermissionList({
      component:
        this.role === "teacher"
          ? "attendance/recordTeacher/index"
          : "attendance/recordDept/index",
    }).then((res) => {
      this.rotationDepartmentList = res.data;
      if (this.role === "dept") {
        this.queryParams.rotationDepartmentId = res.data[0]?.id || "";
        this.queryParamsInit = true;
        this.getStatisticsList();
      }
      this.getList();
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getRecordPage(this.queryParams, this.role).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        setTimeout(() => (this.loading = false), 200);
      });
    },
    /** 获取考勤日期 */
    getDays(data) {
      return Object.keys(data || {})
        .filter((key) => /\d{4}-\d{2}-\d{2}/.test(key))
        .sort();
    },
    /** 获取考勤状态 */
    getAttendanceStatus(data) {
      const defaultStatus = {
        cssClass: "",
        label: "",
      };
      if (!data) {
        return defaultStatus;
      }
      try {
        const parsedData = JSON.parse(data);
        return (
          this.getDictData(
            this.DICT_TYPE.ATTENDANCE_STATUS,
            parsedData.attendanceStatus
          ) || defaultStatus
        );
      } catch (e) {
        return defaultStatus;
      }
    },
    /** 获取是否可以编辑考勤状态 */
    disabledAttendance(data) {
      return data
        ? JSON.parse(data).editEnable !== "1" ||
            JSON.parse(data).attendanceStatus === "3"
        : false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 切换考勤状态 */
    changeAttendanceStatus(row, day, status) {
      const obj = JSON.parse(row[day]);
      updateRecord({ id: obj.id, attendanceStatus: status }, this.role).then(
        (res) => {
          if (res.data) {
            obj.attendanceStatus = status;
            row[day] = JSON.stringify(obj);
          }
        }
      );
    },
    /** 导出考勤 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有考勤记录数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportRecordExcel(params, this.role);
        })
        .then((response) => {
          this.$download.excel(response, "考勤记录.xlsx");
          this.exportLoading = false;
        })
        .catch(() => (this.exportLoading = false));
    },

    /** 查询统计列表 */
    getStatisticsList() {
      this.statisticsLoading = true;
      let params = { ...this.statisticsQueryParams };
      params.startDate = params.startEndDate[0];
      params.endDate = params.startEndDate[1];
      delete params.startEndDate;
      // 执行查询
      getStatisticsPage(params).then((response) => {
        this.statisticsList = response.data.list;
        this.statisticsTotal = response.data.total;
        setTimeout(() => (this.statisticsLoading = false), 200);
      });
    },
    /** 统计搜索按钮操作 */
    handleStatisticsQuery() {
      this.statisticsQueryParams.pageNo = 1;
      this.getStatisticsList();
    },
    /** 统计重置按钮操作 */
    resetStatisticsQuery() {
      this.resetForm("statisticsQueryForm");
      this.handleStatisticsQuery();
    },
    /** 统计导出考勤 */
    handleStatisticsExport() {
      // 处理查询参数
      let params = { ...this.statisticsQueryParams };
      params.startDate = params.startEndDate[0];
      params.endDate = params.startEndDate[1];
      delete params.startEndDate;
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有考勤统计数据项?")
        .then(() => {
          this.statisticsExportLoading = true;
          return exportStatisticsExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "考勤统计.xlsx");
          this.statisticsExportLoading = false;
        })
        .catch(() => (this.statisticsExportLoading = false));
    },
  },
};
</script>

<style scoped>
.attendance-status-cell {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.attendance-status-cell::after {
  content: "";
  display: block;
  position: absolute;
  right: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 10px 0 0;
  border-color: transparent #7f7f7f;
}

.attendance-status-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.attendance-status-content[disabled] {
  cursor: not-allowed;
}

.attendance-status-content.abnormal {
  color: red;
}
</style>
