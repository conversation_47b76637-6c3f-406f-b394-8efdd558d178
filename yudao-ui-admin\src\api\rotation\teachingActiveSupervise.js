import request from "@/utils/request";

// 创建教学活动
export function createTeachingActive(data) {
  return request({
    url: "/rotation/teaching-active-plan/create",
    method: "post",
    data: data,
  });
}

// 更新教学活动
export function updateTeachingActive(data) {
  return request({
    url: "/rotation/teaching-active-plan/update",
    method: "put",
    data: data,
  });
}

// 删除教学活动
export function deleteTeachingActive(id) {
  return request({
    url: "/rotation/teaching-active-plan/delete?id=" + id,
    method: "delete",
  });
}

// 获得教学活动
export function getTeachingActive(id) {
  return request({
    url: "/rotation/teaching-active-plan/get?id=" + id,
    method: "get",
  });
}

// 获得教学活动分页
export function getTeachingActivePage(query) {
  return request({
    url: "/rotation/teaching-active-supervise/page-teaching-active",
    method: "get",
    params: query,
    headers: { component: "rotation/teachingActiveSupervise/index" },
  });
}

// 导出教学活动 Excel
export function exportTeachingActiveExcel(query) {
  return request({
    url: "/rotation/teaching-active-plan/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获得参加学员列表
export function getStudentsList(query) {
  return request({
    url: "/rotation/teaching-active-student-plan/list-students",
    method: "get",
    params: query,
  });
}

// 确认参加
export function confirmJoin(data) {
  return request({
    url: "/rotation/teaching-active-student-plan/confirm-join",
    method: "put",
    data: data,
  });
}

// 撤销参加
export function revokeJoin(data) {
  return request({
    url: "/rotation/teaching-active-student-plan/revoke-join",
    method: "put",
    data: data,
  });
}

// 下载用户导入模板
export function importTemplate() {
  return request({
    url: "/rotation/teaching-active-plan/get-import-template",
    method: "get",
    responseType: "blob",
  });
}

// 获取精简督导表单列表
export function getSimpleFormList(query) {
  return request({
    url: "/rotation/supervise-form/list-all-simple",
    method: "get",
    params: query,
  });
}

// 获取精简督导表单列表
export function getSuperviseFormsList(query) {
  return request({
    url: "/rotation/supervise-result/get-forms",
    method: "get",
    params: query,
  });
}

// 创建督导结果
export function createSuperviseResult(data) {
  return request({
    url: "/rotation/supervise-result/create",
    method: "post",
    data: data,
  });
}

// 获得督导结果
export function getSuperviseResult(query) {
  return request({
    url: "/rotation/supervise-result/get",
    method: "get",
    params: query,
  });
}

// 保存督导反馈
export function saveSuperviseFeedback(data) {
  return request({
    url: "/rotation/supervise-result/save-supervise-feedback",
    method: "post",
    data,
  });
}

// 获得教学督导统计分页
export function pageTeachingSuperviseStatistics(query) {
  return request({
    url: '/rotation/supervise-result/page-teaching-supervise-statistics',
    method: 'get',
    params: query
  })
}

// 获得督导记录分页
export function getSuperviseRecordList(query) {
  return request({
    url: "/rotation/teaching-active-supervise/page-teaching-active-supervise-record",
    method: "get",
    params: query,
  });
}

// 获得督导记录分页
export function getTrainerSuperviseRecordList(query) {
  return request({
    url: "/rotation/teaching-active-supervise/page-trainer-teaching-active-supervise",
    method: "get",
    params: query,
  });
}

// 导出院级培训 Excel
export function exportTeachingActiveSuperviseExcel(query) {
  return request({
    url: "/rotation/teaching-active-supervise/export-teaching-active-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
