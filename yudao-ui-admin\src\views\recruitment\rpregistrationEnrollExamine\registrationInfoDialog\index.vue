<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="70%"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <recruitment-form-residency
      :planId="planId"
      :recruitmentRegistrationId="recruitmentRegistrationId"
      readonly
    />
    <div slot="footer" class="dialog-footer">
      <el-button
        v-if="formData.enrollExamineStatus === 'to_be_confirm'"
        type="primary"
        @click="handleAudit(formData)"
        v-hasPermi="['recruitment:rpregistration-enroll-examine:update']"
      >
        不录取
      </el-button>
      <el-button
        v-if="formData.enrollExamineStatus === 'to_be_confirm'"
        type="primary"
        @click="handleSend('noticeSend')"
        v-hasPermi="['recruitment:rpregistration-enroll-examine:update']"
      >
        发放通知书
      </el-button>
      <el-button @click="cancel">关闭</el-button>
    </div>

    <audit-dialog
      :title="auditDialogTitle"
      :openAudit="openAudit"
      :formData="formData"
      @update:openAudit="(value) => (openAudit = value)"
      @refresh="refresh"
    />

    <notice-dialog
      :noticeOpen="noticeOpen"
      :curRow="formData"
      :noticeOpt="noticeOpt"
      @update:noticeOpen="(value) => (noticeOpen = value)"
      @refresh="refresh"
    />
  </el-dialog>
</template>

<script>
import AuditDialog from "../auditDialog";
import NoticeDialog from "../noticeDialog";
import RecruitmentFormResidency from "../../fill/recruitment-form-residency.vue";

export default {
  name: "RegistrationInfoDialog",
  components: { RecruitmentFormResidency, AuditDialog, NoticeDialog },
  props: {
    title: {
      type: String,
    },
    openRegistrationInfo: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      open: this.openRegistrationInfo,
      openAudit: false,
      auditDialogTitle: "审核信息",
      noticeOpen: false,
      noticeOpt: "",
    };
  },
  computed: {
    recruitmentRegistrationId() {
      return this.formData?.recruitmentRegistrationId?.toString();
    },
    planId() {
      return this.formData?.planId?.toString();
    },
  },
  watch: {
    openRegistrationInfo(newVal) {
      this.open = newVal;
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:openRegistrationInfo", false);
    },

    refresh() {
      this.$emit("refresh");
      this.cancel();
    },

    handleAudit(row) {
      this.auditDialogTitle = `审核确认-${row.name}`;
      this.openAudit = true;
    },

    handleSend(type) {
      this.noticeOpt = type;
      this.noticeOpen = true;
    },
  },
};
</script>

<style lang="scss" scoped></style>
