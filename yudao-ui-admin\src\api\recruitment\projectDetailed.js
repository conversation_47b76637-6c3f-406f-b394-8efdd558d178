import request from '@/utils/request'

// 创建招录项目明细
export function createProjectDetailed(data) {
  return request({
    url: '/recruitment/project-detailed/create',
    method: 'post',
    data: data
  })
}

// 更新招录项目明细
export function updateProjectDetailed(data) {
  return request({
    url: '/recruitment/project-detailed/update',
    method: 'put',
    data: data
  })
}

// 删除招录项目明细
export function deleteProjectDetailed(id) {
  return request({
    url: '/recruitment/project-detailed/delete?id=' + id,
    method: 'delete'
  })
}

// 获得招录项目明细
export function getProjectDetailed(id) {
  return request({
    url: '/recruitment/project-detailed/get?id=' + id,
    method: 'get'
  })
}

// 获得招录项目明细分页
export function getProjectDetailedPage(query) {
  return request({
    url: '/recruitment/project-detailed/page',
    method: 'get',
    params: query
  })
}

// 导出招录项目明细 Excel
export function exportProjectDetailedExcel(query) {
  return request({
    url: '/recruitment/project-detailed/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得招录项目明细集合
export function getProjectDetailedList(query) {
  return request({
    url: '/recruitment/project-detailed/get-project-detaileds',
    method: 'get',
    params: query
  })
}
