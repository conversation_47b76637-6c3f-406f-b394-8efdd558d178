<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :span="5">
        <div class="tree-wrapper">
          <el-tree
            ref="tree"
            :data="tree"
            :props="{ children: 'children', label: 'name' }"
            highlight-current
            node-key="id"
            :expand-on-click-node="false"
            @node-click="handlePointClick"
            default-expand-all
          ></el-tree>
        </div>
      </el-col>

      <el-col :span="14" id="question-wrapper">
        <div v-if="curQuestion" class="question-item">
          <div class="question-item-header">
            <span class="NO">{{ current + 1 }}、</span>
            <span class="type"
              >【<dict-tag
                :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                :value="curQuestion.questionType"
              />】</span
            >
            <span class="title" v-if="curQuestion.questionType !== '3'">
              <text-to-html
                :text="curQuestion.content.title"
                :images="getPreviewImages(curQuestion.content.titleImages)"
              ></text-to-html
              >（ ）
            </span>
            <el-tag
              type="success"
              effect="dark"
              size="small"
              style="cursor: pointer"
              @click="setOpen(true)"
            >
              错题反馈
            </el-tag>
            <div class="compat-choice" v-if="curQuestion.questionType === '3'">
              <div v-for="(choice, key) in curQuestion.content.choiceList">
                {{ key }}、<text-to-html
                  :text="choice"
                  :images="
                    getPreviewImages(
                      (curQuestion.content.choiceImages || {})[key]
                    )
                  "
                ></text-to-html>
              </div>
            </div>
          </div>

          <template
            v-if="['1', '2', '8'].indexOf(curQuestion.questionType) > -1"
          >
            <div class="question-item-cont">
              <div
                v-if="
                  curQuestion.questionType === '1' ||
                  curQuestion.questionType === '8'
                "
              >
                <el-radio-group v-model="curQuestion.answerResult">
                  <el-radio
                    v-for="(choice, key) in curQuestion.content.choiceList"
                    :label="key"
                    :key="key"
                    :class="{
                      'wrong-select':
                        curQuestion.answerResultStatus === '0' &&
                        key === curQuestion.answerResult,
                    }"
                  >
                    {{ key }}、<text-to-html
                      :text="choice"
                      :images="
                        getPreviewImages(
                          (curQuestion.content.choiceImages || {})[key]
                        )
                      "
                    ></text-to-html>
                  </el-radio>
                </el-radio-group>
              </div>
              <div v-if="curQuestion.questionType === '2'">
                <el-checkbox-group v-model="curQuestion.answerResult">
                  <el-checkbox
                    v-for="(choice, key) in curQuestion.content.choiceList"
                    :label="key"
                    :key="key"
                    :class="{
                      'wrong-select':
                        curQuestion.answerResultStatus === '0' &&
                        curQuestion.rightAnswer.indexOf(key) < 0 &&
                        curQuestion.answerResult.indexOf(key) > -1,
                    }"
                  >
                    {{ key }}、<text-to-html
                      :text="choice"
                      :images="
                        getPreviewImages(
                          (curQuestion.content.choiceImages || {})[key]
                        )
                      "
                    ></text-to-html>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
            <div style="height: 35px; padding-left: 26px">
              <el-button
                v-if="!curQuestion.rightAnswer"
                type="primary"
                @click="submitAnswer"
              >
                确定
              </el-button>
            </div>
            <div v-if="curQuestion.rightAnswer" class="question-item-answer">
              正确答案：
              {{
                curQuestion.questionType === "8"
                  ? curQuestion.rightAnswer === "A"
                    ? "正确"
                    : "错误"
                  : curQuestion.rightAnswer
              }}
            </div>
            <div class="question-item-analysis" v-if="curQuestion.analysis">
              <strong>试题解析：</strong> {{ curQuestion.analysis }}
            </div>
          </template>

          <template v-if="curQuestion.questionType === '3'">
            <div class="sub-questions">
              <div class="question-item-cont">
                <div
                  v-for="(title, key, index) in curQuestion.content.titleList"
                  :key="key"
                  :class="{
                    'wrong-select':
                      curQuestion.answerResultStatus === '0' &&
                      (curQuestion.rightAnswer || '').split(',')[index] !==
                        curQuestion.answerResult[index],
                  }"
                >
                  {{ key }}、<text-to-html
                    :text="title"
                    :images="
                      getPreviewImages(
                        (curQuestion.content.titleListImages || {})[key]
                      )
                    "
                  ></text-to-html>
                  (
                  <el-select
                    class="compat-select"
                    size="mini"
                    v-model="curQuestion.answerResult[index]"
                  >
                    <el-option
                      v-for="(choice, key) in curQuestion.content.choiceList"
                      :label="key"
                      :value="key"
                    ></el-option>
                  </el-select>
                  )
                </div>
              </div>
            </div>
            <div style="height: 35px; padding-left: 26px">
              <el-button
                v-if="!curQuestion.rightAnswer"
                type="primary"
                @click="submitAnswer"
                >确定</el-button
              >
            </div>
            <div v-if="curQuestion.rightAnswer" class="question-item-answer">
              正确答案：
              <template
                v-for="ans in (curQuestion.rightAnswer || '')
                  .split(',')
                  .map((val, index) => `${index + 1})、${val}`)"
              >
                {{ ans }}
                <span style="display: inline-block; width: 20px"></span>
              </template>
            </div>
            <div class="question-item-analysis" v-if="curQuestion.analysis">
              <strong>试题解析：</strong> {{ curQuestion.analysis }}
            </div>
          </template>

          <template v-if="curQuestion.questionType === '4'">
            <div
              v-if="
                curQuestion.content.subsetTitles &&
                curQuestion.content.subsetTitles.length > 0
              "
              class="sub-questions"
            >
              <div
                v-for="(subTitle, index) in curQuestion.content.subsetTitles"
                :key="index"
                style="margin-top: 10px"
              >
                <div class="question-item-header">
                  <span class="NO">{{ index + 1 }}、</span>
                  <span class="title"
                    ><text-to-html
                      :text="subTitle.title"
                      :images="getPreviewImages(subTitle.titleImages)"
                    ></text-to-html
                    >（）</span
                  >
                </div>
                <div class="question-item-cont">
                  <el-checkbox-group v-model="curQuestion.answerResult[index]">
                    <el-checkbox
                      v-for="(choice, key) in subTitle.choiceList"
                      :label="key"
                      :key="key"
                      :class="{
                        'wrong-select':
                          curQuestion.answerResult[index] &&
                          getSubsetTitleChoiceStatus(
                            curQuestion.answerResult[index],
                            subTitle.answer,
                            key
                          ),
                      }"
                    >
                      {{ key }}、<text-to-html
                        :text="choice"
                        :images="
                          getPreviewImages((subTitle.choiceImages || {})[key])
                        "
                      ></text-to-html>
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
                <div
                  v-if="subTitle.answer && curQuestion.answerResultFormat"
                  class="question-item-answer"
                >
                  正确答案： {{ subTitle.answer }}
                </div>
                <div
                  class="question-item-analysis"
                  v-if="subTitle.analysis && curQuestion.answerResultFormat"
                  style="margin-bottom: 20px"
                >
                  <strong>试题解析：</strong> {{ subTitle.analysis }}
                </div>
              </div>
              <div style="height: 35px; padding-left: 26px; margin-top: 30px">
                <el-button
                  v-if="!curQuestion.rightAnswer"
                  type="primary"
                  @click="submitAnswer"
                  >确定</el-button
                >
              </div>
            </div>
          </template>

          <template v-if="curQuestion.questionType === '10'">
            <div class="question-item-cont">
              <div
                v-for="(result, index) in curQuestion.answerResult"
                class="question-tiankong-row"
              >
                <span>
                  第
                  {{
                    [
                      "一",
                      "二",
                      "三",
                      "四",
                      "五",
                      "六",
                      "七",
                      "八",
                      "九",
                      "十",
                    ][index]
                  }}
                  空
                </span>
                <el-input
                  :key="index"
                  v-model="curQuestion.answerResult[index]"
                  placeholder="请输入对应空答案"
                ></el-input>
              </div>
            </div>
            <div style="height: 35px; padding-left: 26px">
              <el-button
                v-if="!curQuestion.rightAnswer"
                type="primary"
                @click="submitAnswer"
              >
                确定
              </el-button>
            </div>
            <div v-if="curQuestion.rightAnswer" class="question-item-answer">
              正确答案：
              {{
                curQuestion.questionType === "8"
                  ? curQuestion.rightAnswer === "A"
                    ? "正确"
                    : "错误"
                  : curQuestion.rightAnswer
              }}
            </div>
            <div class="question-item-analysis" v-if="curQuestion.analysis">
              <strong>试题解析：</strong> {{ curQuestion.analysis }}
            </div>
          </template>

          <template
            v-if="
              curQuestion.questionType === '5' ||
              curQuestion.questionType === '6'
            "
          >
            <div class="question-item-cont" style="margin-bottom: 10px">
              <el-input
                v-model="curQuestion.answerResult"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 6 }"
                placeholder="请输入试题答案"
              ></el-input>
            </div>
            <div style="height: 35px; padding-left: 26px">
              <el-button
                v-if="!curQuestion.rightAnswer"
                type="primary"
                @click="submitAnswer"
              >
                确定
              </el-button>
            </div>
            <div v-if="curQuestion.rightAnswer" class="question-item-answer">
              正确答案：
              {{
                curQuestion.questionType === "8"
                  ? curQuestion.rightAnswer === "A"
                    ? "正确"
                    : "错误"
                  : curQuestion.rightAnswer
              }}
            </div>
            <div class="question-item-analysis" v-if="curQuestion.analysis">
              <strong>试题解析：</strong> {{ curQuestion.analysis }}
            </div>
          </template>

          <div class="collect-box">
            <i
              v-if="!collectInfo"
              class="el-icon-star-off"
              @click="handleCollect"
              title="收藏"
            ></i>
            <i
              v-else
              class="el-icon-star-on"
              title="取消收藏"
              @click="handleDelCollect"
            ></i>
          </div>
        </div>
        <el-empty v-else description="暂无数据"></el-empty>

        <div v-if="curQuestion" class="question-bottom-btns">
          <el-button
            type="primary"
            size="small"
            @click="handlePre"
            :disabled="current === 0"
          >
            上一题
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="handleNext"
            :disabled="current === questionsList.length - 1"
          >
            下一题
          </el-button>
          <div class="cur-total">
            {{ current + 1 }} / {{ questionsList.length }}
          </div>
        </div>
      </el-col>

      <el-col :span="5">
        <div class="examResult-left">
          <div class="left-header">
            <div class="left-title">答题卡</div>
            <div class="left-title-tips">
              <div class="right">正确</div>
              <div class="wrong">错误</div>
              <div class="yellow">主观判断</div>
            </div>
          </div>
          <div class="left-cont" v-if="questionsTypeList.length > 0">
            <div
              class="left-group"
              v-for="element in questionsTypeList"
              :key="element.questionType"
            >
              <div class="left-group-title">
                <dict-tag
                  :type="DICT_TYPE.EXAM_QUESTION_TYPE"
                  :value="element.questionType"
                />
              </div>
              <div class="left-group-cont">
                <a
                  v-for="(item, index) in element.list"
                  :class="
                    questionsList[item.queNo].answerResultFormat
                      ? questionsList[item.queNo].answerResultStatus === '0'
                        ? 'wrong'
                        : questionsList[item.queNo].answerResultStatus === '2'
                        ? 'yellow'
                        : 'right'
                      : ''
                  "
                  :key="index"
                  @click="handleJumpQue(item)"
                >
                  {{ index + 1 }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <feed-back
      :open="showFeedbackDialog"
      :curQuestion="curQuestion"
      :current="current"
      :pointPath="pointPath"
      @setOpen="setOpen"
    />
  </div>
</template>

<script>
import TextToHtml from "../components/TextToHtml";
import {
  getMyAutogenicTraining,
  getTreeByChildNodes,
  getQuestionsByPointId,
  getQuestionAnswer,
  getUserCollectInfo,
  createUserCollect,
  deleteUserCollect,
} from "@/api/exam/myAutogenictTraining";
import { Loading } from "element-ui";
import FeedBack from "./feedBack.vue";
import store from "@/store";
import { getAccessToken } from "@/utils/auth";

export default {
  name: "MyAutogenictTrainingDetail",
  components: { TextToHtml, FeedBack },
  data() {
    return {
      trainingInfo: {},
      // 知识点树
      tree: [],
      questionsList: [],
      questionsTypeList: [],
      current: 0,
      curQuestion: null,
      showFeedbackDialog: false,
      pointPath: "",
      collectInfo: null,
    };
  },
  created() {
    const trainingId = this.$route.params && this.$route.params.trainingId;
    getMyAutogenicTraining({ id: trainingId }).then((res) => {
      this.trainingInfo = res.data || {};
      this.getTree(this.trainingInfo.pointIds);
    });
  },
  methods: {
    getPreviewImages(images) {
      return (images || []).map((item) => {
        const url = `${process.env.VUE_APP_BASE_API}${
          item.url
        }?token=${getAccessToken()}`;
        console.log("getPreviewImages====", url);
        return url;
      });
    },
    getSubsetTitleChoiceStatus(answerResult, answer, key) {
      // console.log("getSubsetTitleChoiceStatus", answerResult, answer, key);
      const answerWrong = [...answerResult].sort().join("") !== answer;
      return answerWrong && answerResult.indexOf(key) > -1;
    },
    /** 查询知识点树 */
    getTree(pointIds = "") {
      return getTreeByChildNodes({ pointIds: pointIds }).then((res) => {
        this.tree = res.data;
        const firstLeafNode = this.findFirstLeafNode(this.tree);

        this.getQuestions(firstLeafNode.id);
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(firstLeafNode.id);
        });
      });
    },

    findFirstLeafNode(tree) {
      if (!tree || tree.length === 0) {
        return null;
      }

      for (let node of tree) {
        if (!node.children || node.children.length === 0) {
          return node;
        } else {
          const childLeaf = this.findFirstLeafNode(node.children);
          if (childLeaf) {
            return childLeaf;
          }
        }
      }

      return null;
    },

    getPathById(tree, id, path = []) {
      for (const node of tree) {
        if (node.id === id) {
          return [node.name, ...path];
        }
        if (node.children) {
          const childPath = this.getPathById(node.children, id, [
            node.name,
            ...path,
          ]);
          if (childPath.length > 0) {
            return childPath;
          }
        }
      }
      return [];
    },

    handlePointClick(data, node) {
      if (node && !node.isLeaf) {
        return;
      }
      const pointId = data.id;
      this.getQuestions(pointId);
    },
    getQuestions(pointId) {
      const path = this.getPathById(this.tree, pointId);
      this.pointPath = path.reverse().join(" - ");

      this.current = 0;
      let loadingInstance = Loading.service({
        target: document.getElementById("question-wrapper"),
      });
      getQuestionsByPointId({ pointId }).then((res) => {
        const list = res.data || [];
        list.sort((a, b) => {
          if (a.questionType < b.questionType) {
            return -1;
          }
          if (a.questionType > b.questionType) {
            return 1;
          }
          return 0;
        });
        // console.log("list ==", list);
        const obj = {};
        list.forEach((question, index) => {
          question.queNo = index;
          try {
            question.content = JSON.parse(question.content);
          } catch (e) {
            // console.log("catch===", index, e);
            question.content = { title: "", choiceList: [] };
          }
          question.answerResult = "";
          if (question.questionType === "2") {
            question.answerResult = [];
          }
          if (question.questionType === "3") {
            question.answerResult = [];
          }
          if (question.questionType === "4") {
            question.answerResult = (question.content?.subsetTitles || []).map(
              () => []
            );
          }
          if (question.questionType === "10") {
            // 匹配中文括号对（）或英文括号对()或下划线__________
            const regex = /(?:（）|\(\)|_{6,})/g;
            const matches = question.content?.title.match(regex);
            question.answerResult =
              matches?.map((item) => item.replace(/（|）|\(|\)|_/g, "")) || [];
          }

          if (obj[question.questionType]) {
            obj[question.questionType].push(question);
          } else {
            obj[question.questionType] = [question];
          }
        });
        loadingInstance.close();
        this.questionsList = list;
        if (this.questionsList.length > 0) {
          this.curQuestion = this.questionsList?.[this.current] || {};
          // console.log("this.curQuestion===", this.curQuestion);
          this.getUserCollectInfo();
        } else {
          this.curQuestion = null;
        }

        const arr = [];
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            arr.push({ questionType: key, list: obj[key] });
          }
        }
        this.questionsTypeList = arr;
        // console.log("this.questionsTypeList===", this.questionsTypeList);
      });
    },
    async queryQuestionAnswer() {
      const res = await getQuestionAnswer({ id: this.curQuestion.id });
      // console.log("res===", res);
      const { data } = res;
      return data;
    },
    getUserCollectInfo() {
      getUserCollectInfo({ questionId: this.curQuestion.id }).then((res) => {
        this.collectInfo = res.data;
        // console.log("this.collectInfo===", this.collectInfo);
      });
    },
    handlePre() {
      this.current = this.current - 1;
      this.curQuestion = this.questionsList?.[this.current] || {};
      this.getUserCollectInfo();
    },
    handleNext() {
      this.current = this.current + 1;
      this.curQuestion = this.questionsList?.[this.current] || {};
      console.log("handleNext curQuestion===", this.curQuestion);
      this.getUserCollectInfo();
    },
    handleJumpQue(item) {
      // console.log("handleJumpQue item==", item);
      this.current = item.queNo;
      this.curQuestion = this.questionsList?.[item.queNo];
      this.getUserCollectInfo();
      // console.log("handleJumpQue curQuestion===", this.curQuestion);
    },
    async submitAnswer() {
      const _curQuestion = JSON.parse(JSON.stringify(this.curQuestion));
      // console.log("submitAnswer==", _curQuestion);
      const { answerResult, questionType } = _curQuestion;
      let val = answerResult;
      if (questionType == 2) {
        val = answerResult.join("");
      }
      if (questionType == 3 || questionType == 10) {
        val = answerResult.join(",");
      }
      if (questionType == 4) {
        val = answerResult.map((item) => item.join(",")).join(",");
        // console.log("4 val", val);
      }

      const result = await this.queryQuestionAnswer();
      this.$nextTick(() => {
        let answerResultStatus = "1";
        if (result.answer !== val) {
          answerResultStatus = "0"; // 0错误  1正确
        }
        if (["5", "6", "9", "10"].indexOf(questionType) > -1) {
          answerResultStatus = "2"; // 主观判断
        }

        this.questionsList[this.current].answerResult = answerResult;
        this.questionsList[this.current].rightAnswer = result.answer;
        this.questionsList[this.current].analysis = result.analysis;
        this.questionsList[this.current].answerResultFormat = val;
        this.questionsList[this.current].answerResultStatus =
          answerResultStatus;

        _curQuestion.answerResultFormat = val;
        _curQuestion.rightAnswer = result.answer;
        _curQuestion.answer = result.answer;
        _curQuestion.analysis = result.analysis;
        _curQuestion.answerResultStatus = answerResultStatus;

        this.curQuestion = _curQuestion;
        console.log("this.curQuestion=====", this.curQuestion);
        this.$forceUpdate();
      });
    },
    setOpen(flag) {
      this.showFeedbackDialog = flag;
    },
    handleCollect() {
      const params = {
        questionId: this.curQuestion.id,
        userId: store.getters.userId,
      };
      createUserCollect(params).then((res) => {
        this.$message.success("收藏成功");
        this.getUserCollectInfo();
      });
    },

    handleDelCollect() {
      deleteUserCollect(this.collectInfo.id).then((res) => {
        this.$message.success("取消收藏成功");
        this.getUserCollectInfo();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tree-wrapper {
  border: 1px solid #e6ebf2;
  height: calc(100vh - 130px);
  overflow: auto;
  padding: 5px 0;
}

.examResult-left {
  height: calc(100vh - 130px);
  background: #fff;
  border: 1px solid #e6ebf2;
  position: relative;
  display: flex;
  flex-direction: column;

  .left-header {
    padding: 10px 15px;
    border-bottom: 1px #eee solid;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-title {
      font-size: 16px;
    }

    .left-title-tips {
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .right,
      .wrong,
      .yellow {
        position: relative;
        padding-left: 15px;
      }
      .right {
        margin-left: 15px;
        &::before {
          content: " ";
          display: inline-block;
          width: 10px;
          height: 10px;
          background: #409eff;
          position: absolute;
          left: 0;
          top: 3px;
        }
      }
      .wrong {
        margin-left: 15px;
        &::before {
          content: " ";
          display: inline-block;
          width: 10px;
          height: 10px;
          background: #f56c6c;
          position: absolute;
          left: 0;
          top: 3px;
        }
      }
      .yellow {
        margin-left: 15px;
        &::before {
          content: " ";
          display: inline-block;
          width: 10px;
          height: 10px;
          background: #f2ae03;
          position: absolute;
          left: 0;
          top: 3px;
        }
      }
    }
  }

  .left-cont {
    height: calc(100% - 50px);
    padding: 15px;
    overflow-y: auto;

    .left-group {
      .left-group-title {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        position: relative;
        padding-left: 12px;

        &::before {
          content: " ";
          display: inline-block;
          width: 5px;
          height: 14px;
          background: #409eff;
          position: absolute;
          left: 0;
          top: 3px;
        }
      }
      .left-group-cont {
        padding-top: 10px;
        padding-bottom: 20px;
        margin-right: -10px;

        a {
          display: inline-block;
          width: 30px;
          height: 30px;
          text-align: center;
          line-height: 30px;
          background: #eee;
          color: #333;
          font-size: 14px;
          margin-right: 10px;
          margin-bottom: 10px;
          cursor: pointer;

          &.right {
            background: #409eff;
            color: #fff;
          }

          &.wrong {
            background: #f56c6c;
            color: #fff;
          }

          &.yellow {
            background: #f2ae03;
            color: #fff;
          }
        }
      }
    }
  }

  .left-bottom {
    height: 50px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    position: absolute;
    width: 100%;
    bottom: 0;

    .el-button {
      width: 100%;
    }
  }
}

.question-item {
  margin-bottom: 30px;
  font-size: 15px;
  position: relative;
  padding-right: 35px;

  .question-item-header {
    .type {
      color: dodgerblue;
    }
    .score {
      color: #999;
    }
  }
  .question-item-cont {
    padding-top: 15px;
    padding-left: 25px;
    .el-radio {
      display: block;
      margin-bottom: 10px;
      width: fit-content;
    }
    .el-checkbox {
      display: block;
      margin-bottom: 10px;
      width: fit-content;
    }
    .wrong-select {
      color: #f56c6c;

      ::v-deep .el-radio__inner,
      ::v-deep .el-checkbox__inner {
        border-color: #f56c6c;
        background: #f56c6c;
      }
      ::v-deep .el-radio__label,
      ::v-deep .el-checkbox__label {
        color: #f56c6c;
      }

      ::v-deep .el-input__inner {
        color: #f56c6c;
        border-color: #f56c6c;
      }

      ::v-deep .el-select__caret {
        color: #f56c6c;
      }
    }

    .question-tiankong-row {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      span {
        display: inline-block;
        width: 60px;
        margin-right: 10px;
        text-align: right;
      }
    }
  }
  .question-item-answer {
    padding: 10px 0 0 25px;
  }
  .question-item-analysis {
    padding: 10px 0 0 25px;
  }
  .el-radio-group {
    display: block;
  }

  .collect-box {
    position: absolute;
    display: inline-block;
    right: 10px;
    top: 5px;

    i {
      color: #409eff;
      font-size: 20px;
      cursor: pointer;
    }
  }
}

.question-bottom-btns {
  text-align: center;
  position: relative;

  .cur-total {
    position: absolute;
    display: inline-block;
    left: 0;
    top: 2px;
  }
}
</style>
