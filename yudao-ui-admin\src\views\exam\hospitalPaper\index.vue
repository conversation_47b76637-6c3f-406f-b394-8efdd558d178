<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" size="small" label-width="70px" :model="queryParams" :inline="true" ref="queryForm">
      <el-form-item label="试卷名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入试卷名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="学员类型">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" clearable>
          <el-option v-for="dict in studentTypeList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="组卷方式">
        <el-select v-model="queryParams.generationMethod" placeholder="请选择组卷方式" clearable>
          <el-option v-for="dict in getDictDatas(DICT_TYPE.EXAM_GENERATION_METHOD)"
                     :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="考试类型">
        <el-select v-model="queryParams.examePaperType" placeholder="请选择考试类型" clearable>
          <el-option v-for="dict in getDictDatas(DICT_TYPE.HOSPITAL_EXAM_TYPE)"
                     :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['exam:hospital-paper:create']">新增</el-button>
        <el-button plain icon="el-icon-unlock" size="mini" :disabled="!closeIds" @click="openPaperList"
                   v-hasPermi="['exam:hospital-paper:status']">开启</el-button>
        <el-button plain icon="el-icon-lock" size="mini" :disabled="!openIds" @click="closePaperList"
                   v-hasPermi="['exam:hospital-paper:status']">关闭</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" fixed="left"></el-table-column>
      <el-table-column label="试卷名称" align="center" prop="name" width="200px" show-overflow-tooltip fixed="left" />
      <el-table-column label="总分" align="center" prop="totalScore" />
      <el-table-column label="及格分" align="center" prop="passScore" />
      <el-table-column label="答题时间" align="center" prop="answerMinuteTime" />
      <el-table-column label="开始时间" align="center" prop="beginTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.beginTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="组卷方式" align="center" prop="generationMethod">
        <template slot-scope="scope">
          {{ getDictDataLabel(DICT_TYPE.EXAM_GENERATION_METHOD, scope.row.generationMethod) }}
        </template>
      </el-table-column>
      <el-table-column label="考试类型" align="center" prop="examePaperType">
        <template slot-scope="scope">
          {{ getDictDataLabel(DICT_TYPE.HOSPITAL_EXAM_TYPE, scope.row.examePaperType) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" fixed="right">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :inactive-value="0"
            :active-value="1"
            :disabled="!$auth.hasPermi('exam:hospital-paper:status')"
            @change="handleChangeStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300px" fixed="right">
        <template slot-scope="{row}">
          <el-button v-if="row.answerResultCount > 0" size="mini" type="text" icon="el-icon-view"
                     @click="handleView(row)" v-hasPermi="['exam:hospital-paper:update']">查看</el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(row)"
                     v-hasPermi="['exam:hospital-paper:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-set-up"
                     @click="handleConfig(row)" v-hasPermi="['exam:hospital-paper:update']">配置</el-button>
          <el-button v-if="row.paperConfig.isAuthentication || row.paperConfig.isPreventionCheat" size="mini" type="text"
                     icon="el-icon-key" @click="handleSafe(row)" v-hasPermi="['exam:hospital-paper:update']">安全</el-button>
          <el-button size="mini" type="text" icon="el-icon-user" @click="handleCandidate(row)"
                     v-hasPermi="['exam:imitate-paper:update']">参考人员</el-button>
          <el-dropdown style="margin-left: 10px">
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-hasPermi="['exam:hospital-paper:update']"
                icon="el-icon-data-analysis"
                @click.native="handleAnalysis(row)"
              >分析</el-dropdown-item>
              <el-dropdown-item
                v-if="row.generationMethod === 'fixed'"
                v-hasPermi="['exam:hospital-paper:export']"
                icon="el-icon-download"
                @click.native="handleExport(row)"
              >导出</el-dropdown-item>
              <el-dropdown-item
                v-hasPermi="['exam:hospital-paper:create']"
                icon="el-icon-copy-document"
                @click.native="handleCopy(row)"
              >复制</el-dropdown-item>
              <el-dropdown-item
                v-hasPermi="['exam:hospital-paper:delete']"
                icon="el-icon-delete"
                @click.native="handleDelete(row)"
              >删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <paper-composition-dialog :type="type" ref="paperCompositionDialog" @saved="getList"></paper-composition-dialog>

    <paper-safe-center-dialog ref="paperSafeCenterDialog"></paper-safe-center-dialog>

    <paper-config-dialog ref="paperConfigDialog" @saved="getList"></paper-config-dialog>

    <paper-candidate-drawer ref="paperCandidateDrawer" type="imitate"></paper-candidate-drawer>

    <paper-analysis-dialog ref="paperAnalysisDialog"></paper-analysis-dialog>
  </div>
</template>

<script>
import { closePaper, closePaperList, deletePaper, getPaperPage, openPaper, openPaperList, copyPaper, exportPaperWord } from "@/api/exam/paper";
import { getCurrentUserStudentTypes } from '@/api/system/permission';
import PaperCompositionDialog from '../components/paper-composition-dialog';
import PaperSafeCenterDialog from '../components/paper-safe-center-dialog';
import PaperConfigDialog from '../components/paper-config-dialog';
import PaperCandidateDrawer from '../components/paper-candidate-drawer';
import PaperAnalysisDialog from '../components/paper-analysis-dialog';

const TYPE = "hospital";

export default {
  name: "paper-hospital",
  components: { PaperCompositionDialog, PaperSafeCenterDialog, PaperConfigDialog, PaperCandidateDrawer, PaperAnalysisDialog },
  data() {
    return {
      type: TYPE,
      // 学员类型列表
      studentTypeList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        studentType: "",
        beginTime: [],
        endTime: [],
        generationMethod: null,
        examePaperType: null,
        departmentId: null,
        rotationDepartmentId: null,
        status: null,
      },
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考卷管理列表
      list: [],
      // 选中的试卷
      selectedPapers: [],
    };
  },
  computed: {
    openIds() {
      const ids = this.selectedPapers.filter(item => item.status === 1).map(item => item.id);
      return this.selectedPapers.length === ids.length ? ids.join(",") : "";
    },
    closeIds() {
      const ids = this.selectedPapers.filter(item => item.status !== 1).map(item => item.id);
      return this.selectedPapers.length === ids.length ? ids.join(",") : "";
    },
  },
  created() {
    getCurrentUserStudentTypes({ 'component': 'exam/hospitalPaper/index' })
      .then(res => this.studentTypeList = res.data);
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getPaperPage(this.queryParams, TYPE).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 开启关闭试卷 */
    handleChangeStatus(row) {
      if (row.status === 1) {
        openPaper(row.id, TYPE).then(() => {
          this.$message.success("试卷" + row.name + "已开启");
        }).catch(() => {
          row.status = 0;
        })
      } else {
        closePaper(row.id, TYPE).then(() => {
          this.$message.success("试卷" + row.name + "已关闭");
        }).catch(() => {
          row.status = 0;
        })
      }
    },
    /** 表格选中 */
    handleSelectionChange(val) {
      this.selectedPapers = val;
    },
    /** 开启选中试卷 */
    openPaperList() {
      openPaperList(this.closeIds, TYPE).then(() => {
        this.getList();
        this.$message.success("选中试卷已开启");
      })
    },
    /** 关闭选中试卷 */
    closePaperList() {
      closePaperList(this.openIds, TYPE).then(() => {
        this.getList();
        this.$message.success("选中试卷已关闭");
      })
    },
    /** 新增试卷 */
    handleAdd() {
      this.$refs.paperCompositionDialog.handleAdd();
    },
    /** 编辑试卷 */
    handleUpdate(row) {
      this.$refs.paperCompositionDialog.handleUpdate(row);
    },
    /** 查看试卷 */
    handleView(row) {
      this.$refs.paperCompositionDialog.handleView(row);
    },
    /** 试卷更多配置 */
    handleConfig(row) {
      this.$refs.paperConfigDialog.handleConfig(row.id);
    },
    /** 安全中心 */
    handleSafe(row) {
      this.$refs.paperSafeCenterDialog.handleOpen(row);
    },
    /** 参考人员 */
    handleCandidate(row) {
      this.$refs.paperCandidateDrawer.handleOpen(row.id, row.name);
    },
    /** 试卷分析操作 */
    handleAnalysis(row) {
      this.$refs.paperAnalysisDialog.handleOpen(row.id);
    },
    /** 试卷复制操作 */
    handleCopy(row) {
      copyPaper(row.id, TYPE).then(() => {
        this.$message.success("复制试卷成功");
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除考卷' + row.name + '的数据项?').then(function() {
        return deletePaper(row.id, TYPE);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出试卷 */
    handleExport(row) {
      exportPaperWord(row.id, TYPE).then(response => {
        this.$download.word(response, row.name + '.docx')
      });
    },
  }
};
</script>

<style scoped lang="scss">
</style>
