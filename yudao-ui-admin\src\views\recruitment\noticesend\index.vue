<template>
  <div class="app-container noticesend">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" filterable placeholder="请选择学员类型">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                    :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item label="报名项目" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入报名项目" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="queryParams.sex" placeholder="请选择性别" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="学历" prop="highestAcademic">
        <el-select v-model="queryParams.highestAcademic" filterable placeholder="请选择学历">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
                    :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="职称" prop="positionalTitles">
        <el-select v-model="queryParams.positionalTitles" filterable placeholder="请选择职称">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)"
                    :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="时长" prop="recruitMonths">
        <el-input v-model="queryParams.recruitMonths" placeholder="请输入时长" clearable/>
      </el-form-item>
      <el-form-item label="工龄">
        <div class="seniority-box">
          <el-input v-model="queryParams.seniorities[0]" clearable/>
        </div>
        <div class="seniority-line">~</div>
        <div class="seniority-box">
          <el-input v-model="queryParams.seniorities[1]" clearable/>
        </div>
      </el-form-item>
      <el-form-item label="状态" prop="noticeSendStatus">
        <el-select v-model="queryParams.noticeSendStatus" filterable placeholder="请选择" clearable>
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.REGISTRATION_NOTICE_SEND_STATUS)"
                    :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-finished" size="mini" @click="handleBatchAudit"
                   v-hasPermi="['recruitment:noticesend:update']">批量发放通知书</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['recruitment:noticesend:update']">导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        width="55"
        fixed
      >
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="nickname" width="100" fixed>
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="viewUserInfo(scope.row)">{{ scope.row.nickname }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150" fixed class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleSend(scope.row)"
                     v-hasPermi="['recruitment:noticesend:update']">发放通知书</el-button>
          <el-button size="mini" type="text" @click="handleEdit(scope.row)"
                     v-hasPermi="['recruitment:noticesend:update']">信息修改</el-button>
        </template>
      </el-table-column>

      <el-table-column label="性别" align="center" prop="sex" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="学历" align="center" prop="highestAcademic" width="120">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_EDUCATION" :value="scope.row.highestAcademic" />
        </template>
      </el-table-column>
      <el-table-column label="职称" align="center" prop="positionalTitles" width="120">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES" :value="scope.row.positionalTitles" />
        </template>
      </el-table-column>
      <el-table-column label="工龄" align="center" prop="seniority" width="80" />
      <el-table-column label="工作单位" align="center" prop="selectedUnit" width="200" />
      <el-table-column label="报名项目" align="center" prop="projectName" width="180" />
      <el-table-column label="时长(月)" align="center" prop="recruitMonths" width="80" />
      <el-table-column label="提交时间" align="center" prop="commitTime" width="180" />
      <el-table-column label="学费(元)" align="center" prop="tuition" width="100" />
      <el-table-column label="备注" align="center" prop="remarks" />

    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 下发通知书 -->
    <el-dialog title="" :visible.sync="open" width="1000px" v-dialogDrag append-to-body>
      <div ref="noticeContent" id="noticeContent-box" v-html="noticeContent" style="padding: 0 50px;"></div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
        <el-button type="primary" @click="handleDown">下载</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </div>
    </el-dialog>

    <!--  批量下发通知书  -->
    <el-dialog title="批量下发确认" :visible.sync="openBatch" width="520px" v-dialogDrag append-to-body>
      <p>您正在进行批量下发通知书操作，请选择学员报到时间后提交！</p>
      <el-form ref="batchForm" :model="batchForm" :rules="batchRules" label-width="80px">
        <el-form-item label="报到时间" prop="reportTime">
          <el-date-picker
            v-model="batchForm.reportTime"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="openBatch = false">再考虑一下，关闭</el-button>
        <el-button type="primary" :loading="batchSubmitting" @click="handleBatchSend">确定批量下发，提交</el-button>
      </div>
    </el-dialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="editTitle" :visible.sync="openEdit" width="500px" v-dialogDrag append-to-body>
      <el-form ref="editform" :model="editForm" :rules="editRules" label-width="100px">
          <el-form-item label="报名项目：" prop="recruitmentProjectId">
              <el-select v-model="editForm.recruitmentProjectId" @change="handleProjectChange" style="width: 100%;">
                <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="选择时长：" prop="recruitmentProjectDetailed">
              <el-select v-model="editForm.recruitmentProjectDetailed" style="width: 100%;">
                <el-option v-for="item in projectDetailedList" :key="item.id" :label="`${item.recruitMonths}个月`" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学费：" prop="detailedTuition">
              <el-input-number :controls="false" disabled :value="curRow.tuition" style="width: 100%;"></el-input-number>
            </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelEdit">关闭</el-button>
        <el-button type="primary" @click="submitEditForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { getProjectsByPlanId, getPlanProjectDetailedList } from "@/api/recruitment/registration";
import { exportSuperviseFormExcel } from "@/api/rotation/superviseForm";
import { updateRegistrationinfo } from "@/api/recruitment/check";
import { getNoticesendPage, getNoticeContent, sendNotice, getRegistration, batchSendNotice } from "@/api/recruitment/noticesend";

export default {
  name: "Noticesend",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督导表单列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        studentType: '',
        projectName: '',
        sex: '',
        highestAcademic: '',
        positionalTitles: '',
        recruitMonths: '',
        seniorities: ['', ''],
        noticeSendStatus: 'to_be_issued'
      },
      detailInfo: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      noticeContent: '',
      time1: '',
      time2: '',

      editTitle: '',
      openEdit: false,
      editRules: {
        recruitmentProjectId: [{ required: true, message: "请选择", trigger: "change" }],
        recruitmentProjectDetailed: [{ required: true, message: "请选择", trigger: "change" }],
      },
      editForm: {},
      projectList: [],
      projectDetailedList: [],
      curRow: {},
      planId: '',

      multipleSelection: [],
      openBatch: false,
      batchForm: {},
      batchRules: {
        reportTime: [{ required: true, message: "请选择报到时间", trigger: "change" }]
      },
      batchSubmitting: false,
    };
  },
  created() {
    this.getList();

  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.list = []
      // 执行查询
      getNoticesendPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    handleEdit(row){
      this.curRow = row
      this.editTitle = `修改信息-${row.nickname}`
      getRegistration({id: row.recruitmentRegistrationId}).then(res => {
        const data = res.data
        const { planId, recruitmentProjectId } = data
        this.planId = planId
        getProjectsByPlanId(planId).then(res => {
          this.projectList = res.data;

          this.handleProjectChange(recruitmentProjectId)

          this.editForm = data
          this.openEdit = true
        });
      })
    },

    cancelEdit(){
      this.openEdit = false
    },

    handleProjectChange(value) {
      getPlanProjectDetailedList(this.planId, value).then(res => {
        this.projectDetailedList = res.data;
      });
    },

    viewUserInfo(row){
      getRegistration({id: row.recruitmentRegistrationId}).then(res => {
        const data = res.data
        const { planId } = data
        this.$router.push({
          path: 'personalInfo',
          query: { planId, recruitmentRegistrationId: row.recruitmentRegistrationId },
        });
      })
    },

    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.noticeContent = ''
    },
    /** 表单重置 */
    reset() {
      this.form = {
        formType: '1',
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    updateContent(event) {
      // debugger
      console.log('updateContent==', event)
      // this.noticeContent = event.target.innerHTML;
    },

    handleSend(row){
      getNoticeContent(row.recruitmentRegistrationId).then(res => {
        this.detailInfo = res.data
        this.noticeContent = res.data.noticeContent

        this.open = true;
      })
    },

    /** 提交按钮 */
    submitForm() {
      const htmlContent = this.$refs.noticeContent.innerHTML;
      console.log('htmlContent===', htmlContent)
      const params = {
        noticeContent: htmlContent,
        recruitmentRegistrationId: this.detailInfo.recruitmentRegistrationId
      }
      //提交
      sendNotice(params).then(response => {
        this.$modal.msgSuccess("操作成功");
        this.open = false;
        this.getList();
      });
    },

    /** 批量发放通知书选中 */
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    resetBatchForm() {
      this.batchForm = { reportTime: [] };
      this.resetForm("batchForm");
    },

    handleBatchAudit(){
      if (this.multipleSelection.length === 0) {
        this.$modal.msgWarning("请选择至少一项操作");
        return;
      }
      this.resetBatchForm();
      this.openBatch = true;
    },

    /** 批量发放通知书 */
    handleBatchSend() {
      this.$refs.batchForm.validate(valid => {
        if (valid) {
          this.batchSubmitting = true;
          batchSendNotice({
            recruitmentRegistrationIds: this.multipleSelection.map(item => item.recruitmentRegistrationId),
            reportBeginTime: this.batchForm.reportTime[0],
            reportEndTime: this.batchForm.reportTime[1],
          }).then(() => {
            this.$modal.msgSuccess("操作成功");
            this.openBatch = false;
            this.getList();
          }).finally(() => this.batchSubmitting = false);
        }
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有督导表单数据项?').then(() => {
          this.exportLoading = true;
          return exportSuperviseFormExcel(params);
        }).then(response => {
          this.$download.excel(response, '督导表单.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    },

    submitEditForm(){
      this.$refs["editform"].validate(valid => {
        if (!valid) {
          return;
        }

        const params = {
          id: this.curRow.recruitmentRegistrationId,
          recruitmentProjectDetailed: this.editForm.recruitmentProjectDetailed,
          recruitmentProjectId: this.editForm.recruitmentProjectId
        }

        updateRegistrationinfo(params).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.openEdit = false;
          this.getList();
        });
      });
    },

    handleDown(){
      this.exportPDF('noticeContent-box', '进修人员报道通知书')
    },

    exportPDF(tableId, fileName){
      const table = document.getElementById(tableId);
      html2canvas(table).then(canvas => {
        // debugger
        const contentWidth = canvas .width;
        const contentHeight = canvas.height;
        const pageHeight = contentWidth / 592.28 * 841.89;
        let leftHeight = contentHeight;
        let position = 0;
        const imgWidth = 595.28;
        const imgHeight = 592.28 / contentWidth * contentHeight;
        const pageData = canvas .toDataURL('image/jpeg', 1.0);
        const pdf = new jsPDF( '', 'pt','a4');
        if (leftHeight < pageHeight) {
          pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;

            if (leftHeight > 0) {
              pdf.addPage();
            }
          }
        }
        pdf.save(`${fileName}.pdf`);
      })
    }
  }
};
</script>

<style lang="scss">
.noticesend {
  position: relative;

  .top-radio-box{
    position: relative;
    margin-bottom: 15px;
    &::before{
      position: absolute;
      content: ' ';
      display: block;
      width: 100%;
      height: 1px;
      background: #ddd;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .el-radio-button:first-child .el-radio-button__inner{
      border-radius: 4px 0 0 0;
    }
    .el-radio-button:last-child .el-radio-button__inner{
      border-radius: 0 4px 0 0;
    }
  }

  .seniority-box{
    display: inline-block;
    width: 80px;
  }
  .seniority-line{
    display: inline-block;
    width: 25px;
    text-align: center;
  }
}

.superviseForm-indicator-dialog{

.el-dialog__body{
  padding-top: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.indicators-wapper{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .indicators-wapper-head{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;
    border-bottom: 1px #ddd solid;
  }

  .indicators-wapper-tables{
    max-height: calc(100vh - 240px);
    flex: auto;
    padding-top: 13px;
    overflow-y: auto;
  }
}
}
</style>
