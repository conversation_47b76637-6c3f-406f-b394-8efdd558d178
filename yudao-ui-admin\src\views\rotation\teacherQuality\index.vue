<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="带教老师" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入带教老师" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item label="教研室" prop="staffRoomValue" label-width="80px">
          <el-select v-model="queryParams.staffRoomValue" filterable clearable placeholder="请选择教研室">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.STAFF_ROOM)"
            :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
      </el-form-item>

      <el-form-item label="科室" prop="departmentId">
        <el-select v-model="queryParams.departmentId" filterable clearable placeholder="请选择医院科室" size="small">
          <el-option
              v-for="item in departmentOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="统计时间" prop="dates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.dates"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:teacher-quality:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="科室" align="center" width="100" prop="departmentName" fixed />
      <el-table-column label="带教老师" align="center" width="80" prop="nickname" fixed />
      <el-table-column label="带教数量" align="center" width="80" prop="stuCnt" fixed />
      <el-table-column label="学生评价带教汇总" align="center" width="80" prop="studentAppraiseTeacher" fixed />

      <el-table-column label="小讲课授课数量" align="center" min-width="80" prop="active1Count" >
        <template slot-scope="scope">
          <span>{{ scope.row.active1Count === null ? '--' : scope.row.active1Count }}</span>
        </template>
      </el-table-column>
      <el-table-column label="小讲课学生评价授课汇总" align="center" min-width="80" prop="active1Comments" >
        <template slot-scope="scope">
          <span>{{ scope.row.active1Comments === null ? '--' : scope.row.active1Comments }}</span>
        </template>
      </el-table-column>
      <el-table-column label="小讲课督导听课评价汇总" align="center" min-width="80" prop="active1Opinions" >
        <template slot-scope="scope">
          <span>{{ scope.row.active1Opinions === null ? '--' : scope.row.active1Opinions }}</span>
        </template>
      </el-table-column>

      <el-table-column label="教学查房授课数量" align="center" min-width="80" prop="active2Count" >
        <template slot-scope="scope">
          <span>{{ scope.row.active2Count === null ? '--' : scope.row.active2Count }}</span>
        </template>
      </el-table-column>
      <el-table-column label="教学查房学生评价授课汇总" align="center" min-width="80" prop="active2Comments" >
        <template slot-scope="scope">
          <span>{{ scope.row.active2Comments === null ? '--' : scope.row.active2Comments }}</span>
        </template>
      </el-table-column>
      <el-table-column label="教学查房督导听课评价汇总" align="center" min-width="80" prop="active2Opinions" >
        <template slot-scope="scope">
          <span>{{ scope.row.active2Opinions === null ? '--' : scope.row.active2Opinions }}</span>
        </template>
      </el-table-column>

      <el-table-column label="病例讨论授课数量" align="center" min-width="80" prop="active3Count" >
        <template slot-scope="scope">
          <span>{{ scope.row.active3Count === null ? '--' : scope.row.active3Count }}</span>
        </template>
      </el-table-column>
      <el-table-column label="病例讨论学生评价授课汇总" align="center" min-width="80" prop="active3Comments" >
        <template slot-scope="scope">
          <span>{{ scope.row.active3Comments === null ? '--' : scope.row.active3Comments }}</span>
        </template>
      </el-table-column>
      <el-table-column label="病例讨论督导听课评价汇总" align="center" min-width="80" prop="active3Opinions" >
        <template slot-scope="scope">
          <span>{{ scope.row.active3Opinions === null ? '--' : scope.row.active1Score }}</span>
        </template>
      </el-table-column>

      <el-table-column label="技能操作授课数量" align="center" min-width="80" prop="active4Count" >
        <template slot-scope="scope">
          <span>{{ scope.row.active4Count === null ? '--' : scope.row.active4Count }}</span>
        </template>
      </el-table-column>
      <el-table-column label="技能操作学生评价授课汇总" align="center" min-width="80" prop="active4Comments" >
        <template slot-scope="scope">
          <span>{{ scope.row.active4Comments === null ? '--' : scope.row.active4Comments }}</span>
        </template>
      </el-table-column>
      <el-table-column label="技能操作督导听课评价汇总" align="center" min-width="80" prop="active4Opinions" >
        <template slot-scope="scope">
          <span>{{ scope.row.active4Opinions === null ? '--' : scope.row.active4Opinions }}</span>
        </template>
      </el-table-column>

      <el-table-column label="理论大课授课数量" align="center" min-width="80" prop="hospitalTraining3Count" >
        <template slot-scope="scope">
          <span>{{ scope.row.hospitalTraining3Count === null ? '--' : scope.row.hospitalTraining3Count }}</span>
        </template>
      </el-table-column>
      <el-table-column label="理论大课学生评价授课汇总" align="center" min-width="80" prop="hospitalTraining3Comments" >
        <template slot-scope="scope">
          <span>{{ scope.row.hospitalTraining3Comments === null ? '--' : scope.row.hospitalTraining3Comments }}</span>
        </template>
      </el-table-column>
      <el-table-column label="理论大课督导听课评价汇总" align="center" min-width="80" prop="hospitalTraining3Opinions" >
        <template slot-scope="scope">
          <span>{{ scope.row.hospitalTraining3Opinions === null ? '--' : scope.row.hospitalTraining3Opinions }}</span>
        </template>
      </el-table-column>

      <el-table-column label="实验课程授课数量" align="center" min-width="80" prop="hospitalTraining5Count" >
        <template slot-scope="scope">
          <span>{{ scope.row.hospitalTraining5Count === null ? '--' : scope.row.hospitalTraining5Count }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实验课程学生评价授课汇总" align="center" min-width="80" prop="hospitalTraining5Comments" >
        <template slot-scope="scope">
          <span>{{ scope.row.hospitalTraining5Comments === null ? '--' : scope.row.hospitalTraining5Comments }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实验课程督导听课评价汇总" align="center" min-width="80" prop="hospitalTraining5Opinions" >
        <template slot-scope="scope">
          <span>{{ scope.row.hospitalTraining5Opinions === null ? '--' : scope.row.hospitalTraining5Opinions }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
  </div>
</template>

<script>
import { getDepartmentSimpleList } from "@/api/system/department";
import { getTeacherQualityPage, exportTeacherQuality } from "@/api/rotation/teacherQuality";

export default {
  name: "TeacherQuality",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员入科列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 50,
        nickname: '',
        staffRoomValue: '',
        departmentId: '',
        dates: []
      },
      // 表单参数
      departmentOptions: [],
      currentRow: {}
    };
  },
  created() {
    this.getList();
    this.getDepartment();
  },
methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getTeacherQualityPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then(res => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有数据项?').then(() => {
          this.exportLoading = true;
          return exportTeacherQuality(params);
        }).then(response => {
          this.$download.excel(response, '带教教学质量.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
<style lang="scss">
</style>
