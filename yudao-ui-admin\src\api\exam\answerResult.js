import request from '@/utils/request'

// 获得考试试卷，开始考试
export function getPaper(query) {
  return request({
    url: '/exam/answer-result/get-exam-paper',
    method: 'get',
    params: query
  })
}

// 创建考试答卷结果
export function createResult(data) {
  return request({
    url: '/exam/answer-result/create',
    method: 'post',
    data: data
  })
}

// 根据考试对象获得最终考试答卷结果
export function getAnswerFinalResult(query) {
  return request({
    url: '/exam/answer-result/get-final-by-exam-object-id',
    method: 'get',
    params: query
  })
}

// 根据考试对象获得考试答卷结果列表
export function getAnswerResultList(query) {
  return request({
    url: '/exam/answer-result/list-by-exam-object-id',
    method: 'get',
    params: query
  })
}

// 根据考试对象获得个人考试答卷结果列表
export function getOwnAnswerResultList(query) {
  return request({
    url: '/exam/answer-result/list-own-by-exam-object-id',
    method: 'get',
    params: query
  })
}

// 获得考试答卷结果
export function getAnswerResult(id) {
  return request({
    url: '/exam/answer-result/get',
    method: 'get',
    params: { id }
  })
}

// 获得自己包含权限考试答卷结果
export function getOwnAnswerResult(id) {
  return request({
    url: '/exam/answer-result/get-own',
    method: 'get',
    params: { id }
  })
}
