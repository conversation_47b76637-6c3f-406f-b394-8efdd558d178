import request from "@/utils/request";

// 获得年度总结待提交内容
export function getSummaryContent(query) {
  return request({
    url: "/rotation/annual-summary-content/get-commit",
    method: "get",
    params: query,
  });
}

// 暂存年度总结内容
export function annualSummarySave(data) {
  return request({
    url: "/rotation/annual-summary-content/save",
    method: "post",
    data: data,
  });
}

// 提交年度总结内容
export function annualSummaryCommit(data) {
  return request({
    url: "/rotation/annual-summary-content/commit",
    method: "post",
    data: data,
  });
}
