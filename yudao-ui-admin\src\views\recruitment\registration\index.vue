<template>
  <div class="app-container">
    <div class="left-side">
      <div
        v-for="item in studentTypeList"
        :key="item.value"
        :class="['recruitment-type', { 'is-active': item.value === studentType }]"
        @click="handleStudentTypeChange(item.value)"
      >{{ item.label }}</div>
    </div>
    <div class="right-content" v-loading="loading">
      <div :class="['recruitment-plan', `is-${getRegistrationStatus(item).status}`]" v-for="(item, index) in list" :key="index">
        <el-form label-width="90px">
          <el-form-item label="名称：">{{ item.planName }}</el-form-item>
          <el-form-item label="报名时间：">{{ item.applicationStartTime }} - {{ item.applicationEndTime }}</el-form-item>
          <el-form-item label="报到时间：">{{ item.reportBeginTime }} - {{ item.reportEndTime }}</el-form-item>
          <el-form-item label="状态：">{{ getRegistrationStatus(item).text }}</el-form-item>
        </el-form>
        <div class="status-more">
          <template v-if="item.reported">
            <i class="el-icon-finished"></i>
            已报名
          </template>
          <template v-if="getRegistrationStatus(item).status === 'ing'">
            <i class="el-icon-timer" style="margin-left: 20px"></i>
            距离报名截止还有 <span>{{ getRegistrationStatus(item).day }}</span> 天
          </template>
        </div>
        <div class="handle-area">
          <el-button type="primary" @click="viewDetail(item)">查看详情</el-button>
          <el-button
            v-if="getRegistrationStatus(item).status === 'ing' && !item.reported"
            type="warning"
            @click="handleApply(item)"
          >我要报名</el-button>
          <el-button type="primary" plain v-if="item.reported" @click="handleToExamine(item)">查看报名审批情况</el-button>
        </div>
      </div>
      <el-empty description="暂无数据" v-if="list.length === 0"></el-empty>
    </div>

    <el-dialog :visible.sync="brochureOpen" title="招生简章" width="1000px" :close-on-click-modal="false">
      <div v-if="handlePlan" v-html="handlePlan.brochure"></div>
      <div style="text-align: center" slot="footer" v-if="handlePlan">
        <el-button type="primary" :disabled="previewTime < handlePlan.brochurePreviewTime" @click="handleToFill">
          已阅<span v-if="previewTime < handlePlan.brochurePreviewTime">({{handlePlan.brochurePreviewTime - previewTime}})</span>
        </el-button>
      </div>
    </el-dialog>

    <plan-dialog :open.sync="detailOpen" type="get" :id="handlePlan.planId" v-if="handlePlan"></plan-dialog>
  </div>
</template>

<script>
import { getRegistrationList } from "@/api/recruitment/registration";
import PlanDialog from "@/views/recruitment/plan/plan-dialog";

export default {
  name: "Registration",
  components: { PlanDialog },
  data() {
    return {
      loading: false,
      studentTypeList: [],
      studentType: "",
      list: [],
      handlePlan: null,
      brochureOpen: false,
      previewTime: 0,
      detailOpen: false,
    };
  },
  mounted() {
    this.studentTypeList = (this.getDictDatas(this.DICT_TYPE.SYSTEM_STUDENT_TYPE) || [])
      .filter(item => ['进修生', '短期培训', '住院医师'].indexOf(item.label) > -1);
    this.studentType = this.studentTypeList[0]?.value;
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getRegistrationList(this.studentType).then(response => {
        this.list = response.data;
        this.loading = false;
      });
    },
    handleStudentTypeChange(value) {
      this.studentType = value;
      this.getList();
    },
    getRegistrationStatus(item) {
      const { applicationStartTime, applicationEndTime, currentTime } = item;
      const startTime = new Date(applicationStartTime).getTime();
      const endTime = new Date(applicationEndTime).getTime();
      const curTime = new Date(currentTime || new Date()).getTime();
      if (curTime < startTime) {
        return { status: "pre", text: "报名未开始" }
      } else if (curTime < endTime) {
        const day = Math.ceil((endTime - curTime) / (1000 * 60 * 60 * 24));
        return { status: "ing", text: "正在报名中", day }
      } else {
        return { status: "end", text: "报名已结束" }
      }
    },
    viewDetail(item) {
      this.handlePlan = item;
      this.detailOpen = true;
    },
    handleApply(item) {
      this.handlePlan = item;
      this.brochureOpen = true;
      this.previewTime = 0;
      clearInterval(this.interval);
      this.interval = setInterval(() => {
        this.previewTime += 1;
      }, 1000);
    },
    handleToFill() {
      const { planName, planId, recruitmentRegistrationId } = this.handlePlan;
      clearInterval(this.interval);
      this.brochureOpen = false;
      this.$router.push({
        path: 'fill',
        query: { planName, planId, recruitmentRegistrationId, studentType: this.studentType },
      });
    },
    handleToExamine(item) {
      this.handlePlan = item;
      this.handleToFill();
    },
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;

  .left-side {
    flex-basis: 150px;
    flex-shrink: 0;
    margin-right: 20px;

    .recruitment-type {
      width: 100%;
      font-size: 15px;
      line-height: 36px;
      background: #e1e1e1;
      color: #000;
      text-align: center;
      cursor: pointer;
      margin-bottom: 5px;
      border-radius: 2px;

      &.is-active {
        background: #409EFF;
        color: #FFFFFF;
      }
    }
  }

  .right-content {
    flex-grow: 1;

    .recruitment-plan {
      padding: 16px;
      border: 1px solid #eeeeee;
      border-radius: 2px;
      display: flex;
      margin-bottom: 20px;

      ::v-deep .el-form-item {
        margin-bottom: 0;
      }

      .status-more {
        flex-grow: 1;
        padding: 0 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15px;
        .el-icon-finished, .el-icon-timer {
          font-size: 28px;
          color: #1890ff;
        }
        span {
          color: orangered;
        }
      }

      .handle-area {
        display: flex;
        flex-direction: column;
        justify-content: center;

        ::v-deep .el-button {
          margin: 0;
          width: 154px;
        }

        ::v-deep .el-button:not(:last-child) {
          margin-bottom: 10px;
        }
      }

      &.is-ing {
        background: #e2ffe2;
        border: 1px solid #67c23a;
      }

      &.is-pre {
        background: #efefef;
      }

      &.is-end {
        background: #e9e9ff;
      }
    }
  }
}

</style>
