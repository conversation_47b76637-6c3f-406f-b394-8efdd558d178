import request from '@/utils/request'

// 获得考试系统推送考试成绩，多次考试取最高值,返回-1代表未参加考试
export function getSyncExamResults(scheduleDetailsId) {
  return request({
    url: '/rotation/sync-exam-results/getSyncExamResultsByScheduleDetailsId',
    method: 'get',
    params: { scheduleDetailsId }
  })
}

// 获得考试系统推送试卷列表, 根据排班详情id和试卷类型
export function getSyncExamPaperOpenList(data) {
  return request({
    url: '/rotation/sync-exam-paper/getSyncExamPaperOpenList',
    method: 'post',
    data
  })
}

// 创建考试系统开卷记录-获取开卷地址
export function createSyncExamOpen(data) {
  return request({
    url: '/rotation/sync-exam-open/create',
    method: 'post',
    data
  })
}

// 根据排班详情获得考试系统推送考试成绩
export function getExamResults(scheduleDetailsId) {
  return request({
    url: '/rotation/sync-exam-results/getSyncExamResultsSimpleList',
    method: 'get',
    params: { scheduleDetailsId }
  })
}
