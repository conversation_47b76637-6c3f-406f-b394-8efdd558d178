<template>
  <div class="supervise-record-dialog">
    <el-dialog
      title="督导记录"
      width="600px"
      v-dialog-drag
      append-to-body
      :visible="visible"
      @update:visible="$emit('update:visible', $event)"
    >
      <p style="margin-top: 0">
        培训名称：{{ superviseObject && superviseObject.name }}
      </p>
      <el-table v-loading="recordLoading" :data="recordList">
        <el-table-column label="督导人" align="center" prop="nickname" />
        <el-table-column label="评分" align="center" prop="score" />
        <el-table-column label="反馈进度" align="center" prop="feedbackCount">
          <template v-slot="scope">{{
            scope.row.feedbackCount === 0 ? "未反馈" : "已反馈"
          }}</template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="120"
          class-name="small-padding fixed-width"
        >
          <template v-slot="scope">
            <el-button
              v-if="canFeedback && !scope.row.feedbackCount"
              size="mini"
              type="text"
              @click="handleFeedback(scope.row)"
              >督导反馈</el-button
            >
            <el-button
              size="mini"
              type="text"
              @click="handleView(scope.row)"
              v-else
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog title="查看督导评价" :visible.sync="auditVisible" width="900px">
      <el-tabs>
        <el-tab-pane
          v-for="(item, index) in resultForms"
          :key="item.superviseFormName"
          :label="item.superviseFormName"
        >
          <el-button
            type="primary"
            style="position: absolute; right: 0; top: 0"
            :loading="exportloading"
            @click="handleDown(index, item.superviseFormName)"
            >导出</el-button
          >
          <div :id="'supervise-appraise-' + index">
            <el-form inline label-width="90px" class="export-item">
              <el-form-item
                label="培训名称"
                style="width: 100%; white-space: nowrap"
                >{{ superviseObject.name }}</el-form-item
              >
              <el-form-item label="培训级别：" style="width: 32%">
                <dict-tag
                  :type="DICT_TYPE.TEACHING_TRAINING_LEVEL"
                  :value="superviseObject.trainingLevel"
                ></dict-tag>
              </el-form-item>
              <el-form-item label="培训类型：" style="width: 32%">
                <dict-tag
                  :type="DICT_TYPE.TEACHING_TRAINING_TYPE"
                  :value="superviseObject.trainingType"
                ></dict-tag>
              </el-form-item>
              <el-form-item label="培训人：" style="width: 32%">{{
                superviseObject.nickname
              }}</el-form-item>
              <el-form-item label="开展时间：" style="width: 100%">
                {{
                  superviseObject.startTime && superviseObject.endTime
                    ? parseTime(
                        superviseObject.startTime,
                        "{y}-{m}-{d} {h}:{i}"
                      ) +
                      " ~ " +
                      parseTime(superviseObject.endTime, "{y}-{m}-{d} {h}:{i}")
                    : "--"
                }}
              </el-form-item>
              <el-form-item label="培训地点：" style="width: 100%">{{
                superviseObject.trainingAddress
              }}</el-form-item>
            </el-form>

            <div class="form-item-wrapper" v-if="item.resultExts.length > 0">
              <div class="form-item" v-for="(ext, index) in item.resultExts" :key="index">
                <span class="form-label">{{ ext.superviseFormExtName }}：</span>
                <span>{{ ext.superviseFormExtVal }}</span>
              </div>
            </div>
            <div
              v-for="(resultItem, i) in item.resultItems"
              :key="i"
              class="export-item"
              style="margin-bottom: 20px"
            >
              <header class="table-header">
                <span style="margin-right: 20px"
                  >评分项目：{{ resultItem.superviseFormItemName }}</span
                >
                <span style="margin-right: 20px"
                  >总分：{{ resultItem.superviseFormItemScore }}</span
                >
                <span>督导得分：{{ resultItem.score }}</span>
              </header>
              <el-table :data="resultItem.resultFormSubItems" border>
                <el-table-column
                  label="评分要素"
                  prop="superviseFormSubItemName"
                ></el-table-column>
                <el-table-column
                  label="分值"
                  prop="superviseFormSubItemScore"
                  width="100px"
                ></el-table-column>
                <el-table-column label="得分" width="150px">
                  <template v-slot="scope">
                    <el-input-number
                      style="width: 120px"
                      v-model="scope.row.score"
                      controls-position="right"
                      :min="0"
                      :max="scope.row.superviseFormSubItemScore"
                      disabled
                    ></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="备注" width="150px">
                  <template v-slot="scope">
                    <el-input
                      style="width: 120px"
                      v-model="scope.row.remark"
                      disabled
                    ></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div
              class="export-item"
              style="text-align: right; margin-bottom: 20px"
            >
              <span style="margin-right: 20px;">督导专家：{{ superviseNickname }}</span>
              督导总分：{{ item.score }}
            </div>

            <el-form class="export-item" label-width="90px">
              <el-form-item label="活动自评：" v-if="selfAssessment">
                <el-input
                  style="width: 100%"
                  type="textarea"
                  v-model="selfAssessment"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="督导意见：" style="width: 100%">
                <el-input
                  style="width: 100%"
                  type="textarea"
                  v-model="item.opinion"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item
                label="照片上传："
                style="width: 100%; white-space: nowrap"
              >
                <imageUpload v-model="item.pictures" :limit="9999" disabled />
              </el-form-item>
              <el-form-item label="督导反馈：">
                <el-input
                  style="width: 100%"
                  type="textarea"
                  placeholder="请填写督导反馈意见！"
                  v-model="item.feedback"
                  :disabled="!submitStatus"
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" v-if="submitStatus">
        <el-button type="primary" @click="submitFeedback">提交反馈</el-button>
        <el-button @click="auditVisible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getHosSuperviseRecordList } from "@/api/rotation/hospitalTrainingSupervise";
import {
  getSuperviseResult,
  saveSuperviseFeedback,
} from "@/api/rotation/teachingActiveSupervise";
import ImageUpload from "@/components/ImageUpload";
import { exportPDF } from "@/utils/exportUtils";

export default {
  name: "supervise-record-dialog",
  components: { ImageUpload },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    superviseObject: Object,
    canFeedback: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      recordLoading: false,
      recordList: [],
      auditVisible: false,
      resultForms: [],
      selfAssessment: "",
      superviseNickname: "",
      submitStatus: false,
      exportloading: false,
    };
  },
  methods: {
    queryRecordList() {
      this.recordLoading = true;
      getHosSuperviseRecordList({
        pageNo: 1,
        pageSize: 9999,
        hospitalTrainingId: this.superviseObject.id,
      }).then((res) => {
        this.recordList = res.data.list;
        this.recordLoading = false;
      });
    },
    handleView(row) {
      this.submitStatus = false;
      getSuperviseResult({ id: row.superviseResultId }).then((res) => {
        this.resultForms = res.data?.resultFormCreateReqVOS || [];
        this.selfAssessment = res.data?.selfAssessment || "";
        this.superviseNickname = row.nickname || "";
        this.auditVisible = true;
      });
    },
    handleFeedback(row) {
      this.handleView(row);
      this.submitStatus = true;
    },
    submitFeedback() {
      const feedbacks = this.resultForms.map((item) => ({
        superviseResultFormId: item.id,
        feedback: item.feedback,
      }));
      this.$confirm("确认提交督导反馈么，提交后不可修改！", "提示").then(() => {
        saveSuperviseFeedback(feedbacks).then(() => {
          this.$message.success("提交反馈成功！");
          this.auditVisible = false;
          this.queryRecordList();
        });
      });
    },
    handleDown(index, superviseFormName) {
      this.exportloading = true;
      exportPDF(`supervise-appraise-${index}`, `${superviseFormName}`, () => {
        this.exportloading = false;
      });
    },
  },
  watch: {
    visible(value) {
      if (value) {
        this.queryRecordList();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.table-header {
  padding: 10px;
  border: 1px solid #dfe6ec;
  background: #f8f8f9;
  position: relative;
  top: 1px;
}

.form-item-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;

  .form-item {
    flex-basis: calc(33.3% - 20px);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }

  .form-label {
    flex-shrink: 0;
    font-size: 14px;
    color: #606266;
  }
}
</style>
