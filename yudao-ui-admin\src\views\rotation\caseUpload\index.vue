<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学员姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入学员姓名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="轮转科室" prop="rotationDepartmentName">
        <el-input v-model="queryParams.rotationDepartmentName" placeholder="请输入轮转科室" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="派送单位" prop="dispatchingUnit">
        <el-input v-model="queryParams.dispatchingUnit" placeholder="请输入派送单位" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select v-model="queryParams.grade" placeholder="请选择学员类型" filterable clearable size="small">
          <el-option v-for="grade in gradeOptions"
                       :key="grade" :label="grade" :value="grade"/>
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" filterable clearable size="small" @change="queryStudentTypeChange">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select v-model="queryParams.major" placeholder="请选择培训专业" filterable clearable size="small">
          <el-option v-for="major in majorOptions"
                       :key="major.code" :label="major.name" :value="major.code"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:student-enrollment:create']">新增</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:student-enrollment:export']">导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="学员姓名" align="center" prop="nickname" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="手机号" align="center" prop="mobile" />
      <el-table-column label="派送单位" align="center" prop="dispatchingUnit" />
      <el-table-column label="状态" align="center" prop="rotationStatus">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.ROTATION_STATUS" :value="scope.row.rotationStatus" />
        </template>
      </el-table-column>
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType" />
        </template>
      </el-table-column>
      <el-table-column label="培训专业" align="center" prop="majorName" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column label="轮转科室" align="center" prop="rotationDeptName" />
      <el-table-column label="轮转时间" align="center" prop="rotationDate" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handlePics(scope.row)"
                     v-hasPermi="['rotation:schedule-details-case-upload:save']">
                     病例上传
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog title="病例上传" :visible.sync="openPics" width="800px" v-dialogDrag append-to-body>
      <div>
        <imageUpload
          v-model="form.casePictures"
          :limit="9999"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPics">保 存</el-button>
        <el-button @click="cancelSubmitPics">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImageUpload from '@/components/ImageUpload';
import { getSimpleMajorList } from "@/api/system/major";
import { getStudentGradeList } from "@/api/system/userStudent";
import { getCaseUploadPage, getCaseUpload, saveCaseUpload } from "@/api/rotation/caseUpload";

export default {
  name: "CaseUpload",
  components: {
    ImageUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员入科列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      openPics: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: '',
        rotationDepartmentName: '',
        grade: '',
        studentType: '',
        major: '',
        dispatchingUnit: ''
      },
      // 表单参数
      form: {},
      gradeOptions: [],
      majorOptions: [],
      skillsExaminerList: [],
      archiaterList: [],
      teacherList: [],
      currentRow: {},
    };
  },
  created() {
    this.getList();
    this.getGradeList();
  },
methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getCaseUploadPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /**查询年级 */
    getGradeList() {
      this.gradeOptions = []
      getStudentGradeList().then(res => {
        this.gradeOptions = res.data
      })
    },
    /**根据学员类型获取培训专业 */
    queryStudentTypeChange(val) {
      this.majorOptions = []
      getSimpleMajorList({studentType: val}).then(res => {
        this.majorOptions = res.data
      })
    },
    /** 取消按钮 */
    cancelSubmitPics() {
      this.openPics = false;
    },
    /** 表单重置 */
    reset() {
      this.form = {
        casePictures: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handlePics(row) {
      this.reset();
      const id = row.scheduleDetailsId;
      this.currentRow = row;
      getCaseUpload(id).then(response => {
        this.form = response.data;
        this.openPics = true;
      });
    },
    submitPics() {
      const params = {
        id: this.currentRow.scheduleDetailsId,
        casePictures: this.form.casePictures
      }
      saveCaseUpload(params).then(response => {
        this.$modal.msgSuccess("保存成功");
        this.openPics = false;
        this.getList();
      });
    },
  }
};
</script>
<style lang="scss">
.student-enrollment-dialog{
  .el-form-item__content{
    position: relative;
  }
  .el-icon-circle-plus-outline,
  .el-icon-remove-outline{
    font-weight: bold;
    font-size: 24px;
    position: absolute;
    top: 5px;
    right: 6px;
    cursor: pointer;
  }
  .el-icon-circle-plus-outline{
    right: 36px;
  }
}
</style>