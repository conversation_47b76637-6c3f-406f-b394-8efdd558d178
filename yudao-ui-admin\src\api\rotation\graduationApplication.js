import request from '@/utils/request'

// 获得结业申请条件和学员数据
export function getApplicationRequire(query) {
  return request({
    url: '/rotation/graduation-application/get-application-require',
    method: 'get',
    params: query
  })
}

// 创建结业申请
export function createGraduationApplication(data) {
  return request({
    url: '/rotation/graduation-application/create',
    method: 'post',
    data: data
  })
}

//获得结业申请(流程嵌套页调用)
export function getApplicationApplication(query) {
  return request({
    url: '/rotation/graduation-application/get',
    method: 'get',
    params: query
  })
}

export function createFeedback(data) {
  return request({
    url: '/rotation/graduation-application/feedback',
    method: 'put',
    data: data
  })
}
