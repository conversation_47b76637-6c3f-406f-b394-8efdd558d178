import request from '@/utils/request'

// 创建考勤记录
export function createRecord(data, role) {
  return request({
    url: `/attendance/${role}/record/create`,
    method: 'post',
    data: data
  })
}

// 更新考勤记录
export function updateRecord(data, role) {
  return request({
    url: `/attendance/${role}/record/updateAttendanceStatus`,
    method: 'put',
    data: data
  })
}

// 删除考勤记录
export function deleteRecord(id, role) {
  return request({
    url: `/attendance/${role}/record/delete?id=${id}`,
    method: 'delete'
  })
}

// 获得考勤记录
export function getRecord(id, role) {
  return request({
    url: `/attendance/${role}/record/get?id=${id}`,
    method: 'get'
  })
}

// 获得考勤记录分页
export function getRecordPage(query, role) {
  return request({
    url: `/attendance/${role}/record/page`,
    method: 'get',
    params: query
  })
}

// 导出考勤记录 Excel
export function exportRecordExcel(query, role) {
  return request({
    url: `/attendance/${role}/record/export-excel`,
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得科室考勤记录统计分页
export function getStatisticsPage(query) {
  return request({
    url: '/attendance/dept/record/statistics-page',
    method: 'get',
    params: query
  })
}

// 导出考勤记录统计 Excel
export function exportStatisticsExcel(query) {
  return request({
    url: '/attendance/dept/record/statistics-export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
