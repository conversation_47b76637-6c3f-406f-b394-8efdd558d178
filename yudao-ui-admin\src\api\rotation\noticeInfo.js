import request from '@/utils/request'

// 创建通知
export function createNoticeInfo(data) {
  return request({
    url: '/rotation/notice-info/create',
    method: 'post',
    data: data
  })
}

// 更新通知
export function updateNoticeInfo(data) {
  return request({
    url: '/rotation/notice-info/update',
    method: 'put',
    data: data
  })
}

// 删除通知
export function deleteNoticeInfo(id) {
  return request({
    url: '/rotation/notice-info/delete?id=' + id,
    method: 'delete'
  })
}

// 获得通知
export function getNoticeInfo(id) {
  return request({
    url: '/rotation/notice-info/get?id=' + id,
    method: 'get'
  })
}

// 获得通知分页
export function getNoticeInfoPage(query) {
  return request({
    url: '/rotation/notice-info/page',
    method: 'get',
    params: query
  })
}

// 导出通知 Excel
export function exportNoticeInfoExcel(query) {
  return request({
    url: '/rotation/notice-info/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得院外通知分页
export function getForeignNoticeInfoPage(query) {
  return request({
    url: '/rotation/notice-info/page-foreign',
    method: 'get',
    params: query
  })
}

// 获得院内通知分页
export function getInternalNoticeInfoPage(query) {
  return request({
    url: '/rotation/notice-info/page-internal',
    method: 'get',
    params: query
  })
}

// 阅读通知
export function viewNoticeInfo(id) {
  return request({
    url: '/rotation/notice-info/view?id=' + id,
    method: 'get'
  })
}

// 获得通知接收人分页
export function getNoticeReceiverPage(query) {
  return request({
    url: '/rotation/notice-info/page-receiver',
    method: 'get',
    params: query
  })
}
