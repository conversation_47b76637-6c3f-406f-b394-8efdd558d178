<template>
  <!-- 对话框(添加 / 修改) -->
  <el-dialog
    custom-class="user-worker-dialog"
    :title="dialogTitle"
    :visible.sync="open"
    width="80%"
    v-dialogDrag
    append-to-body
    @close="cancel"
  >
    <div class="steps-box">
      <el-radio-group v-model="activeStep" size="medium" @input="changeStep">
        <el-radio-button :label="0">基础信息</el-radio-button>
        <!-- <el-radio-button v-if="(opt === 'view')" :label="1">培训信息</el-radio-button> -->
      </el-radio-group>
    </div>
    <div v-if="activeStep == 0" class="form-box">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="150px"
        :class="opt === 'view' ? 'form-view' : ''"
      >
        <el-row :gutter="10">
          <el-col :md="16" :lg="16" :xl="16">
            <el-row :gutter="10">
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="姓名：" prop="nickname">
                  <span v-if="opt === 'view'">{{ form.nickname }}</span>
                  <el-input
                    v-else
                    v-model="form.nickname"
                    placeholder="请输入姓名"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="用户名：" prop="username">
                  <span v-if="opt === 'view'">{{ form.username }}</span>
                  <el-input
                    v-else
                    v-model="form.username"
                    placeholder="请输入用户名"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="职工类型：" prop="workTypes">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.SYSTEM_USER_WORK_TYPE"
                      :value="form.workTypes"
                    />
                  </span>
                  <el-select
                    v-else
                    v-model="form.workTypes"
                    filterable
                    placeholder="请选择职工类型"
                  >
                    <el-option
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.SYSTEM_USER_WORK_TYPE
                      )"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="科室：" prop="deptIds">
                  <span v-if="opt === 'view'">
                    {{ handleDeptIds(form.deptIds) }}
                  </span>
                  <el-select
                    v-else
                    v-model="form.deptIds"
                    filterable
                    multiple
                    placeholder="请选择医院科室"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in departmentOptions"
                      :key="parseInt(item.id)"
                      :label="item.name"
                      :value="item.id.toString()"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="证件类型：" prop="certificateType">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.SYSTEM_USER_CERTIFICATE_TYPE"
                      :value="form.certificateType"
                    />
                  </span>
                  <el-select
                    v-else
                    v-model="form.certificateType"
                    filterable
                    placeholder="请选择证件类型"
                  >
                    <el-option
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.SYSTEM_USER_CERTIFICATE_TYPE
                      )"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="证件号码：" prop="certificateNumber">
                  <span v-if="opt === 'view'">{{
                    form.certificateNumber
                  }}</span>
                  <el-input
                    v-else
                    v-model="form.certificateNumber"
                    placeholder="请输入证件号码"
                    @blur="getIdcardInfoHandler"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="用户性别：" prop="sex">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.SYSTEM_USER_SEX"
                      :value="form.sex"
                    />
                  </span>
                  <el-select
                    v-else
                    v-model="form.sex"
                    filterable
                    placeholder="请选择用户性别"
                  >
                    <el-option
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.SYSTEM_USER_SEX
                      )"
                      :key="dict.value"
                      :label="dict.label"
                      :value="parseInt(dict.value)"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="婚姻状况：" prop="maritalStatus">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.SYSTEM_USER_MARITAL_STATUS"
                      :value="form.maritalStatus"
                    />
                  </span>
                  <el-radio-group v-else v-model="form.maritalStatus">
                    <el-radio
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.SYSTEM_USER_MARITAL_STATUS
                      )"
                      :key="dict.value"
                      :label="dict.value"
                      >{{ dict.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="出生日期：" prop="birthday">
                  <span v-if="opt === 'view'">{{ form.birthday }}</span>
                  <el-date-picker
                    v-else
                    clearable
                    v-model="form.birthday"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="选择出生日期"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="是否党员：" prop="isPartyMember">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.INFRA_BOOLEAN_STRING"
                      :value="form.isPartyMember"
                    />
                  </span>
                  <el-select
                    v-else
                    v-model="form.isPartyMember"
                    filterable
                    placeholder="请选择是否党员"
                  >
                    <el-option
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.INFRA_BOOLEAN_STRING
                      )"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="民族：" prop="nation">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.SYSTEM_NATION"
                      :value="form.nation"
                    />
                  </span>
                  <el-select
                    v-else
                    v-model="form.nation"
                    filterable
                    placeholder="请选择民族"
                  >
                    <el-option
                      v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_NATION)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="手机号码：" prop="mobile">
                  <span v-if="opt === 'view'">{{ form.mobile }}</span>
                  <el-input
                    v-else
                    v-model="form.mobile"
                    placeholder="请输入手机号码"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="健康状况：" prop="healthStatus">
                  <span v-if="opt === 'view'">{{ form.healthStatus }}</span>
                  <el-select
                    v-else
                    v-model="form.healthStatus"
                    filterable
                    placeholder="请选择健康状况"
                  >
                    <el-option
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.SYSTEM_USER_HEALTH_STATUS
                      )"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="用户邮箱：" prop="email">
                  <span v-if="opt === 'view'">{{ form.email }}</span>
                  <el-input
                    v-else
                    v-model="form.email"
                    placeholder="请输入用户邮箱"
                    maxlength="100"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="毕业时间：" prop="graduationDate">
                  <span v-if="opt === 'view'">{{ form.graduationDate }}</span>
                  <el-date-picker
                    v-else
                    clearable
                    v-model="form.graduationDate"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="选择毕业时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="毕业院校：" prop="graduationSchool">
                  <span v-if="opt === 'view'">{{ form.graduationSchool }}</span>
                  <el-input
                    v-else
                    v-model="form.graduationSchool"
                    placeholder="请输入毕业院校"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="工作开始时间：" prop="workStartDate">
                  <span v-if="opt === 'view'">{{ form.workStartDate }}</span>
                  <el-date-picker
                    v-else
                    clearable
                    v-model="form.workStartDate"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="选择工作开始时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="学历：" prop="education">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.SYSTEM_USER_EDUCATION"
                      :value="form.education"
                    />
                  </span>
                  <el-select
                    v-else
                    v-model="form.education"
                    filterable
                    placeholder="请选择学历"
                  >
                    <el-option
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.SYSTEM_USER_EDUCATION
                      )"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="学位：" prop="degree">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.SYSTEM_USER_DEGREE"
                      :value="form.degree"
                    />
                  </span>
                  <el-select
                    v-else
                    v-model="form.degree"
                    filterable
                    placeholder="请选择学位"
                  >
                    <el-option
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.SYSTEM_USER_DEGREE
                      )"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="职称：" prop="positionalTitles">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES"
                      :value="form.positionalTitles"
                    />
                  </span>
                  <el-select
                    v-else
                    v-model="form.positionalTitles"
                    filterable
                    placeholder="请选择职称"
                  >
                    <el-option
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES
                      )"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="取得时间：" prop="obtainDate">
                  <span v-if="opt === 'view'">{{ form.obtainDate }}</span>
                  <el-date-picker
                    v-else
                    clearable
                    v-model="form.obtainDate"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="选择取得时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item
                  label="主治职称取得时间："
                  prop="chiefPhysicianObtainDate"
                >
                  <span v-if="opt === 'view'">
                    {{ form.chiefPhysicianObtainDate }}
                  </span>
                  <el-date-picker
                    v-else
                    clearable
                    v-model="form.chiefPhysicianObtainDate"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="选择主治职称取得时间"
                  />
                </el-form-item>
              </el-col>

              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="带教专业：" prop="teacherMajorCodes">
                  <span v-if="opt === 'view'">
                    {{ handleMajorCodes(form.teacherMajorCodes) }}
                  </span>
                  <el-select
                    v-else
                    v-model="form.teacherMajorCodes"
                    filterable
                    multiple
                    placeholder="请选择带教专业"
                  >
                    <el-option
                      v-for="dict in majorList"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="开户银行：" prop="depositBank">
                  <span v-if="opt === 'view'">{{ form.depositBank }}</span>
                  <el-input
                    v-else
                    v-model="form.depositBank"
                    placeholder="请输入开户银行"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="银行卡号：" prop="bankCardNo">
                  <span v-if="opt === 'view'">{{ form.bankCardNo }}</span>
                  <el-input
                    v-else
                    v-model="form.bankCardNo"
                    placeholder="请输入银行卡号"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="是否全科师资：" prop="isGeneralTeacher">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.INFRA_BOOLEAN_STRING"
                      :value="form.isGeneralTeacher"
                    />
                  </span>
                  <el-radio-group v-else v-model="form.isGeneralTeacher">
                    <el-radio
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.INFRA_BOOLEAN_STRING
                      )"
                      :key="dict.value"
                      :label="dict.value"
                      >{{ dict.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="是否骨干师资：" prop="isKeyTeacher">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.INFRA_BOOLEAN_STRING"
                      :value="form.isKeyTeacher"
                    />
                  </span>
                  <el-radio-group v-else v-model="form.isKeyTeacher">
                    <el-radio
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.INFRA_BOOLEAN_STRING
                      )"
                      :key="dict.value"
                      :label="dict.value"
                      >{{ dict.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <el-form-item label="是否导师：" prop="isMentor">
                  <span v-if="opt === 'view'">
                    <dict-tag
                      :type="DICT_TYPE.INFRA_BOOLEAN_STRING"
                      :value="form.isMentor"
                    />
                  </span>
                  <el-radio-group v-else v-model="form.isMentor">
                    <el-radio
                      v-for="dict in this.getDictDatas(
                        DICT_TYPE.INFRA_BOOLEAN_STRING
                      )"
                      :key="dict.value"
                      :label="dict.value"
                      >{{ dict.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :md="8" :lg="8" :xl="8">
            <userAvatar :user="form" @info="getAvatar" :opt="opt" />

            <el-row :gutter="10" style="padding-left: 40px; margin-top: 20px">
              <el-col :md="12" :lg="12" :xl="12">
                <div style="margin-bottom: 5px"><b>学历证书：</b></div>
                <el-form-item
                  label=""
                  label-width="0"
                  prop="educationCertificate"
                >
                  <imageUpload
                    v-model="form.educationCertificate"
                    :limit="1"
                    :disabled="opt === 'view' ? true : false"
                    :isShowTip="false"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div style="margin-bottom: 5px"><b>学位证书：</b></div>
                <el-form-item label="" label-width="0" prop="degreeCertificate">
                  <imageUpload
                    v-model="form.degreeCertificate"
                    :limit="1"
                    :disabled="opt === 'view' ? true : false"
                    :isShowTip="false"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="24" :xl="24">
                <div style="margin-bottom: 5px"><b>职称证书：</b></div>
                <el-form-item
                  label=""
                  label-width="0"
                  prop="positionalTitlesCertificate"
                >
                  <imageUpload
                    v-model="form.positionalTitlesCertificate"
                    :limit="1"
                    :disabled="opt === 'view' ? true : false"
                    :isShowTip="opt === 'view' ? false : true"
                  />
                  <!-- <el-input v-model="form.positionalTitlesCertificate" placeholder="请输入职称证书地址" /> -->
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>

        <el-row :gutter="10" style="margin-top: 10px">
          <el-col :md="24" :lg="24" :xl="24">
            <div style="margin-bottom: 5px">
              <b>师资证书列表（点击下方右侧“+”号添加）</b>
            </div>
            <certificateTable
              :formItem="teacherCertificateUpdateReqVOS"
              :opt="opt"
              :majorList="majorList"
              @change="setFormItems"
            />
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- <div v-if="(activeStep == 1 && opt === 'view')" class="workInfo-box">
        <div class="info-block-box">
          <div class="info-block-title">带教老师信息</div>
          <div class="info-block-cont">
            <el-row :gutter="10">
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>科室：</label>
                  <span>呼吸内科（医院科室）</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>学员类型：</label>
                  <span>住院医师、实习生</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>任职开始时间：</label>
                  <span>2022-01-21</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>任职结束时间：</label>
                  <span>2022-10-21</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="info-block-box">
          <div class="info-block-title">教学秘书信息</div>
          <div class="info-block-cont">
            <el-row :gutter="10">
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>科室：</label>
                  <span>呼吸内科（医院科室）</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>学员类型：</label>
                  <span>住院医师、实习生</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>任职开始时间：</label>
                  <span>2022-01-21</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>任职结束时间：</label>
                  <span>2022-10-21</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="info-block-box">
          <div class="info-block-title">教学主任信息</div>
          <div class="info-block-cont">
            <el-row :gutter="10">
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>科室：</label>
                  <span>呼吸内科（医院科室）</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>学员类型：</label>
                  <span>住院医师、实习生</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>任职开始时间：</label>
                  <span>2022-01-21</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>任职结束时间：</label>
                  <span>2022-10-21</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="info-block-box">
          <div class="info-block-title">技能考官信息</div>
          <div class="info-block-cont">
            <el-row :gutter="10">
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>科室：</label>
                  <span>呼吸内科（医院科室）</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>学员类型：</label>
                  <span>住院医师、实习生</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>任职开始时间：</label>
                  <span>2022-01-21</span>
                </div>
              </el-col>
              <el-col :md="12" :lg="12" :xl="12">
                <div class="block-item">
                  <label>任职结束时间：</label>
                  <span>2022-10-21</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div> -->
    <div slot="footer" class="dialog-footer" v-if="opt !== 'view'">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import userAvatar from "../userAvatar.vue";
import ImageUpload from "@/components/ImageUpload";
import certificateTable from "../certificateTable.vue";
import { getSimpleMajorList } from "@/api/system/major";
import { getDepartmentSimpleList } from "@/api/system/department";
import { getIdcardInfo } from "@/api/system/userStudent";
import {
  createUserWorker,
  updateUserWorker,
  getUserWorker,
} from "@/api/system/userWorker";

export default {
  props: {
    dialogTitle: {
      type: String,
    },
    dialogOpen: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: [String, Number],
    },
    opt: {
      type: String,
      default: "",
    },
  },
  components: { userAvatar, ImageUpload, certificateTable },
  data() {
    const checkCertificateNumber = (rule, value, callback) => {
      if (this.form.certificateType == 1 && value) {
        const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的证件号码"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const checkMobile = (rule, value, callback) => {
      if (value) {
        const reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的手机号码"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const checkEmail = (rule, value, callback) => {
      if (value) {
        const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的邮箱地址"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
          {
            min: 4,
            max: 30,
            message: "长度在 4 到 30 个字符",
            trigger: "blur",
          },
        ],
        nickname: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        certificateNumber: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkCertificateNumber, trigger: "blur" },
        ],
        mobile: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkMobile, trigger: "blur" },
        ],
        email: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkEmail, trigger: "blur" },
        ],
      },
      open: false,
      activeStep: 0,
      majorList: [],
      teacherCertificateUpdateReqVOS: [],
      departmentOptions: [],
    };
  },
  watch: {
    dialogOpen(newVal) {
      if (newVal) {
        this.activeStep = 0;

        Promise.all([this.getDepartment(), this.getSimpleMajors()])
          .then((results) => {
            console.log("results====", results);
            this.departmentOptions = results[0].data;
            this.majorList = results[1].data;
            // this.majorList = res.data;

            this.reset();

            if (this.opt !== "add") {
              const id = this.userId;
              getUserWorker(id).then((response) => {
                if (!response.data) {
                  this.$message.warning("当前用户不是职工");
                  this.$emit("update:dialogOpen", false);
                  return;
                }
                this.form = response.data;
                if (response.data.isPartyMember !== null) {
                  this.form.isPartyMember =
                    response.data.isPartyMember.toString();
                }
                if (response.data.isGeneralTeacher !== null) {
                  this.form.isGeneralTeacher =
                    response.data.isGeneralTeacher.toString();
                }
                if (response.data.isKeyTeacher !== null) {
                  this.form.isKeyTeacher =
                    response.data.isKeyTeacher.toString();
                }
                if (response.data.isMentor !== null) {
                  this.form.isMentor = response.data.isMentor.toString();
                }
                if (response.data.deptIds) {
                  this.form.deptIds = response.data.deptIds.split(",");
                }
                if (response.data.teacherMajorCodes) {
                  this.form.teacherMajorCodes =
                    response.data.teacherMajorCodes.split(",");
                }
                this.teacherCertificateUpdateReqVOS =
                  this.form?.teacherCertificateUpdateReqVOS || [];
                this.open = newVal;
              });
            } else {
              this.teacherCertificateUpdateReqVOS = [];
              this.open = newVal;
            }
          })
          .catch((error) => {
            console.error("failed:", error.message);
          });
      } else {
        this.open = newVal;
      }
    },
  },
  methods: {
    async getDepartment() {
      this.departmentOptions = [];
      // 获得科室列表
      return await getDepartmentSimpleList(0);
    },

    async getSimpleMajors() {
      this.majorList = [];
      // 获得专业列表
      return await getSimpleMajorList();
    },

    cancel() {
      this.$emit("update:dialogOpen", false);
      this.reset();
    },
    handleMajorCodes(teacherMajorCodes) {
      let arr = teacherMajorCodes || [];
      let arrtxt = [];
      this.majorList.forEach((item) => {
        if (arr.includes(item.code)) {
          arrtxt.push(item.name);
        }
      });
      return arrtxt.join(",");
    },

    handleDeptIds(deptIds) {
      let arr = deptIds || [];
      let arrtxt = [];
      this.departmentOptions.forEach((item) => {
        if (arr.includes(item.id.toString())) {
          arrtxt.push(item.name);
        }
      });
      return arrtxt.join(",");
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        username: undefined,
        nickname: undefined,
        workTypes: undefined,
        avatar: undefined,
        sex: undefined,
        email: undefined,
        mobile: undefined,
        certificateType: undefined,
        certificateNumber: undefined,
        birthday: undefined,
        maritalStatus: undefined,
        nation: undefined,
        isPartyMember: "false",
        isMentor: "false",
        healthStatus: undefined,
        graduationDate: undefined,
        graduationSchool: undefined,
        education: undefined,
        degree: undefined,
        workStartDate: undefined,
        positionalTitles: undefined,
        positionalTitlesCertificate: undefined,
        educationCertificate: undefined,
        degreeCertificate: undefined,
        obtainDate: undefined,
        chiefPhysicianObtainDate: undefined,
        teacherMajorCodes: [],
        deptIds: [],
        depositBank: undefined,
        bankCardNo: undefined,
        isGeneralTeacher: "false",
        isKeyTeacher: "false",
        pmsList: [],
      };
      this.resetForm("form");
    },
    /** 切换步骤条*/
    changeStep(val) {
      this.activeStep = val;
    },
    /** 设置头像*/
    getAvatar(picUrl) {
      this.form.avatar = picUrl;
    },
    setFormItems(list) {
      console.log("证书数据===", list);
      this.teacherCertificateUpdateReqVOS = list;
    },
    /** 获取身份证相关信息*/
    getIdcardInfoHandler(e) {
      if (this.form.certificateType == 1) {
        const params = {
          certificateNumber: this.form.certificateNumber,
          certificateType: "1",
        };
        getIdcardInfo(params).then((response) => {
          let birthday = response.data.birthday;
          let tempARR = birthday.split("");
          tempARR.splice(4, 0, "-");
          tempARR.splice(7, 0, "-");
          const _birthday = tempARR.join("");

          this.form.sex = response.data.sex;
          this.form.birthday = _birthday;
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        let params = {
          ...this.form,
          teacherMajorCodes:
            this.form.teacherMajorCodes && this.form.teacherMajorCodes.length
              ? this.form.teacherMajorCodes.join(",")
              : "",
          deptIds:
            this.form.deptIds && this.form.deptIds.length
              ? this.form.deptIds.join(",")
              : "",
        };
        // 修改的提交
        if (this.form.id != null) {
          // debugger
          params.teacherCertificateUpdateReqVOS =
            this.teacherCertificateUpdateReqVOS.map((item) => ({
              ...item,
              userId: this.form.id,
            }));
          updateUserWorker(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.cancel();
            this.$emit("refresh");
          });
          return;
        }
        params.teacherCertificateUpdateReqVOS =
          this.teacherCertificateUpdateReqVOS;
        // 添加的提交
        createUserWorker(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.cancel();
          this.$emit("refresh");
        });
      });
    },
  },
};
</script>

<style lang="scss">
.user-worker-dialog {
  .el-dialog__body {
    flex: auto;
    padding-top: 5px;
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .steps-box {
      border-radius: 4px;
      background: #f5f7fa;

      .el-radio-button__inner {
        background: transparent;
        border: none;
        border-radius: 0;
      }

      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        color: #1890ff;
        background-color: transparent;
        border-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
        position: relative;

        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 100%;
          height: 2px;
          background: #1890ff;
          bottom: 0;
          left: 0;
        }
      }
    }

    .el-select {
      width: 100%;
    }

    .form-box {
      flex: auto;
      overflow-y: auto;
      padding: 15px 10px 0 10px;

      .form-view {
        .el-form-item {
          margin-bottom: 0;
        }
      }

      .el-upload--picture-card {
        width: 120px;
        height: 120px;
        line-height: 118px;
      }

      .el-upload-list--picture-card .el-upload-list__item {
        width: 120px;
        height: 120px;
      }
    }

    .el-date-editor.el-input {
      width: 100%;
    }

    .workInfo-box {
      flex: auto;
      overflow-y: auto;
      // padding: 15px 10px 0 10px;
      // width: 60%;
      // margin-left: 50px;
      padding: 20px 100px 20px 50px;

      .info-block-box {
        margin-bottom: 34px;

        .info-block-title {
          font-size: 16px;
          font-weight: bold;
        }
        .info-block-cont {
          .block-item {
            margin-top: 15px;
            label {
              color: #606266;
              font-weight: 700;
            }
            span {
            }
          }
        }
      }
    }
  }
}
</style>
