<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申报人" prop="applyNickname">
        <el-input
          v-model="queryParams.applyNickname"
          placeholder="请输入申报人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评优年度" prop="year">
        <el-date-picker
          clearable
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          format="yyyy"
          placeholder="选择评优年度"
        />
      </el-form-item>
      <el-form-item label="评优项目" prop="teachersEvaluationProject">
        <el-select
          v-model="queryParams.teachersEvaluationProject"
          placeholder="请选择评优项目"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.TEACHERS_EVALUATION_PROJECT
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="遴选结果" prop="selectionResult">
        <el-select
          v-model="queryParams.selectionResult"
          placeholder="请选择遴选结果"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.TEACHERS_EVALUATION_SELECTION_RESULT
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleBatch"
          v-hasPermi="['teachers:evaluation-plan-apply-confirm:create']"
        >
          批量确认
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column
        label="申报人"
        align="center"
        prop="applyNickname"
        width="90"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="viewUserInfo(scope.row)">
            {{ scope.row.applyNickname }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="用户名"
        align="center"
        prop="applyUsername"
        width="90"
      />
      <el-table-column label="项目名称" align="center" prop="name" />
      <el-table-column
        label="评优年度"
        align="center"
        prop="year"
        width="100"
      />
      <el-table-column
        label="评优项目"
        align="center"
        prop="teachersEvaluationProject"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHERS_EVALUATION_PROJECT"
            :value="scope.row.teachersEvaluationProject"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="申报时间"
        align="center"
        prop="applyTime"
        width="150"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applyTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="评优内容"
        align="center"
        prop="content"
        width="90"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="viewContent(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="申报材料"
        align="center"
        prop="declareStartDate"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            v-for="item in scope.row.applicationDocument"
            @click="viewPdf(item.url, item.name)"
          >
            {{ item.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="得分"
        align="center"
        prop="selectionScore"
        width="180"
      >
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.selectionScore"
            :min="0"
            :step="0.1"
            label="请根据申报材料打分"
            style="width: 150px"
            @change="(currentValue) => updateScore(currentValue, scope.row)"
          ></el-input-number>
        </template>
      </el-table-column>
      <el-table-column label="遴选结果" align="center" prop="selectionResult">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHERS_EVALUATION_SELECTION_RESULT"
            :value="scope.row.selectionResult"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.selectionResult === 'to_be_confirmed'"
            type="text"
            @click="handleUpdate(scope.row, 'edit')"
            v-hasPermi="['teachers:evaluation-plan-apply-confirm:update']"
          >
            遴选确认
          </el-button>
          <el-button
            v-else
            type="text"
            @click="handleUpdate(scope.row, 'view')"
            v-hasPermi="['teachers:evaluation-plan-apply-confirm:delete']"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <div class="form-block">申报内容</div>
        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="评优计划名称" prop="name">
              <span>{{ form.name }}</span>
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="评优年度" prop="year">
              <span>{{ form.year }}</span>
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="评优项目" prop="teachersEvaluationProject">
              <dict-tag
                :type="DICT_TYPE.TEACHERS_EVALUATION_PROJECT"
                :value="form.teachersEvaluationProject"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="申报人姓名" prop="applyUserName">
              <span>{{ form.applyUserName }}</span>
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="申报用户名" prop="name">
              <span>{{ form.applyUserId }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="form-block">申报材料</div>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="评优内容" prop="content">
              <el-input
                v-model="form.content"
                type="textarea"
                placeholder="简述申请的评优内容"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件信息" prop="applicationDocument">
              <FileUpload
                v-model="form.applicationDocument"
                :limit="1"
                :fileSize="50"
                :fileType="['doc', 'pdf']"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <div class="form-block">申报确认</div>
        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="评选结果" prop="selectionResult">
              <el-radio-group
                v-model="form.selectionResult"
                :disabled="opt === 'view'"
              >
                <el-radio
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.TEACHERS_EVALUATION_SELECTION_RESULT
                  )"
                  v-if="dict.value !== 'to_be_confirmed'"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-if="opt !== 'view'">
          确 定
        </el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审批 -->
    <el-dialog
      title="批量审批"
      :visible.sync="openBatch"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <el-form
        ref="batchForm"
        :model="batchForm"
        :rules="batchRules"
        label-width="100px"
      >
        <el-form-item label="遴选结果" prop="selectionResult">
          <el-radio-group v-model="batchForm.selectionResult">
            <el-radio
              v-for="dict in this.getDictDatas(
                DICT_TYPE.TEACHERS_EVALUATION_SELECTION_RESULT
              )"
              v-if="dict.value !== 'to_be_confirmed'"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchForm">确 定</el-button>
        <el-button @click="cancelBatch">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审批 -->
    <el-dialog
      title="评优内容"
      :visible.sync="openCont"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="contForm" :model="contForm" label-width="80px">
        <el-form-item label="评优内容" prop="content">
          <el-input
            v-model="contForm.content"
            type="textarea"
            placeholder="简述申请的评优内容"
            :disabled="true"
          />
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 职工档案信息 -->
    <use-worder-dialog
      :dialogTitle="useInfoDialogTitle"
      :dialogOpen="useInfoDialogOpen"
      :userId="curRow.applyUserId"
      opt="view"
      @update:dialogOpen="(value) => (useInfoDialogOpen = value)"
    />

    <!-- 学员档案信息 -->
    <user-student-dialog
      :dialogTitle="studentInfoDialogTitle"
      :dialogOpen="studentInfoDialogOpen"
      :userId="curRow.applyUserId"
      opt="view"
      @update:dialogOpen="(value) => (studentInfoDialogOpen = value)"
    />
  </div>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import useWorderDialog from "../../system/userWorker/useWorderDialog";
import userStudentDialog from "../../system/userStudent/userStudentDialog";
import {
  deleteEvaluationPlanApplyConfirm,
  getEvaluationPlanApplyConfirmPage,
  exportEvaluationPlanApplyConfirmExcel,
  updateEvaluationScore,
  updateBatchResult,
  getEvaluationPlanApply,
  updateSelectionResult,
} from "@/api/teachers/evaluationPlanApplyConfirm";
import { getAccessToken } from "@/utils/auth";

export default {
  name: "EvaluationPlanApplyConfirm",
  components: { FileUpload, useWorderDialog, userStudentDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 师资评优计划申请确认列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: undefined,
        applyNickname: undefined,
        year: undefined,
        teachersEvaluationProject: undefined,
        selectionResult: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        teachersEvaluationPlanId: [
          { required: true, message: "评优计划id不能为空", trigger: "blur" },
        ],
        selectionResult: [
          { required: true, message: "遴选结果不能为空", trigger: "change" },
        ],
      },
      openBatch: false,
      batchForm: {
        selectionResult: "be_selected",
      },
      batchRules: {
        selectionResult: [
          { required: true, message: "遴选结果不能为空", trigger: "change" },
        ],
      },
      multipleSelection: [],
      opt: "",
      openCont: false,
      contForm: {
        content: "",
      },
      useInfoDialogTitle: "职工档案信息",
      useInfoDialogOpen: false,
      studentInfoDialogTitle: "员工档案信息",
      studentInfoDialogOpen: false,
      curRow: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getEvaluationPlanApplyConfirmPage(this.queryParams).then((response) => {
        const list = response.data.list || [];
        list.forEach((item) => {
          if (item.applicationDocument) {
            try {
              item.applicationDocument = JSON.parse(item.applicationDocument);
            } catch (error) {
              item.applicationDocument = [];
            }
          }
        });
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        selectionResult: "be_selected",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加师资评优计划申请确认";
    },
    /** 修改按钮操作 */
    handleUpdate(row, opt) {
      this.reset();
      this.opt = opt;
      const id = row.id;
      getEvaluationPlanApply({ id }).then((response) => {
        const details = response.data || {};
        const { planApplyConfirmRespVO, planApplyRespVO, planRespVO } = details;
        planApplyConfirmRespVO.confirmId = planApplyConfirmRespVO.id;
        if (planApplyRespVO.applicationDocument) {
          try {
            planApplyRespVO.applicationDocument = JSON.parse(
              planApplyRespVO.applicationDocument
            );
          } catch (error) {
            planApplyRespVO.applicationDocument = [];
          }
        }
        const data = Object.assign(
          {},
          planApplyConfirmRespVO,
          planApplyRespVO,
          planRespVO
        );
        this.form = data;
        this.open = true;
        this.title = opt === "view" ? "查看遴选详情" : "遴选确认";
      });
    },
    viewUserInfo(row) {
      const { userType } = row;
      this.curRow = row;
      // 职工用户
      if (userType === 1) {
        this.useInfoDialogOpen = true;
        this.useInfoDialogTitle = `${row.applyNickname}职工档案信息`;
      } else {
        this.studentInfoDialogOpen = true;
        this.studentInfoDialogTitle = `${row.applyNickname}员工档案信息`;
      }
    },
    viewPdf(url, name) {
      const token = getAccessToken();
      const path = url + "?token=" + token;
      window.open(path, "_blank");
    },
    viewContent(row) {
      const id = row.id;
      getEvaluationPlanApply({ id }).then((response) => {
        const details = response.data || {};
        const { planApplyRespVO } = details;
        this.openCont = true;
        this.contForm.content = planApplyRespVO.content;
      });
    },
    updateScore(val, row) {
      const id = row.id;
      const params = {
        id: id,
        selectionScore: val,
      };
      updateEvaluationScore(params).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = {
          id: this.form.confirmId,
          selectionResult: this.form.selectionResult,
        };
        // 添加的提交
        updateSelectionResult(params).then((response) => {
          this.$modal.msgSuccess("提交成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除师资评优计划申请确认编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteEvaluationPlanApplyConfirm(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有师资评优计划申请确认数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportEvaluationPlanApplyConfirmExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "师资评优计划申请确认.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    handleSelectionChange(val) {
      console.log("多选：", val);
      this.multipleSelection = val;
    },
    handleBatch() {
      if (this.multipleSelection.length == 0) {
        this.$modal.msgWarning("请选择数据项");
        return;
      }
      this.openBatch = true;
    },
    submitBatchForm() {
      this.$refs["batchForm"].validate((valid) => {
        if (!valid) {
          return;
        }
        const list = this.multipleSelection.map((item) => {
          return {
            id: item.id,
            selectionResult: this.batchForm.selectionResult,
          };
        });
        this.$modal
          .confirm("确认对所选人员进行批量入选/落选操作吗?")
          .then(function () {
            return updateBatchResult(list);
          })
          .then(() => {
            this.openBatch = false;
            this.getList();
            this.$modal.msgSuccess("批量确认成功");
          })
          .catch(() => {});
      });
    },
    cancelBatch() {
      this.openBatch = false;
    },
  },
};
</script>

<style scoped lang="scss">
.form-block {
  font-weight: bold;
  font-size: 16px;
  position: relative;
  padding-left: 20px;
  padding-bottom: 8px;
  border-bottom: 1px #1890ff solid;
  margin-bottom: 15px;

  &::before {
    content: "";
    position: absolute;
    width: 100%;
    width: 7px;
    background: #1890ff;
    top: 0px;
    left: 0;
    bottom: 0;
  }
}
</style>
