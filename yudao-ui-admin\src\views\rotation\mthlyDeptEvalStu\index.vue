<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="70px"
      v-show="showSearch"
    >
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入学员姓名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="评价月份" prop="appraiseMonth">
        <el-date-picker
          v-model="queryParams.appraiseMonth"
          type="month"
          placeholder="选择月"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="评价科室" prop="rotationDepartmentId">
        <el-select
          v-model="queryParams.rotationDepartmentId"
          placeholder="请选择评价科室"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="item in queryDeptList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="评价状态" prop="appraised">
        <el-select
          v-model="queryParams.appraised"
          placeholder="请选择评价状态"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.ROTATION_EVALUATION_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="学员姓名"
        prop="nickname"
        align="center"
      ></el-table-column>
      <el-table-column
        label="用户名"
        prop="username"
        align="center"
      ></el-table-column>
      <el-table-column
        label="评价月份"
        prop="appraiseMonth"
        align="center"
      ></el-table-column>
      <el-table-column
        label="评价科室"
        prop="rotationDepartmentName"
        align="center"
      ></el-table-column>
      <el-table-column label="评价状态" prop="appraised" align="center">
        <template v-slot="scope">
          {{ scope.row.appraised ? "已评价" : "未评价" }}
          <!-- <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.appraised"
          ></dict-tag> -->
        </template>
      </el-table-column>
      <el-table-column label="评价得分" prop="score" align="center">
        <template v-slot="scope">{{
          scope.row.score === null ? "--" : scope.row.score
        }}</template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.score !== null"
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleView(scope.row)"
          >
            查看评价
          </el-button>
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
          >
            进入评价
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <AppraiseDialog
      v-if="formData"
      :title="title"
      :open="open"
      :data="formData"
      :appraiseSourceId="curRow && curRow.id"
      :personalSummary="(curRow && curRow.personalSummary) || ''"
      :disabled="appraiseDisabled"
      @setOpen="setOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import AppraiseDialog from "./appraiseDigitDialog";
import {
  getRotationDepartmentPermissionList,
  getDeptStudentPage,
  getAppraiseItemList,
  exportDeptStudentExcel,
} from "@/api/rotation/mthlyDeptEvalStu";

export default {
  name: "MthlyDeptEvalStu",
  components: {
    AppraiseDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      list: [],
      queryDeptList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: "",
        appraiseMonth: "",
        rotationDepartmentId: "",
        appraised: "",
      },
      formData: null,
      open: false,
      title: "",
      curRow: null,
      appraiseDisabled: false,
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
    };
  },
  created() {
    getRotationDepartmentPermissionList({
      component: "rotation/mthlyDeptEvalStu/index",
    }).then((res) => {
      this.queryDeptList = res.data || [];
    });

    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getDeptStudentPage(this.queryParams).then((response) => {
        const list = response.data.list;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleView(row) {
      const id = row.id;
      this.curRow = row;
      this.appraiseDisabled = true;
      getAppraiseItemList(id).then((response) => {
        this.formData = response.data;
        this.open = true;
        this.title = `查看评价-${row.nickname}`;
      });
    },
    handleEdit(row) {
      const id = row.id;
      this.curRow = row;
      this.appraiseDisabled = false;
      getAppraiseItemList(id).then((response) => {
        const list = response.data || [];
        list.forEach((element) => {
          element.score = element.appraiseProcessItemScore;
        });
        this.formData = list;
        this.open = true;
        this.title = `正在评价-${row.nickname}`;
      });
    },
    setOpen(flag) {
      this.open = flag;
      this.editForm = null;
      this.curRow = null;
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出科室月评价学员数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportDeptStudentExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "科室月评价学员.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>
