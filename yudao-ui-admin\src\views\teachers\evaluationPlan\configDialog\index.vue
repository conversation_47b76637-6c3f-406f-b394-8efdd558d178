<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="600px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="110px">
      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="项目名称" prop="name">
            <span>{{ form.name }}</span>
          </el-form-item>
        </el-col>

        <el-col :md="12" :lg="12" :xl="12">
          <el-form-item label="项目开始日期" prop="name">
            <span>{{ form.declareStartDate }}</span>
          </el-form-item>
        </el-col>

        <el-col :md="12" :lg="12" :xl="12">
          <el-form-item label="项目截止日期" prop="declareEndDate">
            <el-date-picker
              style="width: 100%"
              v-model="form.declareEndDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="截止日期"
              :picker-options="pickerOptions"
            />
          </el-form-item>
        </el-col>

        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="遴选说明" prop="remarks">
            <el-input
              type="textarea"
              v-model="form.remarks"
              placeholder="请在此处填写此次聘任的相关要求/说明"
              :autosize="{ minRows: 2 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm"> 确 认 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateConfig } from "@/api/teachers/evaluationPlan";

export default {
  name: "ConfigDialog",
  components: {},
  props: {
    title: {
      type: String,
    },
    openConfig: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      open: this.openConfig,

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        declareEndDate: [
          {
            required: true,
            message: "项目截止日期不能为空",
            trigger: "change",
          },
        ],
        remarks: [
          { required: true, message: "遴选说明不能为空", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    openConfig(newVal) {
      this.form = {
        ...this.formData,
        declareStartDate: this.formData.declareDate[0],
        declareEndDate: this.formData.declareDate[1],
      };
      console.log("this.form===", this.formData, this.form);
      this.open = newVal;
    },
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate: (time) => {
          if (this.formData.declareStartDate) {
            return (
              time.getTime() <
              new Date(this.formData.declareStartDate).getTime()
            );
          }
          return false;
        },
      };
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:openConfig", false);
    },

    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = {
          ...this.form,
          // declareStartDate: this.form.declareDate[0],
          // declareEndDate: this.form.declareDate[1],
          publishObjects: this.form.publishObjects.join(","),
          applicationDocumentTemplates: this.form.applicationDocumentTemplates
            ? JSON.stringify(this.form.applicationDocumentTemplates)
            : "",
        };
        delete params.declareDate;

        updateConfig(params).then((response) => {
          this.$modal.msgSuccess("配置成功");
          this.$emit("refresh");
          this.cancel();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
