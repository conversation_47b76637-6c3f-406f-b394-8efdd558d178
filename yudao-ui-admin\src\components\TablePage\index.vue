<template>
  <div class="table-page">
    <!-- 搜索工作栏 -->
    <el-form size="small" inline v-show="showSearch">
      <el-form-item v-for="(item, index) in filters" :key="index">
        
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-has-permi="['rotation:teaching-active:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-has-permi="['rotation:teaching-active:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getTableData"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="tableData">
      <slot></slot>
      <slot name="empty"></slot>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="pageNo" :limit.sync="pageSize"
                @pagination="getTableData"/>
  </div>
</template>

<script>
export default {
  name: 'tablePage',
  props: {
    filters: [],
    queryApi: null,
    extraParams: {},
  },
  data() {
    return {
      conditionParams: {},
      showSearch: false,
      total: 0,
      pageNo: 1,
      pageSize: 10,
      tableData: [],
      loading: false,
    }
  },
  computed: {
    queryParams() {
      return { ...this.extraParams, ...this.conditionParams }
    },
  },
  methods: {
    getTableData() {
      this.loading = true;
      this.queryApi(this.queryParams).then(res => {
        this.tableData = res.list;
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      })
    },
  },
  created() {
    this.getTableData();
  },
}
</script>

<style scoped>

</style>
