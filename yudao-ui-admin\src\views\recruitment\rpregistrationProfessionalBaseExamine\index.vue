<template>
  <div class="app-container rpregistrationProfessionalBaseExamine">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="98px"
    >
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报名专业" prop="majorCode">
        <el-select
          v-model="queryParams.majorCode"
          placeholder="请选择报名专业"
          clearable
          filterable
        >
          <el-option
            v-for="item in majorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select
          v-model="queryParams.sex"
          placeholder="请选择性别"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学历" prop="highestAcademic">
        <el-select
          v-model="queryParams.highestAcademic"
          filterable
          clearable
          placeholder="请选择学历"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否服从调剂" prop="isObeyAdjustment">
        <el-select
          v-model="queryParams.isObeyAdjustment"
          filterable
          clearable
          placeholder="请选择是否服从调剂"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="审核状态" prop="professionalBaseExamineStatus">
        <el-select
          v-model="queryParams.professionalBaseExamineStatus"
          filterable
          clearable
          placeholder="请选择审核状态"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.REGISTRATION_EXAMINE_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="审核结果" prop="professionalBaseExamineResult">
        <el-select
          v-model="queryParams.professionalBaseExamineResult"
          filterable
          clearable
          placeholder="请选择审核结果"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.REGISTRATION_PROFESSIONAL_BASE_EXAMINE_RESULT
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="姓名" align="center" prop="name" width="100">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="viewUserInfo(scope.row)">
            {{ scope.row.name }}
          </el-button>
        </template>
      </el-table-column>

      <el-table-column label="性别" align="center" prop="sex">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.sex" />
        </template>
      </el-table-column>

      <el-table-column label="学历" align="center" prop="highestAcademic">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_USER_EDUCATION"
            :value="scope.row.highestAcademic"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="报名专业"
        align="center"
        prop="majorName"
      ></el-table-column>

      <el-table-column label="报名时间" align="center" prop="commitTime" />

      <el-table-column
        label="审核状态"
        align="center"
        prop="professionalBaseExamineStatus"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.REGISTRATION_EXAMINE_STATUS"
            :value="scope.row.professionalBaseExamineStatus"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="审核结果"
        align="center"
        prop="professionalBaseExamineResult"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.REGISTRATION_PROFESSIONAL_BASE_EXAMINE_RESULT"
            :value="scope.row.professionalBaseExamineResult"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="是否服从调剂"
        align="center"
        prop="isObeyAdjustment"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.INFRA_BOOLEAN_STRING"
            :value="scope.row.isObeyAdjustment"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="希望调剂专业"
        align="center"
        prop="hopeMajorNames"
      ></el-table-column>

      <el-table-column
        label="操作"
        align="center"
        width="150"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.professionalBaseExamineStatus === 'to_be_examine'"
            size="mini"
            type="text"
            @click="handleAudit(scope.row)"
            v-hasPermi="[
              'recruitment:rpregistration-professional-base-examine:update',
            ]"
          >
            审批
          </el-button>
          <el-button
            v-if="scope.row.professionalBaseExamineStatus === 'examined'"
            size="mini"
            type="text"
            @click="handleViewAuditInfo(scope.row)"
            v-hasPermi="[
              'recruitment:rpregistration-professional-base-examine:query',
            ]"
          >
            查看审批
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <audit-dialog
      :title="auditDialogTitle"
      :openAudit="openAudit"
      :formData="curRow"
      @update:openAudit="(value) => (openAudit = value)"
      @refresh="getList"
    />

    <registration-info-dialog
      :title="registrationInfoDialogTitle"
      :openRegistrationInfo="openRegistrationInfo"
      :formData="curRow"
      @update:openRegistrationInfo="(value) => (openRegistrationInfo = value)"
      @refresh="getList"
    />

    <audit-info-dialog
      :title="auditInfoDialogTitle"
      :openAuditInfo="openAuditInfo"
      :formData="curRow"
      @update:openAuditInfo="(value) => (openAuditInfo = value)"
      @refresh="getList"
    />
  </div>
</template>

<script>
import AuditDialog from "./auditDialog";
import RegistrationInfoDialog from "./registrationInfoDialog";
import AuditInfoDialog from "../rpregistrationExamine/auditInfoDialog";
import { getSimpleMajorList } from "@/api/system/major";
import { getProfessionalBaseExamPage } from "@/api/recruitment/rpregistrationProfessionalBaseExamine";

export default {
  name: "RpregistrationProfessionalBaseExamine",
  components: { AuditDialog, RegistrationInfoDialog, AuditInfoDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督导表单列表
      list: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: "",
        majorCode: "",
        sex: "",
        highestAcademic: "",
        isObeyAdjustment: "",
        professionalBaseExamineStatus: "to_be_examine",
        professionalBaseExamineResult: "",
      },
      // 弹出层标题
      auditDialogTitle: "审核确认",
      // 是否显示弹出层
      openAudit: false,
      openUpdate: false,
      updateDialogTitle: "专业修改",
      curRow: {},
      majorList: [],
      openRegistrationInfo: false,
      registrationInfoDialogTitle: "报名信息",
      auditInfoDialogTitle: "审核信息",
      openAuditInfo: false,
    };
  },
  created() {
    this.getList();
    getSimpleMajorList().then((res) => {
      this.majorList = res.data;
    });
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.list = [];
      // 执行查询
      getProfessionalBaseExamPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    handleAudit(row) {
      this.curRow = row;
      this.auditDialogTitle = `审核确认-${row.name}`;
      this.openAudit = true;
    },

    viewUserInfo(row) {
      this.curRow = row;
      this.registrationInfoDialogTitle = `报名信息-${row.name}`;
      this.openRegistrationInfo = true;
    },

    handleViewAuditInfo(row) {
      this.curRow = row;
      this.auditInfoDialogTitle = `审核信息-${row.name}`;
      this.openAuditInfo = true;
    },

    /** 表单重置 */
    reset() {
      this.form = {
        formType: "1",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>

<style lang="scss">
.rpregistrationProfessionalBaseExamine {
  position: relative;

  .top-radio-box {
    position: relative;
    margin-bottom: 15px;
    &::before {
      position: absolute;
      content: " ";
      display: block;
      width: 100%;
      height: 1px;
      background: #ddd;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .el-radio-button:first-child .el-radio-button__inner {
      border-radius: 4px 0 0 0;
    }
    .el-radio-button:last-child .el-radio-button__inner {
      border-radius: 0 4px 0 0;
    }
  }

  .seniority-box {
    display: inline-block;
    width: 80px;
  }
  .seniority-line {
    display: inline-block;
    width: 25px;
    text-align: center;
  }
}

.superviseForm-indicator-dialog {
  .el-dialog__body {
    padding-top: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .indicators-wapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .indicators-wapper-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 8px;
      border-bottom: 1px #ddd solid;
    }

    .indicators-wapper-tables {
      max-height: calc(100vh - 240px);
      flex: auto;
      padding-top: 13px;
      overflow-y: auto;
    }
  }
}
</style>
