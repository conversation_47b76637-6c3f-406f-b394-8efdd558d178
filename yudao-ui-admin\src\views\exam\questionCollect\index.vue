<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="题目名称" prop="questionName">
        <el-input
          v-model="queryParams.questionName"
          placeholder="请输入题目id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="序号" align="center" prop="id">
        <template slot-scope="scope">{{
          (queryParams.pageNo - 1) * queryParams.pageSize + scope.$index + 1
        }}</template>
      </el-table-column>
      <el-table-column label="试题内容" align="center" prop="title">
        <template v-slot="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewDetail(scope.row)"
          >
            {{ scope.row.title || "--" }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="试题类型" align="center" prop="questionType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.EXAM_QUESTION_TYPE"
            :value="scope.row.questionType"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="收藏时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleUncollect(scope.row)"
            v-hasPermi="['exam:question-collect:query']"
            >取消收藏</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      title="试题预览"
      :visible.sync="open"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <div class="feedback-item">
        <span class="label">试题内容：</span>
        <question-view :curQuestion="curQuestion" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  deleteQuestionCollect,
  getQuestionCollectPage,
  getQuestionAnswer,
} from "@/api/exam/questionCollect";
import QuestionView from "../components/QuestionView.vue";

export default {
  name: "QuestionCollect",
  components: { QuestionView },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考试题目收藏列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        questionName: null,
      },
      curQuestion: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getQuestionCollectPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 修改按钮操作 */
    viewDetail(row) {
      getQuestionAnswer({ id: row.questionId }).then((response) => {
        const question = response.data;
        try {
          question.content = JSON.parse(question.content);
        } catch (e) {
          question.content = { title: "", choiceList: [] };
        }

        if (question.questionType === "10") {
          const choiceArr = (question.answer || "").split(",").map((key) => ({
            desc: "",
            images: [],
            answer: key,
          }));
          question.choiceArr = choiceArr;
        }
        this.curQuestion = question;
        this.open = true;
      });
    },

    /** 取消收藏按钮操作 */
    handleUncollect(row) {
      const id = row.id;
      this.$modal
        .confirm("确认取消收藏该题目吗")
        .then(function () {
          return deleteQuestionCollect(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("取消收藏成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.feedback-item {
  margin-bottom: 10px;
  .label {
    font-weight: bold;
  }
}
</style>
