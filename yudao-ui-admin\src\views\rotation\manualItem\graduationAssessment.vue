<template>
  <div class="graduation-assessment">
    <el-card shadow="none" v-if="(config.examineItems || '').indexOf('1') > -1">
      <div slot="header">
        <h2>出科理论考核</h2>
        <p>出科前，需要参加出科理论考试。</p>
      </div>
      <div class="exam-info">
        <span>{{ info.rotationDepartmentName }}</span>
        <span class="score-text">{{ examScore >-1 ? `${examScore}分` : '暂未参加考核' }}</span>
        <el-button type="primary" size="mini" @click="joinExam">参加考试</el-button>
        <el-button type="primary" size="mini" @click="viewPaper" v-if="examScore > -1">查看试卷</el-button>
      </div>
    </el-card>

    <el-card shadow="none" v-if="(config.examineItems || '').indexOf('2') > -1">
      <div slot="header">
        <h2>出科技能考核</h2>
        <span class="skill-examiner">技能考官：{{ skillInfo.skillTeacherNames }}</span>
        <p>出科前，需要参加出科技能操作考试。</p>
      </div>
      <div v-if="skillInfo.score">
        <span style="margin-right: 20px">累计用时：{{ skillInfo.costTime }}分钟</span>
        <span style="margin-right: 20px">考核科目：{{ skillInfo.resultFormRespVOS.map(item => item.graduationAssessmentFormName).join(' + ') }}</span>
        <span>考核分数： <el-link type="primary" @click="handleView">{{ skillInfo.score }}</el-link></span>
      </div>
      <div v-else>暂无技能考试信息</div>
    </el-card>

    <el-card shadow="none" v-if="(config.examineItems || '').indexOf('3') > -1">
      <div slot="header">
        <h2>形成性评价</h2>
        <p>以下是您在本科室参加的相关考核。</p>
      </div>
      <div class="list-item" v-for="item in auditFormList" :key="item.createTime">
        <span>考核时间：{{ item.createTime }}</span>
        <span style="width: 350px">考核表单：{{ item.name }}</span>
        <span style="width: 150px">指导老师：{{ item.teacherName }}</span>
        <span>反馈状态：{{ item.feedbackStatus ? "已反馈" : "未反馈" }}</span>
        <div>
          <el-button type="primary" size="mini" @click="toEvaluationFeedback(item)" v-if="!item.feedbackStatus">进入反馈</el-button>
          <el-button type="primary" size="mini" @click="viewEvaluationDetail(item)" v-if="item.feedbackStatus">查看明细</el-button>
        </div>
      </div>
      <div v-if="auditFormList.length === 0">暂无形成性评价</div>
    </el-card>

    <el-dialog title="试卷列表" :visible.sync="paperVisible">
      <ul class="paper-list">
        <li v-for="item in paperList" :key="item.examsoluId">
          <span>{{ item.name }}</span>
          <el-button type="primary" size="mini" @click="startExam(item.id)">开始考试</el-button>
        </li>
        <li v-if="paperList.length === 0">暂未设置试卷</li>
      </ul>
    </el-dialog>

    <el-dialog title="试卷查看" :visible.sync="examResultVisible" width="60%">
      <el-table :data="examResults" border>
        <el-table-column label="试卷名称" prop="paperName" align="center"></el-table-column>
        <el-table-column label="考试时间" prop="startTime" width="170px" align="center"></el-table-column>
        <el-table-column label="交卷时间" prop="endTime" width="170px" align="center"></el-table-column>
        <el-table-column label="试卷得分" prop="score" width="80px" align="center"></el-table-column>
        <el-table-column label="操作" width="90px" align="center">
          <template v-slot="scope">
            <el-link
              v-if="scope.row.isAllowView"
              style="margin-right: 5px"
              type="primary"
              @click="handleResultPreview(scope.row.id)"
            >查看</el-link>
            <el-link
              v-if="scope.row.download"
              type="primary"
              @click="handleResultDownload(scope.row.download)"
            >下载</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <audit-dialog :visible.sync="auditVisible" :schedule-details-id="id" :student-id="info.studentId"></audit-dialog>

    <el-dialog title="查看形成行评价" :visible.sync="checkVisible" width="800px">
      <component v-if="formId" :is="`form-${formId}`" :form-data="formData" check></component>
    </el-dialog>

    <el-dialog title="形成行评价反馈" :visible.sync="feedbackVisible" width="800px">
      <component v-if="formId" :is="`form-${formId}`" :form-data="formData" feedback ref="form"></component>
      <span slot="footer">
        <el-button type="primary" @click="sureFeedback">确定</el-button>
        <el-button @click="feedbackVisible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getGraduationAssessment } from '@/api/rotation/manual'
import { getExamPaperList } from '@/api/exam/paper'
import { getAnswerFinalResult, getOwnAnswerResultList } from '@/api/exam/answerResult'
import { validateAuthenticationCode, getPaperPureConfig } from '@/api/exam/paperConfig'
import { getFormativeEvaluationResultList, updateFormativeEvaluationResult } from '@/api/rotation/formativeEvaluationResult'
import ImageUpload from '@/components/ImageUpload'
import AuditDialog from './auditDialog'

const requireComponents = require.context("@/views/rotation/common/formativeEvaluationForm", false, /\.vue$/);
const componentsObj = {};
requireComponents.keys().forEach(filePath => {
  const componentName = filePath.split("/")[1].replace(/\.vue$/, "");
  const componentConfig = requireComponents(filePath);
  componentsObj[componentName] = componentConfig.default || componentConfig;
});

export default {
  name: 'graduationAssessment',
  components: { ImageUpload, AuditDialog, ...componentsObj },
  props: {
    info: Object,
    config: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      id: +this.$route.query.id,
      examScore: -1,
      paperVisible: false,
      paperList: [],
      skillInfo: {},
      examResultVisible: false,
      examResults: [],
      auditVisible: false,
      auditFormList: [],
      checkVisible: false,
      feedbackVisible: false,
      formId: null,
      formData: {},
      feedbackId: null,
    }
  },
  methods: {
    joinExam() {
      getExamPaperList({
        examePaperType: '1',
        examObjectId: this.id,
        exameType: "rotation_exam",
        openingSetting: "1",
      }).then(res => {
        this.paperList = res?.data || []
        this.paperVisible = true
      })
    },
    startExam(id) {
      getPaperPureConfig(id).then(res => {
        const jump = (code = '') => {
          const url = this.$router.resolve({
            path: '/onlineExam',
            query: { examObjectId: this.id, paperId: id, code }
          }).href
          window.open(url, '_blank')
        }
        if (res.data.isAuthentication) {
          this.$prompt('请输入考试码', '校验确认', {
            inputPattern: /^\d{6}$/,
            inputErrorMessage: '考试码为6位数字'
          }).then(({ value }) => {
            validateAuthenticationCode({ paperId: id, code: value }).then((res) => {
              if (res.data) {
                jump(value)
              }
            })
          })
        } else {
          jump()
        }
      })
    },
    viewPaper() {
      getOwnAnswerResultList({ examePaperType: '1', examObjectId: this.id, exameType: "rotation_exam" }).then(res => {
        this.examResults = res?.data || []
        this.examResultVisible = true
      })
    },
    handleResultPreview(id) {
      const url = this.$router.resolve({
        path: '/examResult',
        query: { id }
      }).href;
      window.open(url, '_blank');
    },
    handleResultDownload(url) {
      window.location.href = url
    },
    handleView() {
      this.auditVisible = true;
    },
    toEvaluationFeedback(row) {
      this.feedbackVisible = true;
      this.formData = JSON.parse(row.result);
      this.formId = row.formId;
      this.feedbackId = row.id;
    },
    sureFeedback() {
      const valid = this.$refs.form.validData();
      if (valid) {
        this.$confirm("提交后不可再修改，是否继续提交？", "提示").then(() => {
          updateFormativeEvaluationResult({ id: this.feedbackId, result: JSON.stringify(this.formData) }).then(() => {
            this.$message.success("反馈成功！");
            this.feedbackVisible = false;
            getFormativeEvaluationResultList(this.id).then(res => this.auditFormList = res.data);
          });
        });
      }
    },
    viewEvaluationDetail(row) {
      this.checkVisible = true;
      this.formData = JSON.parse(row.result);
      this.formId = row.formId;
    },
  },
  created() {
    getGraduationAssessment(this.id).then(res => this.skillInfo = res.data)
    getAnswerFinalResult({
      exameType: 'rotation_exam',
      examePaperType: '1',
      examObjectId: this.id,
    }).then(res => this.examScore = res.data?.score)
    getFormativeEvaluationResultList(this.id).then(res => this.auditFormList = res.data);
  }
}
</script>

<style lang="scss" scoped>
.graduation-assessment {
  font-size: 14px;

  ::v-deep .el-card {
    margin-bottom: 20px;
    position: relative;
  }

  h2 {
    margin: 0;
    font-size: 18px;
  }

  p {
    margin: 10px 0 5px 0;
  }

  .skill-examiner {
    position: absolute;
    right: 20px;
  }

  .exam-info {
    display: flex;
    align-items: center;
  }

  .score-text {
    margin: 0 20px;
    flex-grow: 1;
  }

  .paper-list {
    padding: 0;
    margin: 0;

    li {
      margin: 0 0 20px 0;
      list-style-type: none;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
      border: 1px solid #f1f1f1;
    }
  }

  .list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    &:not(:last-child) {
      border-bottom: 1px solid #eee;
    }
  }
}

.stem-form-item ::v-deep .el-form-item__content {
  width: calc(100% - 90px);
}
</style>
