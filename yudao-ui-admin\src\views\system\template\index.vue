<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模板名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入模板名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="模板类型" prop="templateType">
        <el-select v-model="queryParams.templateType" placeholder="请选择模板类型" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_TEMPLATE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="内容类型" prop="templateContentType">
        <el-select v-model="queryParams.templateContentType" placeholder="请选择内容类型" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_TEMPLATE_CONTENT)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="模板状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择模板状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['system:template:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['system:template:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="模板名称" align="center" prop="name">
        <template slot-scope="scope">
          <el-link type="primary" @click="handlePreview(scope.row)">{{ scope.row.name }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType" />
        </template>
      </el-table-column>
      <el-table-column label="模板类型" align="center" prop="templateType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_TEMPLATE" :value="scope.row.templateType" />
        </template>
      </el-table-column>
      <el-table-column label="内容类型" align="center" prop="templateContentType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_TEMPLATE_CONTENT" :value="scope.row.templateContentType" />
        </template>
      </el-table-column>
      <el-table-column label="模板状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['system:template:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['system:template:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="学员类型" prop="studentType">
          <el-select v-model="form.studentType" placeholder="请选择学员类型">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                       :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板类型" prop="templateType">
          <el-select v-model="form.templateType" placeholder="请选择模板类型">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_TEMPLATE)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容类型" prop="templateContentType">
          <el-select v-model="form.templateContentType" placeholder="请选择内容类型">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_TEMPLATE_CONTENT)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板文件" prop="file" v-if="form.templateContentType === 'doc'">
          <fileUploadInfo v-model="form.file" :limit="1" :file-size="50" :file-type="['doc']" />
        </el-form-item>
        <el-form-item label="模板内容" prop="contentHtml" v-if="form.templateContentType === 'html'">
          <tinymce v-model="form.contentHtml" placeholder="请输入模板内容" autoresize></tinymce>
        </el-form-item>
        <el-form-item label="模板状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                      :key="dict.value" :label="parseInt(dict.value)">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createTemplate, updateTemplate, deleteTemplate, getTemplate, getTemplatePage, exportTemplateExcel } from "@/api/system/template";
import FileUploadInfo from '@/components/FileUploadInfo';
import { getAccessToken } from '@/utils/auth'

export default {
  name: "Template",
  components: {
    FileUploadInfo,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 模板列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        studentType: null,
        templateType: null,
        templateContentType: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "模板名称不能为空", trigger: "blur" }],
        studentType: [{ required: true, message: "学员类型不能为空", trigger: "change" }],
        templateType: [{ required: true, message: "模板类型不能为空", trigger: "change" }],
        templateContentType: [{ required: true, message: "模板内容类型不能为空", trigger: "change" }],
        file: [{ required: true, message: "模板文件不能为空", trigger: "blur" }],
        contentHtml: [{ required: true, message: "模板内容不能为空", trigger: "blur" }],
        status: [{ required: true, message: "模板状态不能为空", trigger: "blur" }],
      },
      // previewOpen: false,
      // previewTitle: "模板预览",
      // docUrl: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getTemplatePage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        studentType: undefined,
        templateType: undefined,
        templateContentType: 'doc',
        file: undefined,
        contentHtml: undefined,
        status: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加模板";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getTemplate(id).then(response => {
        const [url, name] = (response.data.file || '').split('?filename=');
        this.form = response.data;
        this.form.file = url ? [{ url, name: name || url }] : [];
        this.open = true;
        this.title = "修改模板";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        const { file, ...rest } = this.form;
        const data = { ...rest, file: file && file.length > 0 ? `${file[0].url}?filename=${file[0].name}` : '' };
        // 修改的提交
        if (data.id != null) {
          updateTemplate(data).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createTemplate(data).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除模板编号为"' + id + '"的数据项?').then(function() {
          return deleteTemplate(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有模板数据项?').then(() => {
          this.exportLoading = true;
          return exportTemplateExcel(params);
        }).then(response => {
          this.$download.excel(response, '模板.xls');
          this.exportLoading = false;
        }).catch(() => {});
    },
    /** 预览文档 */
    handlePreview(row) {
      const url = 'http://view.officeapps.live.com/op/view.aspx?src=' + row.file.split('?filename=')[0] + `?token=${getAccessToken()}`;
      window.open(url, '_blank');
      // this.previewOpen = true;
      // this.previewTitle = "模板预览-" + row.name;
      // this.docUrl = row.file.split('?filename=')[0] + `?token=${getAccessToken()}`;
    },
  }
};
</script>

<style lang="scss">
.template-preview-dialog {
  max-width: 100%;
  max-height: 100%;

  iframe {
    width: 100%;
    height: calc(100vh - 118px);
    border: none;
  }
}

.tox-tinymce-aux {
  z-index: 3000!important;
}
</style>
