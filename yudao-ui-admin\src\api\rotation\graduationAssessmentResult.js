import request from '@/utils/request'

// 创建出科技能考核
export function createGraduationAssessmentResult(data) {
  return request({
    url: '/rotation/graduation-assessment-result/create',
    method: 'post',
    data: data
  })
}

// 更新出科技能考核
export function updateGraduationAssessmentResult(data) {
  return request({
    url: '/rotation/graduation-assessment-result/update',
    method: 'put',
    data: data
  })
}

// 删除出科技能考核
export function deleteGraduationAssessmentResult(id) {
  return request({
    url: '/rotation/graduation-assessment-result/delete?id=' + id,
    method: 'delete'
  })
}

// 获得出科技能考核
export function getGraduationAssessmentResult(id) {
  return request({
    url: '/rotation/graduation-assessment-result/get?id=' + id,
    method: 'get'
  })
}

// 获得出科技能考核分页
export function getGraduationAssessmentResultPage(query) {
  return request({
    url: '/rotation/graduation-assessment-result/page',
    method: 'get',
    params: query
  })
}

// 导出出科技能考核 Excel
export function exportGraduationAssessmentResultExcel(query) {
  return request({
    url: '/rotation/graduation-assessment-result/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取精简出科技能考核列表
export function getGraduationSkillSimpleList(query) {
  return request({
    url: '/rotation/graduation-assessment-form/list-all-simple',
    method: 'get',
    params: query
  })
}

// 获取考核表单
export function getGraduationSKillForm(query){
  return request({
    url: '/rotation/graduation-assessment-result/get-forms',
    method: 'get',
    params: query
  })
}

// 保存考核表单
export function saveGraduationSkillForm(data) {
  return request({
    url: '/rotation/graduation-assessment-result/create',
    method: 'post',
    data
  })
}

// 获得出科技能考核
export function getGraduationSkillData(id) {
  return request({
    url: '/rotation/graduation-assessment-result/get',
    method: 'get',
    params: { id }
  })
}
