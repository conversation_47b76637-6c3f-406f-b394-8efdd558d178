<template>
  <div class="app-container graduationApplicationCertificate-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="学员姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          filterable
          placeholder="请选择学员类型"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          clearable
          size="small"
        >
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          size="small"
          clearable
        >
          <el-option
            v-for="grade in gradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="queryParams.certificateStatus"
          >未下发</el-checkbox
        >
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['rotation:graduation-certificate:update']"
        >
          信息导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          :disabled="multipleSelection.length === 0"
          :loading="batchCreateLoading"
          v-hasPermi="['rotation:graduation-certificate:update']"
          @click="handleBatchCreate"
        >
          批量下发
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" fixed></el-table-column>
      <el-table-column label="学员姓名" align="center" prop="nickname" fixed />
      <el-table-column label="登录账号" align="center" prop="nickname" fixed />
      <el-table-column
        label="学员类型"
        align="center"
        prop="studentType"
        fixed
      />
      <el-table-column label="培训专业" align="center" prop="major" />
      <el-table-column label="培训项目" align="center" prop="project" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column
        label="培训开始时间"
        align="center"
        width="110"
        prop="trainingStartDate"
      />
      <el-table-column
        label="培训结束时间"
        align="center"
        width="110"
        prop="trainingEndDate"
      />
      <el-table-column label="培训时长" align="center" prop="rotationTime" />
      <el-table-column
        label="结业时间"
        align="center"
        width="110"
        prop="graduationDate"
      />
      <el-table-column
        label="证书编号"
        align="center"
        width="150"
        prop="certificateId"
      />
      <el-table-column label="Ⅰ类学分" align="center" prop="creditsOne" />
      <el-table-column label="Ⅱ类学分" align="center" prop="creditsTwo" />
      <el-table-column
        label="证书下发状态"
        align="center"
        width="100"
        prop="certificateStatus"
      >
        <template slot-scope="scope">
          {{ scope.row.certificateStatus ? "已下发" : "未下发" }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="200"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="!scope.row.certificateStatus"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            >信息修改</el-button
          >
          <el-button
            v-if="!scope.row.certificateStatus"
            size="mini"
            type="text"
            @click="handleSend(scope.row)"
            >证书下发</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            @click="handleView(scope.row)"
            >证书查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 信息导入 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      title="证书信息修改"
      :visible.sync="editOpen"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="editForm" :model="editForm" label-width="110px">
        <el-row :gutter="10">
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="学员姓名" prop="nickname">
              <el-input
                v-model="editForm.nickname"
                placeholder="请输入学员姓名"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="登录账号" prop="username">
              <el-input
                v-model="editForm.username"
                placeholder="请输入登录账号"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="学员类型" prop="studentType">
              <el-input
                v-model="editForm.studentType"
                placeholder="请输入学员类型"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="培训专业" prop="major">
              <el-select
                v-model="editForm.major"
                placeholder="请选择培训专业"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in queryMajorList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="进修项目" prop="project">
              <el-input
                v-model="editForm.project"
                placeholder="请输入进修项目"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="年级" prop="grade">
              <el-input v-model="editForm.grade" placeholder="请输入年级" />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="培训时间" prop="trainingDate">
              <el-date-picker
                style="width: 100%"
                v-model="editForm.trainingDate"
                type="datetimerange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="培训时长" prop="rotationTime">
              <el-input
                v-model="editForm.rotationTime"
                placeholder="请输入培训时长"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="结业时间" prop="graduationDate">
              <el-date-picker
                v-model="editForm.graduationDate"
                type="date"
                placeholder="选择日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="证书编号" prop="certificateId">
              <el-input
                v-model="editForm.certificateId"
                placeholder="请输入证书编号"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="Ⅰ类学分" prop="creditsOne">
              <el-input-number
                v-model="editForm.creditsOne"
                placeholder="请输入Ⅰ类学分"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="Ⅱ类学分" prop="creditsTwo">
              <el-input-number
                v-model="editForm.creditsTwo"
                placeholder="请输入Ⅱ类学分"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitEditForm()"
          :loading="submitEditLoading"
          >确 定</el-button
        >
        <el-button @click="cancelEditForm">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 对话框-->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="880px"
      v-dialogDrag
      append-to-body
      custom-class="talkRecord-dialog"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="下发方式" prop="certificateMethod">
          <el-radio-group v-model="form.certificateMethod">
            <el-radio
              v-for="dict in this.getDictDatas(DICT_TYPE.CERTIFICATE_METHOD)"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>

        <el-form-item label="结业时间" prop="graduationDate">
          <el-date-picker
            clearable
            v-model="form.graduationDate"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择结业时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item
          v-if="form.certificateMethod === '2'"
          label="证书上传"
          prop="certificateUrl"
        >
          <imageUpload
            v-model="form.certificateUrl"
            :limit="1"
            activeTypeName=""
          />
        </el-form-item>

        <el-row :gutter="10" v-if="form.certificateMethod === '1'">
          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="学员姓名" prop="nickname">
              <el-input
                v-model="form.nickname"
                placeholder="请输入学员姓名"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="登录账号" prop="username">
              <el-input
                v-model="form.username"
                placeholder="请输入登录账号"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="学员类型" prop="studentType">
              <el-input
                v-model="form.studentType"
                placeholder="请输入学员类型"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="培训时间" prop="trainingDate">
              <el-date-picker
                style="width: 100%"
                v-model="form.trainingDate"
                type="datetimerange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="进修项目" prop="project">
              <el-input v-model="form.project" placeholder="请输入进修项目" />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="培训时长" prop="rotationTime">
              <el-input
                v-model="form.rotationTime"
                placeholder="请输入培训时长"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="证书编号" prop="certificateId">
              <el-input
                v-model="form.certificateId"
                placeholder="请输入证书编号"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="Ⅰ类学分" prop="creditsOne">
              <el-input-number
                v-model="form.creditsOne"
                placeholder="请输入Ⅰ类学分"
              />
            </el-form-item>
          </el-col>

          <el-col :md="12" :lg="12" :xl="12">
            <el-form-item label="Ⅱ类学分" prop="creditsTwo">
              <el-input-number
                v-model="form.creditsTwo"
                placeholder="请输入Ⅱ类学分"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 对话框-->
    <el-dialog
      :title="viewTitle"
      :visible.sync="openView"
      width="880px"
      v-dialogDrag
      append-to-body
      custom-class="graduationApplicationCertificate-dialog"
    >
      <div class="preview-img-box">
        <img :src="dialogImageUrl" class="preview-img" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";
import { getSimpleMajorList } from "@/api/system/major";
import { getStudentGradeList } from "@/api/system/userStudent";
import {
  getCertificatePage,
  createCertificate,
  importTemplate,
  updateCertificate,
  getGraduationCertificate,
  batchCreateCertificate,
  getTemplateByType,
  previewDocTemplateUrl,
} from "@/api/rotation/graduationApplicationCertificate";
import { getAccessToken } from "@/utils/auth";
import { getBaseHeader } from "@/utils/request";

export default {
  name: "GraduationApplicationCertificate",
  components: {
    FileUpload,
    ImageUpload,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      batchCreateLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级培训列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: null,
        major: null,
        grade: null,
        certificateStatus: true,
        studentType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        certificateUrl: [
          { required: true, message: "请上传证书", trigger: "change" },
        ],
        // communicationWay: [{ required: true, message: "交流方式不能为空", trigger: "change" }],
        // communicationDate: [{ required: true, message: "交流日期不能为空", trigger: "change" }],
        // communicationContent: [{ required: true, message: "主要交流内容不能为空", trigger: "blur" }],
      },
      curActive: {},

      // 年级列表
      gradeList: [],
      queryMajorList: [],

      openView: false,
      viewTitle: "",
      dialogImageUrl: "",
      multipleSelection: [],
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/admin-api/rotation/graduation-certificate/import",
      },
      editOpen: false,
      editForm: {},
      submitEditLoading: false,
    };
  },
  created() {
    getSimpleMajorList().then((res) => {
      this.queryMajorList = res.data;
    });
    getStudentGradeList().then((res) => (this.gradeList = res.data));
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getCertificatePage({
        ...this.queryParams,
        certificateStatus: !this.queryParams.certificateStatus,
      }).then((response) => {
        const list = response.data.list;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      // this.resetForm("form");
      this.form = {
        id: undefined,
        certificateMethod: "1",
        certificateUrl: undefined,
        graduationDate: "",
        nickname: "",
        username: "",
        studentType: "",
        project: "",
        rotationTime: "",
        certificateId: "",
        creditsOne: "",
        creditsTwo: "",
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(val) {
      console.log("handleSelectionChange===", val);
      this.multipleSelection = val;
    },
    handleUpdate(row) {
      this.editForm = {};
      getGraduationCertificate(row.id).then((res) => {
        const data = res.data || {};
        this.editForm = data;
        const { trainingStartDate, trainingEndDate } = data;
        if (trainingStartDate && trainingEndDate) {
          this.editForm.trainingDate = [trainingStartDate, trainingEndDate];
        }
        this.editOpen = true;
      });
    },

    submitEditForm() {
      this.$refs["editForm"].validate((valid) => {
        if (!valid) {
          return;
        }
        this.submitEditLoading = true;
        console.log("this.editForm===", this.editForm);
        const params = {
          ...this.editForm,
        };
        params.trainingStartDate = this.editForm.trainingDate[0];
        params.trainingEndDate = this.editForm.trainingDate[1];

        updateCertificate(params).then((res) => {
          this.$modal.msgSuccess("修改成功");
          this.editOpen = false;
          this.getList();
          this.submitEditLoading = false;
        });
      });
    },
    cancelEditForm() {
      this.editOpen = false;
      this.editForm = {};
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "信息导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.$download.excel(response, "信息导入模板.xlsx");
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.open = false;

      // 拼接提示语
      let data = response.data;
      console.log("handleFileSuccess=====", data);
      let text = "创建成功数量：" + data.updateUsernames.length;
      for (const name of data.updateUsernames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + name;
      }
      text +=
        "<br />创建失败数量：" + Object.keys(data.failureUsernames).length;
      for (const name in data.failureUsernames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          name +
          "：" +
          data.failureUsernames[name];
      }
      this.$alert(text, "导入结果", {
        dangerouslyUseHTMLString: true,
        customClass: "import-result-alert",
      });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    handleSend(item) {
      this.reset();
      this.curActive = item;
      this.title = `证书下发--${item.nickname}`;
      getGraduationCertificate(item.id).then((res) => {
        const data = res.data || {};
        this.form = data;
        this.form.id = item.id;
        this.form.certificateMethod = "1";
        this.form.graduationDate = item.graduationDate;
        const { trainingStartDate, trainingEndDate } = data;
        if (trainingStartDate && trainingEndDate) {
          this.form.trainingDate = [trainingStartDate, trainingEndDate];
        }
        this.open = true;
      });
    },
    handleView(item) {
      this.curActive = item;
      if (item.certificateMethod == 1) {
        const params = {
          studentType: item.studentTypeCode,
          templateType: "graduation_certificate",
        };
        getTemplateByType(params).then((res) => {
          const url = previewDocTemplateUrl(
            res.data.id,
            item.id,
            `graduationCertificate${item.id}`
          );
          window.open(url);
        });
      } else {
        this.viewTitle = `证书查看--${item.nickname}`;
        this.dialogImageUrl = `${
          item.certificateUrl
        }?token=${getAccessToken()}`;
        this.openView = true;
      }
    },

    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        console.log("this.form===", this.form);
        const params = { ...this.form };
        // 提交
        createCertificate(params).then((response) => {
          this.$modal.msgSuccess("下发成功");
          this.open = false;
          this.getList();
        });
      });
    },
    handleBatchCreate() {
      this.batchCreateLoading = true;
      const ids = this.multipleSelection.map((item) => item.id).join(",");
      batchCreateCertificate(ids).then((response) => {
        this.batchCreateLoading = false;
        this.$modal.msgSuccess("批量下发成功");
        this.getList();
      });
    },
  },
};
</script>

<style lang="scss">
.graduationApplicationCertificate-container {
}

.talkRecord-dialog {
  .el-dialog__body {
    padding-right: 100px;
  }
}

.graduationApplicationCertificate-dialog {
  .preview-img-box {
    text-align: center;
  }
  .preview-img {
    max-width: 100%;
    max-height: 600px;
  }
}
</style>
