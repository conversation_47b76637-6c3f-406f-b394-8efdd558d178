<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="110px">
    <el-row :gutter="10">
      <el-col :md="24" :lg="24" :xl="24">
        <el-row :gutter="10">
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="姓名：" prop="nickname">
              <el-input
                v-model="form.nickname"
                placeholder="请输入姓名"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="用户名：" prop="username">
              <el-input
                v-model="form.username"
                placeholder="请输入用户名"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="职工类型：" prop="workTypes">
              <el-select
                v-model="form.workTypes"
                filterable
                placeholder="请选择职工类型"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_WORK_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="婚姻状况：" prop="maritalStatus">
              <el-radio-group v-model="form.maritalStatus">
                <el-radio
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_MARITAL_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="证件类型：" prop="certificateType">
              <el-select
                v-model="form.certificateType"
                filterable
                placeholder="请选择证件类型"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_CERTIFICATE_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="证件号码：" prop="certificateNumber">
              <el-input
                v-model="form.certificateNumber"
                placeholder="请输入证件号码"
                @blur="getIdcardInfoHandler"
                maxlength="25"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="用户性别：" prop="sex">
              <el-select
                v-model="form.sex"
                filterable
                placeholder="请选择用户性别"
              >
                <el-option
                  v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="出生日期：" prop="birthday">
              <el-date-picker
                clearable
                v-model="form.birthday"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择出生日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="是否党员：" prop="isPartyMember">
              <el-select
                v-model="form.isPartyMember"
                filterable
                placeholder="请选择是否党员"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.INFRA_BOOLEAN_STRING
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="民族：" prop="nation">
              <el-select
                v-model="form.nation"
                filterable
                placeholder="请选择民族"
              >
                <el-option
                  v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_NATION)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="手机号码：" prop="mobile">
              <el-input v-model="form.mobile" placeholder="请输入手机号码" />
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="8">
            <el-form-item label="健康状况：" prop="healthStatus">
              <el-select
                v-model="form.healthStatus"
                filterable
                placeholder="请选择健康状况"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_USER_HEALTH_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <!-- <el-col :md="8" :lg="8" :xl="8">
              <userAvatar :user="form" @info="getAvatar" opt="edit" />
            </el-col> -->
    </el-row>

    <el-row :gutter="10">
      <el-col :md="8" :lg="8" :xl="8">
        <el-form-item label="用户邮箱：" prop="email">
          <el-input
            v-model="form.email"
            placeholder="请输入用户邮箱"
            maxlength="100"
          />
        </el-form-item>
      </el-col>
      <el-col :md="8" :lg="8" :xl="8">
        <el-form-item label="毕业时间：" prop="graduationDate">
          <el-date-picker
            clearable
            v-model="form.graduationDate"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择毕业时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :md="8" :lg="8" :xl="8">
        <el-form-item label="毕业院校：" prop="graduationSchool">
          <el-input
            v-model="form.graduationSchool"
            placeholder="请输入毕业院校"
            maxlength="25"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :md="8" :lg="8" :xl="8">
        <el-form-item label="学历：" prop="education">
          <el-select
            v-model="form.education"
            filterable
            placeholder="请选择学历"
          >
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :md="8" :lg="8" :xl="8">
        <el-form-item label="学位：" prop="degree">
          <el-select v-model="form.degree" filterable placeholder="请选择学位">
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_DEGREE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :md="8" :lg="8" :xl="8">
        <el-form-item label="工作开始时间：" prop="workStartDate">
          <el-date-picker
            clearable
            v-model="form.workStartDate"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择工作开始时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-row :gutter="10">
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="职称：" prop="positionalTitles">
            <el-select
              v-model="form.positionalTitles"
              filterable
              placeholder="请选择职称"
            >
              <el-option
                v-for="dict in this.getDictDatas(
                  DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="取得时间：" prop="obtainDate">
            <el-date-picker
              clearable
              v-model="form.obtainDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="选择取得时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="开户银行：" prop="depositBank">
            <el-input
              v-model="form.depositBank"
              placeholder="请输入开户银行"
              maxlength="25"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="是否全科师资：" prop="isGeneralTeacher">
            <el-radio-group v-model="form.isGeneralTeacher">
              <el-radio
                v-for="dict in this.getDictDatas(
                  DICT_TYPE.INFRA_BOOLEAN_STRING
                )"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="是否骨干师资：" prop="isKeyTeacher">
            <el-radio-group v-model="form.isKeyTeacher">
              <el-radio
                v-for="dict in this.getDictDatas(
                  DICT_TYPE.INFRA_BOOLEAN_STRING
                )"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="银行卡号：" prop="bankCardNo">
            <el-input
              v-model="form.bankCardNo"
              placeholder="请输入银行卡号"
              maxlength="25"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="学历证书：" prop="educationCertificate">
            <imageUpload
              v-model="form.educationCertificate"
              :limit="1"
              :disabled="false"
              :isShowTip="true"
            />
          </el-form-item>
        </el-col>

        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="学位证书：" prop="degreeCertificate">
            <imageUpload
              v-model="form.degreeCertificate"
              :limit="1"
              :disabled="false"
              :isShowTip="true"
            />
          </el-form-item>
        </el-col>

        <el-col :md="8" :lg="8" :xl="8">
          <el-form-item label="职称证书：" prop="positionalTitlesCertificate">
            <imageUpload
              v-model="form.positionalTitlesCertificate"
              :limit="1"
              :disabled="false"
              :isShowTip="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-row>

    <el-row :gutter="10">
      <el-col :md="24" :lg="24" :xl="24">
        <div style="margin-bottom: 5px">
          <b>师资证书列表（点击下方右侧“+”号添加）</b>
        </div>
        <certificateTable
          :formItem="teacherCertificateUpdateReqVOS"
          opt="edit"
          :majorList="majorList"
          @change="setFormItems"
        />
      </el-col>
    </el-row>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import userAvatar from "./workUserAvatar";
import ImageUpload from "@/components/ImageUpload";
import { getIdcardInfo } from "@/api/system/userStudent";
import { getCurrentUserWorker } from "@/api/system/userWorker";
import { updateUserWorker } from "@/api/system/user";
import { getSimpleMajorList } from "@/api/system/major";
import certificateTable from "../../userWorker/certificateTable";

export default {
  props: {
    user: {
      type: Object,
    },
  },
  components: { userAvatar, ImageUpload, certificateTable },
  data() {
    const checkCertificateNumber = (rule, value, callback) => {
      if (this.form.certificateType == 1 && value) {
        const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的证件号码"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const checkMobile = (rule, value, callback) => {
      if (value) {
        const reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的手机号码"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const checkEmail = (rule, value, callback) => {
      if (value) {
        const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        if (!reg.test(value)) {
          return callback(new Error("请输入正确的邮箱地址"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      form: {},
      teacherCertificateUpdateReqVOS: [],
      // 表单校验
      rules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
          {
            min: 4,
            max: 16,
            message: "长度在 4 到 16 个字符",
            trigger: "blur",
          },
          // {
          //     pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,16}$/,
          //     message: "用户名由数字和字母组成",
          //     trigger: "blur"
          // }
        ],
        nickname: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        certificateNumber: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkCertificateNumber, trigger: "blur" },
        ],
        mobile: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkMobile, trigger: "blur" },
        ],
        email: [
          { required: false, message: "", trigger: "blur" },
          { validator: checkEmail, trigger: "blur" },
        ],
      },
      majorList: [],
    };
  },
  created() {
    getSimpleMajorList().then((res) => {
      this.majorList = res.data;

      getCurrentUserWorker().then((response) => {
        this.form = response.data;
        this.form.isPartyMember = this.form.isPartyMember?.toString();
        this.teacherCertificateUpdateReqVOS =
          response.data.teacherCertificateUpdateReqVOS;
      });
    });
  },
  methods: {
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let params = {
            ...this.form,
          };
          params.teacherCertificateUpdateReqVOS =
            this.teacherCertificateUpdateReqVOS.map((item) => ({
              ...item,
              userId: this.user.id,
            }));
          updateUserWorker(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
          });
        }
      });
    },
    close() {
      this.$tab.closePage();
    },
    /** 设置头像*/
    getAvatar(picUrl) {
      this.form.avatar = picUrl;
    },
    /** 获取身份证相关信息*/
    getIdcardInfoHandler(e) {
      if (this.form.certificateType == 1) {
        const params = {
          certificateNumber: this.form.certificateNumber,
          certificateType: "1",
        };
        getIdcardInfo(params).then((response) => {
          let birthday = response.data.birthday;
          let tempARR = birthday.split("");
          tempARR.splice(4, 0, "-");
          tempARR.splice(7, 0, "-");
          const _birthday = tempARR.join("");

          this.form.sex = response.data.sex;
          this.form.birthday = _birthday;
        });
      }
    },
    setFormItems(list) {
      console.log("证书数据===", list);
      this.teacherCertificateUpdateReqVOS = list;
    },
  },
};
</script>
