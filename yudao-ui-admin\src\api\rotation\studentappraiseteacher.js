import request from '@/utils/request'

// 获得教学活动分页
export function getStudentappraiseteacherPage(query) {
  return request({
    url: '/rotation/studentappraiseteacher/page',
    method: 'get',
    params: query
  })
}

// 获得教学活动
export function getAppraiseResult(id) {
  return request({
    url: '/rotation/appraise360-result/get?id=' + id,
    method: 'get'
  })
}

// 获得学员评价带教表单
export function getAppraiseForm(id) {
  return request({
    url: '/rotation/studentappraiseteacher/get-form?scheduleDetailsId=' + id,
    method: 'get'
  })
}

// 创建360评价结果
export function createAppraise(data) {
  return request({
    url: '/rotation/appraise360-result/create',
    method: 'post',
    data: data
  })
}

