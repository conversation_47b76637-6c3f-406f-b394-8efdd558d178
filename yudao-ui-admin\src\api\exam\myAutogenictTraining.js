import request from "@/utils/request";

// 获得考试自主训练分页
export function getMyAutogenicTrainingPage(query) {
  return request({
    url: "/exam/my-autogenic-training/page",
    method: "get",
    params: query,
  });
}

// 获得考试自主训练分页
export function getMyAutogenicTraining(query) {
  return request({
    url: "/exam/autogenic-training/get",
    method: "get",
    params: query,
  });
}

// 根据子节点获得考试知识点树
export function getTreeByChildNodes(data) {
  return request({
    url: "/exam/knowledge-point/get-tree-by-child-nodes",
    method: "post",
    data: data,
  });
}

// 获得试卷考试试题
export function getQuestionsByPointId(query) {
  return request({
    url: "/exam/question/get-questions-by-pointId",
    method: "get",
    params: query,
  });
}

// 获得考试试题
export function getQuestionAnswer(query) {
  return request({
    url: "/exam/question/get",
    method: "get",
    params: query,
  });
}

// 创建考试错题反馈
export function createFeedback(data) {
  return request({
    url: "/exam/question-error-feedback/create",
    method: "post",
    data: data,
  });
}

// 根据用户与题目id获得收藏
export function getUserCollectInfo(query) {
  return request({
    url: "/exam/question-collect/get-by-current-user-questionId",
    method: "get",
    params: query,
  });
}

// 创建考试题目收藏
export function createUserCollect(data) {
  return request({
    url: "/exam/question-collect/create",
    method: "post",
    data: data,
  });
}

// 删除考试题目收藏
export function deleteUserCollect(id) {
  return request({
    url: "/exam/question-collect/delete?id=" + id,
    method: "delete",
  });
}
