/* 设置滚动条的样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  /* 滚动槽 */
  /* ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #0000004d;
    border-radius: 10px;
  } */
  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    border-radius: 8px;
     background-color: rgba(200,200,200,0.6);
    /*background-color: #323539;*/
    /* -webkit-box-shadow: inset 0 0 6px #00000080; */
  }
  ::-webkit-scrollbar-thumb:window-inactive {
    background-color: rgba(200,200,200,0.6);
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(200,200,200,0.8);
    -webkit-border-radius: 8px;
  }
  ::-webkit-scrollbar-corner {
    /* background-color: #0d3148; */
    display: none!important
  }

  /* 应用于所有情况
  @-moz-document url-prefix(chrome://), url-prefix(about:),
  url-prefix(file:///), url-prefix(http://), url-prefix(https://){
  */
  /* 滚动条背景基本样式 */
  scrollbar {
    -moz-appearance: none !important;
    background-color: transparent !important; /* 滚动条背景透明 */
    background-image: none !important; /* 滚动条背景图案不显示 */
    position: relative !important; /* 更改滚动条的定位方式为相对 */
    overflow: hidden !important;
    z-index: 999999999 !important; /* 把滚动条提到Z轴最上层 */
  }

  /* 滚动条按钮基本样式 */
  scrollbar thumb {
    -moz-appearance: none !important;
    background-color: #00c1de80 !important;
    border-radius: 10px !important;
    border: 0px !important; /* 滚动条按钮边框 */
    border-color: #0064ff1a !important; /* 滚动条按钮边框颜色和透明度 */
  }

  /* 滚动条按钮:鼠标悬停与点击拖动时基本样式 */
  scrollbar:hover thumb,
  scrollbar thumb:hover,
  scrollbar thumb:active {
    background-color: rgba(221,221,221,0.26) !important;
    border: 0px !important;
  }

  /* 垂直滚动条 */
  /* 把滚动条位置移到屏幕外，这里的像素应该等于垂直滚动条宽度的负值 */
  scrollbar[orient="vertical"] {
    margin-left: -8px !important;
    min-width: 8px !important;
    max-width: 8px !important;
  }

  /* 垂直滚动条按钮的左边框样式 */
  scrollbar thumb[orient="vertical"] {
    border-style: none none none solid !important;
  }

  /* 水平滚动条 */
  /* 把滚动条位置移到屏幕外，这里的像素应该等于垂直滚动条宽度的负值 */
  scrollbar[orient="horizontal"] {
    margin-top: -8px !important;
    height: 8px !important;
    min-height: 8px !important;
    max-height: 8px !important;
  }

  /* 水平滚动条按钮的上边框样式 */
  scrollbar thumb[orient="horizontal"] {
    border-style: solid none none none !important;
  }

  /* 去除垂直与水平滚动条相交汇的角落 */
  scrollbar scrollcorner {
    display: none !important;
  }

  /* 滚动条两端按钮不显示 */
  scrollbar scrollbarbutton {
    display: none !important;
  }