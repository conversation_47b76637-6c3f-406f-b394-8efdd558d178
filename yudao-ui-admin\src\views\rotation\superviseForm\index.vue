<template>
  <div class="app-container superviseForm">

    <div class="top-radio-box">
      <el-radio-group v-model="queryParams.superviseType" size="medium" class="top-radio-group" @input="handleSuperviseTypeChange">
        <el-radio-button v-for="dict in this.getDictDatas(DICT_TYPE.SUPERVISE_TYPE)" :key="dict.value" :label="dict.value">{{dict.label}}</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="表单名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入表单名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="表单类型" prop="formType">
        <el-select v-model="queryParams.formType" placeholder="请选择表单类型" clearable filterable size="small" @change="(val) => handleFormTypeChange(val, 'queryForm')">
          <el-option v-for="dict in formTypeList"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item v-if="queryParams.superviseType == 1" label="开展形式" prop="developForm">
        <el-select v-model="queryParams.developForm" placeholder="请选择开展形式" clearable filterable size="small">
          <el-option v-for="dict in developFormList"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item v-if="queryParams.superviseType == 2"  label="开展对象" prop="developObject">
        <el-select v-model="queryParams.developObject" placeholder="请选择开展形式" clearable filterable size="small">
          <el-option v-for="dict in developObjectList"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:supervise-form:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:supervise-form:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="表单名称" align="center" prop="name" />
      <el-table-column v-if="queryParams.superviseType == 1" label="表单类型" align="center" prop="formType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SUPERVISE_TEACHER_FORM_TYPE" :value="scope.row.formType" />
        </template>
      </el-table-column>
      <el-table-column v-else label="表单类型" align="center" prop="formType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE" :value="scope.row.formType" />
        </template>
      </el-table-column>
      <el-table-column v-if="queryParams.superviseType == 1" label="开展形式" align="center" prop="developName" />
      <el-table-column v-else label="开展对象" align="center" prop="developName" />
      <el-table-column label="表单总分" align="center" prop="score" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['rotation:supervise-form:update']">修改</el-button>
          <el-button v-if="scope.row.status == 0" size="mini" type="text" icon="el-icon-edit" @click="handleMaintain(scope.row, 'edit')"
                     v-hasPermi="['rotation:supervise-form:update']">维护指标</el-button>
          <el-button v-if="queryParams.superviseType == 2" size="mini" type="text" icon="el-icon-document-copy" @click="handleCopy(scope.row, 'copy')"
                     v-hasPermi="['rotation:supervise-form:update']">复制</el-button>
          <el-button v-if="scope.row.status == 1" size="mini" type="text" icon="el-icon-document" @click="handleMaintain(scope.row, 'view')"
                     v-hasPermi="['rotation:supervise-form:update']">查看指标</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['rotation:supervise-form:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" :label-width="queryParams.superviseType == 1 ? '80px' : '150px'">
        <el-form-item label="表单名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入表单名称" :disabled="opt === 'copy'" />
        </el-form-item>
        <el-form-item label="表单类型" prop="formType">
          <el-select v-model="form.formType" placeholder="请选择表单类型" :disabled="opt === 'copy'"
            style="width: 100%;" @change="(val) => handleFormTypeChange(val, 'eidtForm')">
            <el-option v-for="dict in formTypeList"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="queryParams.superviseType == 1" label="开展形式" prop="developForm">
          <el-select v-model="form.developForm" placeholder="请选择开展形式" clearable size="small" style="width: 100%;">
            <el-option v-for="dict in developFormList2"
                        :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item v-if="queryParams.superviseType == 2 && opt !== 'copy'"  label="开展对象" prop="developObject">
          <el-select v-model="form.developObject" multiple placeholder="请选择开展对象" clearable size="small"
            :disabled="opt === 'copy'" style="width: 100%;">
            <el-option v-for="dict in developObjectList2"
                        :key="dict.value" :label="dict.label" :value="dict.value.toString()"/>
          </el-select>
        </el-form-item>
        <el-form-item v-if="queryParams.superviseType == 2 && opt === 'copy'"  label="开展对象" prop="developObject">
          <el-select
            multiple
            v-model="form.developObjects"
            placeholder="请选择开展对象"
            clearable size="small"
            style="width: 100%;"
          >
            <el-option v-for="dict in developObjectList2"
                        :key="dict.value" :label="dict.label" :value="dict.value.toString()"/>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status" :disabled="opt === 'copy'">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                      :key="dict.value" :label="parseInt(dict.value)">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="queryParams.superviseType == 2" label="是否为综合评分表单" prop="isComprehensive">
          <el-radio-group v-model="form.isComprehensive" :disabled="opt === 'copy'">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog custom-class="superviseForm-indicator-dialog" :title="indicatorTitle" :visible.sync="openIndicator" width="900px" v-dialogDrag append-to-body>
      <div class="indicators-wapper">
        <div class="indicators-wapper-head">
          <div>
            <el-button v-if="opt === 'edit'" type="primary" @click="addProject">添加评分项目</el-button>
            <el-button v-if="opt === 'edit'" type="default" @click="addExt">添加扩展文本</el-button>
          </div>
          <span>表单总分：{{ score }}</span>
        </div>

        <div v-if="formItems.length > 0 || formExts.length > 0" class="indicators-wapper-tables">
          <div class="superviseForm-item-wrapper" v-if="formExts.length > 0">
            <div class="form-item" v-for="(ext, index) in formExts" :key="index">
              <el-input class="label-input" size="small" v-model="ext.name" v-if="opt === 'edit'"></el-input>
              <span v-else>{{ ext.name }}：</span>
              <el-input placeholder="请输入" size="small" :disabled="opt !== 'edit'"></el-input>
              <i class="el-icon-delete" @click="deleteExt(index)" v-if="opt === 'edit'"></i>
            </div>
          </div>
          <indicatorTable
            v-for="(formItem, index) in formItems"
            :key="index"
            :NO="index"
            :formItem="formItem"
            :opt="opt"
            @change="setFormItems"
            @delProject="delProject"
            @projectNamechange="projectNamechange"
            :type="queryParams.superviseType"
          />
        </div>
        <div v-else class="indicators-wapper-empty">
          <el-empty description="暂无数据"></el-empty>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitIndicators">确 定</el-button>
        <el-button @click="cancelIndicators">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import indicatorTable from "./indicatorTable";
import { getDictDatas, DICT_TYPE } from '@/utils/dict';
import {getDepartmentSimpleList} from "@/api/system/department";
import { getProfessionalBaseDepartmentPage } from "@/api/system/professionalBaseDepartment";
import { createSuperviseForm, updateSuperviseForm, deleteSuperviseForm, getSuperviseForm,
  getSuperviseFormPage, exportSuperviseFormExcel, getSuperviseFormItems, createSuperviseFormItems, copySuperviseForm } from "@/api/rotation/superviseForm";

export default {
  name: "SuperviseForm",
  components: {
    indicatorTable
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督导表单列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        superviseType: getDictDatas(DICT_TYPE.SUPERVISE_TYPE)[0].value,
        formType: null,
        developObject: null,
        name: null,
        stem: null,
        status: null,
        score: null,
        createTime: [],
        developForm: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        formType: [{ required: true, message: "表单类型不能为空", trigger: "change" }],
        developForm: [{ required: true, message: "开展形式不能为空", trigger: "change" }],
        developObject: [{ required: false, message: "开展对象不能为空", trigger: "change" }],
        name: [{ required: true, message: "表单名称不能为空", trigger: "blur" }],
        status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
        isComprehensive: [{ required: true, message: "是否为综合评分表单不能为空", trigger: "change" }],
      },
      formTypeList: getDictDatas(DICT_TYPE.SUPERVISE_TEACHER_FORM_TYPE),
      developFormList: [],
      developFormList2: [],
      developObjectList: [],
      developObjectList2: [],
      departmentOptions: [],
      openIndicator: false,
      indicatorTitle: '',
      formId: null,
      formExts: [],
      formItems: [],
      score: 0,
      opt: '',
      professionalBaseDepartment: []
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    getProfessionalBaseDepartmentPage({pageNo:1, pageSize: 999}).then(res => {
      const list = res.data.list
      list.forEach(item => {
        item.label = item.professionalBaseName
        item.value = item.professionalBaseValue
      })
      this.professionalBaseDepartment = list
    })
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getSuperviseFormPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then(res => {
        const {data = []} = res
        // 处理 roleOptions 参数
        const list = [];
        data.forEach(item => {
          list.push({
            label: item.name,
            value: item.id
          })
        })
        this.departmentOptions = list;
      })
    },
    handleSuperviseTypeChange(val){
      this.queryParams.name = ''
      this.queryParams.formType = ''
      this.queryParams.developForm = ''
      this.queryParams.developObject = ''
      if (val == 1) {
        this.formTypeList = getDictDatas(DICT_TYPE.SUPERVISE_TEACHER_FORM_TYPE)
      } else {
        this.formTypeList = getDictDatas(DICT_TYPE.SUPERVISE_HOSPITAL_FORM_TYPE)
      }
      this.getList();
    },
    handleFormTypeChange(val, from){
      // debugger
      if (this.queryParams.superviseType == 1) {
        if (val == 1) {
          if (from === 'queryForm') {
            this.developFormList = getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)
          } else {
            this.form.developForm = ''
            this.form.developObject = []
            this.developFormList2 = getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)
          }
        } else {
          if (from === 'queryForm') {
            this.developFormList = getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE)
          } else {
            this.form.developForm = ''
            this.form.developObject = []
            this.developFormList2 = getDictDatas(DICT_TYPE.TEACHING_TRAINING_TYPE)
          }
        }
      } else {
        if (val == 1) {
          // 专业基地
          if (from === 'queryForm') {
            this.developObjectList = this.professionalBaseDepartment
          } else {
            this.developObjectList2 = this.professionalBaseDepartment
          }

        } else if (val == 2) {
          // 教研室
          if (from === 'queryForm') {
            this.developObjectList = getDictDatas(DICT_TYPE.STAFF_ROOM)
          } else {
            this.developObjectList2 = getDictDatas(DICT_TYPE.STAFF_ROOM)
          }

        } else {
          // 科室
          if (from === 'queryForm') {
            this.developObjectList = this.departmentOptions
          } else {
            this.developObjectList2 = this.departmentOptions
          }
        }
      }
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        superviseType: undefined,
        formType: undefined,
        developObject: [],
        developObjects: undefined,
        name: undefined,
        // stem: undefined,
        status: 0,
        isComprehensive: false,
        developForm: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.opt = 'add';
      this.title = "添加督导表单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      this.opt = 'edit';
      getSuperviseForm(id).then(response => {
        this.handleFormTypeChange(response.data.formType, 'eidtForm')
        this.form = response.data;
        let developObject = []
        if(response.data.developObject){
          developObject = response.data.developObject.split(',');
        }
        this.form.developObject = developObject
        console.log('developFormList2=====', this.developFormList2)
        this.open = true;
        this.title = "修改督导表单";
      });
    },
    handleMaintain(row, opt) {
      const id = row.id;
      getSuperviseFormItems(id).then(response => {
        this.formExts = response.data.formExts
        this.formItems = response.data.formItems
        this.score =  response.data.score
        this.formId = response.data.id
        this.opt = opt
        this.openIndicator = true
        this.indicatorTitle = opt === 'edit' ? "表单指标维护" : "查看表单指标";
      });
    },
    handleCopy(row, opt){
      // const id = row.id;
      // getSuperviseFormItems(id).then(response => {
      //   this.formItems = response.data.formItems
      //   this.score =  response.data.score
      //   this.formId = response.data.id
      //   this.opt = opt
      //   this.openIndicator = true
      //   this.indicatorTitle = "复制表单指标";
      // });

      this.reset();
      const id = row.id;
      this.opt = opt
      getSuperviseForm(id).then(response => {
        this.handleFormTypeChange(response.data.formType, 'eidtForm')
        this.form = response.data;
        console.log('developFormList2=====', this.developFormList2)
        this.open = true;
        this.title = "复制表单指标";
      });
    },
    cancelIndicators() {
      this.openIndicator = false;
      // this.reset();
    },
    addExt() {
      this.formExts.push({ name: "属性名称" });
    },
    deleteExt(index) {
      this.formExts.splice(index, 1);
    },
    addProject() {
      const list = JSON.parse(JSON.stringify(this.formItems || []))
      list.push({
        formSubItems: [
          {
            id: undefined,
            name: "",
            score: 0
          }
        ],
        id: undefined,
        name: "",
        score: 0
      })
      this.formItems = list
    },
    computeScore(formItems) {
      let total = 0
      formItems.forEach(item => {
        let subtotal = 0
        if (item.formSubItems && item.formSubItems.length > 0) {
          item.formSubItems.forEach(ele => {
            subtotal = subtotal + parseInt(ele.score)
          })
        }
        item.score = subtotal
        total = total + parseInt(item.score)
      })
      this.score = total
    },
    setFormItems(list, index, field) {
      let formItems = JSON.parse(JSON.stringify(this.formItems || []))
      formItems[index].formSubItems = list
      if (field && field === 'score') {
        this.computeScore(formItems)
      }
      this.formItems = formItems
    },
    delProject(index) {
      let list = JSON.parse(JSON.stringify(this.formItems || []))
      list.splice(index, 1)
      this.formItems = list
      this.computeScore(list)
    },
    projectNamechange(val, index) {
      let list = JSON.parse(JSON.stringify(this.formItems || []))
      list[index].name = val
      this.formItems = list
    },
    submitIndicators() {
      // console.log('submitIndicators===', this.formItems, this.score, this.formId)
      if (this.formItems.length === 0) {
        return this.$modal.msgError("请添加评分项目");
      }
      let proNameFlag = true
      let formSubItemsFlag = true
      let subItemNameFlag = true
      this.formItems.forEach(item => {
        if (!item.name) {
          proNameFlag = false
        }
        if (!item.formSubItems || item.formSubItems.length === 0) {
          formSubItemsFlag = false
        } else {
          item.formSubItems.forEach(ele => {
            if (!ele.name) {
              subItemNameFlag = false
            }
          })
        }
      })
      if (!proNameFlag) {
        return this.$modal.msgError("还有评分项目名称没有填写");
      }
      if (!formSubItemsFlag) {
        return this.$modal.msgError("还有评分项目没有添加评分指标");
      }
      if (!subItemNameFlag) {
        return this.$modal.msgError("还有评分要素没有填写");
      }
      if (this.formExts.some(item => !item.name)) {
        return this.$modal.msgError("还有扩展字段没有填写属性名称");
      }
      const params = {
        id: this.formId,
        formExts: this.formExts,
        formItems: this.formItems,
        score: this.score
      }
      createSuperviseFormItems(params).then(response => {
        this.$modal.msgSuccess("新增成功");
        this.openIndicator = false;
        this.getList();
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        this.form.superviseType = this.queryParams.superviseType
        if (this.opt !== 'copy') {
          const params = {
            ...this.form,
            developObject: this.form.developObject && this.form.developObject.length ? this.form.developObject.join(',') : ''
          }
          // 修改的提交
          if (this.form.id != null) {
            updateSuperviseForm(params).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
            return;
          }
          // 添加的提交
          createSuperviseForm(params).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        } else {
          const params = {
            id: this.form.id,
            developObjects: this.form.developObjects.join(',')
          }
          copySuperviseForm(params).then(response => {
            this.$modal.msgSuccess("复制成功");
            this.open = false;
            this.getList();
          });
        }

      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const {id, name} = row;
      this.$modal.confirm('是否确认删除督导表单为"' + name + '"的数据项?').then(function() {
          return deleteSuperviseForm(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有督导表单数据项?').then(() => {
          this.exportLoading = true;
          return exportSuperviseFormExcel(params);
        }).then(response => {
          this.$download.excel(response, '督导表单.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>

<style lang="scss">
.superviseForm {
  position: relative;

  .top-radio-box{
    position: relative;
    margin-bottom: 15px;
    &::before{
      position: absolute;
      content: ' ';
      display: block;
      width: 100%;
      height: 1px;
      background: #ddd;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .el-radio-button:first-child .el-radio-button__inner{
      border-radius: 4px 0 0 0;
    }
    .el-radio-button:last-child .el-radio-button__inner{
      border-radius: 0 4px 0 0;
    }
  }
}

.superviseForm-indicator-dialog{

.el-dialog__body{
  padding-top: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.indicators-wapper{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .indicators-wapper-head{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;
    border-bottom: 1px #ddd solid;
  }

  .indicators-wapper-tables{
    max-height: calc(100vh - 240px);
    flex: auto;
    padding-top: 13px;
    overflow-y: auto;
  }
}
}

.superviseForm-item-wrapper {
  display: flex;
  gap: 10px 30px;
  flex-wrap: wrap;
  margin-bottom: 10px;

  .el-input {
    width: 128px;
    margin-right: 5px;
  }

  .label-input {
    width: 108px;
  }

  .el-icon-delete {
    font-size: 16px;
    cursor: pointer;
  }
}
</style>
