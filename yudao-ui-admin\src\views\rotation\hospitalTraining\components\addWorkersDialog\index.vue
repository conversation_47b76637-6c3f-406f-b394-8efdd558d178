<template>
  <el-dialog
    title="添加职工"
    :visible="visible"
    width="900px"
    v-dialogDrag
    append-to-body
    @update:visible="$emit('update:visible', $event)"
  >
    <div class="add-student-table">
      <el-form
        :model="queryWorkerParams"
        ref="queryWorkerForm"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="职工姓名" prop="nickname">
          <el-input
            v-model="queryWorkerParams.nickname"
            placeholder="请输入职工姓名"
            clearable
            @keyup.enter.native="handleWorkerQuery"
            style="width: 120"
          />
        </el-form-item>
        <el-form-item label="职工工号" prop="username">
          <el-input
            v-model="queryWorkerParams.username"
            placeholder="请输入职工工号"
            clearable
            @keyup.enter.native="handleWorkerQuery"
            style="width: 120"
          />
        </el-form-item>
        <el-form-item label="角色" prop="roleId">
          <el-select
            v-model="queryWorkerParams.roleId"
            filterable
            clearable
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="item in roleOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="parseInt(item.id)"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="handleWorkerQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetWorkerQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <div>
        <el-table
          v-loading="queryWorderLoading"
          :data="worderList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column
            label="职工姓名"
            width="120"
            align="center"
            prop="nickname"
          />
          <el-table-column label="职工工号" align="center" prop="username" />
          <el-table-column label="角色" align="center" prop="roleNames" />
        </el-table>

        <pagination
          v-show="workerTotal > 0"
          :total="workerTotal"
          :page.sync="queryWorkerParams.pageNo"
          :limit.sync="queryWorkerParams.pageSize"
          @pagination="() => getWorkerList()"
        />
      </div>
      <div
        slot="footer"
        class="dialog-footer"
        style="text-align: right; padding-top: 15px"
      >
        <el-button type="primary" @click="submitAddWorkers">确认添加</el-button>
        <el-button @click="() => $emit('update:visible', false)"
          >取 消</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  getNotJoinedWorkersPage,
  addUsers,
} from "@/api/rotation/hospitalTraining";
import { listSimpleRoles } from "@/api/system/role";

export default {
  name: "AddWorkersDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    curActive: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      queryWorderLoading: false,
      workerTotal: 0,
      queryWorkerParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: "",
        username: "",
        roleId: "",
      },
      userIds: [],
      userTypes: [],
      worderList: [],
      roleOptions: [],
    };
  },
  watch: {
    visible(value) {
      if (value) {
        this.handleWorkerQuery();
        // 获得角色列表
        this.roleOptions = [];
        listSimpleRoles().then((response) => {
          const { data } = response;
          let list = [];
          data.forEach((item) => {
            if (item.code !== "super_admin" && item.code !== "student") {
              list.push(item);
            }
          });
          this.roleOptions = list;
        });
      }
    },
  },
  created() {},
  methods: {
    handleWorkerQuery() {
      this.queryWorkerParams.pageNo = 1;
      this.getWorkerList();
    },
    resetWorkerQuery() {
      this.resetForm("queryWorkerForm");
      this.handleWorkerQuery();
    },

    getWorkerList() {
      this.queryWorderLoading = true;
      const params = {
        ...this.queryWorkerParams,
        hospitalTrainingId: this.curActive.id,
      };
      getNotJoinedWorkersPage(params).then((response) => {
        this.worderList = response.data.list || [];
        this.queryWorderLoading = false;
        this.workerTotal = response.data.total;
      });
    },

    handleSelectionChange(val) {
      // console.log('handleSelectionChange=====', val)
      this.userIds = val.map((item) => item.userId);
      this.userTypes = val.map((item) => item.userType);
    },

    submitAddWorkers() {
      if (this.userIds.length === 0) {
        return this.$modal.msgWarning("请选择学员！");
      }
      const params = {
        hospitalTrainingId: this.curActive.id,
        userIds: this.userIds,
        userType: this.userTypes[0],
      };
      addUsers(params).then((res) => {
        this.$modal.msgSuccess("添加成功");
        this.$emit("update:visible", false);
        this.$emit("refresh");
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
