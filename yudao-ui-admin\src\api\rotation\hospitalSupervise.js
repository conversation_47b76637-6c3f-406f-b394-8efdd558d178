import request from "@/utils/request";

// 创建院级督导
export function createHospitalSupervise(data) {
  return request({
    url: "/rotation/hospital-supervise/create",
    method: "post",
    data: data,
  });
}

// 更新院级督导
export function updateHospitalSupervise(data) {
  return request({
    url: "/rotation/hospital-supervise/update",
    method: "put",
    data: data,
  });
}

// 删除院级督导
export function deleteHospitalSupervise(id) {
  return request({
    url: "/rotation/hospital-supervise/delete?id=" + id,
    method: "delete",
  });
}

// 获得院级督导
export function getHospitalSupervise(id) {
  return request({
    url: "/rotation/hospital-supervise/get?id=" + id,
    method: "get",
  });
}

// 获得院级督导分页
export function getHospitalSupervisePage(query) {
  return request({
    url: "/rotation/hospital-supervise/page",
    method: "get",
    params: query,
  });
}

// 导出院级督导 Excel
export function exportHospitalSuperviseExcel(query) {
  return request({
    url: "/rotation/hospital-supervise/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获取督导专家精简信息列表
export function getSupervisionExpertList() {
  return request({
    url: "/rotation/hospital-supervise/list-supervision-expert",
    method: "get",
  });
}

// 获取整改责任人精简信息列表
export function getSupervisionComprehensiveUser(query) {
  return request({
    url: "/rotation/hospital-supervise/list-comprehensive-user",
    method: "get",
    params: query,
  });
}

// 获得院级督导详情头部信息
export function getHospitalSuperviseHeadInfos(id) {
  return request({
    url: "/rotation/hospital-supervise/get-head-infos",
    method: "get",
    params: { id },
  });
}

// 获得院级督导详情
export function getHospitalSuperviseInfos(id) {
  return request({
    url: "/rotation/hospital-supervise/get-infos",
    method: "get",
    params: { id },
  });
}

// 保存下达意见
export function saveHospitalSuperviseComprehensive(data) {
  return request({
    url: "/rotation/hospital-supervise/save-comprehensive",
    method: "post",
    data,
  });
}

// 获得院级督导历史记录分页
export function getSuperviseHistoryPage(query) {
  return request({
    url: "/rotation/hospital-supervise/page-history",
    method: "get",
    params: query,
  });
}

// 获得院级督导统计分页
export function pageHospitalSuperviseStatistics(query) {
  return request({
    url: "/rotation/hospital-supervise/page-hospital-supervise-statistics",
    method: "get",
    params: query,
  });
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: "/rotation/hospital-supervise/get-import-hospital-supervise-template",
    method: "get",
    responseType: "blob",
  });
}
