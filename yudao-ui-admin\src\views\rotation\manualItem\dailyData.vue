<template>
  <div class="daily-data">
    <el-card shadow="never" style="margin-bottom: 20px;">
      <div slot="header">轮转数据登记情况</div>
      <div class="rotation-content">
        <p class="description-text">当前轮转数据完成比例{{ completionRatio * 100 }}%，要求完成60%方可进行360评价</p>
        <el-collapse accordion @change="handleItemCollapseChange">
          <el-collapse-item v-for="(item, i) in rotationItems" :key="i" :name="i">
            <template slot="title">
              <div class="collapse-title">
                <dict-tag class="title-text" :type="DICT_TYPE.ROTAION_ITEM" :value="item.rotaionItem"></dict-tag>
                <div>
                  <el-progress
                    :text-inside="true"
                    :stroke-width="20"
                    :percentage="Math.min(Math.round(item.examineSuccessCases * 100 / (item.cases || 1)), 100)"
                  ></el-progress>
                  <el-button
                    type="primary"
                    size="mini"
                    @click.stop="handleRecord(item)"
                  >登记</el-button>
                </div>
                <span style="width: 240px">要求：{{ item.cases }}；填写：{{ item.fillCases }}；审核：{{ item.examineSuccessCases }}；</span>
              </div>
            </template>
            <el-collapse v-if="item.subItems" accordion @change="handleSubItemCollapseChange(item.subItems, $event)">
              <el-collapse-item v-for="(subItem, j) in item.subItems" :key="j" :name="j">
                <template slot="title">
                  <div class="collapse-title">
                    <span class="title-text">{{ subItem.subitemName }}</span>
                    <div>
                      <el-progress
                        :text-inside="true"
                        :stroke-width="20"
                        :percentage="Math.min(Math.round(subItem.examineSuccessCases * 100 / (subItem.cases || 1)), 100)"
                      ></el-progress>
                      <el-button type="primary" size="mini" @click.stop="handleRecord(subItem)">登记</el-button>
                    </div>
                    <span>要求：{{ subItem.cases }}；填写：{{ subItem.fillCases }}；审核：{{ subItem.examineSuccessCases }}；</span>
                  </div>
                </template>
                <ul v-if="subItem.caseList">
                  <li v-for="(c, k) in subItem.caseList" :key="k">
                    <div>
                      <span style="margin-right: 30px; display: inline-block; min-width: 130px;">病人姓名：{{ c.patientName }}</span>
                      <span style="margin-right: 30px; display: inline-block; min-width: 150px;">病历号：{{ c.medicalCode }}</span>
                      <span style="margin-right: 30px">登记时间：{{ c.recordDate }}</span>
                      <span style="margin-right: 30px">状态：{{ getDictDataLabel(DICT_TYPE.ROTATION_AUDIT_STATUS, c.auditStatus) }}</span>
                    </div>
                    <div>
                      <el-button v-if="c.auditStatus === '2'" type="danger" size="mini" plain @click="deleteRecord(subItem, k)">删除</el-button>
                      <el-button type="primary" size="mini" plain @click="checkRecord(c, item.subItems)">查看</el-button>
                    </div>
                  </li>
                </ul>
              </el-collapse-item>
            </el-collapse>
            <ul v-if="item.caseList">
              <li v-for="(c, k) in item.caseList" :key="k">
                <div>
                  <span style="margin-right: 30px; display: inline-block; min-width: 130px;">病人姓名：{{ c.patientName }}</span>
                  <span style="margin-right: 30px; display: inline-block; min-width: 150px;">病历号：{{ c.medicalCode }}</span>
                  <span style="margin-right: 30px">登记时间：{{ c.createTime }}</span>
                  <span style="margin-right: 30px">状态：{{ getDictDataLabel(DICT_TYPE.ROTATION_AUDIT_STATUS, c.auditStatus) }}</span>
                </div>
                <div>
                  <el-button v-if="c.auditStatus === '2'" type="danger" size="mini" plain @click="deleteRecord(item, k)">删除</el-button>
                  <el-button type="primary" size="mini" plain @click="checkRecord(c)">查看</el-button>
                </div>
              </li>
            </ul>
          </el-collapse-item>
        </el-collapse>
      </div>

      <el-dialog :visible.sync="recordVisible">
        <dict-tag slot="title" :type="DICT_TYPE.ROTAION_ITEM" :value="recordItem" style="font-size: 18px;"></dict-tag>

        <el-form
          :model="caseRecordForm"
          :rules="caseRecordRules"
          :disabled="isRecordCheck"
          label-width="120px"
          key="caseRecordForm"
          ref="caseRecordForm"
          v-if="formName === 'caseRecordForm'">
          <el-form-item label="病人姓名" prop="patientName" style="display: inline-block; width: 50%;">
            <el-input v-model="caseRecordForm.patientName"></el-input>
          </el-form-item>
          <el-form-item label="病历号" prop="medicalCode" style="display: inline-block; width: 50%">
            <el-input v-model="caseRecordForm.medicalCode"></el-input>
          </el-form-item>
          <el-form-item label="疾病名称" prop="diseaseName" style="display: inline-block; width: 50%">
            <el-input v-model="caseRecordForm.diseaseName"></el-input>
          </el-form-item>
          <el-form-item label="诊断类型" prop="diagnoseType" style="display: inline-block; width: 50%">
            <el-select v-model="caseRecordForm.diagnoseType">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_DIAGNOSE_TYPE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="图片附件" prop="pictures">
            <image-upload v-model="caseRecordForm.pictures" :limit="999"/>
          </el-form-item>
        </el-form>

        <el-form
          :model="diseaseRecordForm"
          :rules="diseaseRecordRules"
          :disabled="isRecordCheck"
          label-width="120px"
          key="diseaseRecordForm"
          ref="diseaseRecordForm"
          v-if="formName === 'diseaseRecordForm'">
          <el-form-item label="病种名称" prop="ruleDepartmentRotationItemId" style="display: inline-block; width: 100%">
            <el-select v-model="diseaseRecordForm.ruleDepartmentRotationItemId">
              <el-option
                v-for="subItem in recordSubItems"
                :key="subItem.ruleDepartmentRotationItemId"
                :label="subItem.subitemName"
                :value="subItem.ruleDepartmentRotationItemId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="病人姓名" prop="patientName" style="display: inline-block; width: 50%">
            <el-input v-model="diseaseRecordForm.patientName"></el-input>
          </el-form-item>
          <el-form-item label="日期" prop="recordDate" style="display: inline-block; width: 50%">
            <el-date-picker v-model="diseaseRecordForm.recordDate" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="病历号/病理号" prop="medicalCode" style="display: inline-block; width: 50%">
            <el-input v-model="diseaseRecordForm.medicalCode"></el-input>
          </el-form-item>
          <el-form-item label="诊断类型" prop="diagnoseType" style="display: inline-block; width: 50%">
            <el-select v-model="diseaseRecordForm.diagnoseType">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_DIAGNOSE_TYPE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="是否主管" prop="isManager" style="display: inline-block; width: 50%">
            <el-radio-group v-model="diseaseRecordForm.isManager">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否抢救" prop="isRescue" style="display: inline-block; width: 50%">
            <el-radio-group v-model="diseaseRecordForm.isRescue">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remarks">
            <el-input type="textarea" v-model="diseaseRecordForm.remarks" placeholder="请在此处记录转归情况与详细的病种信息"></el-input>
          </el-form-item>
        </el-form>

        <el-form
          :model="operationRecordForm"
          :rules="operationRecordRules"
          :disabled="isRecordCheck"
          label-width="120px"
          key="operationRecordForm"
          ref="operationRecordForm"
          v-if="formName === 'operationRecordForm'">
          <el-form-item label="手术名称" prop="ruleDepartmentRotationItemId" style="display: inline-block; width: 100%">
            <el-select v-model="operationRecordForm.ruleDepartmentRotationItemId">
              <el-option
                v-for="subItem in recordSubItems"
                :key="subItem.ruleDepartmentRotationItemId"
                :label="subItem.subitemName"
                :value="subItem.ruleDepartmentRotationItemId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="病人姓名" prop="patientName" style="display: inline-block; width: 50%">
            <el-input v-model="operationRecordForm.patientName"></el-input>
          </el-form-item>
          <el-form-item label="手术日期" prop="recordDate" style="display: inline-block; width: 50%">
            <el-date-picker v-model="operationRecordForm.recordDate" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="病历号" prop="medicalCode" style="display: inline-block; width: 50%">
            <el-input v-model="operationRecordForm.medicalCode"></el-input>
          </el-form-item>
          <el-form-item label="术中职务" prop="intraoperativePosition" style="display: inline-block; width: 50%">
            <el-select v-model="operationRecordForm.intraoperativePosition">
              <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_INTRAOPERATIVE_POSITION)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remarks">
            <el-input type="textarea" v-model="operationRecordForm.remarks" placeholder="请在此处填写手术详细信息"></el-input>
          </el-form-item>
        </el-form>

        <el-form
          :model="skillRecordForm"
          :rules="skillRecordRules"
          :disabled="isRecordCheck"
          key="skillRecordForm"
          label-width="120px"
          ref="skillRecordForm"
          v-if="formName === 'skillRecordForm'">
          <el-form-item label="操作名称" prop="ruleDepartmentRotationItemId" style="display: inline-block; width: 100%">
            <el-select v-model="skillRecordForm.ruleDepartmentRotationItemId">
              <el-option
                v-for="subItem in recordSubItems"
                :key="subItem.ruleDepartmentRotationItemId"
                :label="subItem.subitemName"
                :value="subItem.ruleDepartmentRotationItemId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="病人姓名" prop="patientName" style="display: inline-block; width: 50%">
            <el-input v-model="skillRecordForm.patientName"></el-input>
          </el-form-item>
          <el-form-item label="操作日期" prop="recordDate" style="display: inline-block; width: 50%">
            <el-date-picker v-model="skillRecordForm.recordDate" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="病历号" prop="medicalCode" style="display: inline-block; width: 50%">
            <el-input v-model="skillRecordForm.medicalCode"></el-input>
          </el-form-item>
          <el-form-item label="是否成功" prop="isSuccess" style="display: inline-block; width: 50%">
            <el-radio-group v-model="skillRecordForm.isSuccess">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="失败原因" prop="failReason" v-show="!skillRecordForm.isSuccess">
            <el-input type="textarea" v-model="skillRecordForm.failReason"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remarks">
            <el-input type="textarea" v-model="skillRecordForm.remarks" placeholder="请在此处记录操作技能详细信息"></el-input>
          </el-form-item>
        </el-form>

        <span slot="footer" v-if="!isRecordCheck">
          <el-button type="primary" @click="createRecordData">保存</el-button>
          <el-button @click="cancelRecordData">关闭</el-button>
        </span>
      </el-dialog>
    </el-card>

    <el-card shadow="never">
      <div slot="header">活动参加情况</div>
      <div>
        <p class="description-text">参加各类活动情况统计及要求</p>
        <dl class="active-dl">
          <template v-for="item in activeStatistics">
            <dt :key="item.activeItem">
              <dict-tag :type="DICT_TYPE.ACTIVE_ITEM" :value="item.activeItem" />
              （要求参加：{{ item.cases }}次，总参加次数：{{ item.joinCases }}次）
            </dt>
            <dd v-for="subItem in item.subStatisticses" :key="`${item.activeItem}-${subItem.label}`">
              {{ subItem.label }}{{ subItem.joinCases }}次
            </dd>
            <dd v-if="item.subStatisticses.length === 0">暂无数据</dd>
          </template>
        </dl>
      </div>
    </el-card>
  </div>
</template>

<script>
import ImageUpload from '@/components/ImageUpload';
import {
  getRotationItems,
  getRotationSubItems,
  createRecordData,
  getRecordDataList,
  getRecordData,
  getActiveStatistics,
} from '@/api/rotation/manual'
import { deleteDailyData } from '@/api/rotation/dailyData'

export default {
  name: 'dailyData',
  components: { ImageUpload },
  data() {
    return {
      // 排班详情scheduleDetailsId
      id: this.$route.query.id,
      completionRatio: 0,
      rotationItems: [],
      recordItem: null,
      recordVisible: false,
      isRecordCheck: false,
      // 登记子项列表
      recordSubItems: [],
      // 病历表单
      caseRecordForm: {
        patientName: '',
        medicalCode: '',
        diseaseName: '',
        diagnoseType: '',
        pictures: '',
      },
      caseRecordRules: {
        patientName: [{ required: true, message: '病人姓名必填', trigger: 'blur' }],
        medicalCode: [{ required: true, message: '病历号必填', trigger: 'blur' }],
        diseaseName: [{ required: true, message: '疾病名称必填', trigger: 'blur' }],
        diagnoseType: [{ required: true, message: '诊断类型必填', trigger: 'change' }],
      },
      // 病种表单
      diseaseRecordForm: {
        ruleDepartmentRotationItemId: '',
        patientName: '',
        recordDate: '',
        medicalCode: '',
        diagnoseType: '',
        isManager: false,
        isRescue: false,
        remarks: '',
      },
      diseaseRecordRules: {
        ruleDepartmentRotationItemId: [{ required: true, message: '病种名称必填', trigger: 'blur' }],
        patientName: [{ required: true, message: '病人姓名必填', trigger: 'blur' }],
        recordDate: [{ required: true, message: '日期必填', trigger: 'change' }],
        medicalCode: [{ required: true, message: '病历号必填', trigger: 'blur' }],
        diagnoseType: [{ required: true, message: '诊断类型必填', trigger: 'change' }],
        isManager: [{ required: true, message: '是否主管必填', trigger: 'change' }],
        isRescue: [{ required: true, message: '是否抢救必填', trigger: 'change' }],
      },
      // 手术表单
      operationRecordForm: {
        ruleDepartmentRotationItemId: '',
        patientName: '',
        recordDate: '',
        medicalCode: '',
        intraoperativePosition: '',
        remarks: '',
      },
      operationRecordRules: {
        ruleDepartmentRotationItemId: [{ required: true, message: '手术名称必填', trigger: 'blur' }],
        patientName: [{ required: true, message: '病人姓名必填', trigger: 'blur' }],
        recordDate: [{ required: true, message: '日期必填', trigger: 'change' }],
        medicalCode: [{ required: true, message: '病历号必填', trigger: 'blur' }],
        intraoperativePosition: [{ required: true, message: '术中职务必填', trigger: 'change' }],
      },
      // 操作技能
      skillRecordForm: {
        ruleDepartmentRotationItemId: '',
        patientName: '',
        recordDate: '',
        medicalCode: '',
        isSuccess: true,
        failReason: '',
        remarks: '',
      },
      skillRecordRules: {
        ruleDepartmentRotationItemId: [{ required: true, message: '操作名称必填', trigger: 'blur' }],
        patientName: [{ required: true, message: '病人姓名必填', trigger: 'blur' }],
        recordDate: [{ required: true, message: '操作日期必填', trigger: 'change' }],
        medicalCode: [{ required: true, message: '病历号必填', trigger: 'blur' }],
        isSuccess: [{ required: true, message: '是否成功必填', trigger: 'change' }],
      },
      // 活动统计
      activeStatistics: [],
    }
  },
  computed: {
    formName() {
      return {
        1: 'caseRecordForm',
        2: 'diseaseRecordForm',
        3: 'skillRecordForm',
        4: 'operationRecordForm',
        5: 'caseRecordForm',
        6: 'caseRecordForm',
        7: 'caseRecordForm',
        8: 'caseRecordForm',
      }[this.recordItem]
    }
  },
  methods: {
    handleItemCollapseChange(activeName) {
      const item = this.rotationItems[activeName]
      if (!item) return
      if (item.existsSubItem && !item.subItems) {
        getRotationSubItems(this.id, item.rotaionItem).then(res => {
          this.$set(item, 'subItems', res.data)
        })
      }
      if (!item.existsSubItem && !item.caseList) {
        getRecordDataList({
          scheduleDetailsId: this.id,
          rotationItem: item.rotaionItem,
          ruleDepartmentRotationItemId: item.ruleDepartmentRotationItemId
        }).then(res => this.$set(item, 'caseList', res.data))
      }
    },
    handleSubItemCollapseChange(subItems, activeName) {
      const item = subItems[activeName]
      if (!item.caseList) {
        getRecordDataList({
          scheduleDetailsId: this.id,
          rotationItem: item.rotaionItem,
          ruleDepartmentRotationItemId: item.ruleDepartmentRotationItemId
        }).then(res => this.$set(item, 'caseList', res.data))
      }
    },
    handleRecord(item) {
      this.recordItem = item.rotaionItem
      this.recordVisible = true
      this.isRecordCheck = false
      this.$nextTick(async() => {
        this.$refs[this.formName].resetFields()
        if (item.existsSubItem && !item.subItems) {
          const res = await getRotationSubItems(this.id, item.rotaionItem)
          this.$set(item, 'subItems', res.data)
        }
        if (item.existsSubItem) {
          this.recordSubItems = item.subItems
          this.diseaseRecordForm.ruleDepartmentRotationItemId = ''
        } else {
          this.recordSubItems = [item]
          this.diseaseRecordForm.ruleDepartmentRotationItemId = item.ruleDepartmentRotationItemId
        }
      })
    },
    createRecordData() {
      const data = {
        ...this[this.formName],
        rotaionItem: this.recordItem,
        scheduleDetailsId: this.id
      }
      this.$refs[this.formName].validate(valid => {
        if (valid) {
          createRecordData(data).then(res => {
            this.$message.success('登记成功！')
            this.queryRotationItemList()
            this.cancelRecordData()
          })
        }
      })
    },
    cancelRecordData() {
      try {
        this.$refs[this.formName].resetFields()
      } catch (e) {}
      this.recordVisible = false
    },
    checkRecord(item, subItems) {
      this.recordItem = item.rotaionItem
      this.recordVisible = true
      this.isRecordCheck = true
      if (subItems) {
        this.recordSubItems = subItems
      }
      getRecordData(item.id).then(res => {
        this[this.formName] = res.data
      })
    },
    deleteRecord(item, index) {
      const c = item.caseList[index]
      deleteDailyData(c.id).then(() => {
        item.caseList.splice(index, 1)
        item.fillCases = item.fillCases - 1
      })
    },
    queryRotationItemList() {
      getRotationItems(this.id).then(res => {
        this.completionRatio = res.data.completionRatio
        this.rotationItems = res.data.manualItemSummaryRespVOS
      })
    },
  },
  created() {
    this.queryRotationItemList()
    getActiveStatistics(this.id).then(async res => {
      const dictMap = {
        "1": this.DICT_TYPE.TEACHING_TRAINING_LEVEL, // 院级培训 级别
        "2": this.DICT_TYPE.ROTATION_ACTIVE_TYPE, // 教学活动
      }
      let statistics = res.data || []
      statistics.forEach(item => {
        const dictType = dictMap[item.activeItem]
        if (item.activeItem === '1') {
          item.subStatisticses = item.hospitalTrainingSubStatisticses.map(sub => {
            const label = this.getDictDataLabel(dictType, sub.trainingLevel);
            return { label, joinCases: sub.joinCases };
          })
        }
        if (item.activeItem === '2') {
          const actives = this.getDictDatas(dictType)
          item.subStatisticses = actives.map(active => {
            const match = item.teachingActiveSubStatisticses.find(sub => sub.activeType === active.value)
            return { label: active.label, joinCases: match ? match.joinCases : 0 }
          }).filter(item => item.joinCases)
        }
      })
      this.activeStatistics = statistics
    })
  }
}
</script>

<style lang="scss" scoped>
.daily-data {
  font-size: 14px;

  ::v-deep .el-select, ::v-deep .el-date-editor {
    width: 100%;
  }
}

.collapse-title {
  display: flex;
  justify-content: space-between;
  width: 1000px;
  font-size: 14px;
  padding: 0 10px;

  .title-text {
    display: block;
    width: 140px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    &:hover {
      overflow: visible;
      position: relative;
      z-index: 1;
    }
  }

  ::v-deep .el-progress {
    display: inline-block;
    width: 300px;
    margin-right: 20px;
  }
}

.description-text {
  margin-top: 0;
}

.rotation-content {
  ::v-deep .el-collapse {
    border: 1px solid #e6ebf5
  }

  ::v-deep .el-collapse-item__header {
    background: #fdfdfd;
  }

  ::v-deep .el-collapse-item__wrap {
    padding: 20px;
  }

  ul {
    margin: 0;
    padding: 0;
  }

  li {
    list-style-type: none;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin-bottom: 5px;
  }
}

.active-dl {
  border: 1px solid #eee;

  dt {
    background: #fafafa;
    border-bottom: 1px solid #eee;
    padding: 10px;
  }

  dd {
    border-bottom: 1px solid #eee;
    padding: 10px 0 10px 50px;
    margin: 0;
  }
}
</style>
