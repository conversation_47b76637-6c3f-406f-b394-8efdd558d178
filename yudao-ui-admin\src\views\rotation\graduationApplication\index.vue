<template>
  <div class="app-container graduationApplication-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>结业申请要求与达成情况</span>
      </div>
      <div class="cont info">
        <div v-if="form.admissionEducationSwitch" class="require-item">
          入院教育要求完成次数<span>{{ form.admissionEducationRequire }}</span>次；
          当前实际完成<span :style="[{color: form.admissionEducation < form.admissionEducationRequire ? 'red' : 'green'}]">{{ form.admissionEducation}}</span>次
        </div>
        <div v-if="form.professionalBaseEducationSwitch" class="require-item">
          入专业基地教育要求参与次数<span>{{ form.professionalBaseEducationRequire }}</span>次；
          当前实际完成<span :style="[{color: form.professionalBaseEducation < form.professionalBaseEducationRequire ? 'red' : 'green'}]">{{ form.professionalBaseEducation }}</span>次
        </div>
        <div v-if="form.rotationDepartmentSwitch" class="require-item">
          轮转科室完成率要求（已出科科室/总共轮转科室）<span>{{ form.rotationDepartmentRequire * 100 }} %</span>；
          当前实际完成<span :style="[{color: form.rotationDepartment < form.rotationDepartmentRequire ? 'red' : 'green'}]">{{ (form.rotationDepartment * 100).toFixed(2) }} %</span>
        </div>
        <div v-if="form.rotationDataSwitch" class="require-item">
          轮转数据完成率要求<span>{{ form.rotationDataRequire * 100 }} %</span>；
          当前实际完成<span :style="[{color: form.rotationData < form.rotationDataRequire ? 'red' : 'green'}]">{{ (form.rotationData * 100).toFixed(2) }} %</span>
        </div>
        <div v-if="form.teachingActiveSwitch" class="require-item">
          教学活动参与次数要求<span>{{ form.teachingActiveRequire }}</span>次；
          当前实际参与<span :style="[{color: form.teachingActive < form.teachingActiveRequire ? 'red' : 'green'}]">{{ form.teachingActive }}</span>次
        </div>
        <div v-if="form.hospitalTrainingSwitch" class="require-item">
          院级培训参与次数要求<span>{{ form.hospitalTrainingRequire }}</span>次；
          当前实际参与<span :style="[{color: form.hospitalTraining < form.hospitalTrainingRequire ? 'red' : 'green'}]">{{ form.hospitalTraining }}</span>次
        </div>
        <div v-if="form.annualAssessmentSwitch" class="require-item">
          年度考核合格次数要求<span>{{ form.annualAssessmentRequire }}</span>次；
          当前实际合格次数<span :style="[{color: form.annualAssessment < form.annualAssessmentRequire ? 'red' : 'green'}]">{{ form.annualAssessment }}</span>次
        </div>
        <div v-if="form.qualificationCertificateSwitch" class="require-item">
          要求取得执业医师资格证书：<span>{{ form.qualificationCertificateRequire ? '是' : '否' }}</span>；
          当前证书获取状态：<span :style="[{color: form.qualificationCertificateRequire ? form.qualificationCertificate ? 'green' : 'red' : 'green'}]">{{ form.qualificationCertificate ? '已获取' : '未获取' }}</span>
        </div>
      </div>
    </el-card>

    <el-card v-if="showApplyForm" class="box-card">
      <div slot="header" class="clearfix">
        <span>结业申请发起</span>
      </div>
      <div class="cont applyForm">
        <el-form v-if="!isReapply" ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="结业时间" prop="graduationDate">
                <el-date-picker
                  v-model="form.graduationDate"
                  type="date"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  :disabled="form.result !== null"
                />
            </el-form-item>

            <el-form-item label="结业总结" prop="graduationSummary">
                <el-input v-model="form.graduationSummary" type="textarea" autosize placeholder="请输入内容" :disabled="form.result !== null" />
            </el-form-item>

            <el-form-item label="上传结业材料" prop="graduationMaterials">
                <FileUpload
                  v-model="form.graduationMaterials"
                  :limit="999"
                  :fileSize="50"
                  :fileType="['pdf']"
                  :disabled="form.result !== null"
                />
            </el-form-item>
        </el-form>
        <el-form v-if="form.result == 3 && isReapply" ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="结业时间" prop="graduationDate">
                <el-date-picker
                  v-model="form.graduationDate"
                  type="date"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                />
            </el-form-item>

            <el-form-item label="结业总结" prop="graduationSummary">
                <el-input v-model="form.graduationSummary" type="textarea" autosize placeholder="请输入内容" />
            </el-form-item>

            <el-form-item label="上传结业材料" prop="graduationMaterials">
                <FileUpload
                  v-model="form.graduationMaterials"
                  :limit="999"
                  :fileSize="50"
                  :fileType="['pdf']"
                />
            </el-form-item>
        </el-form>
        <div class="footer" v-if="form.result === null || isReapply">
          <el-button type="primary" @click="submitForm">提交结业申请</el-button>
        </div>
      </div>
    </el-card>

    <el-card class="box-card" v-if="form.result !== null">
      <div slot="header" class="clearfix">
        <span>结业审批情况</span>
      </div>
      <div class="cont auditInfo">
        <el-row>
          <el-col :span="6"><span>提交时间：</span>{{ form.applicationTime }}</el-col>
          <el-col :span="6"><span>审批状态：</span>{{ getDictDataLabel(DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT, form.result) }}</el-col>
          <el-col :span="6"><el-button type="text" @click="handleViewAudit">查看审批情况</el-button></el-col>
          <el-col :span="6" v-if="form.result == 3 && !isReapply"><el-button type="text" @click="handleReapply">重新申请</el-button></el-col>
        </el-row>
      </div>
    </el-card>

    <el-card class="box-card" v-if="form.certificateStatus">
      <div slot="header" class="clearfix">
        <span>结业证书查看</span>
      </div>
      <div class="cont">
        <div v-if="form.certificateMethod == 2" class="preview-img-box">
          <img
              :src="form.certificateUrl"
              class="preview-img"
          />
        </div>
        <el-button v-else type="text" @click="handleViewCertificate">查看证书</el-button>
      </div>
    </el-card>

    <el-card class="box-card" v-if="form.certificateStatus">
      <div slot="header" class="clearfix">
        <span>我的结业反馈</span>
      </div>
      <div class="cont feedback">
        <div class="unfeedback" v-if="!form.graduationFeedback">
          <span>您暂未进行结业反馈，现在</span>
          <el-button type="text" @click="handleFeedback">填写反馈</el-button>
        </div>

        <div v-else>
          <div class="feedbackTime">反馈时间：{{ form.feedbackTime }}</div>
          <div class="feedbackCont">
            <div class="label">反馈内容：</div>
            <el-input v-model="form.graduationFeedback" type="textarea" autosize placeholder="请输入内容" :disabled="true" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 对话框-->
    <el-dialog :title="title" :visible.sync="open" width="680px" v-dialogDrag append-to-body custom-class="graduationApplication-feedback-dialog">
      <el-form ref="feedbackForm" :model="feedbackForm" :rules="feedbackrules" label-width="110px">
        <el-form-item label="反馈或意见" prop="graduationFeedback	">
          <el-input v-model="feedbackForm.graduationFeedback" type="textarea" autosize placeholder="请输入内容" :disabled="false" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFeedbackForm">提交</el-button>
        <el-button @click="cancelFeedbackForm">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  getApplicationRequire,
  createGraduationApplication,
  createFeedback
} from "@/api/rotation/graduationApplication";
import { 
  getTemplateByType, 
  previewDocTemplateUrl 
} from "@/api/rotation/graduationApplicationCertificate";
import FileUpload from '@/components/FileUploadInfo';
import { getAccessToken } from "@/utils/auth";

export default {
  name: "GraduationApplication",
  components: {
    FileUpload
  },
  data() {
    return {
      form: {},
      rules: {
        graduationDate: [{ required: true, message: "结业时间不能为空", trigger: "change" }],
        graduationSummary: [{ required: true, message: "结业总结不能为空", trigger: "blur" }],
        graduationMaterials: [{ required: true, message: "上传结业材料不能为空", trigger: "change" }],
      },
      submitLoading: false,
      open: false,
      title: '',
      feedbackForm: {
        graduationFeedback: ''
      },
      feedbackrules: {
        graduationFeedback: [{ required: true, message: "反馈或意见不能为空", trigger: "blur" }],
      },
      isReapply: false
    };
  },
  computed: {
    showApplyForm() {
      if (
        ( this.form.admissionEducationSwitch ? this.form.admissionEducation >= this.form.admissionEducationRequire ? true : false : true) &&
        ( this.form.professionalBaseEducationSwitch ? this.form.professionalBaseEducation >= this.form.professionalBaseEducationRequire ? true : false : true ) &&
        ( this.form.rotationDepartmentSwitch ? this.form.rotationDepartment >= this.form.rotationDepartmentRequire ? true : false : true ) &&
        ( this.form.rotationDataSwitch ? this.form.rotationData >= this.form.rotationDataRequire ? true : false : true) &&
        ( this.form.teachingActiveSwitch ? this.form.teachingActive >= this.form.teachingActiveRequire ? true : false : true) &&
        ( this.form.hospitalTrainingSwitch ? this.form.hospitalTraining >= this.form.hospitalTrainingRequire ? true : false : true) &&
        ( this.form.annualAssessmentSwitch ? this.form.annualAssessment >= this.form.annualAssessmentRequire ? true : false : true) &&
        ( this.form.qualificationCertificateRequire ? this.form.requireStatus ? true : false : true)
      ) {
        console.log('111')
        return true;
      } else {
        console.log('222')
        return false;
      }
    }
  },
  created() {
    this.getData()
  },
  methods: {
    /** 查询列表 */
    getData() {
      this.loading = true;
      // 执行查询
      getApplicationRequire().then(response => {
        this.form = response.data
        this.form.graduationMaterials = this.form.graduationMaterials ? JSON.parse(this.form.graduationMaterials) : []
        this.form.certificateUrl = `${this.form.certificateUrl}?token=${getAccessToken()}` 
        this.feedbackForm.graduationFeedback = response.data.graduationFeedback
        this.loading = false;
      });
    },
    
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        console.log('submitForm===', this.form)
        const params = {
          ...this.form,
          graduationMaterials: JSON.stringify(this.form.graduationMaterials),
        }
        this.$modal.confirm(`提交后不可修改，确认提交吗？`).then(() => {
          createGraduationApplication(params).then(res => {
            this.$modal.msgSuccess(`结业申请发起成功`);
            this.isReapply = false;
            this.getData()
          }).catch(() => {});
        }).catch(() => {});
      })
    },

    handleFeedback(){
      this.title = '我的结业反馈'
      this.open = true
    },
    cancelFeedbackForm(){
      this.open = false
    },

    submitFeedbackForm(){
      this.$refs["feedbackForm"].validate(valid => {
        if (!valid) {
          return;
        }

        createFeedback({
          id: this.form.id,
          graduationFeedback: this.feedbackForm.graduationFeedback
        }).then(res => {
          this.open = false
          this.$modal.msgSuccess(`提交成功`);
          this.getData()
        }).catch(() => {});
      })
    },

    handleViewAudit() {
      this.$router.push({ path: "/bpm/process-instance/detail", query: { id: this.form.processInstanceId}});
    },

    handleReapply() {
      this.isReapply = true
    },

    handleViewCertificate(){
      const params= {
        studentType: this.form.studentTypeCode,
        templateType: 'graduation_certificate'
      }
      getTemplateByType(params).then(res => {
        const url = previewDocTemplateUrl(res.data.id, this.form.id, `graduationCertificate${this.form.id}`);
        window.open(url);
      })
    }
    
  }
};
</script>

<style lang="scss" scoped>
.graduationApplication-container{
  .box-card{
    margin-bottom: 20px;
  }
  ::v-deep .el-card__header{
    font-size: 16px;
    color: #333;
    font-weight: bold;
  }
  .cont{
    .preview-img{
      max-height: 200px;
    }
  }
  .info{
    div{
      margin-bottom: 12px;
      font-size: 14px;

      span{
        padding: 0 6px;
        font-weight: bold;
      }
    }

    .require-item{
      position: relative;
      padding-left: 20px;

      &::before{
        content: ' ';
        display: inline-block;
        position: absolute;
        left: 0;
        top: 6px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #999;
      }
    }
  }

  .applyForm{
    .footer{
      padding-left: 120px;
    }
  }

  .auditInfo{
    font-size: 14px;
    line-height: 35px;
  }

  .feedback{
    font-size: 14px;
    .unfeedback{
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .feedbackTime{
      margin-bottom: 20px;
    }

    .feedbackCont{
      .label{
        margin-bottom: 5px;
      }
    }
  }
}
</style>
