<template>
  <div class="app-container">
    <el-form>
      <el-form-item label="是否启用考核码：" label-width="128px">
        <el-radio-group v-model="isEnable" @change="handleUpdateEnable">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="当前考核码：" label-width="128px" v-if="isEnable">
        <span class="code-item" v-for="n in examCode">{{ n }}</span>

        <el-button type="primary" @click="regenerateCode">重新生成</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getCode, resetCode, updateCode } from '@/api/exam/code'

export default {
  name: 'Code',
  data() {
    return {
      isEnable: false,
      examCode: "",
    }
  },
  methods: {
    handleUpdateEnable(value) {
      updateCode(value).catch(() => {
        this.isEnable = !value
      })
    },
    regenerateCode() {
      resetCode().then(res => {
        this.examCode = res.data.examCode
      })
    }
  },
  created() {
    getCode().then(res => {
      this.isEnable = res.data.isEnable
      this.examCode = res.data.examCode
    })
  }
}
</script>

<style lang="scss" scoped>
.code-item {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  border: 1px solid #eee;
  border-radius: 2px;
  margin-right: 10px;
}
</style>
