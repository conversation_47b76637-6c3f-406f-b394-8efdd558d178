import request from '@/utils/request'

// 创建日常数据审核
export function createDailyData(data) {
  return request({
    url: '/rotation/daily-data/create',
    method: 'post',
    data: data
  })
}

// 更新日常数据审核
export function updateDailyData(data) {
  return request({
    url: '/rotation/daily-data/update',
    method: 'put',
    data: data
  })
}

// 删除日常数据审核
export function deleteDailyData(id) {
  return request({
    url: '/rotation/daily-data/delete?id=' + id,
    method: 'delete'
  })
}

// 获得日常数据审核
export function getDailyData(id) {
  return request({
    url: '/rotation/daily-data/get?id=' + id,
    method: 'get'
  })
}

// 获得日常数据审核分页
export function getDailyDataPage(query) {
  return request({
    url: '/rotation/daily-data/page',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/dailyData/index'}
  })
}

// 导出日常数据审核 Excel
export function exportDailyDataExcel(query) {
  return request({
    url: '/rotation/daily-data/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获得学员日常数据情况信息
export function getDailyDataStudentInfo(query) {
  return request({
    url: '/rotation/daily-data/get-daily-data-student-info',
    method: 'get',
    params: query
  })
}

// 获得日常数据已审核数据分页
export function getAuditDailyData(query) {
  return request({
    url: '/rotation/daily-data/page-audit',
    method: 'get',
    params: query
  })
}

// 获得待审核列表
export function getNeedAuditDailyData(query) {
  return request({
    url: '/rotation/daily-data/list-checking',
    method: 'get',
    params: query
  })
}

// 日常数据审核不通过
export function dailyDataAuditFail(id) {
  return request({
    url: '/rotation/daily-data/update-audit-fail',
    method: 'put',
    data: { id }
  })
}

// 日常数据审核通过
export function dailyDataAuditSuccess(id) {
  return request({
    url: '/rotation/daily-data/update-audit-success',
    method: 'put',
    data: { id }
  })
}

// 一键审核通过
export function allDailyDataAuditSuccess(scheduleDetailsId, rotationItem) {
  return request({
    url: '/rotation/daily-data/update-audit-success-all',
    method: 'put',
    params: { scheduleDetailsId, rotationItem }
  })
}
