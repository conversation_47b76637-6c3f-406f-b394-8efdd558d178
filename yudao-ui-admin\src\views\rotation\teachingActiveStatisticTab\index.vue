<template>
  <div class="app-container">
    <div class="tabs-box">
      <el-radio-group v-model="activeTab" style="margin-bottom: 20px">
        <el-radio-button label="byTrainer">按培训人统计</el-radio-button>
        <el-radio-button label="byDepartment">按培训科室统计</el-radio-button>
      </el-radio-group>
    </div>

    <div class="tabs-cont">
      <by-trainer v-if="activeTab === 'byTrainer'" />
      <by-department v-if="activeTab === 'byDepartment'" />
    </div>
  </div>
</template>

<script>
import byTrainer from "./byTrainer.vue";
import byDepartment from "./byDepartment.vue";

export default {
  name: "TeachingActiveStatisticTab",
  components: { byTrainer, byDepartment },
  data() {
    return {
      activeTab: "byTrainer",
    };
  },
  created() {},
  methods: {},
};
</script>
<style lang="scss"></style>
