import request from "@/utils/request";

// 获得师资档案分页
export function getArchivesPage(query) {
  return request({
    url: "/teachers/archives/page",
    method: "get",
    params: query,
  });
}

// 获得聘任记录分页
export function getArchivesAppointmentPage(query) {
  return request({
    url: "/teachers/archives/page-appointment",
    method: "get",
    params: query,
  });
}

// 续聘
export function archivesRehire(data) {
  return request({
    url: "/teachers/archives/rehire",
    method: "put",
    data: data,
  });
}

// 解聘
export function archivesDismissal(id) {
  return request({
    url: "/teachers/archives/dismissal?id=" + id,
    method: "put",
  });
}

// 导出师资评优计划 Excel
export function exportArchivesAppointmentExcel(query) {
  return request({
    url: "/teachers/archives/export-appointment",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获得当前用户聘任记录分页
export function pageCurrentAppointment(query) {
  return request({
    url: "/teachers/archives/page-current-appointment",
    method: "get",
    params: query,
  });
}

// 获得当前用户评优评先分页
export function pageCurrentEvaluation(query) {
  return request({
    url: "/teachers/archives/page-current-evaluation",
    method: "get",
    params: query,
  });
}
