<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="活动名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入活动名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="活动类型" prop="activeType">
        <el-select v-model="queryParams.activeType" placeholder="请选择活动类型" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="教研室" prop="staffRoomValue" label-width="80px">
          <el-select v-model="queryParams.staffRoomValue" filterable clearable placeholder="请选择教研室">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.STAFF_ROOM)"
            :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
      </el-form-item>
      <el-form-item label="培训科室" prop="departmentName">
        <el-select v-model="queryParams.departmentName" filterable clearable placeholder="请选择培训科室" size="small">
          <el-option
              v-for="item in departmentOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                      :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="培训人" prop="speakerUsername">
        <el-input v-model="queryParams.speakerUsername" placeholder="请输入培训人姓名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="开展时间" prop="developDates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.developDates"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="督导状态" prop="superviseStatus">
        <el-select v-model="queryParams.superviseStatus" placeholder="请选择督导状态" filterable clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SUPERVISE_STATUS)"
                      :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="isExistRocord">
        <el-checkbox v-model="queryParams.isExistRocord">是否存在督导记录</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:teaching-active-supervise:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="活动名称" align="center" prop="name" width="180" fixed="left">
        <template v-slot="scope">
          {{ scope.row.name }}
          <dict-tag :type="DICT_TYPE.ACTIVE_DEVOLEP_STATUS" :value="scope.row.activeDevolepStatus" />
        </template>
      </el-table-column>
      <el-table-column label="活动类型" align="center" prop="activeType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.ROTATION_ACTIVE_TYPE" :value="scope.row.activeType" />
        </template>
      </el-table-column>
      <el-table-column label="培训科室" align="center" prop="departmentName" />
      <el-table-column label="培训人" align="center" prop="speakerUsername">
        <template slot-scope="scope">
          <el-link type="primary" :underline="false" @click="viewSpeakerSuperviseRecord(scope.row)">{{ scope.row.speakerUsername || '--' }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="计划时间" align="center" prop="startTime" width="290">
        <template slot-scope="scope">
          <span>{{ scope.row.planStartTime && scope.row.planEndTime ? (parseTime(scope.row.planStartTime, '{y}-{m}-{d} {h}:{i}') + ' ~ ' +  parseTime(scope.row.planEndTime, '{y}-{m}-{d} {h}:{i}')) : '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="培训时间" align="center" prop="endTime" width="290">
        <template slot-scope="scope">
          <span>{{ scope.row.startTime && scope.row.endTime ? (parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') + ' ~ ' + parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}')) : '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="出勤率" align="center" prop="attendance" width="120" fixed="right">
        <template slot-scope="scope">
          <el-link type="primary" :underline="false" @click="viewStudentDetail(scope.row)">{{ scope.row.attendance || '--' }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="学员评价" align="center" prop="evaluationScore" width="140" fixed="right">
        <template slot-scope="scope">
          <div class="rate-wrapper">
            <div class="rate-click" :style="{ cursor: scope.row.evaluationScore ? 'pointer' : 'default' }" @click="handleScoreClick(scope.row)"></div>
            <el-rate
              v-model="scope.row.evaluationScore"
              disabled
              show-score
              text-color="#ff9900"
              :max="5"
              score-template="{value}"
            ></el-rate>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="督导专家" align="center" prop="superviseNicknames" width="120">
      </el-table-column>
      <el-table-column label="督导得分" align="center" prop="avgSuperviseScore" width="120" fixed="right">
        <template slot-scope="scope">
          <el-link type="primary" :underline="false" @click="viewSuperviseRecord(scope.row)">{{ scope.row.avgSuperviseScore || '--' }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.superviseResultId" size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
                     v-hasPermi="['rotation:teaching-active-supervise:query']">查看详情</el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-edit" @click="handleSupervise(scope.row)"
                     v-hasPermi="['rotation:supervise-result:create']">督导安排</el-button>
          <el-button size="mini" type="text" icon="el-icon-picture-outline" @click="handlePics(scope.row)"
                     v-hasPermi="['rotation:teaching-active-supervise:query']">
            <el-badge type="success" is-dot :hidden="!scope.row.pictures" >
              <div style="padding: 2px">活动照片</div>
            </el-badge>
          </el-button>

        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="980px" v-dialogDrag append-to-body custom-class="teachingActive-supform-choice">
      <el-form inline>
        <el-form-item label="活动名称">
          <el-input v-model="superviseRow.name" placeholder="请输入教学活动名称" disabled />
        </el-form-item>
        <el-form-item label="活动类型">
          <el-select v-model="superviseRow.activeType" filterable placeholder="请选择活动类型" disabled>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
                      :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="表单名称">
          <el-input v-model="superviseQueryParams.name" placeholder="输入表单名称快速检索表单" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="querySimpleFormList">表单检索</el-button>
        </el-form-item>
      </el-form>
      <el-transfer v-model="selectFormList" :data="superviseFormList" :titles="['表单列表', '已选表单']">
        <span slot-scope="{ option }" :title="option.label">{{ option.label }}</span>
      </el-transfer>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleStartSupervise">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="督导评价" :visible.sync="auditVisible" width="900px">
      <el-tabs>
        <el-tab-pane v-for="item in auditForm.resultFormCreateReqVOS || []" :label="item.superviseFormName">
          <el-form inline label-width="90px">
            <el-form-item label="活动名称" style="width: 100%; white-space: nowrap">{{ superviseRow.name }}</el-form-item>
            <el-form-item label="活动类型：" style="width: 32%">
              <dict-tag :type="DICT_TYPE.ROTATION_ACTIVE_TYPE" :value="superviseRow.activeType"></dict-tag>
            </el-form-item>
            <el-form-item label="培训科室：" style="width: 32%">{{ superviseRow.departmentName }}</el-form-item>
            <el-form-item label="培训人：" style="width: 32%">{{ superviseRow.speakerUsername }}</el-form-item>
            <el-form-item label="培训时间：" style="width: 100%">{{ superviseRow.planStartTime && superviseRow.planEndTime ? (parseTime(superviseRow.planStartTime, '{y}-{m}-{d} {h}:{i}') + ' ~ ' +  parseTime(superviseRow.planEndTime, '{y}-{m}-{d} {h}:{i}')) : '--'}}</el-form-item>
            <el-form-item label="活动地点：" style="width: 100%">{{ superviseRow.adress }}</el-form-item>
          </el-form>

          <div class="form-item-wrapper" v-if="item.resultExts.length > 0">
            <div class="form-item" v-for="(ext, index) in item.resultExts" :key="index">
              <span class="form-label">{{ ext.superviseFormExtName }}：</span>
              <span v-if="isView">{{ ext.superviseFormExtVal }}</span>
              <el-input v-else v-model="ext.superviseFormExtVal" placeholder="请输入"></el-input>
            </div>
          </div>
          <div v-for="(resultItem, i) in item.resultItems" :key="i" style="margin-bottom: 20px">
            <header class="table-header">
              <span style="margin-right: 20px">评分项目：{{ resultItem.superviseFormItemName }}</span>
              <span style="margin-right: 20px">总分：{{ resultItem.superviseFormItemScore }}</span>
              <span>督导得分：{{ resultItem.score }}</span>
            </header>
            <el-table :data="resultItem.resultFormSubItems" border>
              <el-table-column label="评分要素" prop="superviseFormSubItemName"></el-table-column>
              <el-table-column label="分值" prop="superviseFormSubItemScore" width="100px"></el-table-column>
              <el-table-column label="得分" width="150px">
                <template v-slot="scope">
                  <el-input-number
                    style="width: 120px"
                    v-model="scope.row.score"
                    controls-position="right"
                    :min="0"
                    :max="scope.row.superviseFormSubItemScore"
                    :disabled="isView"
                    @change="handleSubItemScoreChange(resultItem, item)"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="备注" width="150px">
                <template v-slot="scope">
                  <el-input
                    style="width: 120px"
                    v-model="scope.row.remark"
                    :disabled="isView"
                    @change="handleSubItemRemarkChange(resultItem, item)"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div style="text-align: right; margin-bottom: 20px;">督导总分：{{item.score}}</div>

          <el-form label-width="100px">
            <el-form-item label="活动自评：" style="width: 100%; white-space: nowrap" v-if="item.selfAssessment">
              <el-input
                type="textarea"
                v-model="item.selfAssessment"
                :disabled="true"
                style="width: 100%"
              ></el-input>
            </el-form-item>
            <el-form-item label="督导意见：" required style="width: 100%">
              <el-input
                type="textarea"
                v-model="item.opinion"
                :disabled="isView"
                style="width: 100%"
              ></el-input>
            </el-form-item>
            <el-form-item label="照片上传：" required style="width: 100%; white-space: nowrap">
              <imageUpload
                v-model="item.pictures"
                :limit="9999"
                :disabled="isView"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" v-if="!isView">
        <el-button type="primary" @click="handleSaveSuperviseForm">确定</el-button>
        <el-button @click="auditVisible = false">取消</el-button>
      </span>
    </el-dialog>

<!--    <el-dialog title="督导记录" :visible.sync="superviseRecordVisible" width="600px" v-dialogDrag append-to-body>
      <div>
        <p style="margin-top: 0;">活动名称：{{superviseRow.name}}</p>
        <el-table v-loading="superviseRecordListLoading" :data="superviseRecordList">
          <el-table-column label="督导人" align="center" prop="nickname" />
          <el-table-column label="评分" align="center" prop="score" />
          <el-table-column label="反馈进度" align="center" prop="feedbackCount" >
            <template v-slot="scope">{{scope.row.feedbackCount == 0 ? '未反馈' : '已反馈'}}</template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleView(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>-->

    <el-dialog :title="superviseRow.speakerUsername + '的督导记录'" :visible.sync="superviseSpeakerRecordVisible" width="600px" v-dialogDrag append-to-body>
      <div>
        <el-table v-loading="superviseSpeakerRecordListLoading" :data="superviseSpeakerRecordList">
          <el-table-column label="活动名称" align="center" prop="teachingActiveName" />
          <el-table-column label="督导得分" align="center" prop="avgSuperviseScore" />
          <el-table-column label="督导专家" align="center" prop="expertNames" />
        </el-table>
      </div>
    </el-dialog>

    <el-dialog title="活动照片" :visible.sync="openPics" width="800px" v-dialogDrag append-to-body>
      <div>
        <imageUpload
          v-model="form.pictures"
          :limit="9999"
          :activeTypeName="this.getDictDataLabel(DICT_TYPE.ROTATION_ACTIVE_TYPE, curActive.activeType)"
          :disabled="true"
        />
      </div>
    </el-dialog>

    <el-dialog title="参加学员数详情页面" :visible.sync="openStudentDetail" width="800px" v-dialogDrag append-to-body>
      <div>
        <el-table v-loading="studentDetailLoading" :data="studentDetailList">
          <el-table-column label="学员姓名" align="center" prop="nickname" />
          <el-table-column label="学员类型" prop="studentType" align="center">
            <template v-slot="scope">
              <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType"></dict-tag>
            </template>
          </el-table-column>
          <el-table-column label="培训专业" align="center" prop="majorName" />
          <el-table-column label="年级" align="center" prop="grade" />
          <el-table-column label="扫码时间" align="center" prop="scanningTime" width="160" >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.scanningTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="参与形式" align="center" prop="joinType" >
            <template v-slot="scope">
              <dict-tag :type="DICT_TYPE.ROTATION_JOIN_TYPE" :value="scope.row.joinType"></dict-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <el-dialog title="评价得分情况" :visible.sync="openScore" width="900px" v-dialog-drag append-to-body>
      <el-tabs style="padding-bottom: 20px">
        <el-tab-pane label="指标平均得分">
          <el-form inline>
            <el-form-item label="评价对象：" style="margin-right: 30px">{{ appraiseObject }}</el-form-item>
            <el-form-item label="授课平均分：">{{ synthesizeScore }}</el-form-item>
          </el-form>
          <el-table style="margin-bottom: 20px" :data="indexScoreList">
            <el-table-column label="序号" type="index" align="center"></el-table-column>
            <el-table-column label="活动类型" align="center">
              <template v-slot="scope">
                <dict-tag :type="DICT_TYPE.ROTATION_ACTIVE_TYPE" :value="currentActive && currentActive.activeType" />
              </template>
            </el-table-column>
            <el-table-column label="评价指标" prop="appraiseKpi" align="center"></el-table-column>
            <el-table-column label="指标得分" prop="score" align="center"></el-table-column>
          </el-table>

          <!-- <el-form>
            <el-form-item label="主讲集中反馈">
              <el-input v-model="indexFeedback" type="textarea" :rows="3" placeholder="您可以在此处填写该活动的整体评价反馈！"></el-input>
            </el-form-item>
            <el-button type="primary" @click="submitIndexFeedback">提交</el-button>
          </el-form> -->
        </el-tab-pane>
        <el-tab-pane label="学员评分详情">
          <el-form inline>
            <el-form-item label="评价对象：" style="margin-right: 30px">{{ appraiseObject }}</el-form-item>
            <el-form-item label="综合得分：">{{ synthesizeScore }}</el-form-item>
          </el-form>
          <el-table :data="studentScoreList">
            <el-table-column label="学员姓名" prop="nickname" align="center"></el-table-column>
            <el-table-column label="学员类型" prop="studentType" align="center">
              <template v-slot="scope">
                <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType"></dict-tag>
              </template>
            </el-table-column>
            <el-table-column label="轮转科室" prop="rotationDepartmentName" align="center"></el-table-column>
            <el-table-column label="评价得分" prop="score" align="center">
              <template v-slot="scope">
                <el-link type="primary" @click.native="showAppraiseDetail(scope.row)">{{ scope.row.score }}</el-link>
              </template>
            </el-table-column>
            <el-table-column label="评价建议" prop="comments" align="center" min-width="150"></el-table-column>
            <el-table-column label="评价时间" prop="createTime" align="center" width="150">
              <template v-slot="scope">
                {{ new Date(scope.row.createTime).toLocaleString() }}
              </template>
            </el-table-column>
            <!-- <el-table-column label="操作" align="center">
              <template v-slot="scope">
                <el-link type="primary" v-if="!scope.row.feedback" @click.native="showSpeakFeedback(scope.row)">主讲反馈</el-link>
                <span v-else>已反馈</span>
              </template>
            </el-table-column> -->
          </el-table>
          <pagination
            v-show="studentScoreTotal > 0"
            :total="studentScoreTotal"
            :page.sync="studentScoreQuery.pageNo"
            :limit.sync="studentScoreQuery.pageSize"
            @pagination="queryAppraiseDetails"/>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog title="主讲反馈" :visible.sync="openSpeakerFeedback" width="400px" append-to-body>
      <el-input type="textarea" placeholder="请在此填写您的评价反馈" :rows="3" v-model="speakerFeedback"></el-input>
      <template v-slot:footer>
        <el-button type="primary" @click="sureSpeakerFeedback">确定</el-button>
        <el-button @click="cancelSpeakerFeedback">取消</el-button>
      </template>
    </el-dialog>

    <appraise-dialog
      v-if="appraiseDetailData"
      :title="appraiseTitle"
      :open="openAppraiseDetail"
      :data="appraiseDetailData"
      disabled
      @setOpen="openAppraiseDetail = $event"
    ></appraise-dialog>

    <supervise-record-dialog :visible.sync="superviseRecordVisible" :supervise-object="superviseRow"></supervise-record-dialog>
  </div>
</template>

<script>
import {getDepartmentSimpleList, getDepartmentPermissionList} from "@/api/system/department";
import { getStudentTypes } from "@/api/system/user";
import { getNotJoinedStuList } from "@/api/system/userStudent";
import { updateTeachingActive, getTeachingActive, getTeachingActivePage, getStudentsList, getSimpleFormList, getSuperviseFormsList,
  createSuperviseResult, getSuperviseResult, exportTeachingActiveSuperviseExcel, getTrainerSuperviseRecordList } from "@/api/rotation/teachingActiveSupervise";
import { addStudentUsers } from "@/api/rotation/teachingActivePublish";
import { getComprehensiveByParam, getAppraiseDetails, saveFeedbackResult } from "@/api/rotation/appraiseActiveFeedbackResult";
import { saveFeedback, getAppraiseResult } from "@/api/rotation/appraiseActive";
import { getSimpleMajorList } from '@/api/system/major';
import FileUpload from '@/components/FileUploadInfo';
import ImageUpload from '@/components/ImageUpload';
import AppraiseDialog from "../studentTeachingActive/appraiseDialog";
import SuperviseRecordDialog from "./supervise-record-dialog";

export default {
  name: "TeachingActiveSupervise",
  components: {
    FileUpload,
    ImageUpload,
    AppraiseDialog,
    SuperviseRecordDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      studentDetailLoading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 教学活动列表
      list: [],
      studentDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openPics: false,
      openStudentDetail: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        activeType: null,
        departmentName: null,
        studentType: null,
        speakerUsername: '',
        developDates: [],
        staffRoomValue: '',
        superviseStatus: '0',
        isExistRocord: false,
      },
      // 表单参数
      form: {},
      curActive: {},
      departmentOptions: [],
      userWorkerOptions: [],
      departmentPermissionOptions: [],
      studentTypesOptions: [],
      pickerOptions: {},
      openQrCode: false,
      curQrcode: '',
      openAddStudents: false,
      studentAddList: [],
      userIds: [],
      queryMajorList: [],
      queryStudentLoading: false,
      queryStudentParams: {
        staffRoomValue: '',
        major: '',
        dispatchingUnit: '',
        departmentIds: []
      },
      openScore: false,
      currentActive: null,
      appraiseObject: null,
      synthesizeScore: "",
      indexFeedbackId: "",
      appraiseActiveId: "",
      indexScoreList: [],
      indexFeedback: "",
      studentScoreQuery: {
        pageNo: 1,
        pageSize: 10,
      },
      studentScoreTotal: 0,
      studentScoreList: [],
      handleStudentScore: null,
      openSpeakerFeedback: false,
      speakerFeedback: "",
      openAppraiseDetail: false,
      appraiseTitle: "",
      appraiseDetailData: null,
      superviseQueryParams: {
        name: "",
        formType: "1",
        superviseType: "1",
        developForm: ""
      },
      superviseFormList: [],
      selectFormList: [],
      superviseRow: {},
      auditForm: {},
      auditVisible: false,
      isView: false,
      superviseRecordVisible: false,
      superviseRecordListLoading: false,
      superviseRecordList: [],
      superviseSpeakerRecordVisible: false,
      superviseSpeakerRecordListLoading: false,
      superviseSpeakerRecordList: []
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    this.getPermissionDepartment();
    this.getStudentTypesList();
    getSimpleMajorList().then(res => this.queryMajorList = res.data);
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getTeachingActivePage(this.queryParams).then(response => {
        const list = response.data.list;
        list.forEach(item => {
          if ( !item.score ) {
            item.evaluationScore = 0
          } else {
            const _score = item.evaluationScore / item.score * 5
            item.evaluationScore = _score
          }
        })
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment(val = '') {
      // 获得科室列表
      if (val) {
        this.queryStudentParams.departmentIds = []
      }
      getDepartmentSimpleList(0, val).then(res => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      })
    },
    getPermissionDepartment() {
      // 获得科室列表
      const params = { 'component': 'rotation/teachingActivePlan/index' }
      getDepartmentPermissionList(params).then(res => {
        // 处理 roleOptions 参数
        this.departmentPermissionOptions = [];
        this.departmentPermissionOptions.push(...res.data);
      })
    },
    getStudentTypesList() {
      const params = { 'component': 'rotation/teachingActivePlan/index' }
      getStudentTypes(params).then(res => {
        this.studentTypesOptions = [];
        this.studentTypesOptions.push(...res.data);
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelSubmitPics() {
      this.openPics = false;
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        activeType: undefined,
        departmentId: undefined,
        speakerUserId: undefined,
        studentTypes: [],
        timeValuePlan: null,
        planStartTime: undefined,
        planEndTime: undefined,
        pictures: undefined,
        timeValue: null,
        startTime: undefined,
        endTime: undefined,
        developWay: undefined,
        joinWay: undefined,
        adress: undefined,
        coursewares: undefined,
      };
      this.resetForm("form");
    },
    timeValuePlanChange(values) {
      this.form.planStartTime = undefined
      this.form.planEndTime = undefined
      this.form.timeValuePlan = null
      if (values) {
        this.form.planStartTime = values[0]
        this.form.planEndTime = values[1]
        this.form.timeValuePlan = values
      }
      this.$forceUpdate()
    },
    timeValueChange(values) {
      this.form.startTime = undefined
      this.form.endTime = undefined
      this.form.timeValue = null
      if (values) {
        this.form.startTime = values[0]
        this.form.endTime = values[1]
        this.form.timeValue = values
      }
      this.$forceUpdate()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加教学活动";
    },
    /** 修改按钮操作 */
    handleSupervise(row) {
      this.superviseRow = row;
      this.superviseQueryParams.developForm = row.activeType
      this.open = true;
      this.title = "督导表单选择";
      this.selectFormList = [];
      this.querySimpleFormList();
    },
    querySimpleFormList() {
      getSimpleFormList(this.superviseQueryParams).then(res => {
        this.superviseFormList = res.data.map(item => ({ key: item.id, label: item.name }));
      });
    },
    handleStartSupervise() {
      if (this.selectFormList.length === 0) {
        this.$message.warning("请选择至少一个督导表单");
        return;
      }
      getSuperviseFormsList({
        superviseFormIds: this.selectFormList.join(","),
        superviseObjectId: this.superviseRow.id
      }).then(res => {
        const _data = res.data
        _data.costTime = 0
        _data.resultFormCreateReqVOS.forEach(item => {
          item.costTime = 0
        })
        this.auditForm = _data;
        this.open = false;
        this.auditVisible = true;
        this.isView = false;
      });
    },
    handleSubItemScoreChange(resultTarget, target) {
      let resultItemScore = 0, totalScore = 0;
      resultTarget.resultFormSubItems.forEach(subItem => {
        resultItemScore += subItem.score;
      });
      resultTarget.score = resultItemScore;
      target.resultItems.forEach(item => {
        totalScore += item.score;
      });
      target.score = totalScore;
    },
    handleSubItemRemarkChange(){

    },
    handleSaveSuperviseForm() {
      // console.log('this.auditForm===', this.auditForm)
      const { resultFormCreateReqVOS = [] } = this.auditForm

      let opinionFlag = true
      let picturesFlag = true
      let superviseFormName = ''
      resultFormCreateReqVOS.forEach(item => {
        if (!item.opinion) {
          opinionFlag = false
          superviseFormName = item.superviseFormName
        }
        if (!item.pictures) {
          picturesFlag = false
          superviseFormName = item.superviseFormName
        }
      })
      if (!opinionFlag) {
        return this.$modal.msgWarning(`请输入${superviseFormName}的督导意见!`);
      }
      if (!picturesFlag) {
        return this.$modal.msgWarning(`请上传${superviseFormName}的照片!`);
      }

      createSuperviseResult(this.auditForm).then(() => {
        this.$message.success('操作成功')
        this.auditVisible = false;
        this.getList();
      });
    },
    /** 考核督导详情 */
    handleView(row) {
      this.isView = true;
      this.superviseRow = row;
      getSuperviseResult({ id: row.superviseResultId }).then(res => {
        this.auditForm = res.data;
        this.auditVisible = true;
      });
    },
    viewSuperviseRecord(row){
      this.superviseRow = row;
      this.superviseRecordVisible = true;
    },
    viewSpeakerSuperviseRecord(row){
      this.superviseRow = row;
      const params = {
        pageNo: 1,
        pageSize: 9999,
        trainerUserId: row.speakerUserId
      }
      this.superviseSpeakerRecordListLoading = true
      getTrainerSuperviseRecordList(params).then(res => {
        this.superviseSpeakerRecordList = res.data.list;
        this.superviseSpeakerRecordVisible = true;
        this.superviseSpeakerRecordListLoading = false;
      });
    },
    handlePics(row) {
      this.reset();
      const id = row.id;
      this.curActive = row;
      getTeachingActive(id).then(response => {
        this.form = response.data;
        this.openPics = true;
      });
    },
    submitPics() {
      const params = {...this.form}
      delete params.timeValue;
      updateTeachingActive(params).then(response => {
        this.$modal.msgSuccess("保存成功");
        this.openPics = false;
        this.getList();
      });
    },
    handleQueryStudent() {
      const params = {
        teachingActiveId: this.curActive.id,
        staffRoomValue: this.queryStudentParams.staffRoomValue,
        major: this.queryStudentParams.major,
        dispatchingUnit: this.queryStudentParams.dispatchingUnit,
        departmentIds: this.queryStudentParams.departmentIds.join(',')
      }
      getNotJoinedStuList(params).then(res => {
        this.studentAddList = res.data || []
      })
    },
    handleAddStudent(){
      this.handleQueryStudent();
      this.openAddStudents = true
    },
    cancelAddStudents() {
      this.openAddStudents = false
    },
    handleSelectionChange(val) {
      console.log('handleSelectionChange=====', val)
      this.userIds = val.map(item => {
        return {
          scheduleDetailsId: item.scheduleDetailsId || null,
          userId: item.studentId
        }
      });
    },
    submitAddStudents() {
      const params = {
        teachingActiveId: this.curActive.id,
        userReqVOList: this.userIds
      }
      addStudentUsers(params).then(res => {
        this.$modal.msgSuccess("添加成功");
        this.openAddStudents = false;
        this.getStudentList(this.curActive.id);
        this.getList()
      })
    },
    viewStudentDetail(row) {
      if (!row.attendance) {
        return
      }
      this.getStudentList(row.id, () => {
        this.openStudentDetail = true;
        this.curActive = row
        this.curActive.timeArr =  row.startTime && row.endTime ? [row.startTime, row.endTime] : []
        this.curActive.studentTypesArr = row.studentTypes ? row.studentTypes.split(',') : null
        this.queryStudentParams.departmentIds = [row.departmentId]
      })
    },
    getStudentList(id, callback) {
      const params = {
        teachingActiveId: id
      }
      this.studentDetailLoading = true
      getStudentsList(params).then(response => {
        this.studentDetailList = response.data || [];
        if (callback) {
          callback()
        }
        this.studentDetailLoading = false;
      });
    },
    // 获取学员评分详情
    queryAppraiseDetails() {
      const { pageNo, pageSize } = this.studentScoreQuery;
      const { id, activeType } = this.currentActive;
      getAppraiseDetails({ activeId: id, activeType, appraiseActiveType: "1", pageNo, pageSize }).then(res => {
        this.studentScoreList = res.data.list;
        this.studentScoreTotal = res.data.total;
      });
    },
    // 查看评价得分情况
    handleScoreClick(item) {
      if (!item.evaluationScore) return;
      this.openScore = true;
      this.studentScoreQuery.pageNo = 1;
      this.currentActive = item;
      const { id, activeType } = item;
      getComprehensiveByParam({ activeId: id, activeType, appraiseActiveType: "1" }).then(res => {
        this.appraiseObject = res.data.targetObject;
        this.synthesizeScore = res.data.score;
        this.indexFeedbackId = res.data.id;
        this.appraiseActiveId = res.data.appraiseActiveId;
        this.indexFeedback = res.data.feedback;
        this.indexScoreList = res.data.resultItemRespVOS;
      });
      this.queryAppraiseDetails();
    },
    // 提交活动评价综合反馈结果
    submitIndexFeedback() {
      if (!this.indexFeedback) {
        this.$message.warning("请先填写综合反馈！");
        return;
      }
      const { id, activeType } = this.currentActive;
      saveFeedbackResult({
        id: this.indexFeedbackId,
        activeId: id,
        appraiseActiveId: this.appraiseActiveId,
        activeType,
        appraiseActiveType: "1",
        feedback: this.indexFeedback
      }).then(() => {
        this.$message.success("提交综合反馈成功！");
      })
    },
    // 主讲反馈
    showSpeakFeedback(row) {
      this.handleStudentScore = row;
      this.openSpeakerFeedback = true;
    },
    cancelSpeakerFeedback() {
      this.openSpeakerFeedback = false;
      this.handleStudentScore = null;
      this.speakerFeedback = "";
    },
    sureSpeakerFeedback() {
      if (!this.speakerFeedback) {
        this.$message.warning("请输入评价反馈再提交！");
        return;
      }
      saveFeedback({ id: this.handleStudentScore.id, feedback: this.speakerFeedback }).then(() => {
        this.$message.success("保存主讲反馈成功！");
        this.queryAppraiseDetails();
        this.openSpeakerFeedback = false;
      });
    },
    // 显示评价详情
    showAppraiseDetail(row) {
      getAppraiseResult({ id: row.id }).then(response => {
        this.appraiseDetailData = response.data;
        this.openAppraiseDetail = true;
        this.appraiseTitle = `查看评价-${row.nickname}`;
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有教学活动督导数据项?').then(() => {
          this.exportLoading = true;
          return exportTeachingActiveSuperviseExcel(params);
        }).then(response => {
          this.$download.excel(response, '教学活动督导.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    },
  }
};
</script>

<style lang="scss" scoped>
.rate-wrapper {
  position: relative;
}

.rate-click {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.valid-tag {
  position: absolute;
  top: 1px;
  right: 0;
}

.table-header {
  padding: 10px;
  border: 1px solid #dfe6ec;
  background: #f8f8f9;
  position: relative;
  top: 1px;
}

.form-item-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;

  .form-item {
    flex-basis: calc(33.3% - 20px);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }

  .form-label {
    flex-shrink: 0;
    font-size: 14px;
    color: #606266;
  }
}
</style>

<style lang="scss">
.teachingActive-supform-choice{
  .el-transfer-panel{
    width: 43%;
  }

  .el-transfer__buttons{
    display: inline-flex;
    flex-direction: column;

    .el-button + .el-button {
      margin-left: 0;
    }
  }
}
</style>
