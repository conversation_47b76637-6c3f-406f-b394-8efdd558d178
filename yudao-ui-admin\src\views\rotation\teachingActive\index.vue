<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="活动名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入活动名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动类型" prop="activeType">
        <el-select
          v-model="queryParams.activeType"
          placeholder="请选择活动类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_ACTIVE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训科室" prop="departmentName">
        <el-select
          v-model="queryParams.departmentName"
          filterable
          clearable
          placeholder="请选择培训科室"
          size="small"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开展时间" prop="developDate">
        <el-date-picker
          clearable
          v-model="queryParams.developDate"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="选择开展时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:teaching-active:create']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:teaching-active:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="编号" align="center" prop="id" /> -->
      <el-table-column label="活动名称" align="center" prop="name" />
      <el-table-column label="活动类型" align="center" prop="activeType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.ROTATION_ACTIVE_TYPE"
            :value="scope.row.activeType"
          />
        </template>
      </el-table-column>
      <el-table-column label="培训科室" align="center" prop="departmentName" />
      <el-table-column label="培训人" align="center" prop="speakerUsername" />
      <!-- <el-table-column label="学员类型" align="center" prop="studentTypes">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentTypes" />
        </template>
      </el-table-column> -->
      <el-table-column
        label="开始时间"
        align="center"
        prop="startTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出勤率" align="center" prop="attendance">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewStudentDetail(scope.row)"
            >{{ scope.row.attendance || "--" }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="评价得分"
        align="center"
        prop="evaluationScore"
        width="140"
      >
        <template slot-scope="scope">
          <el-rate
            v-model="scope.row.evaluationScore"
            disabled
            show-score
            text-color="#ff9900"
            :max="5"
            score-template="{value}"
          >
          </el-rate>
        </template>
      </el-table-column>
      <!-- <el-table-column label="考核平均分" align="center" prop="name" /> -->
      <!-- <el-table-column label="开展方式" align="center" prop="developWay">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.ROTATION_DEVELOP_WAY" :value="scope.row.developWay" />
        </template>
      </el-table-column>
      <el-table-column label="参与方式" align="center" prop="joinWay" />
      <el-table-column label="是否考核" align="center" prop="isExamine">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.isExamine" />
        </template>
      </el-table-column>
      <el-table-column label="考核id" align="center" prop="examineId" />
      <el-table-column label="活动地点" align="center" prop="adress" />
      <el-table-column label="活动课件" align="center" prop="coursewares" />
      <el-table-column label="活动照片集合，逗号分隔" align="center" prop="pictures" /> -->
      <el-table-column
        label="操作"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rotation:teaching-active:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handlePics(scope.row)"
            v-hasPermi="['rotation:teaching-active:update']"
            >活动照片</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handleViewQrcode(scope.row)"
            v-hasPermi="['rotation:teaching-active:update']"
            >查看二维码</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:teaching-active:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="教学活动名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入教学活动名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="活动类型" prop="activeType">
              <el-select
                v-model="form.activeType"
                filterable
                placeholder="请选择活动类型"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.ROTATION_ACTIVE_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
            <el-form-item
              label="培训科室"
              prop="departmentId"
              label-width="80px"
            >
              <el-select
                v-model="form.departmentId"
                filterable
                placeholder="请选择培训科室"
                @change="getUserworkData"
                style="width: 100%"
              >
                <el-option
                  v-for="item in departmentPermissionOptions"
                  :key="parseInt(item.id)"
                  :label="item.name"
                  :value="parseInt(item.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="培训人" prop="speakerUserId">
              <!-- <el-input v-model="form.speakerUserId" placeholder="请输入培训人" /> -->
              <el-select
                v-model="form.speakerUserId"
                filterable
                placeholder="请选择培训人"
              >
                <el-option
                  v-for="user in userWorkerOptions"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
            <el-form-item label="培训时间" prop="timeValue" label-width="80px">
              <!-- <el-input v-model="form.speakerUserId" placeholder="请输入培训人" /> -->
              <el-date-picker
                style="width: 100%"
                v-model="form.timeValue"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"
                @input="timeValueChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="开展方式" prop="developWay">
              <el-select
                v-model="form.developWay"
                filterable
                placeholder="请选择开展方式"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.ROTATION_DEVELOP_WAY
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
            <el-form-item
              v-if="form.developWay == 2"
              label="参与方式"
              prop="joinWay"
              label-width="80px"
            >
              <el-input
                v-model="form.joinWay"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="通知对象" prop="studentTypes">
              <el-select
                v-model="form.studentTypes"
                multiple
                filterable
                placeholder="请选择通知对象"
                style="width: 100%"
              >
                <!-- <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                          :key="dict.value" :label="dict.label" :value="dict.value" /> -->
                <el-option
                  v-for="user in studentTypesOptions"
                  :key="user.value"
                  :label="user.label"
                  :value="user.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <el-row :gutter="10">
          <el-col :md="10" :lg="10" :xl="10">
            <el-form-item label="是否考核" prop="isExamine">
              <el-radio-group v-model="form.isExamine" style="margin-right: 20px;">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14" :xl="14">
          </el-col>
        </el-row> -->

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="活动地点" prop="adress">
              <el-input
                v-model="form.adress"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :md="24" :lg="24" :xl="24">
            <el-form-item label="活动课件">
              <!-- <fileUpload v-model="form.coursewares"/> -->
              <FileUpload
                v-model="form.coursewares"
                :limit="999"
                :fileSize="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable v-model="form.startTime" type="date" value-format="timestamp" placeholder="选择开始时间" />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable v-model="form.endTime" type="date" value-format="timestamp" placeholder="选择结束时间" />
        </el-form-item> -->
        <!-- <el-form-item label="活动照片集合，逗号分隔" prop="pictures">
          <el-input v-model="form.pictures" placeholder="请输入活动照片集合，逗号分隔" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="活动照片"
      :visible.sync="openPics"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <imageUpload
          v-model="form.pictures"
          :limit="9999"
          :activeTypeName="
            this.getDictDataLabel(
              DICT_TYPE.ROTATION_ACTIVE_TYPE,
              curActive.activeType
            )
          "
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPics">保 存</el-button>
        <el-button @click="cancelSubmitPics">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="活动二维码"
      :visible.sync="openQrCode"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <div style="text-align: center">
        <img
          width="220"
          height="220"
          :src="'data:image/png;base64,' + curQrcode"
        />
      </div>
    </el-dialog>

    <el-dialog
      title="参加学员数详情页面"
      :visible.sync="openStudentDetail"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <div style="text-align: right; padding-bottom: 10px">
          <el-button size="mini" @click="handleAddStudent">添加学员</el-button>
        </div>
        <el-table v-loading="studentDetailLoading" :data="studentDetailList">
          <el-table-column label="学员姓名" align="center" prop="nickname" />
          <el-table-column label="学员类型" prop="studentType" align="center">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                :value="scope.row.studentType"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column label="培训专业" align="center" prop="majorName" />
          <el-table-column label="年级" align="center" prop="grade" />
          <el-table-column label="扫码时间" align="center" prop="scanningTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.scanningTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="180"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-if="!scope.row.scanningTime"
                size="mini"
                type="text"
                @click="handleJoin(scope.row)"
                >确认参加</el-button
              >
              <el-button
                v-else
                size="mini"
                type="text"
                @click="handleRevoke(scope.row)"
                >撤销参加</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <el-dialog
      title="添加学员"
      :visible.sync="openAddStudents"
      width="900px"
      v-dialogDrag
      append-to-body
    >
      <div class="add-student-table">
        <div class="head-info">
          <el-form
            :model="curActive"
            ref="curActiveForm"
            size="small"
            :inline="true"
            label-width="120px"
          >
            <el-form-item label="培训时间" prop="timeArr" label-width="80px">
              <el-date-picker
                style="width: 100%"
                v-model="curActive.timeArr"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="true"
              />
            </el-form-item>
            <el-form-item
              label="学员类型"
              prop="studentTypesArr"
              label-width="100px"
            >
              <el-select
                v-model="curActive.studentTypesArr"
                multiple
                filterable
                placeholder="请选择"
                :disabled="true"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_STUDENT_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <el-form
          :model="queryStudentParams"
          ref="queryStudentForm"
          size="small"
          :inline="true"
          label-width="120px"
        >
          <!-- <el-form-item label="学员姓名" prop="nickname" label-width="80px">
            <el-input v-model="queryStudentParams.nickname" placeholder="请输入姓名" clearable @keyup.enter.native="handleQueryStudent" style="width: 120px"/>
          </el-form-item> -->
          <el-form-item label="教研室" prop="staffRoomValue" label-width="80px">
            <el-select
              v-model="queryStudentParams.staffRoomValue"
              filterable
              clearable
              placeholder="请选择教研室"
              @change="getDepartment"
              style="width: 140px"
            >
              <el-option
                v-for="dict in this.getDictDatas(DICT_TYPE.STAFF_ROOM)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="当前医院科室"
            prop="departmentIds"
            label-width="100px"
          >
            <el-select
              v-model="queryStudentParams.departmentIds"
              multiple
              filterable
              clearable
              placeholder="请选择"
              size="small"
              style="width: 400px"
            >
              <el-option
                v-for="item in departmentOptions"
                :key="parseInt(item.id)"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleQueryStudent"
              >学员搜索</el-button
            >
          </el-form-item>
        </el-form>
        <div style="display: flex; justify-content: center">
          <el-table
            v-loading="queryStudentLoading"
            :data="studentAddList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="学员姓名" align="center" prop="nickname" />
            <el-table-column label="学员类型" prop="studentType" align="center">
              <template v-slot="scope">
                <dict-tag
                  :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                  :value="scope.row.studentType"
                ></dict-tag>
              </template>
            </el-table-column>
            <el-table-column label="年级" align="center" prop="grade" />
            <el-table-column label="专业" align="center" prop="majorName" />
            <el-table-column
              label="派送单位"
              align="center"
              prop="dispatchingUnit"
            />
            <el-table-column
              label="当前轮转科室"
              align="center"
              prop="rotationDepartmentName"
            />
          </el-table>
        </div>
        <div
          slot="footer"
          class="dialog-footer"
          style="text-align: right; padding-top: 15px"
        >
          <el-button type="primary" @click="submitAddStudents"
            >确认添加</el-button
          >
          <el-button @click="cancelAddStudents">取 消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDepartmentSimpleList,
  getDepartmentPermissionList,
} from "@/api/system/department";
import {
  getUserWorkerSimpleList,
  getUserWorkerPermissionList,
} from "@/api/system/userWorker";
import { getStudentTypes } from "@/api/system/user";
import { getNotJoinedStuList } from "@/api/system/userStudent";
import { addStudentUsers } from "@/api/rotation/teachingActivePublish";
import {
  createTeachingActive,
  updateTeachingActive,
  deleteTeachingActive,
  getTeachingActive,
  getTeachingActivePage,
  exportTeachingActiveExcel,
  getStudentsList,
  confirmJoin,
  revokeJoin,
} from "@/api/rotation/teachingActive";
// import FileUpload from '@/components/FileUpload';
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "TeachingActive",
  components: {
    FileUpload,
    ImageUpload,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      studentDetailLoading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 教学活动列表
      list: [],
      studentDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openPics: false,
      openStudentDetail: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        activeType: null,
        departmentName: null,
        studentType: null,
        developDate: null,
        isNeedQrcode: true,
        // startTime: [],
        // endTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "教学活动名称不能为空", trigger: "blur" },
        ],
        activeType: [
          { required: true, message: "活动类型不能为空", trigger: "change" },
        ],
        departmentId: [
          { required: true, message: "培训科室不能为空", trigger: "change" },
        ],
        speakerUserId: [
          { required: true, message: "培训人不能为空", trigger: "blur" },
        ],
        studentTypes: [
          {
            type: "array",
            required: true,
            message: "学员类型集合,逗号分隔不能为空",
            trigger: "change",
          },
        ],
        timeValue: [
          { required: true, message: "请选择开展时间", trigger: "change" },
        ],
        // startTime: [{ required: true, message: "开始时间不能为空", trigger: "blur" }],
        // endTime: [{ required: true, message: "结束时间不能为空", trigger: "blur" }],
        developWay: [
          { required: true, message: "开展方式不能为空", trigger: "change" },
        ],
        joinWay: [
          { required: true, message: "参与方式不能为空", trigger: "blur" },
        ],
        isExamine: [
          { required: true, message: "是否考核不能为空", trigger: "change" },
        ],
      },
      departmentOptions: [],
      userWorkerOptions: [],
      departmentPermissionOptions: [],
      studentTypesOptions: [],
      openQrCode: false,
      curQrcode: "",
      curActive: {},
      queryStudentParams: {
        staffRoomValue: "",
        departmentIds: [],
      },
      studentAddList: [],
      userIds: [],
      queryStudentLoading: false,
      openAddStudents: false,
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    this.getPermissionDepartment();
    // this.getUserworkData();
    this.getStudentTypesList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getTeachingActivePage(this.queryParams).then((response) => {
        const list = response.data.list;
        list.forEach((item) => {
          if (!item.score) {
            item.evaluationScore = 0;
          } else {
            const _score = (item.evaluationScore / item.score) * 5;
            item.evaluationScore = _score;
          }
        });
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment(val = "") {
      // 获得科室列表
      if (val) {
        this.queryStudentParams.departmentIds = [];
      }
      getDepartmentSimpleList(0, val).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    // getDepartment() {
    //   // 获得科室列表
    //   getDepartmentSimpleList(0).then(res => {
    //     // 处理 roleOptions 参数
    //     this.departmentOptions = [];
    //     this.departmentOptions.push(...res.data);
    //   })
    // },
    getPermissionDepartment() {
      // 获得科室列表
      const params = { component: "rotation/teachingActive/index" };
      getDepartmentPermissionList(params).then((res) => {
        // 处理 roleOptions 参数
        this.departmentPermissionOptions = [];
        this.departmentPermissionOptions.push(...res.data);
      });
    },
    getStudentTypesList() {
      const params = { component: "rotation/teachingActive/index" };
      getStudentTypes(params).then((res) => {
        this.studentTypesOptions = [];
        this.studentTypesOptions.push(...res.data);
      });
    },
    async getUserworkData(val) {
      this.form.speakerUserId = "";
      const params = {
        departmentId: val,
        component: "rotation/teachingActive/index",
      };
      const { data } = await getUserWorkerPermissionList(params);
      this.userWorkerOptions = data || [];
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelSubmitPics() {
      this.openPics = false;
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        activeType: undefined,
        departmentId: undefined,
        speakerUserId: undefined,
        studentTypes: [],
        timeValue: [],
        startTime: undefined,
        endTime: undefined,
        developWay: undefined,
        joinWay: undefined,
        isExamine: false,
        examineId: undefined,
        adress: undefined,
        coursewares: undefined,
        pictures: undefined,
      };
      this.resetForm("form");
    },
    timeValueChange(values) {
      this.form.startTime = undefined;
      this.form.endTime = undefined;
      if (values) {
        this.form.startTime = values[0];
        this.form.endTime = values[1];
      }
      this.$forceUpdate();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加教学活动";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getTeachingActive(id).then(async (response) => {
        await this.getUserworkData(response.data.departmentId);
        this.form = response.data;
        this.form.studentTypes = this.form.studentTypes
          ? this.form.studentTypes.split(",")
          : [];
        if (this.form.startTime && this.form.endTime) {
          this.form.timeValue = [this.form.startTime, this.form.endTime];
        }
        this.form.coursewares = this.form.coursewares
          ? JSON.parse(this.form.coursewares)
          : [];
        this.open = true;
        this.title = "修改教学活动";
      });
    },
    handlePics(row) {
      this.reset();
      const id = row.id;
      this.curActive = row;
      getTeachingActive(id).then((response) => {
        this.form = response.data;
        this.openPics = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = { ...this.form };
        delete params.timeValue;
        params.studentTypes = params.studentTypes.join(",");
        params.coursewares = JSON.stringify(params.coursewares);
        // 修改的提交
        if (this.form.id != null) {
          updateTeachingActive(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createTeachingActive(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    submitPics() {
      const params = { ...this.form };
      delete params.timeValue;
      updateTeachingActive(params).then((response) => {
        this.$modal.msgSuccess("保存成功");
        this.openPics = false;
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除教学活动编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteTeachingActive(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有教学活动数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportTeachingActiveExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "教学活动.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    viewStudentDetail(row) {
      if (!row.attendance) {
        return;
      }
      this.getStudentList(row.id, () => {
        this.openStudentDetail = true;
        this.curActive = row;
        this.curActive.timeArr =
          row.startTime && row.endTime ? [row.startTime, row.endTime] : [];
        this.curActive.studentTypesArr = row.studentTypes
          ? row.studentTypes.split(",")
          : null;
        this.queryStudentParams.departmentIds = [row.departmentId];
      });
      // this.getStudentList(row.id, () => {
      //   this.openStudentDetail = true;
      // })
    },
    getStudentList(id, callback) {
      const params = {
        teachingActiveId: id,
      };
      this.studentDetailLoading = true;
      getStudentsList(params).then((response) => {
        this.studentDetailList = response.data || [];
        if (callback) {
          callback();
        }
        this.studentDetailLoading = false;
      });
    },
    handleJoin(row) {
      const params = {
        teachingActiveId: row.teachingActiveId,
        userId: row.userId,
      };
      confirmJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getStudentList(row.teachingActiveId);
      });
    },
    handleRevoke(row) {
      const params = {
        teachingActiveId: row.teachingActiveId,
        userId: row.userId,
      };
      revokeJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getStudentList(row.teachingActiveId);
      });
    },
    handleViewQrcode(row) {
      this.curQrcode = row.qrcode;
      this.openQrCode = true;
    },
    handleQueryStudent() {
      const params = {
        teachingActiveId: this.curActive.id,
        staffRoomValue: this.queryStudentParams.staffRoomValue,
        departmentIds: this.queryStudentParams.departmentIds.join(","),
      };
      getNotJoinedStuList(params).then((res) => {
        this.studentAddList = res.data || [];
      });
    },
    handleAddStudent() {
      this.handleQueryStudent();
      this.openAddStudents = true;
    },
    cancelAddStudents() {
      this.openAddStudents = false;
    },
    handleSelectionChange(val) {
      console.log("handleSelectionChange=====", val);
      this.userIds = val.map((item) => {
        return {
          scheduleDetailsId: item.scheduleDetailsId || null,
          userId: item.studentId,
        };
      });
    },
    submitAddStudents() {
      const params = {
        teachingActiveId: this.curActive.id,
        userReqVOList: this.userIds,
      };
      addStudentUsers(params).then((res) => {
        this.$modal.msgSuccess("添加成功");
        this.openAddStudents = false;
        this.getStudentList(this.curActive.id);
        this.getList();
      });
    },
  },
};
</script>
