<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="反馈内容" prop="feedbackContent">
        <el-input
          v-model="queryParams.feedbackContent"
          placeholder="请输入反馈内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="试题标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入试题标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="确认状态" prop="confirmStatus">
        <el-select
          v-model="queryParams.confirmStatus"
          placeholder="请选择确认状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.EXAM_ERROR_CONFIRM_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="序号" align="center" prop="id">
        <template slot-scope="scope">{{
          (queryParams.pageNo - 1) * queryParams.pageSize + scope.$index + 1
        }}</template>
      </el-table-column>
      <el-table-column label="试题内容" align="center" prop="title" />
      <el-table-column label="反馈时间" align="center" prop="createTime" />
      <el-table-column label="反馈内容" align="center" prop="feedbackContent" />
      <el-table-column label="确认状态" align="center" prop="confirmStatus">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.EXAM_ERROR_CONFIRM_STATUS"
            :value="scope.row.confirmStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleView(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      title="错题反馈"
      :visible.sync="open"
      width="600px"
      v-dialogDrag
      append-to-body
    >
      <div class="feedback-item">
        <span class="label">试题内容：</span>
        <question-view :curQuestion="curQuestion" />
      </div>
      <el-form :model="feedbackForm" ref="feedbackForm" label-width="85px">
        <el-form-item label="反馈内容" prop="feedbackContent">
          <el-input
            type="textarea"
            autosize
            v-model="feedbackForm.feedbackContent"
            placeholder="反馈内容"
            disabled
          ></el-input>
        </el-form-item>

        <el-form-item label="反馈回复" prop="replyContent">
          <el-input
            type="textarea"
            autosize
            v-model="feedbackForm.replyContent"
            placeholder="反馈回复"
            disabled
          ></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getQuestionErrorFeedbackPage,
  getQuestionAnswer,
} from "@/api/exam/questionErrorFeedback";
import QuestionView from "../components/QuestionView.vue";

export default {
  name: "questionErrorFeedbackReply",
  components: { QuestionView },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考试错题反馈列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        feedbackContent: null,
        title: null,
        confirmStatus: null,
      },
      curRow: {},
      curQuestion: null,
      feedbackForm: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getQuestionErrorFeedbackPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 查看按钮操作 */
    handleView(row) {
      this.curRow = row;
      getQuestionAnswer({ id: row.questionId }).then((response) => {
        const question = response.data;
        try {
          question.content = JSON.parse(question.content);
        } catch (e) {
          question.content = { title: "", choiceList: [] };
        }

        if (question.questionType === "10") {
          const choiceArr = (question.answer || "").split(",").map((key) => ({
            desc: "",
            images: [],
            answer: key,
          }));
          question.choiceArr = choiceArr;
        }

        this.curQuestion = question;
        this.feedbackForm = row;
        this.open = true;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.feedback-item {
  margin-bottom: 10px;
  .label {
    font-weight: bold;
  }
}
</style>
