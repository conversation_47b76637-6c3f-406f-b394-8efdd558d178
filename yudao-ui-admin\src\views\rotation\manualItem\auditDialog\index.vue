<template>
  <el-dialog title="出科技能考核详情" :visible="visible" @close="$emit('update:visible', false)">
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="(item, index) in skillInfo.resultFormRespVOS || []"
        :label="item.graduationAssessmentFormName"
        :name="index.toString()"
      >
        <div :id="'resultForm-' + index">
          <el-form inline label-width="90px">
            <el-form-item label="学员姓名：" style="width: 24%; white-space: nowrap">{{ studentInfo.nickname }}</el-form-item>
            <el-form-item label="年级：" style="width: 24%">{{ studentInfo.grade }}</el-form-item>
            <el-form-item label="学员类型：" style="width: 24%">
              <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="studentInfo.studentType"></dict-tag>
            </el-form-item>
            <el-form-item label="培训专业：" style="width: 24%">{{ getMajorName(studentInfo.major) }}</el-form-item>
            <el-form-item label="考官姓名：" style="width: 48%">{{ item.creatorNickname }}</el-form-item>
            <el-form-item label="表单名称：" style="width: 100%">{{ item.graduationAssessmentFormName }}</el-form-item>
            <el-form-item class="stem-form-item" label="题干：" style="width: 100%">{{ item.graduationAssessmentFormStem || "--" }}</el-form-item>
            <el-form-item label="表单总分：" style="margin-right: 20px">{{ item.graduationAssessmentFormScore }}</el-form-item>
            <el-form-item label="项目总分：" style="margin-right: 20px;">{{ item.score }}</el-form-item>
            <el-form-item label="考核用时：">
              <el-input-number
                v-model="item.costTime"
                controls-position="right"
                style="width: 100px" :min="0"
                :disabled="true"
              ></el-input-number>分钟
            </el-form-item>
          </el-form>

          <div v-for="(resultItem, i) in item.resultItems" :key="i" style="margin-bottom: 30px">
            <header class="table-header">
              <span style="margin-right: 20px">评分项目：{{ resultItem.graduationAssessmentFormItemName }}</span>
              <span style="margin-right: 20px">总分：{{ resultItem.graduationAssessmentFormItemScore }}</span>
              <span>考核总分：{{ resultItem.score }}</span>
            </header>
            <el-table :data="resultItem.resultFormSubItems" border>
              <el-table-column label="评分要素" prop="graduationAssessmentFormSubItemName"></el-table-column>
              <el-table-column label="分值" prop="graduationAssessmentFormSubItemScore" width="100px"></el-table-column>
              <el-table-column label="考核分" width="200px">
                <template v-slot="scope">
                  <el-input-number
                    style="width: 170px"
                    v-model="scope.row.score"
                    controls-position="right"
                    :min="0"
                    :max="scope.row.graduationAssessmentFormSubItemScore"
                    :disabled="true"
                  ></el-input-number>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <el-form inline label-width="90px">
            <el-form-item label="现场图片：" style="width: 24%; white-space: nowrap">
              <imageUpload
                v-model="item.pictures"
                :limit="9999"
                :disabled="true"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>

    <span slot="footer">
      <el-button type="primary" @click="handleDown">导出</el-button>
    </span>
  </el-dialog>
</template>

<script>
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { getGraduationAssessment } from '@/api/rotation/manual'
import { getUserStudent } from '@/api/system/userStudent'
import { getSimpleMajorList } from '@/api/system/major'
import ImageUpload from '@/components/ImageUpload'

export default {
  components: { ImageUpload },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    scheduleDetailsId: {
      type: Number,
      required: true,
    },
    studentId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      skillInfo: {},
      studentInfo: {},
      majorList: [],
      activeName: '0',
    }
  },
  methods: {
    getSkillInfo() {
      getGraduationAssessment(this.scheduleDetailsId).then(res => this.skillInfo = res.data);
    },
    getStudentInfo() {
      getUserStudent(this.studentId).then(res => {
        this.studentInfo = res.data
        getSimpleMajorList({ studentType: res.data.studentType }).then(res => this.majorList = res.data);
      });
    },
    getMajorName(major) {
      return this.majorList.find(item => item.code === major)?.name
    },
    handleDown(){
      const element = this.skillInfo.resultFormRespVOS[this.activeName]
      this.exportPDF(`resultForm-${this.activeName}`, `${this.studentInfo.nickname}+出科技能考核详情+${element.graduationAssessmentFormName}`)
    },

    exportPDF(tableId, fileName){
      const table = document.getElementById(tableId);
      html2canvas(table, {
        //允许跨域图片的加载
        useCORS: true,
      }).then(canvas => {
        // debugger
        const contentWidth = canvas .width;
        const contentHeight = canvas.height;
        const pageHeight = contentWidth / 592.28 * 841.89;
        let leftHeight = contentHeight;
        let position = 30;
        const imgWidth = 595.28 - 60;
        const imgHeight = (592.28 - 60) / contentWidth * contentHeight;
        const pageData = canvas.toDataURL('image/jpeg', 1.0);
        const pdf = new jsPDF( '', 'pt','a4');
        if (leftHeight < pageHeight) {
          pdf.addImage(pageData, 'JPEG', 30, 30, imgWidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            pdf.addImage(pageData, 'JPEG', 30, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;

            if (leftHeight > 0) {
              pdf.addPage();
            }
          }
        }
        pdf.save(`${fileName}.pdf`);
      })
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.getSkillInfo()
        this.getStudentInfo()
      }
    }
  }
}
</script>

<style lang="scss">

</style>
