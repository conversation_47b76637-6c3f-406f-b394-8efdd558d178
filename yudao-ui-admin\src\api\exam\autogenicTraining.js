import request from "@/utils/request";

// 创建考试自主训练
export function createAutogenicTraining(data) {
  return request({
    url: "/exam/autogenic-training/create",
    method: "post",
    data: data,
  });
}

// 更新考试自主训练
export function updateAutogenicTraining(data) {
  return request({
    url: "/exam/autogenic-training/update",
    method: "put",
    data: data,
  });
}

// 删除考试自主训练
export function deleteAutogenicTraining(id) {
  return request({
    url: "/exam/autogenic-training/delete?id=" + id,
    method: "delete",
  });
}

// 获得考试自主训练
export function getAutogenicTraining(id) {
  return request({
    url: "/exam/autogenic-training/get?id=" + id,
    method: "get",
  });
}

// 获得考试自主训练分页
export function getAutogenicTrainingPage(query) {
  return request({
    url: "/exam/autogenic-training/page",
    method: "get",
    params: query,
  });
}

// 导出考试自主训练 Excel
export function exportAutogenicTrainingExcel(query) {
  return request({
    url: "/exam/autogenic-training/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
