<template>
  <div class="appraise">
    <div class="group-box">
      <el-form inline v-for="(item, index) in (config.appraiseItems || '').indexOf('1') > -1 ? studentAppraiseTeachers : []" :key="index">
        <el-form-item class="w-200" label="带教老师：">{{ item.teacherNickname }}</el-form-item>
        <el-form-item class="w-200" label="状态：">
          <span v-if="item.score" style="color: #13ce66;">已评</span>
          <span v-else style="color: #fc2f2f;">未评</span>
        </el-form-item>
        <el-form-item class="w-300" label="带教时间：">{{ item.startDate }} - {{ item.endDate }}</el-form-item>
        <el-form-item class="w-300" label="评价得分：">
          <el-rate
            v-if="item.score"
            style="margin-right: 20px"
            :value="item.score"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}">
          </el-rate>
          <span v-if="!item.score" style="margin-right: 20px">您还未进行评分哦！</span>
          <el-button v-if="!item.score" size="mini" type="text" icon="el-icon-edit" @click="handleTeacherAppraise(item)">进入评价</el-button>
          <el-button v-if="item.score" size="mini" type="text" icon="el-icon-document" @click="handleView(item)">查看评价</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-form class="group-box" inline v-if="(config.appraiseItems || '').indexOf('2') > -1">
      <el-form-item class="w-200" label="科室名称：">{{ studentAppraiseDept.rotationDepartmentName }}</el-form-item>
      <el-form-item class="w-200" label="状态：">
        <span v-if="studentAppraiseDept.score" style="color: #13ce66;">已评</span>
        <span v-else style="color: #fc2f2f;">未评</span>
      </el-form-item>
      <el-form-item class="w-300" label="轮转时间：">
        {{ studentAppraiseDept.rotationBeginTime }} - {{ studentAppraiseDept.rotationEndTime }}
      </el-form-item>
      <el-form-item class="w-300" label="评价得分：">
        <el-rate
          v-if="studentAppraiseDept.score"
          style="margin-right: 20px"
          :value="studentAppraiseDept.score"
          disabled
          show-score
          text-color="#ff9900"
          score-template="{value}">
        </el-rate>
        <span v-if="!studentAppraiseDept.score" style="margin-right: 20px">您还未进行评分哦！</span>
        <el-button v-if="!studentAppraiseDept.score" size="mini" type="text" icon="el-icon-edit" @click="handleDeptAppraise(studentAppraiseDept)">进入评价</el-button>
        <el-button v-if="studentAppraiseDept.score" size="mini" type="text" icon="el-icon-document" @click="handleView(studentAppraiseDept)">查看评价</el-button>
      </el-form-item>
    </el-form>

    <AppraiseDialog
      v-if="appraiseData"
      :title="title"
      :open="visible"
      :data="appraiseData"
      :appraiseSourceId="appraiseSourceId"
      :appraiseTargetId="appraiseTargetId"
      :disabled="appraiseDisabled"
      @setOpen="setOpen"
      @refreshList="getData"
    />
  </div>
</template>

<script>
import { getAppraise360Statistics } from '@/api/rotation/manual'
import { getAppraiseResult, getAppraiseForm as getAppraiseTeacherForm } from "@/api/rotation/studentappraiseteacher";
import { getAppraiseForm as getAppraiseDeptForm } from "@/api/rotation/studentappraisedept";
// import { getAppraiseResult } from "@/api/rotation/studentappraiseteacher";
import AppraiseDialog from '@/views/components/appraiseDialog'

export default {
  name: 'appraise',
  components: {
    AppraiseDialog
  },
  props: {
    info: Object,
    config: {
      type: Object,
      default: {},
    }
  },
  data() {
    return {
      id: this.$route.query.id,
      studentAppraiseTeachers: [], // 360评价学员评价带教结果
      studentAppraiseDept: {}, // 360评价学员评价科室结果
      title: "",
      visible: false,
      appraiseData: null,
      appraiseSourceId: 0,
      appraiseTargetId: 0,
      appraiseDisabled: false
    }
  },
  methods: {
    getData() {
      getAppraise360Statistics(this.id).then(res => {
        this.studentAppraiseTeachers = res.data.studentAppraiseTeachers
        this.studentAppraiseDept = res.data.studentAppraiseDept
      })
    },
    handleView(row) {
      const id = row.appraise360ResultId;
      this.appraiseDisabled = true
      getAppraiseResult(id).then(response => {
        this.appraiseData = response.data;
        this.visible = true;
        this.title = `查看评价-${row.teacherNickname || row.rotationDepartmentName}`;
      });
    },
    handleTeacherAppraise(row) {
      const id = row.scheduleDetailsId;
      this.curRow = row;
      this.appraiseDisabled = false
      this.appraiseSourceId = this.info.studentId
      this.appraiseTargetId = row.rotationDepartmentId || row.teacherUserId
      getAppraiseTeacherForm(id).then(response => {
        this.appraiseData = response.data
        this.visible = true;
        this.title = `正在评价-${row.teacherNickname}`;
      });
    },
    handleDeptAppraise(row) {
      const id = row.scheduleDetailsId;
      this.curRow = row;
      this.appraiseDisabled = false
      this.appraiseSourceId = this.info.studentId
      this.appraiseTargetId = row.rotationDepartmentId || row.teacherUserId
      getAppraiseDeptForm(id).then(response => {
        this.appraiseData = response.data
        this.visible = true;
        this.title = `正在评价-${row.rotationDepartmentName}`;
      });
    },
    setOpen(flag) {
      this.visible = flag;
      this.curRow = null;
    },
    // handleAppraise(item) {
    //   this.title = "评价" + (item.teacherNickname || item.rotationDepartmentName || "")
    //   getAppraiseResult(item.appraise360ResultId).then(res => this.appraiseData = res.data)
    //   this.appraiseSourceId = this.info.studentId
    //   this.appraiseTargetId = item.rotationDepartmentId || item.teacherUserId
    //   this.visible = true
    // }
  },
  created() {
    this.getData()
  }
}
</script>

<style lang="scss" scoped>
.group-box {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px 16px;
  margin-bottom: 20px;
}

.w-200 {
  width: 200px;
  margin-bottom: 0;
}

.w-300 {
  width: 300px;
  margin-bottom: 0;
}
</style>
