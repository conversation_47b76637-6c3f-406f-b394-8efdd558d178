import request from "@/utils/request";

// 创建师资评优计划申请确认
export function createEvaluationPlanApplyConfirm(data) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/create",
    method: "post",
    data: data,
  });
}

// 更新师资评优计划申请确认
export function updateEvaluationPlanApplyConfirm(data) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/update",
    method: "put",
    data: data,
  });
}

// 删除师资评优计划申请确认
export function deleteEvaluationPlanApplyConfirm(id) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/delete?id=" + id,
    method: "delete",
  });
}

// 获得师资评优计划申请确认
export function getEvaluationPlanApplyConfirm(id) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/get?id=" + id,
    method: "get",
  });
}

// 获得师资评优计划申请确认分页
export function getEvaluationPlanApplyConfirmPage(query) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/page",
    method: "get",
    params: query,
  });
}

// 导出师资评优计划申请确认 Excel
export function exportEvaluationPlanApplyConfirmExcel(query) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 更新师资评优计划申请确认
export function updateEvaluationScore(data) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/update-score",
    method: "put",
    data: data,
  });
}

// 批量更新确认状态
export function updateBatchResult(data) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/update-batch-selection-result",
    method: "put",
    data: data,
  });
}

// 获得师资评优计划申请
export function getEvaluationPlanApply(query) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/get-apply",
    method: "get",
    params: query,
  });
}

// 更新师资评优计划申请确认
export function updateSelectionResult(data) {
  return request({
    url: "/teachers/evaluation-plan-apply-confirm/update-selection-result",
    method: "put",
    data: data,
  });
}
