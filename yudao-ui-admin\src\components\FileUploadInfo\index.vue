<template>
  <div class="upload-file">
    <el-upload
      v-show="!disabled"
      multiple
      :action="`${uploadFileUrl}?directory=${directory}&neddDomain=${needDomain}`"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
      ref="upload"
    >
      <!-- 上传按钮 -->
      <slot>
        <el-button v-if="showBtn" size="mini" type="primary">
          选取文件
        </el-button>
        <el-button
          v-else-if="fileList.length < limit"
          size="mini"
          type="primary"
        >
          选取文件
        </el-button>
      </slot>
      <!-- 上传提示 -->
      <div class="el-upload__tip" slot="tip" v-if="showTip">
        请上传
        <template v-if="fileSize">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        :key="file.url"
        class="el-upload-list__item ele-upload-list__item-content"
        v-for="(file, index) in fileList"
      >
        <!-- <a :href="`${file.url}`" :download="file.name" target="_blank"> -->
        <el-link @click="downloadFile(file.url, file.name)">
          <span class="el-icon-document" style="padding-left: 10px">
            {{ file.name }}
          </span>
        </el-link>
        <div class="ele-upload-list__item-content-action" v-if="!disabled">
          <el-link :underline="false" @click="handleDelete(index)" type="danger"
            >删除</el-link
          >
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script>
import { getAccessToken } from "@/utils/auth";

export default {
  name: "FileUpload",
  props: {
    // 值
    value: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => [
        "doc",
        "docx",
        "xls",
        "xlsx",
        "ppt",
        "pptx",
        "txt",
        "pdf",
      ],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    directory: {
      type: String,
      default: "",
    },
    needDomain: {
      type: Boolean,
      default: true,
    },
    showBtn: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      uploadFileUrl:
        process.env.VUE_APP_BASE_API + "/admin-api/infra/file/upload-get-info", // 请求地址
      headers: { Authorization: "Bearer " + getAccessToken() }, // 设置上传的请求头部
      fileList: [],
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1;
          // 首先将值转为数组
          const list = Array.isArray(val)
            ? JSON.parse(JSON.stringify(val))
            : val.split(",");
          // 然后将数组转为对象数组
          this.fileList = list.map((item) => {
            // debugger
            if (typeof item === "string") {
              if (item.indexOf("?") > -1) {
                item = item.split("?")[0];
              }
              item = { name: item, url: item };
            }
            item.uid = item.uid || new Date().getTime() + temp++;
            if (item.url && item.url.indexOf("?") > -1) {
              item.url = item.url.split("?")[0];
            }
            item.url = item.url + "?token=" + getAccessToken();
            console.log("item=====", item);
            return item;
          });
          // console.log('this.fileList=====', this.fileList)
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          return !!(fileExtension && fileExtension.indexOf(type) > -1);
        });
        if (!isTypeOk) {
          this.$modal.msgError(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      this.$modal.loading("正在上传文件，请稍候...");
      this.number++;
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传文件失败，请重试");
      this.$modal.closeLoading();
    },
    // 上传成功回调
    handleUploadSuccess(res, file, fileList) {
      if (res.code !== 0) {
        const index = fileList.indexOf((item) => item.uid === file.uid);
        fileList.splice(index, 1);
        this.number--;
        this.$modal.msgError(res.msg);
        this.$modal.closeLoading();
        return;
      }
      // edit by 芋道源码
      this.uploadList.push({ name: res.data.name, url: res.data.url });
      if (this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        console.log("this.fileList===", this.fileList);
        this.uploadList = [];
        this.number = 0;
        this.$emit("input", this.fileList);
        // this.$emit("input", this.listToString(this.fileList));
        this.$modal.closeLoading();
      }
    },
    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1);
      this.$emit("input", this.fileList);
      // this.$emit("input", this.listToString(this.fileList));
    },
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
      } else {
        return "";
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        strs += list[i].url + separator;
      }
      return strs !== "" ? strs.substr(0, strs.length - 1) : "";
    },
    downloadFile(url, fileName) {
      // debugger
      // 兼容ie
      if ("ActiveXObject" in window) {
        // ie浏览器没有解决
        // 网上说navigator.msSaveBlob(blob, filename);如果这句话管用，那下面代码不需要了 这个属性那是blob是哪来的，没有测试
        this.createAndRemove(url, fileName); // 实现了ie浏览我的下载，但是没有实现修改文件名的需求
      } else {
        const x = new XMLHttpRequest();
        x.open("GET", url, true);
        x.responseType = "blob";
        x.onload = () => {
          const url = window.URL.createObjectURL(x.response);
          this.handleDownload(url, fileName);
        };
        x.send();
      }
    },
    handleDownload(url, fileName) {
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.download = fileName;
      link.click();
      document.body.removeChild(link);
    },
  },
};
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
