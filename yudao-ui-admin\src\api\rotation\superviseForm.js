import request from '@/utils/request'

// 创建督导表单
export function createSuperviseForm(data) {
  return request({
    url: '/rotation/supervise-form/create',
    method: 'post',
    data: data
  })
}

// 更新督导表单
export function updateSuperviseForm(data) {
  return request({
    url: '/rotation/supervise-form/update',
    method: 'put',
    data: data
  })
}

// 复制督导表单
export function copySuperviseForm(data) {
  return request({
    url: '/rotation/supervise-form/copy',
    method: 'post',
    data: data
  })
}

// 删除督导表单
export function deleteSuperviseForm(id) {
  return request({
    url: '/rotation/supervise-form/delete?id=' + id,
    method: 'delete'
  })
}

// 获得督导表单
export function getSuperviseForm(id) {
  return request({
    url: '/rotation/supervise-form/get?id=' + id,
    method: 'get'
  })
}

// 获得督导表单分页
export function getSuperviseFormPage(query) {
  return request({
    url: '/rotation/supervise-form/page',
    method: 'get',
    params: query
  })
}

// 导出督导表单 Excel
export function exportSuperviseFormExcel(query) {
  return request({
    url: '/rotation/supervise-form/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取维护指标
export function getSuperviseFormItems(id) {
  return request({
    url: '/rotation/supervise-form/get-form-item?id=' + id,
    method: 'get'
  })
}

// 维护指标
export function createSuperviseFormItems(data) {
  return request({
    url: '/rotation/supervise-form/create-form-item',
    method: 'post',
    data: data
  })
}