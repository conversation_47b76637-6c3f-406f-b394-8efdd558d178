<template>
  <!-- 试卷安全中心 -->
  <el-dialog title="试卷安全中心" :visible.sync="open">
    <div class="safe-title">试卷名称：{{ curRow && curRow.name }}</div>
    <el-tabs v-model="active" type="card">
      <el-tab-pane label="考核码" name="1" v-if="curRow && curRow.paperConfig.isAuthentication">
        <span>当前考核码：</span>
        <span class="code-item" v-for="n in authenticationCode">{{ n }}</span>
        <el-button type="primary" @click="regenerateAuthenticationCode">重新生成</el-button>
        <div class="qrcode-wrapper">
          <QRCode :text="authenticationCode"></QRCode>
        </div>
      </el-tab-pane>
      <el-tab-pane label="解锁码" name="2" v-if="curRow && curRow.paperConfig.isPreventionCheat">
        <span>当前考核码：</span>
        <span class="code-item" v-for="n in unlockCode">{{ n }}</span>
        <el-button type="primary" @click="regenerateUnlockCode">重新生成</el-button>
        <div class="qrcode-wrapper">
          <QRCode :text="unlockCode"></QRCode>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div slot="footer" class="dialog-footer">
      <el-button @click="open = false">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getPaperConfig, resetAuthenticationCode, resetUnlockCode } from "@/api/exam/paperConfig";
import QRCode from '@/components/QRCode';

export default {
  name: 'paper-safe-center-dialog',
  components: { QRCode },
  data() {
    return {
      curRow: null,
      open: false,
      active: "1",
      authenticationCode: "",
      unlockCode: "",
    }
  },
  methods: {
    handleOpen(row) {
      this.curRow = row;
      this.active = row.paperConfig.isAuthentication ? "1" : "2";
      getPaperConfig(row.id).then(res => {
        this.authenticationCode = res.data.authenticationCode;
        this.unlockCode = res.data.unlockCode;
        this.open = true;
      });
    },
    regenerateAuthenticationCode() {
      resetAuthenticationCode(this.curRow.id).then(res => {
        this.authenticationCode = res.data;
      });
    },
    regenerateUnlockCode() {
      resetUnlockCode(this.curRow.id).then(res => {
        this.unlockCode = res.data;
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.safe-title {
  font-size: 16px;
  margin-bottom: 15px;
}

.code-item {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  border: 1px solid #eee;
  border-radius: 2px;
  margin-right: 10px;
}

.qrcode-wrapper {
  padding: 20px 0 0 90px;
}
</style>
