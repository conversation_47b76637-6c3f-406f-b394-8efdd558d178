import request from "@/utils/request";

// 获取标准精简方案列表
export function getDeptplanList(query) {
  return request({
    url: "/rotation/deptplan/page",
    method: "get",
    params: query,
    headers: { component: "rotation/deptRotationInfo/index" },
  });
}

// 导出教学活动 Excel
export function exportDeptplanExcel(query) {
  return request({
    url: "/rotation/deptplan/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 学员信息分页查询
export function getJoinUserList(query) {
  return request({
    url: "/rotation/deptplan/page-join-user",
    method: "get",
    params: query,
    headers: { component: "rotation/deptRotationInfo/index" },
  });
}
