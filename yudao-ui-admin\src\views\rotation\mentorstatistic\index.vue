<template>
  <div class="app-container talkAudit-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="学员姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训专业" prop="major">
        <el-select
          v-model="queryParams.major"
          placeholder="请选择培训专业"
          clearable
          size="small"
        >
          <el-option
            v-for="item in queryMajorList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="queryParams.grade"
          placeholder="请选择年级"
          size="small"
          clearable
        >
          <el-option
            v-for="grade in gradeList"
            :key="grade"
            :label="grade"
            :value="grade"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="导师姓名" prop="mentorName">
        <el-input
          v-model="queryParams.mentorName"
          placeholder="请输入导师姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="学员姓名" align="center" prop="studentName" />
      <el-table-column label="培训专业" align="center" prop="majorName" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column label="导师姓名" align="center" prop="mentorName" />
      <el-table-column
        label="谈话记录完成情况"
        align="center"
        prop="completedPercentage"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewRecordList(scope.row)"
          >
            {{
              `${scope.row.completed}/${scope.row.total} (${scope.row.completedPercentage})`
            }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="学员评价导师"
        align="center"
        prop="appraiseMentor"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewAppraise(scope.row, 'mentor')"
            >{{ scope.row.appraiseMentor || "--" }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="导师评价学员"
        align="center"
        prop="appraiseStudent"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewAppraise(scope.row, 'student')"
            >{{ scope.row.appraiseStudent || "--" }}</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="谈话记录查看"
      :visible.sync="openRecordList"
      width="1000px"
      v-dialogDrag
      append-to-body
      custom-class="audit-list-dialog"
    >
      <div>
        <el-form
          :model="queryRecordListParams"
          ref="queryRecordListForm"
          size="small"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="学员姓名">
            <span>{{ curActive.studentName }}</span>
          </el-form-item>
          <el-form-item
            label="交流日期"
            prop="communicationDate"
            style="width: 300px"
          >
            <el-date-picker
              type="daterange"
              clearable
              v-model="queryRecordListParams.communicationDate"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="searchRecordList"
              style="width: 220px"
            />
          </el-form-item>
          <el-form-item
            label="审核状态"
            prop="auditStatus"
            style="width: 200px"
          >
            <el-select
              v-model="queryRecordListParams.auditStatus"
              placeholder="请选择审核状态"
              clearable
              size="small"
              @change="searchRecordList"
              style="width: 120px"
            >
              <el-option
                v-for="dict in this.getDictDatas(
                  DICT_TYPE.ROTATION_AUDIT_STATUS
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="完成数/要求数"
            ><span>{{
              `${curActive.completed}/${curActive.total} (${curActive.completedPercentage})`
            }}</span></el-form-item
          >
        </el-form>
        <el-table v-loading="loadingRecordList" :data="recordList">
          <el-table-column
            label="交流日期"
            align="center"
            prop="communicationDate"
          />
          <el-table-column
            label="交流方式"
            prop="communicationWay"
            align="center"
          >
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.MENTOR_COMMUNICATION_WAY"
                :value="scope.row.communicationWay"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="交流地点"
            align="center"
            prop="communicationAddress"
          />
          <el-table-column label="审核状态" prop="auditStatus" align="center">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.ROTATION_AUDIT_STATUS"
                :value="scope.row.auditStatus"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column label="填写时间" align="center" prop="createTime" />
          <el-table-column label="审核时间" align="center" prop="auditTime" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleRecordDetail(scope.row)"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="recordTotal > 0"
          :total="recordTotal"
          :page.sync="queryRecordListParams.pageNo"
          :limit.sync="queryRecordListParams.pageSize"
          @pagination="getRecordList"
        />
      </div>
    </el-dialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="680px"
      v-dialogDrag
      append-to-body
      custom-class="talkRecord-dialog"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="180px"
        :disabled="opt === 'view'"
      >
        <el-form-item label="交流方式" prop="communicationWay">
          <el-select
            v-model="form.communicationWay"
            placeholder="请选择交流方式"
            style="width: 100%"
          >
            <el-option
              v-for="dict in this.getDictDatas(
                DICT_TYPE.MENTOR_COMMUNICATION_WAY
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="交流日期" prop="communicationDate">
          <el-date-picker
            clearable
            v-model="form.communicationDate"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择开展时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="交流地点" prop="communicationAddress">
          <el-input
            type="textarea"
            v-model="form.communicationAddress"
            placeholder="请输入交流地点"
          />
        </el-form-item>

        <el-form-item label="主要交流内容" prop="communicationContent">
          <el-input
            type="textarea"
            v-model="form.communicationContent"
            placeholder="请输入主要交流内容"
            autosize
          />
        </el-form-item>

        <el-form-item label="图片上传" prop="photo">
          <imageUpload
            v-model="form.photo"
            :limit="9999"
            activeTypeName=""
            :disabled="opt === 'view'"
          />
        </el-form-item>

        <el-form-item label="附件上传" prop="file">
          <FileUpload
            v-model="form.file"
            :limit="999"
            :fileSize="50"
            :disabled="opt === 'view'"
          />
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      :title="appraiseTitle"
      :visible.sync="openAppraiseList"
      width="800px"
      v-dialogDrag
      append-to-body
      custom-class="audit-list-dialog"
    >
      <div>
        <el-table :data="appraiseList">
          <el-table-column
            :label="this.appraiseType === 'mentor' ? '评价对象' : '评价人'"
            align="center"
            :prop="
              this.appraiseType === 'mentor'
                ? 'mentorUserName'
                : 'studentUserName'
            "
          />
          <el-table-column
            label="评价年月"
            prop="appraiseBeginDate"
            align="center"
          ></el-table-column>
          <el-table-column label="学员评价导师" align="center" prop="score">
            <template slot-scope="scope">
              <el-link
                type="primary"
                :underline="false"
                @click="viewAppraiseDetail(scope.row)"
                >{{ scope.row.score || "--" }}</el-link
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="appraiseTotal > 0"
          :total="appraiseTotal"
          :page.sync="queryAppraisesParams.pageNo"
          :limit.sync="queryAppraisesParams.pageSize"
          @pagination="getAppraiseList"
        />
      </div>
    </el-dialog>

    <AppraiseDialog
      v-if="formAppraiseData"
      :title="appraiseFormTitle"
      :open="openFormAppraise"
      :data="formAppraiseData"
      :disabled="true"
      @setOpen="setOpen"
      @refreshList="getList"
    />
  </div>
</template>

<script>
import AppraiseDialog from "../appraiseMentor/appraiseDialog";
import FileUpload from "@/components/FileUploadInfo";
import ImageUpload from "@/components/ImageUpload";
import { getSimpleMajorList } from "@/api/system/major";
import { getStudentGradeList } from "@/api/system/userStudent";
import {
  getMentorStatisticPage,
  getCompleteStatusPage,
  getMentorPage,
  getStudentPage,
  exportMentorStatisticExcel,
} from "@/api/rotation/mentorstatistic";
import { getMentorNote } from "@/api/rotation/talkRecord";
import { getAppraiseForm } from "@/api/rotation/appraiseMentor";

export default {
  name: "MentorStatistic",
  components: {
    FileUpload,
    ImageUpload,
    AppraiseDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 院级培训列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        studentName: "",
        major: "",
        grade: "",
        mentorName: "",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communicationAddress: [
          { required: true, message: "交流地点不能为空", trigger: "blur" },
        ],
        communicationWay: [
          { required: true, message: "交流方式不能为空", trigger: "change" },
        ],
        communicationDate: [
          { required: true, message: "交流日期不能为空", trigger: "change" },
        ],
        communicationContent: [
          { required: true, message: "主要交流内容不能为空", trigger: "blur" },
        ],
      },
      curActive: {},
      opt: "",

      // 年级列表
      gradeList: [],
      queryMajorList: [],

      openAppraiseList: false,
      appraiseList: [],
      appraiseTotal: 0,
      queryAppraisesParams: {
        pageNo: 1,
        pageSize: 10,
        studentUserId: "",
      },
      appraiseType: "",
      appraiseTitle: "",

      formAppraiseData: null,
      openFormAppraise: false,
      appraiseFormTitle: "",

      openRecordList: false,
      loadingRecordList: false,
      queryRecordListParams: {
        pageNo: 1,
        pageSize: 10,
        studentUserId: null,
        communicationDate: null,
        auditStatus: null,
      },
      recordList: [],
      recordTotal: 0,
    };
  },
  created() {
    getSimpleMajorList().then((res) => {
      this.queryMajorList = res.data;
    });
    getStudentGradeList().then((res) => (this.gradeList = res.data));
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getMentorStatisticPage(this.queryParams).then((response) => {
        const list = response.data.list;
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        nickname: undefined,
        major: undefined,
        grade: undefined,
        isToAudit: true,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    viewAppraise(row, type) {
      this.curActive = row;
      this.queryAppraisesParams.studentUserId = row.studentUserId;
      this.queryAppraisesParams.pageNo = 1;
      this.appraiseType = type;
      this.getAppraiseList();
    },
    getAppraiseList() {
      const api =
        this.appraiseType === "mentor" ? getMentorPage : getStudentPage;
      api(this.queryAppraisesParams).then((response) => {
        const { data } = response;
        this.appraiseList = data.list || [];
        this.appraiseTotal = data.total;
        this.appraiseTitle =
          this.appraiseType === "mentor"
            ? "学员评价导师列表"
            : "导师评价学员列表";
        this.openAppraiseList = true;
      });
    },
    viewRecordList(row) {
      this.curActive = row;
      this.openRecordList = true;
      this.queryRecordListParams.pageNo = 1;
      this.queryRecordListParams.studentUserId = row.studentUserId;
      this.getRecordList();
    },
    getRecordList() {
      this.loadingRecordList = true;
      // 执行查询
      getCompleteStatusPage(this.queryRecordListParams).then((response) => {
        const list = response.data.list;
        this.recordList = list;
        this.recordTotal = response.data.total;
        this.loadingRecordList = false;
      });
    },
    searchRecordList() {
      this.$nextTick(() => {
        this.queryRecordListParams.pageNo = 1;
        this.getRecordList();
      });
    },

    /** 查看记录详情 */
    handleRecordDetail(row) {
      this.reset();
      const id = row.id;
      this.opt = "view";
      getMentorNote(id).then((response) => {
        const { data } = response;
        this.form = data;
        this.form.file = JSON.parse(this.form.file);
        this.open = true;
        this.title = "查看谈话记录";
      });
    },

    viewAppraiseDetail(row) {
      const id = row.id;

      getAppraiseForm(id).then((response) => {
        this.formAppraiseData = response.data;
        this.openFormAppraise = true;
        this.appraiseFormTitle = `查看评价-${
          row.mentorUserName || row.studentUserName
        }`;
      });
    },

    setOpen(flag) {
      this.openFormAppraise = flag;
    },

    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportMentorStatisticExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "导师信息统计.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss">
.talkAudit-container {
}

.audit-list-dialog {
  .table-top {
    padding-bottom: 10px;
    display: flex;
    align-items: center;

    div {
      margin-right: 20px;

      span.lable {
        font-weight: bold;
      }
    }
  }
}

.talkRecord-dialog {
  .el-dialog__body {
    padding-right: 100px;
  }
}
</style>
