import request from '@/utils/request'

// 获得院级培训分页
export function getHospitalTrainingPage(query) {
  return request({
    url: '/rotation/hospital-training-evaluate/page',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/hospitalTrainingEvaluate/index'}
  })
}

// 创建
export function createHospitalTrainingEvaluate(data) {
  return request({
    url: '/rotation/hospital-training-evaluate/create',
    method: 'post',
    data: data
  })
}

// 查看详情
export function getHospitalTrainingEvaluate(evaluateId) {
  return request({
    url: '/rotation/hospital-training-evaluate/getEvaluateDetailsById?evaluateId=' + evaluateId,
    method: 'get'
  })
}

// 获得院级培训听评课记录分页
export function getHospitalTrainingLogPage(query) {
  return request({
    url: '/rotation/hospital-training-evaluate/logPage',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/hospitalTrainingEvaluate/index'}
  })
}

// 获得听评课记录分页
export function getEvaluateRecordPage(query) {
  return request({
    url: '/rotation/hospital-training-evaluate/page-hospital-training-evaluate-record',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/hospitalTrainingEvaluate/index'}
  })
}

// 保存听评课反馈
export function saveEvaluateFeedback(data) {
  return request({
    url: '/rotation/hospital-training-evaluate/save-evaluate-feedback',
    method: 'post',
    data: data
  })
}
