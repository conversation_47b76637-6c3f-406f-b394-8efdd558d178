<template>
  <div class="app-container">
    <el-radio-group
      style="margin-bottom: 20px"
      size="medium"
      v-model="queryParams.appraiseProcessType"
      @change="handleAppraiseActiveTypeChange"
    >
      <el-radio-button
        v-for="(item, index) in appraiseTypeList"
        :key="index"
        :label="item.value"
        >{{ item.label }}</el-radio-button
      >
    </el-radio-group>

    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="评价指标" prop="appraiseKpi">
        <el-input
          v-model="queryParams.appraiseKpi"
          placeholder="请输入评价指标"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:appraise-process:create']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-setting"
          size="mini"
          @click="handleSaveNote"
          v-hasPermi="['rotation:appraise-active-notice:update']"
        >
          评价次数设置
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="序号"
        type="index"
        align="center"
      ></el-table-column>
      <el-table-column label="评价指标" align="center" prop="appraiseKpi" />
      <el-table-column label="指标分值" align="center" prop="score" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="120"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rotation:appraise-process:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:appraise-process:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="评价指标" prop="appraiseKpi">
          <el-input v-model="form.appraiseKpi" placeholder="请输入评价指标" />
        </el-form-item>
        <el-form-item label="指标分值" prop="score">
          <el-input-number
            v-model="form.score"
            :min="0"
            controls-position="right"
            placeholder="请输入指标分值"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="configTitle"
      :visible.sync="openConfig"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <div
        v-if="
          ['3', '4', '5', '6', '7', '8', '9', '10'].indexOf(
            queryParams.appraiseProcessType
          ) > -1
        "
        class="config-box"
      >
        <span><span style="color: red">*</span>评价生成日期：</span>
        <el-date-picker
          v-model="appraiseBeginDate"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        >
        </el-date-picker>
      </div>
      <div class="config-box">
        <span><span style="color: red">*</span>评价周期： </span
        ><span> 每间隔 </span
        ><el-input
          v-model="appraiseIntervalMonths"
          placeholder="请输入"
          clearable
          style="width: 90px"
        /><span>个月，评价一次；</span>
      </div>
      <div class="config-box">
        <span>注：若不做配置，则默认为每间隔一个月，评价一次！</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitConfigForm">确 定</el-button>
        <el-button @click="cancelConfigForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAppraiseProcessPage,
  createAppraiseProcess,
  updateAppraiseProcess,
  getAppraiseProcess,
  deleteAppraiseProcess,
  getProcessConfig,
  saveProcessConfig,
} from "@/api/rotation/appraiseProcess";

export default {
  name: "AppraiseActiveItem",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 活动评价项列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 活动指标类型
      appraiseTypeList: [],
      // 活动类型
      rotationActiveTypeList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        appraiseProcessType: 1,
        appraiseKpi: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        appraiseKpi: [
          { required: true, message: "评价指标不能为空", trigger: "blur" },
        ],
        score: [
          { required: true, message: "指标分值不能为空", trigger: "blur" },
        ],
      },
      // 活动须知
      activeNote: "",
      configTitle: "",
      openConfig: false,
      appraiseIntervalMonths: "",
      appraiseBeginDate: "",
    };
  },
  created() {
    this.appraiseTypeList = this.getDictDatas(
      this.DICT_TYPE.APPRAISE_PROCESS_TYPE
    );
    this.queryParams.appraiseProcessType = this.appraiseTypeList[0]?.value;
    // this.rotationActiveTypeList = this.getDictDatas(this.DICT_TYPE.ROTATION_ACTIVE_TYPE);
    // this.queryParams.activeType = this.rotationActiveTypeList[0]?.value;
    this.getList();
    // this.getActiveNote();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getAppraiseProcessPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        appraiseKpi: undefined,
        score: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加评价项";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getAppraiseProcess(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改评价项";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateAppraiseProcess(this.form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createAppraiseProcess({
          appraiseProcessItemCreateReqVO: this.form,
          appraiseProcessType: this.queryParams.appraiseProcessType,
        }).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除" + row.appraiseKpi + "评价指标?")
        .then(function () {
          return deleteAppraiseProcess(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 指标类型改变 */
    handleAppraiseActiveTypeChange() {
      this.getList();
      this.appraiseBeginDate = "";
      this.appraiseIntervalMonths = "";
    },
    /** 编辑活动须知 */
    handleSaveNote() {
      getProcessConfig({
        appraiseProcessType: this.queryParams.appraiseProcessType,
      }).then((res) => {
        const appraiseProcessType = this.queryParams.appraiseProcessType;
        switch (appraiseProcessType) {
          case "1":
            debugger;
            this.configTitle = "学员对导师的评价次数设置";
            break;
          case "2":
            this.configTitle = "导师对学员的评价次数设置";
            break;
          case "3":
            this.configTitle = "科室对带教的评价次数设置";
            break;
          case "4":
            this.configTitle = "专业基地对带教的评价次数设置";
            break;
          case "5":
            this.configTitle = "医院管理员对带教的评价次数设置";
            break;
          case "6":
            this.configTitle = "专业基地对学员的评价次数设置";
            break;
          case "7":
            this.configTitle = "带教自评评价次数设置";
            break;
          case "8":
            this.configTitle = "学员自评评价次数设置";
            break;
          case "9":
            this.configTitle = "月度科室评价学员次数设置";
            break;
          default:
            this.configTitle = "评价次数设置";
        }

        this.appraiseIntervalMonths = res.data?.appraiseIntervalMonths || 1;
        this.appraiseBeginDate = res.data?.appraiseBeginDate || "";
        this.openConfig = true;
      });
    },
    cancelConfigForm() {
      this.openConfig = false;
    },
    submitConfigForm() {
      if (
        ["3", "4", "5", "6", "7", "8", "9"].indexOf(
          this.queryParams.appraiseProcessType
        ) > -1 &&
        !this.appraiseIntervalMonths
      ) {
        return this.$modal.msgError("请输入评价生成日期");
      }
      if (!this.appraiseIntervalMonths) {
        return this.$modal.msgError("请输入评价次数");
      }
      saveProcessConfig({
        appraiseIntervalMonths: this.appraiseIntervalMonths,
        appraiseBeginDate: this.appraiseBeginDate,
        appraiseProcessType: this.queryParams.appraiseProcessType,
      }).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.openConfig = false;
        this.getList();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}

.active-note {
  font-size: 15px;
  margin-bottom: 8px;

  .el-icon-edit {
    color: #999999;
    cursor: pointer;
    &:hover {
      color: #46a6ff;
    }
  }
}

.config-box {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .el-input {
    margin: 0 10px;
  }
}
</style>
