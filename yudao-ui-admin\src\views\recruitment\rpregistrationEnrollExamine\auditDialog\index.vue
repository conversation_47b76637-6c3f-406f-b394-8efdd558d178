<template>
  <el-dialog
    :title="title"
    :visible="open"
    width="600px"
    v-dialogDrag
    append-to-body
    destroy-on-close
    @close="cancel"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row :gutter="10">
        <el-col :md="24" :lg="24" :xl="24">
          <el-form-item label="录取建议" prop="enrollExamineRecommend">
            <el-input
              type="textarea"
              v-model="form.enrollExamineRecommend"
              placeholder="请输入录取建议"
              :autosize="{ minRows: 2 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm"> 确 认 </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { auditRpregistrationEnrollExam } from "@/api/recruitment/rpregistrationEnrollExamine";

export default {
  name: "AuditDialog",
  components: {},
  props: {
    title: {
      type: String,
    },
    openAudit: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      open: this.openAudit,

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        enrollExamineRecommend: [
          { required: true, message: "录取建议不能为空", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    openAudit(newVal) {
      this.form = {
        recruitmentRegistrationId: this.formData.recruitmentRegistrationId,
      };
      this.open = newVal;
    },
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:openAudit", false);
    },

    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }

        auditRpregistrationEnrollExam(this.form).then((response) => {
          this.$modal.msgSuccess("审核成功");
          this.$emit("refresh");
          this.cancel();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
