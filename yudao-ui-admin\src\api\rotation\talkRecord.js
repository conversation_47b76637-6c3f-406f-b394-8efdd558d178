import request from '@/utils/request'

// 获得导师谈话记录分页
export function getMentorNotesPage(query) {
    return request({
      url: '/rotation/mentor-interview-notes/page',
      method: 'get',
      params: query
    })
}

// 获得导师谈话记录需完成总数和已完成数
export function getTotalAndCompleted(query) {
    return request({
      url: '/rotation/mentor-interview-notes/getTotalAndCompleted',
      method: 'get',
      params: query
    })
}

// 创建导师谈话记录
export function createMentorNotes(data) {
    return request({
      url: '/rotation/mentor-interview-notes/create',
      method: 'post',
      data: data
    })
}

// 更新导师谈话记录
export function updateMentorNotes(data) {
    return request({
      url: '/rotation/mentor-interview-notes/update',
      method: 'put',
      data: data
    })
}

// 获得导师谈话记录
export function getMentorNote(id) {
    return request({
      url: '/rotation/mentor-interview-notes/get?id=' + id,
      method: 'get'
    })
}

// 导出督导表单 Excel
export function exportMentorNotes(query) {
    return request({
      url: '/rotation/mentor-interview-notes/export-excel',
      method: 'get',
      params: query,
      responseType: 'blob'
    })
}






















