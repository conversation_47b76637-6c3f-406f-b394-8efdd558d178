import request from '@/utils/request'

// 获得考试试卷配置
export function getPaperConfig(id) {
  return request({
    url: '/exam/paper-config/get?id=' + id,
    method: 'get'
  })
}

// 获得考试试卷配置 不包含考试码和锁屏码
export function getPaperPureConfig(id) {
  return request({
    url: '/exam/paper-config/get-simple?id=' + id,
    method: 'get'
  })
}

// 保存考试试卷配置
export function savePaperConfig(data) {
  return request({
    url: '/exam/paper-config/save',
    method: 'put',
    data: data
  })
}

// 重置考核码
export function resetAuthenticationCode(id) {
  return request({
    url: '/exam/paper-config/reset-authentication-code?id=' + id,
    method: 'get'
  })
}

// 重置解锁码
export function resetUnlockCode(id) {
  return request({
    url: '/exam/paper-config/reset-unlock-code?id=' + id,
    method: 'get'
  })
}

// 验证考试码
export function validateAuthenticationCode(query) {
  return request({
    url: '/exam/paper-config/validate-authentication-code',
    method: 'get',
    params: query
  })
}

// 验证解锁码
export function validateUnlockCode(query) {
  return request({
    url: '/exam/paper-config/validate-unlock-code',
    method: 'get',
    params: query
  })
}
