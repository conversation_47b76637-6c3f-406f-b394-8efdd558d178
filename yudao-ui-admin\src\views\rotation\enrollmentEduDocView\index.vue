<template>
  <div class="bg-wrapper">
    <div class="enrollment-edu-doc-view">
      <h2>{{ deptName }}入科教育文档</h2>
      <p class="doc-label">科室简介:</p>
      <p class="doc-content">{{ eduDocInfo.departmentIntro || "无" }}</p>
      <p class="doc-label">科室人员:</p>
      <el-table :data="eduDocInfo.departmentWorkers ? JSON.parse(eduDocInfo.departmentWorkers) : []" border>
        <el-table-column label="职务" prop="post">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_USER_POST" :value="scope.row.post"></dict-tag>
          </template>
        </el-table-column>
        <el-table-column label="姓名" prop="name"></el-table-column>
        <el-table-column label="联系方式" prop="tel"></el-table-column>
        <el-table-column label="职称" prop="positionalTitle">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES" :value="scope.row.positionalTitle"></dict-tag>
          </template>
        </el-table-column>
      </el-table>
      <p class="doc-label">人员简介:</p>
      <p class="doc-content">{{ eduDocInfo.userIntro || "无" }}</p>
      <p class="doc-label">工作环境介绍:</p>
      <p class="doc-content">{{ eduDocInfo.workEnv || "无" }}</p>
      <p class="doc-label">诊治范围:</p>
      <p class="doc-content">{{ eduDocInfo.treatScope || "无" }}</p>
      <p class="doc-label">科室固定学术及活动安排:</p>
      <el-table :data="eduDocInfo.departmentJourney ? JSON.parse(eduDocInfo.departmentJourney) : []" border>
        <el-table-column label="时间" prop="time"></el-table-column>
        <el-table-column label="地点" prop="addr"></el-table-column>
        <el-table-column label="内容" prop="cont"></el-table-column>
      </el-table>
      <p class="doc-label">科室考勤制度:</p>
      <p class="doc-content">{{ eduDocInfo.attendanceSystem || "无" }}</p>
      <p class="doc-label">入科教育文档:</p>
      <div class="edu-docs">
        <a v-for="file in eduDocInfo.files ? JSON.parse(eduDocInfo.files) : []" :href="`${file.url}?token=${getAccessToken()}`" target="_blank">
          {{ file.name }}
        </a>
        <span v-if="!eduDocInfo.files">无</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getEnrollmentEduDoc, studyEnrollmentEduDoc } from '@/api/rotation/manual'
import { getAccessToken } from '@/utils/auth'

export default {
  name: 'index',
  data() {
    return {
      scheduleDetailsId: this.$route.query.scheduleDetailsId,
      id: this.$route.query.id,
      deptName: this.$route.query.dept,
      eduDocInfo: {}
    }
  },
  methods: { getAccessToken },
  created() {
    getEnrollmentEduDoc(this.scheduleDetailsId).then(res => this.eduDocInfo = res.data)
    studyEnrollmentEduDoc(this.id)
  }
}
</script>

<style lang="scss" scoped>
.bg-wrapper {
  padding: 20px;
  background: #fdfdfd;
}

.enrollment-edu-doc-view {
  font-size: 14px;
  width: 800px;
  padding: 1px 20px 20px 20px;
  background: #fff;

  h2 {
    font-size: 18px;
    font-weight: 600;
    text-align: center;
  }

  .doc-label {
    font-weight: 600;
    background: #f1f1f1;
    padding: 5px 10px;
  }

  .doc-content {
    border: 1px solid #e9e9e9;
    padding: 10px;
    border-radius: 2px;
    margin-bottom: 30px;
  }

  ::v-deep .el-table {
    margin-bottom: 30px;
  }

  .edu-docs {
    color: #409EFF;
    padding-bottom: 20px;

    a {
      margin: 0 20px 10px 0;
      &:hover {
        text-decoration: underline;
        text-underline-offset: 5px;
      }
    }
  }
}
</style>
