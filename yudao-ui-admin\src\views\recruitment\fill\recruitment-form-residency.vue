<template>
  <div class="info-fill">
    <el-card shadow="hover">
      <div slot="header">报名信息</div>
      <el-form ref="registrationForm" inline label-position="top" :model="registrationVO" :rules="registrationRules" :disabled="readonly">
        <el-form-item label="报名专业：" prop="majorCode">
          <el-select v-model="registrationVO.majorCode">
            <el-option v-for="item in majorList" :key="item.majorCode" :label="item.majorName" :value="item.majorCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否服从调剂" prop="isObeyAdjustment">
          <el-radio-group v-model="registrationVO.isObeyAdjustment">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="希望调剂专业" prop="hopeMajorCodes" v-if="registrationVO.isObeyAdjustment">
          <el-select
            :value="registrationVO.hopeMajorCodes ? registrationVO.hopeMajorCodes.split(',') : []"
            multiple
            collapse-tags
            @change="registrationVO.hopeMajorCodes = $event.join(',')">
            <el-option v-for="item in majorList" :key="item.majorCode" :label="item.majorName" :value="item.majorCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报考人员类别" prop="recruitmentPersonnelType">
          <el-select v-model="registrationVO.recruitmentPersonnelType">
            <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_PERSONNEL_TYPE)" :key="dict.value"
              :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工作单位全称" prop="workUnit" v-if="registrationVO.recruitmentPersonnelType === '2'">
          <el-input v-model="registrationVO.workUnit"></el-input>
        </el-form-item>
        <el-form-item label="既往是否参加过住院医师规范化培训" prop="isPreviousTrained">
          <el-radio-group v-model="registrationVO.isPreviousTrained">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="既往培训单位" prop="previousTrainingUnit" v-if="registrationVO.isPreviousTrained">
          <el-input v-model="registrationVO.previousTrainingUnit"></el-input>
        </el-form-item>
        <el-form-item label="既往培训专业" prop="previousTrainingMajor" v-if="registrationVO.isPreviousTrained">
          <el-input v-model="registrationVO.previousTrainingMajor"></el-input>
        </el-form-item>
        <el-form-item label="既往培训起止时间" prop="previousTrainingStartDate" v-if="registrationVO.isPreviousTrained">
          <el-date-picker
            style="width: 100%"
            type="daterange"
            start-placeholder="开始时间"
            range-separator="至"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
            :value="registrationVO.previousTrainingStartDate && registrationVO.previousTrainingEndDate ?
             [registrationVO.previousTrainingStartDate, registrationVO.previousTrainingEndDate] : []"
            @input="(value) => {
              registrationVO.previousTrainingStartDate = value ? value[0] : ''
              registrationVO.previousTrainingEndDate = value ? value[1] : ''
            }"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">基本信息</div>
      <el-form ref="baseInfoForm" inline label-position="top" :model="baseinfoVO" :rules="baseInfoRules" :disabled="readonly">
        <el-form-item label="姓名：" prop="name">
          <el-input v-model="baseinfoVO.name"></el-input>
        </el-form-item>
        <el-form-item label="性别：" prop="sex">
          <el-radio-group v-model="baseinfoVO.sex">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                      :key="dict.value" :label="+dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="民族：" prop="nation">
          <el-select v-model="baseinfoVO.nation" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_NATION)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item class="avatar-form-item" prop="avatar">
          <div class="avatar-upload">
            <image-upload
              v-model="baseinfoVO.avatar"
              :limit="1"
              :is-show-tip="false"
            ></image-upload>
            <p>
              使用1寸白底彩色免冠照片 <br/>
              请谨慎认真上传!
            </p>
          </div>
        </el-form-item>
        <el-form-item label="外语水平：" prop="foreignLanguageLevel">
          <el-select v-model="baseinfoVO.foreignLanguageLevel">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_FOREIGN_LANGUAGE_ABILITY)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="证件类型：" prop="idType">
          <el-select v-model="baseinfoVO.idType">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_CERTIFICATE_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="证件号码：" prop="idNo">
          <el-input v-model="baseinfoVO.idNo" @change="handleIdNoChange"></el-input>
        </el-form-item>
        <el-form-item label="出生日期：" prop="birthday">
          <el-date-picker v-model="baseinfoVO.birthday" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="年龄：" prop="age">
          <el-input-number v-model="baseinfoVO.age" controls-position="right" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="移动电话：" prop="mobilePhone">
          <el-input v-model="baseinfoVO.mobilePhone"></el-input>
        </el-form-item>
        <el-form-item label="邮箱地址：" prop="email">
          <el-input v-model="baseinfoVO.email"></el-input>
        </el-form-item>
        <el-form-item label="QQ：" prop="qq">
          <el-input v-model="baseinfoVO.qq"></el-input>
        </el-form-item>
        <el-form-item label="微信：" prop="weixin">
          <el-input v-model="baseinfoVO.weixin"></el-input>
        </el-form-item>
        <el-form-item label="籍贯：" prop="nativePlace">
          <el-select v-model="baseinfoVO.nativePlace" filterable>
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.NATIVE_PLACE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="最高学历：" prop="highestAcademic">
          <el-select v-model="baseinfoVO.highestAcademic">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="最高学历获得时间：" prop="highestAcademicTime">
          <el-date-picker v-model="baseinfoVO.highestAcademicTime" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="最高学历毕业院校" prop="highestAcademicSchool">
          <el-input v-model="baseinfoVO.highestAcademicSchool"></el-input>
        </el-form-item>
        <el-form-item label="最高学位：" prop="highestDegree">
          <el-select v-model="baseinfoVO.highestDegree">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_DEGREE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="最高学位毕业院校" prop="highestDegreeSchool">
          <el-input v-model="baseinfoVO.highestDegreeSchool"></el-input>
        </el-form-item>
        <el-form-item label="开户银行：" prop="bank">
          <el-input v-model="baseinfoVO.bank" placeholder="某某银行某某支行"></el-input>
        </el-form-item>
        <el-form-item label="银行卡号：" prop="bankCardNumber">
          <el-input v-model="baseinfoVO.bankCardNumber"></el-input>
        </el-form-item>
        <el-form-item label="现住地址：" prop="currentAddress">
          <el-input v-model="baseinfoVO.currentAddress"></el-input>
        </el-form-item>
        <el-form-item label="政治面貌：" prop="politicalOutlook">
          <el-input v-model="baseinfoVO.politicalOutlook"></el-input>
        </el-form-item>
        <el-form-item label="健康状况：" prop="healthCondition">
          <el-select v-model="baseinfoVO.healthCondition">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_HEALTH_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="固定电话：" prop="fixedPhoneAreaCode">
          <el-input style="width: 65px" v-model="baseinfoVO.fixedPhoneAreaCode"></el-input>
          -
          <el-input style="width: 125px" v-model="baseinfoVO.fixedPhoneNumber"></el-input>
        </el-form-item>
        <el-form-item label="紧急联系人：" prop="emergencyContact">
          <el-input v-model="baseinfoVO.emergencyContact"></el-input>
        </el-form-item>
        <el-form-item label="与本人关系：" prop="emergencyRelationship">
          <el-input v-model="baseinfoVO.emergencyRelationship"></el-input>
        </el-form-item>
        <el-form-item label="紧急联系人电话：" prop="emergencyTelephone">
          <el-input v-model="baseinfoVO.emergencyTelephone"></el-input>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="hover">
      <div class="add-header" slot="header">
        <div><span class="required-sign">*</span>学历信息【自本科起填写】</div>
        <el-button v-if="!readonly" icon="el-icon-plus" type="primary" size="small" @click="addEduInfo">添加</el-button>
      </div>
      <el-table :data="eduInfoVOS">
        <el-table-column label="起止时间" prop="startDate" width="320px">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.startDate }} - {{ scope.row.endDate || "至今" }}</span>
            <template v-else>
              <el-date-picker class="short-item" v-model="scope.row.startDate" value-format="yyyy-MM-dd" placeholder="起始时间"></el-date-picker> -
              <el-date-picker class="short-item" v-model="scope.row.endDate" value-format="yyyy-MM-dd" placeholder="至今" clearable></el-date-picker>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="学校名称" prop="schoolName">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.schoolName }}</span>
            <el-input v-else v-model="scope.row.schoolName"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="专业" prop="major">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.major }}</span>
            <el-input v-else v-model="scope.row.major"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="学历" prop="education">
          <template v-slot="scope">
            <span v-if="readonly">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_EDUCATION, scope.row.education) }}</span>
            <el-select v-else v-model="scope.row.education">
              <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_EDUCATION)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="学位" prop="degree">
          <template v-slot="scope">
            <span v-if="readonly">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_DEGREE, scope.row.degree) }}</span>
            <el-select v-else v-model="scope.row.degree">
              <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_DEGREE)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="60px" v-if="!readonly">
          <template v-slot="scope">
            <el-link type="danger" @click="eduInfoVOS.splice(scope.row.$index, 1)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">资格证书信息</div>
      <el-form ref="certificateInfoForm" inline label-position="top" :model="certificateInfoVO" :rules="certificateInfoRules" :disabled="readonly">
        <el-form-item label="是否具有国家医师资格证书：" prop="isHavePhysicianQualificationCertificate">
          <el-select v-model="certificateInfoVO.isHavePhysicianQualificationCertificate">
            <el-option :value="true" label="是"></el-option>
            <el-option :value="false" label="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否通过国家医师资格证书考试：" prop="isPassPhysicianQualificationCertificate" v-if="!certificateInfoVO.isHavePhysicianQualificationCertificate">
          <el-select v-model="certificateInfoVO.isPassPhysicianQualificationCertificate">
            <el-option :value="true" label="是"></el-option>
            <el-option :value="false" label="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="专业技术资格证书编号:" prop="physicianQualificationCertificateNum" v-if="certificateInfoVO.isHavePhysicianQualificationCertificate">
          <el-input v-model="certificateInfoVO.physicianQualificationCertificateNum"></el-input>
        </el-form-item>
        <el-form-item label="获取时间:" prop="physicianQualificationCertificateTime" v-if="certificateInfoVO.isHavePhysicianQualificationCertificate">
          <el-date-picker v-model="certificateInfoVO.physicianQualificationCertificateTime" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="是否具有执业证书：" prop="isHaveMedicalPracticingCertificate">
          <el-select v-model="certificateInfoVO.isHaveMedicalPracticingCertificate">
            <el-option :value="true" label="是"></el-option>
            <el-option :value="false" label="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执业证书编号:" prop="medicalPracticingCertificateNum" v-if="certificateInfoVO.isHaveMedicalPracticingCertificate">
          <el-input v-model="certificateInfoVO.medicalPracticingCertificateNum"></el-input>
        </el-form-item>
        <el-form-item label="获取时间:" prop="medicalPracticingCertificateTime" v-if="certificateInfoVO.isHaveMedicalPracticingCertificate">
          <el-date-picker v-model="certificateInfoVO.medicalPracticingCertificateTime" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="执业范围" prop="medicalPracticingScope" v-if="certificateInfoVO.isHaveMedicalPracticingCertificate">
          <el-input v-model="certificateInfoVO.medicalPracticingScope"></el-input>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="hover">
      <div class="add-header" slot="header">工作经历
        <el-button v-if="!readonly" icon="el-icon-plus" type="primary" size="small" @click="addWorkHistory">添加</el-button>
      </div>
      <el-table :data="workHistoryInfoVOS">
        <el-table-column label="起止时间" prop="startDate" width="320px">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.startDate }} - {{ scope.row.endDate || "至今" }}</span>
            <template v-else>
              <el-date-picker class="short-item" v-model="scope.row.startDate" value-format="yyyy-MM-dd" placeholder="起始时间"></el-date-picker> -
              <el-date-picker class="short-item" v-model="scope.row.endDate" value-format="yyyy-MM-dd" placeholder="至今" clearable></el-date-picker>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="单位名称" prop="unitName">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.unitName }}</span>
            <el-input v-else v-model="scope.row.unitName"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="所在部门/科室" prop="department">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.department }}</span>
            <el-input v-else v-model="scope.row.department"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="职称" prop="positionalTitles">
          <template v-slot="scope">
            <span v-if="readonly">{{ getDictDataLabel(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES, scope.row.positionalTitles) }}</span>
            <el-select v-else v-model="scope.row.positionalTitles">
              <el-option v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_USER_POSITIONAL_TITLES)"
                         :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="担任行政职务" prop="administrativPosition">
          <template v-slot="scope">
            <span v-if="readonly">{{ scope.row.administrativPosition }}</span>
            <el-input v-else v-model="scope.row.administrativPosition"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="60px" v-if="!readonly">
          <template v-slot="scope">
            <el-link type="danger" @click="workHistoryInfoVOS.splice(scope.row.$index, 1)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">资料上传</div>
      <el-form ref="fileInfoForm" inline label-position="top" :model="fileInfoVO" :rules="fileInfoRules" :disabled="readonly">
        <el-form-item label="身份证国徽面" prop="frontOfIdCard">
          <image-upload v-model="fileInfoVO.frontOfIdCard" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="身份证头像面" prop="reverseOfIdCard">
          <image-upload v-model="fileInfoVO.reverseOfIdCard" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="最高学历证书(应届生上传学信网学历认证)" prop="highestAcademicCertificate">
          <image-upload v-model="fileInfoVO.highestAcademicCertificate" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="最高学位证书(应届生上传学信网学位认证)" prop="highestDegreeCertificate">
          <image-upload v-model="fileInfoVO.highestDegreeCertificate" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="国家医师资格证书" prop="physicianQualificationCertificate">
          <image-upload v-model="fileInfoVO.physicianQualificationCertificate" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item label="执业证书" prop="medicalPracticingCertificate">
          <image-upload v-model="fileInfoVO.medicalPracticingCertificate" :limit="1" :is-show-tip="false"></image-upload>
        </el-form-item>
        <el-form-item style="width: 100%" label="附件上传" prop="files">
          <div class="files-label" slot="label">
            <span>附件上传</span>
            <el-link type="primary" :underline="false" @click="handleShowBrochure">查看附件要求</el-link>
            <el-link
              v-for="file in files"
              class="file-link"
              :key="file.name"
              :underline="false"
              @click="downloadFile(file.url, file.name)"
            >{{ file.name }}</el-link>
          </div>
          <file-upload-info
            v-model="fileInfoVO.files"
            :file-size="100"
            :limit="1"
            :file-type="['pdf']"
          ></file-upload-info>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getResidencyApplicationInfo, getResidencyApplicationInfoByPlanId } from '@/api/recruitment/registration'
import { getPlanHide, getPlanMajorList } from '@/api/recruitment/plan'
import { getAccessToken } from '@/utils/auth'
import ImageUpload from '@/components/ImageUpload'
import FileUploadInfo from "@/components/FileUploadInfo";

export default {
  name: "recruitment-form",
  components: { ImageUpload, FileUploadInfo },
  props: {
    planId: String,
    recruitmentRegistrationId: String,
    readonly: Boolean,
  },
  data() {
    return {
      // 专业列表
      majorList: [],
      // 报名信息
      registrationVO: {
        id: undefined,
        planId: this.planId,
        majorCode: "", // 报名专业
        isObeyAdjustment: false, // 是否服从调剂
        hopeMajorCodes: "", // 希望调剂专业
        recruitmentPersonnelType: "", // 报考人员类别
        workUnit: "", // 工作单位全称
        isPreviousTrained: false, // 既往是否参加过住院医师规范化培训
        previousTrainingUnit: "", // 既往培训单位
        previousTrainingMajor: "", // 既往培训专业
        previousTrainingStartDate: "", // 既往培训起止时间
        previousTrainingEndDate: "",
        previousTrainingDate: [],
      },
      registrationRules: {
        majorCode: [{ required: true, message: "报名专业必选", trigger: "change" }],
        isObeyAdjustment: [{ required: true, message: "是否服从调剂必选", trigger: "change" }],
        hopeMajorCodes: [{ required: true, message: "希望调剂专业必选", trigger: "change" }],
        recruitmentPersonnelType: [{ required: true, message: "报考人员类别必选", trigger: "change" }],
        workUnit: [{ required: true, message: "工作单位全称必填", trigger: "blur" }],
        isPreviousTrained: [{ required: true, message: "既往是否参加过住院医师规范化培训必选", trigger: "change" }],
        previousTrainingUnit: [{ required: true, message: "既往培训单位必填", trigger: "blur" }],
        previousTrainingMajor: [{ required: true, message: "既往培训专业必填", trigger: "blur" }],
        previousTrainingStartDate: [{ required: true, message: "既往培训起止时间必选", trigger: "chang" }],
      },
      // 基本信息
      baseinfoVO: {
        age: null,
        bank: "",
        bankCardNumber: "",
        relatedProfessionalBackground: "",
        birthday: "",
        currentAddress: "",
        email: "",
        emergencyContact: "",
        emergencyRelationship: "",
        emergencyTelephone: "",
        fixedPhoneAreaCode: null,
        fixedPhoneNumber: null,
        foreignLanguageLevel: "",
        healthCondition: "",
        highestAcademic: "",
        highestAcademicTime: "",
        highestAcademicSchool: "",
        highestDegree: "",
        highestDegreeSchool: "",
        idNo: "",
        idType: "",
        mobilePhone: "",
        name: "",
        nation: "",
        nativePlace: "",
        politicalOutlook: "",
        qq: "",
        recruitmentRegistrationId: null,
        sex: null,
        weixin: ""
      },
      baseInfoRules: {
        name: [{ required: true, message: "姓名必填", trigger: "blur" }],
        sex: [{ required: true, message: "性别必选", trigger: "change" }],
        nation: [{ required: true, message: "民族必选", trigger: "change" }],
        foreignLanguageLevel: [{ required: true, message: "外语水平必选", trigger: "change" }],
        idType: [{ required: true, message: "证件类型必选", trigger: "change" }],
        idNo: [{ required: true, message: "证件号码必填", trigger: "blur" }],
        birthday: [{ required: true, message: "出生日期必选", trigger: "change" }],
        age: [{ required: true, message: "年龄必填", trigger: "blur" }],
        mobilePhone: [{ required: true, message: "移动电话必填", trigger: "blur" }, { type: "regexp", pattern: /^\d{11}$/, message: "请输入正确的移动电话号码", trigger: "blur" }],
        email: [{ required: true, message: "邮箱必填", trigger: "blur" }, { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }],
        weixin: [{ required: true, message: "微信必填", trigger: "blur" }],
        nativePlace: [{ required: true, message: "籍贯必选", trigger: "change" }],
        highestAcademic: [{ required: true, message: "最高学历必选", trigger: "change" }],
        highestAcademicTime: [{ required: true, message: "最高学历获得时间必选", trigger: "change" }],
        highestAcademicSchool: [{ required: true, message: "最高学历毕业院校必填", trigger: "blur" }],
        highestDegree: [{ required: true, message: "最高学位必选", trigger: "change" }],
        highestDegreeSchool: [{ required: true, message: "最高学位毕业院校必填", trigger: "blur" }],
        currentAddress: [{ required: true, message: "现住地址必填", trigger: "blur" }],
        politicalOutlook: [{ required: true, message: "政治面貌必填", trigger: "blur" }],
        healthCondition: [{ required: true, message: "健康状况必选", trigger: "change" }],
        emergencyContact: [{ required: true, message: "紧急联系人必填", trigger: "change" }],
        emergencyRelationship: [{ required: true, message: "与本人关系必填", trigger: "blur" }],
        emergencyTelephone: [{ required: true, message: "联系电话必填", trigger: "blur" }],
        // 固定电话验证
        fixedPhoneAreaCode: [{
          validator: (rule, value, callback) => {
            if (value) {
              if (!/^\d{2,4}$/.test(value)) {
                callback(new Error('区号格式错误（2-4位数字）'));
              } else {
                if (!/^\d{7,8}$/.test(this.baseinfoVO.fixedPhoneNumber)) {
                  callback(new Error('电话号码格式错误（7-8位数字）'));
                } else {
                  callback();
                }
              }
            } else {
              if (this.baseinfoVO.fixedPhoneNumber) {
                callback(new Error('区号格式错误（2-4位数字）'));
              } else {
                callback();
              }
            }
          },
          trigger: "blur"
        }],
      },
      // 学历信息
      eduInfoVOS: [],
      // 资格证书信息
      certificateInfoVO: {
        isHavePhysicianQualificationCertificate: false,
        isPassPhysicianQualificationCertificate: false,
        physicianQualificationCertificateNum: "",
        physicianQualificationCertificateTime: "",
        isHaveMedicalPracticingCertificate: false,
        medicalPracticingCertificateNum: "",
        medicalPracticingCertificateTime: "",
        medicalPracticingScope: "",
      },
      certificateInfoRules: {
        isHavePhysicianQualificationCertificate: [{ required: true, message: "是否具有专业技术资格证书必选", trigger: "change" }],
        isPassPhysicianQualificationCertificate: [{ required: true, message: "是否通过专业资格证书考试", trigger: "change" }],
        physicianQualificationCertificateNum: [{ required: true, message: "专业技术资格证书编号必填", trigger: "blur" }],
        physicianQualificationCertificateTime: [{ required: true, message: "专业技术资格证书获取时间必选", trigger: "change" }],
        isHaveMedicalPracticingCertificate: [{ required: true, message: "是否具有执业证书必选", trigger: "change" }],
        medicalPracticingCertificateNum: [{ required: true, message: "执业证书编号必填", trigger: "blur" }],
        medicalPracticingCertificateTime: [{ required: true, message: "执业证书获取时间必选", trigger: "change" }],
        medicalPracticingScope: [{ required: true, message: "执业范围必填", trigger: "blur" }],
      },
      // 工作经历
      workHistoryInfoVOS: [],
      // 资料上传
      fileInfoVO: {
        frontOfIdCard: "",
        highestAcademicCertificate: "",
        highestDegreeCertificate: "",
        medicalPracticingCertificate: "",
        physicianQualificationCertificate: "",
        reverseOfIdCard: "",
        files: "",
      },
      fileInfoRules: {
        frontOfIdCard: [{ required: true, message: "身份证国徽面必填", trigger: "change" }],
        reverseOfIdCard: [{ required: true, message: "身份证头像面必填", trigger: "change" }],
        highestAcademicCertificate: [{ required: true, message: "最高学历证书必填", trigger: "change" }],
        files: [{ required: true, message: "附件必填", trigger: "change" }],
      },
      planInfo: null,
    }
  },
  computed: {
    files() {
      let files = this.planInfo?.files;
      if (!files) return [];
      try {
        files = JSON.parse(files);
      } catch (e) {}
      return files;
    }
  },
  methods: {
    getAccessToken,
    getEduInfo() {
      const queryPromise = this.recruitmentRegistrationId ?
        getResidencyApplicationInfo(this.recruitmentRegistrationId) :
        getResidencyApplicationInfoByPlanId(this.planId);

      queryPromise.then(res => {
        if (res.data.registrationVO) {
          this.registrationVO = res.data.registrationVO;
          this.registrationVO.planId = this.planId;
        }
        if (res.data.baseinfoVO) this.baseinfoVO = res.data.baseinfoVO;
        if (res.data.eduInfoVOS) this.eduInfoVOS = res.data.eduInfoVOS;
        if (res.data.certificateInfoVO) this.certificateInfoVO = res.data.certificateInfoVO;
        if (res.data.workHistoryInfoVOS) this.workHistoryInfoVOS = res.data.workHistoryInfoVOS;
        if (res.data.unitInfoVO) this.unitInfoVO = res.data.unitInfoVO;
        if (res.data.otherInfoVO) this.otherInfoVO = res.data.otherInfoVO;
        if (res.data.fileInfoVO) {
          this.fileInfoVO = res.data.fileInfoVO;
          try {
            const files = JSON.parse(this.fileInfoVO.files);
            this.fileInfoVO.files = this.readonly ? files : files.slice(0, 1);
          } catch(e) {
            this.fileInfoVO.files = undefined;
          }
        }
        this.$nextTick(() => {
          this.$refs.registrationForm.clearValidate()
          this.$refs.baseInfoForm.clearValidate()
        });
      });
    },
    addEduInfo() {
      this.eduInfoVOS.push({
        degree: "",
        education: "",
        endDate: "",
        major: "",
        recruitmentRegistrationId: 0,
        schoolName: "",
        startDate: "",
      });
    },
    addWorkHistory() {
      this.workHistoryInfoVOS.push({
        administrativPosition: "",
        department: "",
        endDate: "",
        positionalTitles: "",
        recruitmentRegistrationId: 0,
        startDate: "",
        unitName: "",
      });
    },
    validForm() {
      const { registrationVO, baseinfoVO, eduInfoVOS, certificateInfoVO, workHistoryInfoVOS, fileInfoVO } = this;
      const registrationPromise = this.$refs.registrationForm.validate();
      const baseInfoPromise = this.$refs.baseInfoForm.validate();
      const certificateInfoPromise = this.$refs.certificateInfoForm.validate();
      const eduInfoPromise = new Promise((resolve, reject) => {
        if (eduInfoVOS.length > 0) {
          resolve();
        } else {
          reject();
        }
      });
      const { files, ..._fileInfoVo } = fileInfoVO;
      _fileInfoVo.files = files && files.length > 0 ? JSON.stringify(files) : "";
      return Promise.all([registrationPromise, baseInfoPromise, certificateInfoPromise, eduInfoPromise]).then(() => ({
        registrationVO, baseinfoVO, eduInfoVOS, certificateInfoVO, workHistoryInfoVOS, fileInfoVO: _fileInfoVo
      })).catch(() => {
        this.$message.warning("请检查数据是否都填写正确～");
        return Promise.reject();
      });
    },
    handleIdNoChange(value) {
      const idCardNoPattern = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}[\dXx]$/;

      if (this.baseinfoVO.idType === "1" && idCardNoPattern.test(value)) {
        const birthYear = value.slice(6, 10);
        const birthMonth = value.slice(10, 12);
        const birthDate = value.slice(12, 14);
        const now = new Date();
        let age = now.getFullYear() - birthYear - 1;
        if ((now.getMonth() + 1) > +birthMonth || ((now.getMonth() + 1) === +birthMonth && now.getDate() > +birthDate)) {
          age += 1;
        }
        this.baseinfoVO.birthday = `${birthYear}-${birthMonth}-${birthDate}`;
        this.baseinfoVO.age = age;
      }
    },
    handleShowBrochure() {
      this.$alert(this.planInfo?.brochure, '招生简章', {
        dangerouslyUseHTMLString: true,
        customClass: 'recruitment-brochure-alert'
      });
    },
    downloadFile(url, fileName) {
      // 兼容ie
      if ("ActiveXObject" in window) {
        // ie浏览器没有解决
        // 网上说navigator.msSaveBlob(blob, filename);如果这句话管用，那下面代码不需要了 这个属性那是blob是哪来的，没有测试
        this.createAndRemove(url, fileName); // 实现了ie浏览我的下载，但是没有实现修改文件名的需求
      } else {
        const x = new XMLHttpRequest();
        x.open("GET", url, true);
        x.responseType = "blob";
        x.onload = () => {
          const url = window.URL.createObjectURL(x.response);
          this.handleDownload(url, fileName);
        };
        x.send();
      }
    },
    handleDownload(url, fileName) {
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.download = fileName;
      link.click();
      document.body.removeChild(link);
    },
  },
  created() {
    getPlanMajorList(this.planId).then(res => {
      this.majorList = res.data;
    });
    getPlanHide(this.planId).then(res => {
      this.planInfo = res.data;
    });
    this.getEduInfo();
  },
}
</script>

<style lang="scss" scoped>
.info-fill ::v-deep .el-card {
  margin-bottom: 10px;
}

.info-fill ::v-deep .el-card__header {
  font-size: 15px;
  font-weight: 600;
}

.info-fill ::v-deep .el-form-item__label {
  padding-bottom: 0!important;
  white-space: nowrap;
}

.info-fill ::v-deep .el-form-item {
  width: 25%;
  margin-right: 0;
  padding-right: 20px;
  margin-bottom: 10px;
}

.info-fill .avatar-form-item {
  float: right;
  height: 280px;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  >p {
    line-height: 1.8;
  }
}

.avatar-upload ::v-deep .el-upload {
  height: 207px!important;
  background: #eee url(~@/assets/images/avatar.png) no-repeat center 56px;
  background-size: auto 80%;
  .el-icon-plus {
    display: none;
  }
}

.avatar-upload ::v-deep .el-upload-list__item {
  height: 207px!important;
}

.info-fill .full-item {
  width: 100%!important;
}

.info-fill .short-item {
  width: 142px!important;
}

.add-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remark-input {
  position: absolute;
  ::v-deep .el-textarea__inner {
    min-height: 36px!important;
  }
}

.files-label {
  display: inline-flex;
  align-items: center;
  padding-top: 20px;
  ::v-deep .el-link--primary {
    margin-left: 20px;
  }
  .file-link {
    margin-left: 5px;
  }
}

.required-sign {
  color: #ff4949;
  margin-right: 4px;
}
</style>

<style lang="scss">
.recruitment-brochure-alert {
  width: 800px!important;
}
</style>
