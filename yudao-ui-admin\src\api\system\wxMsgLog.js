import request from '@/utils/request'

// 创建用户微信消息日志
export function createWxMsgLog(data) {
  return request({
    url: '/system/wx-msg-log/create',
    method: 'post',
    data: data
  })
}

// 更新用户微信消息日志
export function updateWxMsgLog(data) {
  return request({
    url: '/system/wx-msg-log/update',
    method: 'put',
    data: data
  })
}

// 删除用户微信消息日志
export function deleteWxMsgLog(id) {
  return request({
    url: '/system/wx-msg-log/delete?id=' + id,
    method: 'delete'
  })
}

// 获得用户微信消息日志
export function getWxMsgLog(id) {
  return request({
    url: '/system/wx-msg-log/get?id=' + id,
    method: 'get'
  })
}

// 获得用户微信消息日志分页
export function getWxMsgLogPage(query) {
  return request({
    url: '/system/wx-msg-log/page',
    method: 'get',
    params: query
  })
}

// 导出用户微信消息日志 Excel
export function exportWxMsgLogExcel(query) {
  return request({
    url: '/system/wx-msg-log/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
