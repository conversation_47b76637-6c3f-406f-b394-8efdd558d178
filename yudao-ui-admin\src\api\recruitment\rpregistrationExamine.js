import request from "@/utils/request";

// 获得招录住培信息审核分页
export function getRpregistrationExamPage(query) {
  return request({
    url: "/recruitment/rpregistration-examine/page",
    method: "get",
    params: query,
  });
}

// 审核
export function auditRpregistrationExam(data) {
  return request({
    url: "/recruitment/rpregistration-examine/examine",
    method: "put",
    data: data,
  });
}

// 审核
export function updateRpregistrationExam(data) {
  return request({
    url: "/recruitment/rpregistration-examine/update-major",
    method: "put",
    data: data,
  });
}

// 获得意向招生专业列表
export function getPlanMajorList(recruitmentRegistrationId) {
  return request({
    url: "/recruitment/resident-physician-registration/list-major",
    method: "get",
    params: { recruitmentRegistrationId },
  });
}

// 导出
export function exportRpregistrationExamineExcel(query) {
  return request({
    url: "/recruitment/rpregistration-examine/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
