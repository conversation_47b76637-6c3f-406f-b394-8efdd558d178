<template>
  <div>
    <h3 class="sheet-name">出科考核评分表</h3>

    <el-form class="form-info" style="margin-bottom: 20px" inline>
      <el-form-item style="margin-right: 30px" label="姓名：">{{ scoreForm.nickName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="学员类型：">{{ scoreForm.studentTypeName }}</el-form-item>
      <el-form-item style="margin-right: 30px" label="专业：">{{ scoreForm.majorName }}</el-form-item>
      <el-form-item label="轮转科室：">{{ scoreForm.rotationDepartmentName }}</el-form-item>
      <el-form-item label="轮转时间：">{{ scoreForm.rotationTime }}</el-form-item>
    </el-form>

    <table class="score-table">
      <tr>
        <td>轮转数据登记情况</td>
        <td colspan="3" style="text-align: left">
          <div
            class="flex-row direction-column"
            v-for="item in (form.manualTotal && form.manualTotal.manualItemSummaryRespVOS || [])"
            :key="item.rotaionItem">
            <strong style="margin-right: 15px">{{ getDictDataLabel(DICT_TYPE.ROTAION_ITEM, item.rotaionItem) }}</strong>
            <div>
              要求数：<span style="margin-right: 10px;">{{ item.cases }}</span>
              完成数：<span style="margin-right: 10px">{{ item.examineSuccessCases }}</span>
              完成比例：{{ Math.min(Math.round(item.examineSuccessCases * 100 / (item.cases || 1)), 100) }}%
            </div>
          </div>
          <div v-if="(form.manualTotal && form.manualTotal.manualItemSummaryRespVOS || []).length === 0">暂无数据</div>
        </td>
      </tr>
      <tr>
        <td>活动参加情况</td>
        <td colspan="3" style="text-align: left">
          <div
            class="flex-row"
            v-for="(item, index) in (form.teachingActiveStatisticsRespVOS || []).filter(item => item.activeItem === '2')"
            :key="index">
            <div>
              <strong>{{ getDictDataLabel(DICT_TYPE.ACTIVE_ITEM, item.activeItem) }}</strong>
              （要求参加：{{ item.cases }}次，总参加：{{ item.joinCases }}次）
            </div>
            <div style="padding-left: 15px;">
              <div
                class="flex-row"
                v-for="subItem in item.teachingActiveSubStatisticses"
                :key="subItem.activeType">
                {{ getDictDataLabel(DICT_TYPE.ROTATION_ACTIVE_TYPE, subItem.activeType) }}{{ subItem.joinCases }}次
              </div>
            </div>
          </div>
          <div v-if="(form.teachingActiveStatisticsRespVOS || []).filter(item => item.activeItem === '2').length === 0">暂无数据</div>
        </td>
      </tr>
      <tr>
        <td rowspan="2">医德医风（10分）</td>
        <td>
          医疗作风、廉洁行医 <br/>
          服务态度、医惠关系
        </td>
        <td colspan="2">
          <el-input-number
            class="score-input"
            v-model="form.epr"
            :min="0"
            :max="5"
            :controls="false"
            :disabled="check"
            @change="handleChange"
          ></el-input-number>
        </td>
      </tr>
      <tr>
        <td>
          工作负责、主动认真 <br/>
          团结协作、遵守制度
        </td>
        <td colspan="2">
          <el-input-number
            class="score-input"
            v-model="form.wec"
            :min="0"
            :max="5"
            :controls="false"
            :disabled="check"
            @change="handleChange"
          ></el-input-number>
        </td>
      </tr>
      <tr>
        <td>出勤状况（5分）</td>
        <td>
          出勤 <span class="record-value">{{form.attendanceDays}}</span> 天；
          休息 <span class="record-value">{{form.attendanceRestDays}}</span> 天 <br/>
          迟到早退 <span class="record-value">
          {{form.attendanceLateDays === undefined ? '' : form.attendanceLateDays + form.attendanceLeaveEarlyDays}}
          </span> 天；
          请假 <span class="record-value">{{form.attendanceLeaveDays}}</span> 天 <br/>
        </td>
        <td colspan="2">
          <el-input-number
            class="score-input"
            v-model="form.attendanceScore"
            :min="0"
            :max="5"
            :controls="false"
            :disabled="check"
            @change="handleChange"
          ></el-input-number>
        </td>
      </tr>
      <tr>
        <td>出科考核</td>
        <td>理论考试（50分）</td>
        <td>实际分数 <span class="record-value">{{ form.syncExamResultsActualScore }}</span>*0.5</td>
        <td style="width: 100px">{{ form.syncExamResultsScore || "未参加" }}</td>
      </tr>
      <tr>
        <td>出科考核</td>
        <td>技能考核（35分）</td>
        <td>实际分数 <span class="record-value">{{ form.graduationAssessmentActualScore }}</span>*0.35</td>
        <td style="width: 100px">{{ form.graduationAssessmentScore || "未参加" }}</td>
      </tr>
      <tr>
        <td colspan="2">考核综合成绩（100分）</td>
        <td colspan="2">{{ form.comprehensiveScore }}</td>
      </tr>
<!--      <tr>
        <td colspan="2">所在科室考核小组总体评价</td>
        <td colspan="2">
          <el-radio-group>
            <el-radio label="合格"></el-radio>
            <el-radio label="不合格"></el-radio>
          </el-radio-group>
        </td>
      </tr>-->
    </table>
  </div>
</template>

<script>
export default {
  name: 'sheet-general',
  components: {},
  props: {
    scoreForm: Object,
    check: Boolean,
  },
  data() {
    return {
      form: {}
    }
  },
  methods: {
    formValidate() {
      const isUndefinedOrNull = (val) => val === undefined || val === null;
      if (
        isUndefinedOrNull(this.form.epr) ||
        isUndefinedOrNull(this.form.wec) ||
        isUndefinedOrNull(this.form.attendanceScore)
      ) {
        this.$message.warning("请全部完成评分");
        return false;
      }
      return true;
    },
    handleChange() {
      this.form.comprehensiveScore = +((+this.form.epr || 0) +
        (+this.form.wec || 0) +
        (+this.form.attendanceScore || 0) +
        (+this.form.syncExamResultsScore || 0) +
        (+this.form.graduationAssessmentScore || 0)).toFixed(1);
    },
  },
  created() {
    this.form = this.scoreForm;
    this.handleChange();
  },
}
</script>

<style lang="scss" scoped>
.sheet-name {
  font-size: 15px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 10px;
}

.form-info {
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}

.score-table {
  line-height: 1.5;
  border-collapse: collapse;
  width: 100%;

  th, td {
    border: 1px solid #DCDFE6;
    text-align: center;
    padding: 7px 5px;
  }
}

.required-tag {
  color: red;
  padding-right: 2px;
}

.score-input {
  width: 80px;
}

.short-input-number {
  width: 50px;
  ::v-deep .el-input__inner {
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #eee;
  }
}

.record-value {
  display: inline-block;
  min-width: 30px;
  padding: 5px;
  border-bottom: 1px solid #eee;
}

.flex-row {
  display: flex;
  margin-bottom: 4px;
  align-items: center;
  strong {
    flex-shrink: 0;
  }
  ::v-deep .el-radio {
    margin-bottom: 4px;
  }
  ::v-deep .el-radio__label {
    font-size: 1rem;
  }
  &.direction-column {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
