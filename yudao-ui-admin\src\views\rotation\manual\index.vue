<template>
  <div class="app-container">
    <ul class="schedule-list" :style="{ width: `${count * 400 - 50}px` }">
      <li
        v-for="item in dealList"
        :style="{ float: item.float, marginRight: item.marginRight }"
      >
        <div
          :class="[
            'schedule-wrapper',
            `is-status-${item.rotationStatus}`,
            { hover: !['0', '4'].includes(item.rotationStatus) },
          ]"
          @click="handleItemClick(item)"
        >
          <h2>{{ item.rotationDepartmentName }}</h2>
          <div class="schedule-info">
            <div>带教老师：{{ item.tutor }}</div>
            <div>教学主任：{{ item.teachingDirector }}</div>
            <div>
              轮转时间：{{ item.rotationTime
              }}<dict-tag
                :type="DICT_TYPE.ROTATION_CYCLE"
                :value="item.rotationCycle"
              />
            </div>
            <div>理论成绩：{{ item.theoreticalAchievements }}分</div>
            <div>
              轮转次数：{{ item.rotationIndex }}/{{ item.rotationSize }}
            </div>
          </div>
          <el-progress
            :text-inside="true"
            :stroke-width="16"
            :percentage="Math.round(item.rateOfProgress * 100)"
          ></el-progress>
          <p>{{ item.rotationBeginTime }} 至 {{ item.rotationEndTime }}</p>
          <span
            :class="['schedule-status', `is-status-${item.rotationStatus}`]"
          >
            <dict-tag
              :type="DICT_TYPE.ROTATION_STATUS"
              :value="item.rotationStatus"
            />
          </span>
        </div>
        <i
          v-if="!item.isLast"
          :class="[item.icon, item.position, `status-${item.rotationStatus}`]"
        ></i>
      </li>
    </ul>
    <div class="text-center pb-5" v-if="dealList.length > 0">
      <el-button type="primary" plain size="small" :loading="exportLoading" @click="exportManual">导出轮转手册</el-button>
    </div>
    <el-empty description="暂无轮转计划安排" v-if="!dealList.length"></el-empty>
  </div>
</template>

<script>
import {
  getRotationTeachingSecretary,
  getUserManual,
  exportManualPdf,
} from "@/api/rotation/manual";

export default {
  name: "Manual",
  data() {
    return {
      list: [],
      count: 3,
      exportLoading: false,
    };
  },
  computed: {
    // 按左到右，右到左重新排序
    dealList() {
      const result = [];
      this.list.forEach((item, index) => {
        const odd = Math.floor(index / this.count) % 2 === 0; // 奇数行
        const isLast = index === this.list.length - 1; // 是否最后一个
        const isEnd = (index + 1) % this.count === 0; // 是否奇数行最后一个
        const isFirst = (index + 1) % this.count === 1; // 是否偶数行第一个
        if (odd) {
          result.push({
            ...item,
            icon: isEnd ? "arrow-bottom" : "arrow-right",
            float: "left",
            position: isEnd ? "bottom" : "right",
            marginRight: isEnd ? "0px" : "80px",
            isLast,
          });
        } else {
          result.push({
            ...item,
            icon: isEnd ? "arrow-bottom" : "arrow-back",
            float: "right",
            position: isEnd ? "bottom" : "left",
            marginRight: isFirst ? "0px" : "80px",
            isLast,
          });
        }
      });
      return result;
    },
  },
  methods: {
    handleItemClick(item) {
      if (["0", "4"].includes(item.rotationStatus)) {
        getRotationTeachingSecretary(item.scheduleDetailsId).then((res) => {
          this.$message.info(`请联系科室教学秘书${res.data}办理入科`);
        });
        return;
      }
      this.$router.push({
        path: "manual-item",
        query: { id: item.scheduleDetailsId },
      });
    },
    resizeMethod() {
      this.count = Math.floor((document.body.clientWidth - 200) / 400);
    },
    exportManual() {
      this.exportLoading = true;
      exportManualPdf().then(response => {
        this.$download.pdf(response, '轮转手册.pdf');
        this.exportLoading = false;
      }).catch(() => this.exportLoading = false);
    },
  },
  created() {
    getUserManual().then((res) => (this.list = res.data));
    this.resizeMethod();
    window.onresize = this.resizeMethod;
  },
};
</script>

<style lang="scss" scoped>
.schedule-list {
  &::after {
    content: "";
    display: block;
    clear: both;
  }

  li {
    float: left;
    width: 320px;
    margin: 0 80px 80px 0;
    list-style-type: none;
    position: relative;
  }

  .schedule-wrapper {
    box-shadow: 0 0 5px #eee;
    border-radius: 5px;
    padding: 20px;
    transition: all 0.5s;
    position: relative;

    &.is-status-0 {
      border: 2px solid #cbe9fc;

      & + i {
        background-image: url(~@/assets/images/arrow-blue.png);
      }
    }

    &.is-status-1 {
      border: 2px solid #cbfccb;

      & + i {
        background-image: url(~@/assets/images/arrow.gif);
      }
    }

    &.is-status-2 {
      border: 2px solid #e1e1e1;

      & + i {
        background-image: url(~@/assets/images/arrow-gray.png);
      }
    }

    &.is-status-3 {
      border: 2px solid #fcf2cb;

      & + i {
        background-image: url(~@/assets/images/arrow-gray.png);
      }
    }

    &.is-status-4 {
      border: 2px solid #fcd2cb;

      & + i {
        background-image: url(~@/assets/images/arrow-gray.png);
      }
    }

    &.hover:hover {
      cursor: pointer;
      transform: translateY(-8px);
    }
  }

  h2 {
    margin: 0 0 16px 0;
  }

  .schedule-info {
    margin-bottom: 10px;

    div {
      float: left;
      width: 50%;
      font-size: 14px;
      margin-bottom: 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &::after {
      content: "";
      display: block;
      clear: both;
    }
  }

  p {
    margin-bottom: 0;
    font-size: 14px;
  }

  .schedule-status {
    position: absolute;
    top: -1px;
    right: -1px;
    border-top-right-radius: 5px;
    background: #e1e1e1;
    color: #333;
    padding: 4px 8px;

    &.is-status-0 {
      // 待入科
      background: #cbe9fc;
      color: #00567c;
    }

    &.is-status-1 {
      // 轮转中
      background: #cbfccb;
      color: #018c01;
    }

    &.is-status-2 {
      // 已出科
      background: #e1e1e1;
      color: #333;
    }

    &.is-status-3 {
      // 逾期未出科
      background: #fcf2cb;
      color: #7c6700;
    }

    &.is-status-4 {
      // 逾期未入科
      background: #fcd2cb;
      color: #7c0e00;
    }
  }

  i {
    position: absolute;
    top: 60px;
    right: -75px;
    color: #555;
    width: 70px;
    height: 70px;
    background-position: center;
    background-size: 70px;

    &.bottom {
      top: auto;
      bottom: -75px;
      left: 115px;
      transform: rotate(90deg);
    }

    &.left {
      right: auto;
      left: -75px;
      transform: rotate(180deg);
    }
  }
}
</style>
