import request from '@/utils/request'

// 获得入科教育文档分页
export function getDepartmentPage(query) {
  return request({
    url: '/system/department/page',
    method: 'get',
    params: query
  })
}

// 保存入科教育文档
export function saveEnrollmentEduDoc(data) {
  return request({
    url: '/rotation/enrollment-edu-doc/save',
    method: 'post',
    data: data
  })
}

// 创建入科教育文档
export function createEnrollmentEduDoc(data) {
  return request({
    url: '/rotation/enrollment-edu-doc/create',
    method: 'post',
    data: data
  })
}

// 更新入科教育文档
export function updateEnrollmentEduDoc(data) {
  return request({
    url: '/rotation/enrollment-edu-doc/update',
    method: 'put',
    data: data
  })
}

// 删除入科教育文档
export function deleteEnrollmentEduDoc(id) {
  return request({
    url: '/rotation/enrollment-edu-doc/delete?id=' + id,
    method: 'delete'
  })
}

// 获得入科教育文档
export function getEnrollmentEduDoc(id) {
  return request({
    url: '/rotation/enrollment-edu-doc/get?id=' + id,
    method: 'get'
  })
}

// 获得入科教育文档分页
export function getEnrollmentEduDocPage(query) {
  return request({
    url: '/rotation/enrollment-edu-doc/page',
    method: 'get',
    params: query,
    headers: {'component': 'rotation/enrollmentEduDoc/index'}
  })
}

// 导出入科教育文档 Excel
export function exportEnrollmentEduDocExcel(query) {
  return request({
    url: '/rotation/enrollment-edu-doc/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
