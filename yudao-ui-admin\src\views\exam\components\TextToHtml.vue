<template>
  <div class="text-to-html">
    <template v-for="(item, index) in list">
      <span v-if="item.type === 'text'" :key="index">{{ item.value }}</span>
      <el-image
        v-if="item.type === 'image'"
        class="view-pic"
        title="查看图片"
        :key="index"
        :src="picUrl"
        :preview-src-list="[item.value]"
        @click.native.prevent
      ></el-image>
    </template>
    <el-image
      v-if="images && images.length > 0"
      class="view-pic"
      title="查看图片"
      :src="picUrl"
      :preview-src-list="images"
      @click.native.prevent
    ></el-image>
  </div>
</template>

<script>
export default {
  name: 'TextToHtml',
  props: {
    text: {
      type: String,
      default: ""
    },
    images: {
      type: Array,
      default: null,
    },
  },
  data() {
    return {
      picUrl: require("@/assets/images/view-pic.png"),
    }
  },
  computed: {
    list() {
      const reg = /!\[image\]\(.*?\)/g;
      const arr = this.text.split(reg);
      const result = [];
      for (let i = 0; i < arr.length; i++ ) {
        result.push({ type: "text", value: arr[i] });
        if (i !== arr.length - 1) {
          const image = reg.exec(this.text)[0]
          result.push({ type: "image", value: "/" + image.substring(10, image.length - 2) });
        }
      }
      return result;
    }
  },
}
</script>

<style scoped>
.text-to-html {
  display: inline;
}

.view-pic {
  width: 32px;
  height: 32px;
  vertical-align: middle;
}
</style>

<style>
.el-image-viewer__img {
  background-color: #fff;
}
</style>
