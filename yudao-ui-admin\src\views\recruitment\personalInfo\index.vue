<template>
    <div class="app-container">
        <recruitment-form 
            ref="recruitmentForm" 
            :plan-id="$route.query.planId" 
            :recruitment-registration-id="$route.query.recruitmentRegistrationId"
            :readonly="true"
        ></recruitment-form>
    </div>
  </template>
  
  <script>
  import RecruitmentForm from "@/views/recruitment/fill/recruitment-form";
  
  export default {
    name: 'personalInfo',
    components: { RecruitmentForm },
    data() {
      return {

      }
    },
    methods: {
        
    },
    created() {
      
    }
  }
  </script>
  
  <style lang="scss" scoped>
  
  </style>
  