import jsPDF from "jspdf";
import html2canvas from "html2canvas";

// 改进后的isSplit函数，考虑了边界条件
/**
 * 检查是否需要在指定索引处的节点进行分页分割
 * 此函数用于确定在渲染到 A4 纸时，是否需要在当前节点之后插入分页符
 * 如果当前节点的底部位于页面高度内，且下一个节点的顶部超出页面高度，则需要分页
 * @param {NodeList} nodes - 要检查的 DOM 节点列表
 * @param {number} index - 当前节点在节点列表中的索引
 * @param {number} pageHeight - A4 纸的页面高度（像素）
 * @returns {boolean} - 如果需要分页，返回 true；否则返回 false
 */
const isSplit = (nodes, index, pageHeight) => {
  // 计算当前这块dom是否跨越了a4大小，以此分割
  if (
    nodes[index].offsetTop + nodes[index].offsetHeight < pageHeight &&
    (index + 1 === nodes.length ||
      nodes[index + 1].offsetTop + nodes[index + 1].offsetHeight > pageHeight)
  ) {
    return true;
  }
  return false;
};

// 改进后的pdf函数，添加了错误处理和更精确的分页逻辑
const handlePdf = (pdfDom, title, callback) => {
  console.log("pdf enter=");
  // 避免出现浏览器滚动条导致的内容不全处理
  document.body.scrollTop = document.documentElement.scrollTop = 0;
  //div内部滚动导致内容不全处理
  // document.getElementById('app').style.height = 'auto';
  setTimeout(() => {
    html2canvas(document.getElementById(pdfDom), {
      allowTaint: true,
      useCORS: true,
      scale: 1.5,
      dpi: 200,
    })
      .then((canvas) => {
        var contentWidth = canvas.width;
        var contentHeight = canvas.height;

        //一页pdf显示html页面生成的canvas高度;
        var pageHeight = (contentWidth / (592.28 - 60)) * 841.89;
        //未生成pdf的html页面高度
        var leftHeight = contentHeight;
        //页面偏移
        var position = 30;
        //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
        var imgWidth = 595.28 - 60;
        var imgHeight = ((592.28 - 60) / contentWidth) * contentHeight;

        var pageData = canvas.toDataURL("image/jpeg", 1.0);

        var pdf = new jsPDF("", "pt", "a4");

        //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
        //当内容未超过pdf一页显示的范围，无需分页
        if (leftHeight < pageHeight) {
          pdf.addImage(pageData, "JPEG", 30, 30, imgWidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            pdf.addImage(pageData, "JPEG", 30, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;
            //避免添加空白页
            if (leftHeight > 0) {
              pdf.addPage();
            }
          }
        }
        pdf.save(`${title}.pdf`);
        callback && callback();
      })
      .catch((err) => {
        console.log("html2canvas err=", err);
        // 添加错误处理逻辑，例如提示用户
        alert("PDF导出失败，请重试");
      });
  }, 5000);
};

export function exportPDF(pdfDom, title, callback) {
  const A4_WIDTH = 592.28 - 60; //-60减去边距;
  const A4_HEIGHT = 841.89 - 0; //-30减去边距;
  // $myLoading 自定义等待动画组件，实现导出事件的异步等待交互
  // dom的id。
  let target = document.getElementById(pdfDom);
  let pageHeight = (target.scrollWidth / A4_WIDTH) * A4_HEIGHT;
  // 获取分割dom，此处为class类名为item的dom
  let lableListID = target.getElementsByClassName("export-item");
  // let lableListID = document.getElementsByClassName('item');
  // 进行分割操作，当dom内容已超出a4的高度，则将该dom前插入一个空dom，把他挤下去，分割
  for (let i = 0; i < lableListID.length; i++) {
    console.log("export-item index = ", i);
    // 计算当前标签在A4纸上所需的页数 // 当前标签的顶部偏移量加上标签的高度，除以A4纸的可打印高度
    let multiple = Math.ceil(
      (lableListID[i].offsetTop + lableListID[i].offsetHeight) / pageHeight
    );
    if (isSplit(lableListID, i, multiple * pageHeight)) {
      let divParent = lableListID[i].parentNode; // 获取该div的父节点
      let newNode = document.createElement("div");
      newNode.className = "emptyDiv";
      newNode.style.background = "#fff";
      let _H =
        multiple * pageHeight -
        (lableListID[i].offsetTop + lableListID[i].offsetHeight);
      newNode.style.height = _H + 30 + "px";
      newNode.style.width = "100%";
      let next = lableListID[i].nextSibling; // 获取div的下一个兄弟节点
      // 判断兄弟节点是否存在
      // console.log(next);
      if (next) {
        // 存在则将新节点插入到div的下一个兄弟节点之前，即div之后
        divParent.insertBefore(newNode, next);
      } else {
        // 不存在则直接添加到最后,appendChild默认添加到divParent的最后
        divParent.appendChild(newNode);
      }
    }
  }
  handlePdf(pdfDom, title, callback);
}
