<template>
  <div class="app-container">
    <el-radio-group class="mb10" size="medium" v-model="queryParams.appraise360Type" @change="getList">
      <el-radio-button v-for="item in appraise360TypeList" :label="item.value">{{ item.label }}</el-radio-button>
    </el-radio-group>

    <el-tabs class="mb10" v-model="queryParams.deptType" @tab-click="getList">
      <el-tab-pane
        v-for="item in deptTypeList"
        :label="item.label"
        :name="item.value"
      ></el-tab-pane>
    </el-tabs>

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="评价指标" prop="appraiseKpi">
        <el-input v-model="queryParams.appraiseKpi" placeholder="请输入评价指标" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select v-model="queryParams.studentType" placeholder="请选择学员类型" size="small">
          <el-option v-for="dict in studentTypeList" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:appraise360:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:appraise360:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="序号" type="index" align="center"></el-table-column>
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_STUDENT_TYPE" :value="scope.row.studentType" />
        </template>
      </el-table-column>
      <el-table-column label="评价指标" align="center" prop="appraiseKpi" />
      <el-table-column label="评价维度" align="center" prop="appraise360Dimension" >
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.APPRAISE_360_DIMENSION" :value="scope.row.appraise360Dimension" />
        </template>
      </el-table-column>
      <el-table-column label="指标分值" align="center" prop="score" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['rotation:appraise360:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['rotation:appraise360:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="评价指标" prop="appraiseKpi">
          <el-input v-model="form.appraiseKpi" placeholder="请输入评价指标" />
        </el-form-item>
        <el-form-item label="评价维度" prop="appraise360Dimension">
          <el-select v-model="form.appraise360Dimension" placeholder="请选择学员类型" style="width: 100%;">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.APPRAISE_360_DIMENSION)"
                       :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="学员类型" prop="studentType">
          <el-select v-model="form.studentType" placeholder="请选择学员类型" style="width: 100%;">
            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
                       :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
        <el-form-item label="指标分值" prop="score">
          <el-input-number v-model="form.score" :min="0" controls-position="right" placeholder="请输入指标分值" style="width: 100%;" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createAppraise360Item, updateAppraise360Item, deleteAppraise360Item, getAppraise360Item, getAppraise360ItemPage, exportAppraise360ItemExcel } from "@/api/rotation/appraise360";

export default {
  name: "Appraise360Item",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 360评价项列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 360评价指标类型
      appraise360TypeList: [],
      // 科室类型
      deptTypeList: [],
      // 学员类型
      studentTypeList: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        appraise360Type: "",
        deptType: "",
        appraiseKpi: null,
        studentType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        studentType: [{ required: true, message: "学员类型不能为空", trigger: "change" }],
        appraiseKpi: [{ required: true, message: "评价指标不能为空", trigger: "blur" }],
        score: [{ required: true, message: "指标分值不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.appraise360TypeList = this.getDictDatas(this.DICT_TYPE.APPRAISE_360_TYPE);
    this.queryParams.appraise360Type = this.appraise360TypeList[0]?.value;
    this.deptTypeList = this.getDictDatas(this.DICT_TYPE.SYSTEM_DEPARTMENT_TYPE);
    this.queryParams.deptType = this.deptTypeList[0]?.value;
    this.studentTypeList = this.getDictDatas(this.DICT_TYPE.SYSTEM_STUDENT_TYPE);
    this.queryParams.studentType = this.studentTypeList[0]?.value;
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getAppraise360ItemPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        appraise360Id: null,
        appraise360Dimension: '',
        studentType: undefined,
        appraiseKpi: undefined,
        score: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.appraise360Type = this.appraise360TypeList[0]?.value;
      this.queryParams.deptType = this.deptTypeList[0]?.value;
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加360评价项";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getAppraise360Item(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改360评价项";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.appraise360Id != null) {
          updateAppraise360Item(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createAppraise360Item({
          appraise360ItemCreateReqVO: this.form,
          appraise360Type: this.queryParams.appraise360Type,
          deptType: this.queryParams.deptType,
          studentType: this.form.studentType,
        }).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除' + row.appraiseKpi + '评价指标?').then(function() {
          return deleteAppraise360Item(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有360评价项数据项?').then(() => {
          this.exportLoading = true;
          return exportAppraise360ItemExcel(params);
        }).then(response => {
          this.$download.excel(response, '360评价项.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    },
  }
};
</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}
</style>
