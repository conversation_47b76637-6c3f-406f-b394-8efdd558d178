<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="发布年度" prop="releaseYear">
        <el-input
          v-model="queryParams.releaseYear"
          placeholder="请输入发布年度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:annual-summary-config:create']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:annual-summary-config:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="编号" align="center" prop="id" /> -->
      <el-table-column label="发布年度" align="center" prop="releaseYear" />
      <el-table-column label="学员类型" align="center" prop="studentType">
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="scope.row.studentType"
          />
        </template>
      </el-table-column>
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column
        label="关联理论考核成绩"
        align="center"
        prop="associateTheoryScore"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.INFRA_BOOLEAN_STRING"
            :value="scope.row.associateTheoryScore"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="关联技能考核成绩"
        align="center"
        prop="associateSkillScore"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.INFRA_BOOLEAN_STRING"
            :value="scope.row.associateSkillScore"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="总结提交开始日期"
        align="center"
        prop="summaryStartDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.summaryStartDate }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="总结提交截止日期"
        align="center"
        prop="summaryEndDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.summaryEndDate }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleViewConfig(scope.row)"
          >
            查看
          </el-button>
          <el-button size="mini" type="text" @click="handleView(scope.row)">
            查看完成情况
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:annual-summary-config:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="发布年度" required prop="releaseYear">
          <el-date-picker
            v-model="form.releaseYear"
            type="year"
            format="yyyy"
            value-format="yyyy"
            placeholder="选择年"
            :disabled="Boolean(form.id)"
          >
          </el-date-picker>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="学员类型" required prop="studentType">
              <el-select
                v-model="form.studentType"
                placeholder="请选择学员类型"
                @change="handleStudentType"
                :disabled="Boolean(form.id)"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.SYSTEM_STUDENT_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="发布年级" required prop="grade">
              <el-select
                v-model="form.grade"
                placeholder="请选择年级"
                filterable
                clearable
                size="small"
                :disabled="Boolean(form.id)"
              >
                <el-option
                  v-for="grade in studentGradeList"
                  :key="grade"
                  :label="grade"
                  :value="grade"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item
              label="关联理论考核成绩"
              required
              prop="associateTheoryScore"
            >
              <el-radio-group
                v-model="form.associateTheoryScore"
                :disabled="Boolean(form.id)"
              >
                <el-radio
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.INFRA_BOOLEAN_STRING
                  )"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="成绩查询范围"
              prop="theoryDate"
              v-if="form.associateTheoryScore === 'true'"
            >
              <el-date-picker
                clearable
                v-model="form.theoryDate"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择成绩查询范围"
                style="width: 100%"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="Boolean(form.id)"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="关联技能考核成绩" prop="associateSkillScore">
              <el-radio-group
                v-model="form.associateSkillScore"
                :disabled="Boolean(form.id)"
              >
                <el-radio
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.INFRA_BOOLEAN_STRING
                  )"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="成绩查询范围"
              prop="skilltDate"
              v-if="form.associateSkillScore === 'true'"
            >
              <el-date-picker
                clearable
                v-model="form.skilltDate"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择成绩查询范围"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                :disabled="Boolean(form.id)"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="总结提交日期" prop="summaryDate">
              <el-date-picker
                clearable
                v-model="form.summaryDate"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择总结提交开始日期"
                start-placeholder="开始日期"
                end-placeholder="截止日期"
                :disabled="Boolean(form.id)"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!Boolean(form.id)">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <ViewDialog :visible.sync="viewVisible" :curRow="curRow" />
  </div>
</template>

<script>
import {
  createAnnualSummaryConfig,
  updateAnnualSummaryConfig,
  deleteAnnualSummaryConfig,
  getAnnualSummaryConfig,
  getAnnualSummaryConfigPage,
  exportAnnualSummaryConfigExcel,
  getGradeList,
} from "@/api/rotation/annualSummaryConfig";
import ViewDialog from "./components/viewDialog";

export default {
  name: "AnnualSummaryConfig",
  components: { ViewDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 年度总结配置列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        releaseYear: null,
        studentType: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        releaseYear: [
          { required: true, message: "发布年度不能为空", trigger: "change" },
        ],
        studentType: [
          { required: true, message: "学员类型不能为空", trigger: "change" },
        ],
        grade: [{ required: true, message: "年级不能为空", trigger: "blur" }],
        theoryDate: [
          {
            required: true,
            message: "理论考核成绩查询范围不能为空",
            trigger: "blur",
          },
        ],
        skilltDate: [
          {
            required: true,
            message: "技能考核成绩查询范围不能为空",
            trigger: "blur",
          },
        ],
        summaryDate: [
          {
            required: true,
            message: "总结提交日期不能为空",
            trigger: "blur",
          },
        ],
      },
      studentGradeList: [],
      viewVisible: false,
      curRow: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getAnnualSummaryConfigPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        releaseYear: undefined,
        studentType: undefined,
        grade: undefined,
        associateTheoryScore: "true",
        associateSkillScore: "true",
        theoryDate: undefined,
        skilltDate: undefined,
        summaryDate: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    getStudentGradeList(params) {
      getGradeList(params).then((res) => {
        this.studentGradeList = res.data;
      });
    },
    handleStudentType(val) {
      const params = {
        releaseYear: this.form.releaseYear,
        studentType: val,
      };
      if (!params.releaseYear) {
        this.$message.warning("请选择发布年度");
        return;
      }
      this.getStudentGradeList(params);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加年度总结配置";
    },
    handleView(row) {
      this.viewVisible = true;
      this.curRow = row;
    },
    /** 修改按钮操作 */
    handleViewConfig(row) {
      this.reset();
      const id = row.id;
      getAnnualSummaryConfig(id).then((response) => {
        this.form = response.data;
        this.form.associateSkillScore =
          this.form.associateSkillScore.toString();

        this.form.associateTheoryScore =
          this.form.associateTheoryScore.toString();

        if (this.form.theoryStartDate && this.form.theoryEndDate) {
          this.form.theoryDate = [
            this.form.theoryStartDate,
            this.form.theoryEndDate,
          ];
        }

        if (this.form.skilltStartDate && this.form.skilltEndDate) {
          this.form.skilltDate = [
            this.form.skilltStartDate,
            this.form.skilltEndDate,
          ];
        }

        if (this.form.summaryStartDate && this.form.summaryEndDate) {
          this.form.summaryDate = [
            this.form.summaryStartDate,
            this.form.summaryEndDate,
          ];
        }

        this.open = true;
        this.title = "查看年度总结配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const { theoryDate, skilltDate, summaryDate, ...others } = this.form;
        const params = {
          ...others,
        };
        if (theoryDate && theoryDate.length) {
          params.theoryStartDate = theoryDate[0];
          params.theoryEndDate = theoryDate[1];
        }

        if (skilltDate && skilltDate.length) {
          params.skilltStartDate = skilltDate[0];
          params.skilltEndDate = skilltDate[1];
        }

        if (summaryDate && summaryDate.length) {
          params.summaryStartDate = summaryDate[0];
          params.summaryEndDate = summaryDate[1];
        }

        // 修改的提交
        if (this.form.id != null) {
          updateAnnualSummaryConfig(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createAnnualSummaryConfig(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm("是否确认删除该年度总结配置?")
        .then(function () {
          return deleteAnnualSummaryConfig(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有年度总结配置数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportAnnualSummaryConfigExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "年度总结配置.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>
