<template>
  <el-dialog title="试卷分析" :visible.sync="open" width="1200px">
    <div id="paper-analysis-content">
      <h2 class="section-title">基础信息</h2>
      <el-row class="table-like">
        <el-col :span="4">试卷名称</el-col>
        <el-col :span="4">{{ baseInfo.name }}</el-col>
        <el-col :span="4">考试开始时间</el-col>
        <el-col :span="4">{{ parseTime(baseInfo.beginTime) }}</el-col>
        <el-col :span="4">考试结束时间</el-col>
        <el-col :span="4">{{ parseTime(baseInfo.endTime) }}</el-col>
        <el-col :span="4">总分</el-col>
        <el-col :span="4">{{ baseInfo.totalScore }}</el-col>
        <el-col :span="4">及格分</el-col>
        <el-col :span="4">{{ baseInfo.passScore }}</el-col>
        <el-col :span="4">答题时间</el-col>
        <el-col :span="4">{{ baseInfo.answerMinuteTime }}</el-col>
      </el-row>

      <h2 class="section-title">成绩统计</h2>
      <el-row class="table-like">
        <el-col :span="4">参考人数</el-col>
        <el-col :span="4">{{ resultStatistics.joinUserNum }}</el-col>
        <el-col :span="4">最高分</el-col>
        <el-col :span="4">{{ resultStatistics.highestScore }}</el-col>
        <el-col :span="4">最低分</el-col>
        <el-col :span="4">{{ resultStatistics.lowestScore }}</el-col>
        <el-col :span="4">平均分</el-col>
        <el-col :span="4">{{ resultStatistics.avgScore }}</el-col>
        <el-col :span="4">及格人数</el-col>
        <el-col :span="4">{{ resultStatistics.passUserNum }}</el-col>
        <el-col :span="4">及格率</el-col>
        <el-col :span="4">{{ resultStatistics.passRate }}</el-col>
        <el-col :span="4">缺考人数</el-col>
        <el-col :span="4">{{ resultStatistics.absencesUserNum }}</el-col>
        <el-col :span="16" style="background: transparent"></el-col>
      </el-row>

      <h2 class="section-title">题目分析</h2>
      <el-row class="mb20">
        <el-col :span="8">
          <v-echart :options="difficultyOptions" v-if="difficultyOptions"></v-echart>
        </el-col>
        <el-col :span="8">
          <v-echart :options="questionTypeOptions" v-if="questionTypeOptions"></v-echart>
        </el-col>
        <el-col :span="8">
          <v-echart :options="scoreDistributionOptions" v-if="scoreDistributionOptions"></v-echart>
        </el-col>
      </el-row>

      <h2 class="section-title">知识点分析</h2>
      <el-table class="mb20" :data="pointStatistics" border>
        <el-table-column label="排名" type="index"></el-table-column>
        <el-table-column label="知识点" prop="pointName"></el-table-column>
        <el-table-column label="题目数量" prop="questionNum"></el-table-column>
        <el-table-column label="得分率" prop="scoreRate"></el-table-column>
      </el-table>

      <h2 class="section-title">参考人员列表</h2>
      <el-table :data="userStatistics" border>
        <el-table-column label="姓名" prop="nickname"></el-table-column>
        <el-table-column label="用户名" prop="username"></el-table-column>
        <el-table-column label="考试次数" prop="examCount"></el-table-column>
        <el-table-column label="得分" prop="maxScore"></el-table-column>
      </el-table>
    </div>

    <template v-slot:footer>
      <div class="text-center">
        <el-button @click="open = false">关闭</el-button>
        <el-button type="primary" :loading="exporting" @click="handleExport">导出</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { getPaperBaseInfo, getDifficultyStatistics, getPaperUserStatistics, getPointStatistics,
  getQuestionTypeStatistics, getResultStatistics, getScoreDistributionStatistics } from '@/api/exam/analysis';
import VEchart from '@/components/VEchart'
import { exportPDF } from '@/utils/exportUtils'

export default {
  name: 'paper-analysis-dialog',
  components: { VEchart },
  data() {
    return {
      id: "",
      open: false,
      baseInfo: {},
      resultStatistics: {},
      pointStatistics: [],
      userStatistics: [],
      difficultyOptions: null,
      questionTypeOptions: null,
      scoreDistributionOptions: null,
      exporting: false,
    }
  },
  methods: {
    initData() {
      this.baseInfo = {};
      this.resultStatistics = {};
      this.pointStatistics = [];
      this.userStatistics = [];
      this.difficultyOptions = null;
      this.questionTypeOptions = null;
      this.scoreDistributionOptions = null;
      getPaperBaseInfo(this.id).then(res => this.baseInfo = res.data);
      getResultStatistics(this.id).then(res => this.resultStatistics = res.data);
      getDifficultyStatistics(this.id).then(res => {
        this.difficultyOptions = {
          title: {
            text: '难度分析',
            left: 'left',
            textStyle: { fontSize: 15, color: '#555' },
          },
          grid: {
            left: 30,
            top: 60,
            bottom: 60,
            right: 50,
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: "shadow"
            }
          },
          legend: {
            data: ['题目数量', '得分率'],
            bottom: '10px',
          },
          xAxis: [
            {
              type: 'category',
              data: res.data.map(item => this.getDictDataLabel(this.DICT_TYPE.EXAM_DIFFICULTY_TYPE, item.difficulty)),
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLabel: { formatter: '{value}' },
            },
            {
              type: 'value',
              min: 0,
              max: 100,
              interval: 10,
              axisLabel: { formatter: '{value} %' },
              splitLine: { show: false },
            }
          ],
          series: [
            {
              name: '题目数量',
              type: 'bar',
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' 题';
                }
              },
              data: res.data.map(item => item.questionNum)
            },
            {
              name: '得分率',
              type: 'line',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' %';
                }
              },
              data: res.data.map(item => item.scoreRate)
            }
          ]
        };
      });
      getQuestionTypeStatistics(this.id).then(res => {
        this.questionTypeOptions = {
          title: {
            text: '题型分析',
            left: 'left',
            textStyle: { fontSize: 15, color: '#555' },
          },
          grid: {
            left: 30,
            top: 60,
            bottom: 60,
            right: 50,
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: "shadow"
            }
          },
          legend: {
            data: ['题目数量', '得分率'],
            bottom: '10px',
          },
          xAxis: [
            {
              type: 'category',
              data: res.data.map(item => this.getDictDataLabel(this.DICT_TYPE.EXAM_QUESTION_TYPE, item.questionType)),
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                formatter: '{value}'
              }
            },
            {
              type: 'value',
              min: 0,
              max: 100,
              interval: 10,
              axisLabel: {
                formatter: '{value} %'
              },
              splitLine: { show: false },
            }
          ],
          series: [
            {
              name: '题目数量',
              type: 'bar',
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' 题';
                }
              },
              data: res.data.map(item => item.questionNum)
            },
            {
              name: '得分率',
              type: 'line',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' %';
                }
              },
              data: res.data.map(item => item.scoreRate)
            }
          ]
        };
      });
      getScoreDistributionStatistics(this.id).then(res => {
        this.scoreDistributionOptions = {
          title: {
            text: '考试成绩分布',
            left: 'left',
            textStyle: { fontSize: 15, color: '#555' },
          },
          grid: {
            left: 0,
            top: 60,
            bottom: 0,
            right: 100,
          },
          tooltip: {
            trigger: 'item',
          },
          legend: {
            orient: 'vertical',
            right: 'right',
            top: 'middle'
          },
          series: [
            {
              name: '考试成绩分布',
              type: 'pie',
              radius: '70%',
              center: ['40%', '50%'],
              data: [
                { name: '0-10', value: res.data?.distribution1to10 },
                { name: '11-20', value: res.data?.distribution10to20 },
                { name: '21-30', value: res.data?.distribution20to30 },
                { name: '31-40', value: res.data?.distribution30to40 },
                { name: '41-50', value: res.data?.distribution40to50 },
                { name: '51-60', value: res.data?.distribution50to60 },
                { name: '61-70', value: res.data?.distribution60to70 },
                { name: '71-80', value: res.data?.distribution70to80 },
                { name: '81-90', value: res.data?.distribution80to90 },
                { name: '91-100', value: res.data?.distribution90tMax	 },
              ],
              label: {
                formatter: '{c}',
                position: 'inside'
              }
            }
          ]
        }
      });
      getPointStatistics(this.id).then(res => this.pointStatistics = res.data);
      getPaperUserStatistics(this.id).then(res => this.userStatistics = res.data);
    },
    handleOpen(id) {
      this.id = id;
      this.open = true;
      this.initData();
    },
    handleExport() {
      this.exporting = true;
      exportPDF('paper-analysis-content', this.baseInfo.name + '试卷分析', () => {
        this.exporting = false;
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.section-title {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #000;
}

.table-like {
  margin-bottom: 20px;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;
  text-align: center;
  line-height: 1.5;
  display: flex;
  flex-wrap: wrap;
  .el-col {
    padding: 8px 2px;
    min-height: 38px;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    display: flex;
    align-items: center;
    justify-content: center;
    &:nth-child(odd) {
      background: #f8f8f9;
    }
  }
}
</style>
