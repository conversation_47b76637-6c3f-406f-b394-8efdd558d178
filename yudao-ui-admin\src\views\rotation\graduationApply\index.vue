<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="排班详情id" prop="scheduleDetailsId">
        <el-input v-model="queryParams.scheduleDetailsId" placeholder="请输入排班详情id" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="学员id" prop="studentId">
        <el-input v-model="queryParams.studentId" placeholder="请输入学员id" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="出科审核状态" prop="graduationAuditStatus">
        <el-select v-model="queryParams.graduationAuditStatus" placeholder="请选择出科审核状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.GRADUATION_AUDIT_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="个人小结" prop="personalSummary">
        <el-input v-model="queryParams.personalSummary" placeholder="请输入个人小结" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="带教老师userId" prop="teacherUserId">
        <el-input v-model="queryParams.teacherUserId" placeholder="请输入带教老师userId" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="带教审核时间" prop="teacherAuditTime">
        <el-date-picker v-model="queryParams.teacherAuditTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item label="带教评价" prop="teacherAppraise">
        <el-input v-model="queryParams.teacherAppraise" placeholder="请输入带教评价" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="科室老师userId" prop="deptUserId">
        <el-input v-model="queryParams.deptUserId" placeholder="请输入科室老师userId" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="带教退回原因" prop="teacherRepulseReason">
        <el-input v-model="queryParams.teacherRepulseReason" placeholder="请输入带教退回原因" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="科室审核时间" prop="deptAuditTime">
        <el-date-picker v-model="queryParams.deptAuditTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item label="科室评价" prop="deptAppraise">
        <el-input v-model="queryParams.deptAppraise" placeholder="请输入科室评价" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="科室退回原因" prop="deptRepulseReason">
        <el-input v-model="queryParams.deptRepulseReason" placeholder="请输入科室退回原因" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="总体评价状态" prop="totalityAppraiseStatus">
        <el-select v-model="queryParams.totalityAppraiseStatus" placeholder="请选择总体评价状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.GRADUATION_TOTALITY_APPRAISE_STATUS)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="总体评价不合格原因" prop="totalityAppraiseUnqualifiedReason">
        <el-input v-model="queryParams.totalityAppraiseUnqualifiedReason" placeholder="请输入总体评价不合格原因" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['rotation:graduation-apply:create']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" :loading="exportLoading"
                   v-hasPermi="['rotation:graduation-apply:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="排班详情id" align="center" prop="scheduleDetailsId" />
      <el-table-column label="学员id" align="center" prop="studentId" />
      <el-table-column label="出科审核状态" align="center" prop="graduationAuditStatus">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.GRADUATION_AUDIT_STATUS" :value="scope.row.graduationAuditStatus" />
        </template>
      </el-table-column>
      <el-table-column label="个人小结" align="center" prop="personalSummary" />
      <el-table-column label="带教老师userId" align="center" prop="teacherUserId" />
      <el-table-column label="带教审核时间" align="center" prop="teacherAuditTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.teacherAuditTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="带教评价" align="center" prop="teacherAppraise" />
      <el-table-column label="科室老师userId" align="center" prop="deptUserId" />
      <el-table-column label="带教退回原因" align="center" prop="teacherRepulseReason" />
      <el-table-column label="科室审核时间" align="center" prop="deptAuditTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deptAuditTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="科室评价" align="center" prop="deptAppraise" />
      <el-table-column label="科室退回原因" align="center" prop="deptRepulseReason" />
      <el-table-column label="总体评价状态" align="center" prop="totalityAppraiseStatus">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.GRADUATION_TOTALITY_APPRAISE_STATUS" :value="scope.row.totalityAppraiseStatus" />
        </template>
      </el-table-column>
      <el-table-column label="总体评价不合格原因" align="center" prop="totalityAppraiseUnqualifiedReason" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['rotation:graduation-apply:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['rotation:graduation-apply:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="500px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="排班详情id" prop="scheduleDetailsId">
          <el-input v-model="form.scheduleDetailsId" placeholder="请输入排班详情id" />
        </el-form-item>
        <el-form-item label="学员id" prop="studentId">
          <el-input v-model="form.studentId" placeholder="请输入学员id" />
        </el-form-item>
        <el-form-item label="出科审核状态" prop="graduationAuditStatus">
          <el-radio-group v-model="form.graduationAuditStatus">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.GRADUATION_AUDIT_STATUS)"
                      :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="个人小结" prop="personalSummary">
          <el-input v-model="form.personalSummary" placeholder="请输入个人小结" />
        </el-form-item>
        <el-form-item label="带教老师userId" prop="teacherUserId">
          <el-input v-model="form.teacherUserId" placeholder="请输入带教老师userId" />
        </el-form-item>
        <el-form-item label="带教审核时间" prop="teacherAuditTime">
          <el-date-picker clearable v-model="form.teacherAuditTime" type="date" value-format="timestamp" placeholder="选择带教审核时间" />
        </el-form-item>
        <el-form-item label="带教评价" prop="teacherAppraise">
          <el-input v-model="form.teacherAppraise" placeholder="请输入带教评价" />
        </el-form-item>
        <el-form-item label="科室老师userId" prop="deptUserId">
          <el-input v-model="form.deptUserId" placeholder="请输入科室老师userId" />
        </el-form-item>
        <el-form-item label="带教退回原因" prop="teacherRepulseReason">
          <el-input v-model="form.teacherRepulseReason" placeholder="请输入带教退回原因" />
        </el-form-item>
        <el-form-item label="科室审核时间" prop="deptAuditTime">
          <el-date-picker clearable v-model="form.deptAuditTime" type="date" value-format="timestamp" placeholder="选择科室审核时间" />
        </el-form-item>
        <el-form-item label="科室评价" prop="deptAppraise">
          <el-input v-model="form.deptAppraise" placeholder="请输入科室评价" />
        </el-form-item>
        <el-form-item label="科室退回原因" prop="deptRepulseReason">
          <el-input v-model="form.deptRepulseReason" placeholder="请输入科室退回原因" />
        </el-form-item>
        <el-form-item label="总体评价状态" prop="totalityAppraiseStatus">
          <el-radio-group v-model="form.totalityAppraiseStatus">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.GRADUATION_TOTALITY_APPRAISE_STATUS)"
                      :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="总体评价不合格原因" prop="totalityAppraiseUnqualifiedReason">
          <el-input v-model="form.totalityAppraiseUnqualifiedReason" placeholder="请输入总体评价不合格原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createGraduationApply, updateGraduationApply, deleteGraduationApply, getGraduationApply, getGraduationApplyPage, exportGraduationApplyExcel } from "@/api/rotation/graduationApply";

export default {
  name: "GraduationApply",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出科审核列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        scheduleDetailsId: null,
        studentId: null,
        graduationAuditStatus: null,
        personalSummary: null,
        teacherUserId: null,
        teacherAuditTime: [],
        teacherAppraise: null,
        deptUserId: null,
        teacherRepulseReason: null,
        deptAuditTime: [],
        deptAppraise: null,
        deptRepulseReason: null,
        totalityAppraiseStatus: null,
        totalityAppraiseUnqualifiedReason: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        scheduleDetailsId: [{ required: true, message: "排班详情id不能为空", trigger: "blur" }],
        studentId: [{ required: true, message: "学员id不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getGraduationApplyPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        scheduleDetailsId: undefined,
        studentId: undefined,
        graduationAuditStatus: undefined,
        personalSummary: undefined,
        teacherUserId: undefined,
        teacherAuditTime: undefined,
        teacherAppraise: undefined,
        deptUserId: undefined,
        teacherRepulseReason: undefined,
        deptAuditTime: undefined,
        deptAppraise: undefined,
        deptRepulseReason: undefined,
        totalityAppraiseStatus: undefined,
        totalityAppraiseUnqualifiedReason: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加出科审核";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getGraduationApply(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改出科审核";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateGraduationApply(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createGraduationApply(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除出科审核编号为"' + id + '"的数据项?').then(function() {
          return deleteGraduationApply(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = {...this.queryParams};
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal.confirm('是否确认导出所有出科审核数据项?').then(() => {
          this.exportLoading = true;
          return exportGraduationApplyExcel(params);
        }).then(response => {
          this.$download.excel(response, '出科审核.xlsx');
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
