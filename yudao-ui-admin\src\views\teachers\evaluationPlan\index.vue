<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评优项目" prop="teachersEvaluationProject">
        <el-select
          v-model="queryParams.teachersEvaluationProject"
          placeholder="请选择评优项目"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(
              DICT_TYPE.TEACHERS_EVALUATION_PROJECT
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['teachers:evaluation-plan:create']"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['teachers:evaluation-plan:export']"
          >导出</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="项目名称" align="center" prop="name" />
      <el-table-column label="评选年度" align="center" prop="year" />
      <el-table-column
        label="评优项目"
        align="center"
        prop="teachersEvaluationProject"
      >
        <template slot-scope="scope">
          <dict-tag
            :type="DICT_TYPE.TEACHERS_EVALUATION_PROJECT"
            :value="scope.row.teachersEvaluationProject"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="申报开始日期"
        align="center"
        prop="declareStartDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.declareStartDate }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="申报截止日期"
        align="center"
        prop="declareEndDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.declareEndDate }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="申报人数"
        align="center"
        prop="evaluationPlanUserNum"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.evaluationPlanUserNum === 0">
            {{ scope.row.evaluationPlanUserNum }}
          </span>
          <el-button v-else type="text" @click="viewApplyDialog(scope.row)">
            {{ scope.row.evaluationPlanUserNum }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.evaluationPlanUserNum == 0"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['teachers:evaluation-plan:update']"
          >
            修改
          </el-button>
          <el-button
            v-if="scope.row.evaluationPlanUserNum > 0"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['teachers:evaluation-plan:update']"
          >
            查看
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleConfig(scope.row)"
            v-hasPermi="['teachers:evaluation-plan:update']"
          >
            配置
          </el-button>
          <el-button
            v-if="scope.row.evaluationPlanUserNum == 0"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['teachers:evaluation-plan:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="评选年度" prop="year">
              <el-date-picker
                clearable
                v-model="form.year"
                type="year"
                value-format="yyyy"
                format="yyyy"
                placeholder="选择评选年度"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="评优项目" prop="teachersEvaluationProject">
              <el-select
                v-model="form.teachersEvaluationProject"
                placeholder="请选择评优项目"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in this.getDictDatas(
                    DICT_TYPE.TEACHERS_EVALUATION_PROJECT
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="申报开始截止日期" prop="declareDate">
              <el-date-picker
                clearable
                v-model="form.declareDate"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                start-placeholder="申报开始日期"
                end-placeholder="申报截止日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="发布对象" prop="publishObjects">
              <el-select
                v-model="form.publishObjects"
                multiple
                filterable
                placeholder="请选择发布对象"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in roleOptions"
                  :key="parseInt(item.id)"
                  :label="item.name"
                  :value="item.id.toString()"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="遴选说明" prop="remarks">
              <el-input
                type="textarea"
                v-model="form.remarks"
                placeholder="请在此处填写此次聘任的相关要求/说明"
                :autosize="{ minRows: 2 }"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="申请表模板">
              <FileUpload
                v-model="form.applicationDocumentTemplates"
                :limit="1"
                :fileSize="50"
                :fileType="['doc', 'pdf']"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <config-dialog
      :title="configDialogTitle"
      :openConfig="openConfig"
      :formData="form"
      @update:openConfig="(value) => (openConfig = value)"
      @refresh="getList"
    />

    <apply-person-dialog
      :openApply="openApply"
      :curRow="curRow"
      @update:openApply="(value) => (openApply = value)"
      @refresh="getList"
    />
  </div>
</template>

<script>
import FileUpload from "@/components/FileUploadInfo";
import ConfigDialog from "./configDialog";
import ApplyPersonDialog from "./applyPersonDialog";
import { listSimpleRoles } from "@/api/system/role";
import {
  createEvaluationPlan,
  updateEvaluationPlan,
  deleteEvaluationPlan,
  getEvaluationPlan,
  getEvaluationPlanPage,
  exportEvaluationPlanExcel,
} from "@/api/teachers/evaluationPlan";

export default {
  name: "EvaluationPlan",
  components: { FileUpload, ConfigDialog, ApplyPersonDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 师资评优计划列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        year: null,
        year: [],
        teachersEvaluationProject: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        year: [
          { required: true, message: "评选年度不能为空", trigger: "blur" },
        ],
        teachersEvaluationProject: [
          { required: true, message: "评优项目不能为空", trigger: "change" },
        ],
        declareDate: [
          {
            type: "array",
            required: true,
            message: "申报日期不能为空",
            trigger: "change",
          },
        ],
        publishObjects: [
          {
            required: true,
            message: "发布对象不能为空",
            trigger: "blur",
          },
        ],
        remarks: [
          { required: true, message: "遴选说明不能为空", trigger: "blur" },
        ],
        applicationDocumentTemplates: [
          { required: true, message: "申请表模板不能为空", trigger: "blur" },
        ],
      },
      openConfig: false,
      configDialogTitle: "项目配置",
      openApply: false,
      roleOptions: [],
      curRow: {},
    };
  },
  created() {
    // 获得角色列表
    this.roleOptions = [];
    listSimpleRoles().then((response) => {
      const list = response.data;
      list.forEach((item) => {
        if (item.code !== "super_admin" && item.code !== "student") {
          this.roleOptions.push(item);
        }
      });
    });
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getEvaluationPlanPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        year: undefined,
        teachersEvaluationProject: undefined,
        declareDate: [],
        // declareStartDate: undefined,
        // declareEndDate: undefined,
        publishObjects: undefined,
        remarks: undefined,
        applicationDocumentTemplates: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加师资评优计划";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.handleDetails(row, () => {
        this.open = true;
        this.title = "修改师资评优计划";
      });
    },
    handleDetails(row, callback) {
      this.reset();
      const id = row.id;
      getEvaluationPlan(id).then((response) => {
        const info = response.data;
        this.form.id = info.id;
        this.form.name = info.name;
        this.form.teachersEvaluationProject = info.teachersEvaluationProject;
        this.form.year = info.year.toString();
        this.form.publishObjects = info.publishObjects.split(",");
        const { declareStartDate, declareEndDate } = response.data;
        this.form.declareDate = [declareStartDate, declareEndDate];
        if (info.applicationDocumentTemplates) {
          try {
            this.form.applicationDocumentTemplates = JSON.parse(
              info.applicationDocumentTemplates
            );
          } catch (error) {
            this.form.applicationDocumentTemplates = [];
          }
        }
        this.form.remarks = info.remarks;
        console.log("this.form===", this.form);
        callback();
      });
    },
    handleConfig(row) {
      this.handleDetails(row, () => {
        this.openConfig = true;
      });
    },
    viewApplyDialog(row) {
      this.curRow = row;
      this.openApply = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = {
          ...this.form,
          declareStartDate: this.form.declareDate[0],
          declareEndDate: this.form.declareDate[1],
          publishObjects: this.form.publishObjects.join(","),
          applicationDocumentTemplates: this.form.applicationDocumentTemplates
            ? JSON.stringify(this.form.applicationDocumentTemplates)
            : "",
        };
        delete params.declareDate;

        // 修改的提交
        if (this.form.id != null) {
          updateEvaluationPlan(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createEvaluationPlan(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除师资评优计划项目为"' + row.name + '"的数据项?')
        .then(function () {
          return deleteEvaluationPlan(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有师资评优计划数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportEvaluationPlanExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "师资评优计划.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>
