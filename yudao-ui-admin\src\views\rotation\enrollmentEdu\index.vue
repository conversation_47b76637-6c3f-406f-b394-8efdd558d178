<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px"
    >
      <el-form-item label="入科教育名称" prop="name" label-width="98px">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入入科教育名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医院科室" prop="departmentName">
        <el-select
          v-model="queryParams.departmentName"
          filterable
          clearable
          placeholder="请选择医院科室"
          size="small"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学员类型" prop="studentType">
        <el-select
          v-model="queryParams.studentType"
          placeholder="请选择学员类型"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_STUDENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开展时间" prop="developDates">
        <el-date-picker
          type="daterange"
          clearable
          v-model="queryParams.developDates"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="活动照片" prop="isUploadPicture">
        <el-select
          v-model="queryParams.isUploadPicture"
          placeholder="请选择是否有活动照片"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['rotation:enrollment-edu:create']"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rotation:enrollment-edu:export']"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="入科教育名称" align="center" prop="name">
        <template slot-scope="scope">
          {{ scope.row.name }}
          <dict-tag
            class="valid-tag"
            size="mini"
            :type="DICT_TYPE.ACTIVE_DEVOLEP_STATUS"
            :value="scope.row.activeDevolepStatus"
          />
        </template>
      </el-table-column>
      <el-table-column label="科室名称" align="center" prop="departmentName" />
      <!-- <el-table-column label="学员类型" align="center" prop="studentTypes" /> -->
      <el-table-column label="学员类型" align="center" prop="studentTypes">
        <template slot-scope="scope">
          <dict-tag
            v-for="item in scope.row.studentTypes.split(',')"
            :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
            :value="item"
            style="display: block"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="开始时间"
        align="center"
        prop="startTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="主讲人" align="center" prop="speakerUsername" />
      <el-table-column label="出勤率" align="center" prop="attendance">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="viewStudentDetail(scope.row)"
            >{{ scope.row.attendance }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="评价得分"
        align="center"
        prop="evaluationScore"
        width="140"
      >
        <template slot-scope="scope">
          <div class="rate-wrapper">
            <div
              class="rate-click"
              :style="{
                cursor: scope.row.evaluationScore ? 'pointer' : 'default',
              }"
              @click="handleScoreClick(scope.row)"
            ></div>
            <el-rate
              v-model="scope.row.evaluationScore"
              disabled
              show-score
              text-color="#ff9900"
              :max="5"
              score-template="{value}"
            >
            </el-rate>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['rotation:enrollment-edu:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handlePics(scope.row)"
            v-hasPermi="['rotation:enrollment-edu:update']"
            >活动照片</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-picture-outline"
            @click="handleViewQrcode(scope.row)"
            v-hasPermi="['rotation:enrollment-edu:update']"
            >查看二维码</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['rotation:enrollment-edu:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="108px">
        <el-form-item label="入科教育名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入入科教育名称" />
        </el-form-item>
        <el-form-item
          class="half-item"
          label="医院科室"
          prop="departmentId"
          style="width: 40%"
        >
          <el-select
            v-model="form.departmentId"
            filterable
            placeholder="请选择医院科室"
            @change="getUserworkData"
            :disabled="opt === 'edit'"
          >
            <el-option
              v-for="item in departmentPermissionOptions"
              :key="parseInt(item.id)"
              :label="item.name"
              :value="parseInt(item.id)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="half-item"
          label="主讲人"
          prop="speakerUserId"
          style="width: 60%"
        >
          <el-select
            v-model="form.speakerUserId"
            filterable
            placeholder="请选择主讲人"
            style="width: 100%"
          >
            <el-option
              v-for="user in userWorkerOptions"
              :key="user.id"
              :label="user.nickname"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学员类型" prop="studentTypes">
          <el-select
            v-model="form.studentTypes"
            multiple
            filterable
            placeholder="请选择学员类型"
            :disabled="opt === 'edit'"
            style="width: 100%"
          >
            <el-option
              v-for="user in studentTypesOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="half-item"
          label="开展方式"
          prop="developWay"
          style="width: 40%"
        >
          <el-select
            v-model="form.developWay"
            filterable
            placeholder="请选择开展方式"
          >
            <el-option
              v-for="dict in this.getDictDatas(DICT_TYPE.ROTATION_DEVELOP_WAY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="half-item"
          label="开展时间"
          prop="timeValue"
          style="width: 60%"
        >
          <el-date-picker
            style="width: 100%"
            v-model="form.timeValue"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="timeValueChange"
            :disabled="opt === 'edit'"
          />
        </el-form-item>

        <el-form-item
          :label="form.developWay == 2 ? '参与方式' : '开展地点'"
          prop="joinWay"
        >
          <el-input
            type="textarea"
            v-model="form.joinWay"
            :placeholder="
              form.developWay == 2 ? '输入会议号或会议链接' : '输入开展地点'
            "
          />
        </el-form-item>
        <!-- <el-form-item label="是否考核" prop="isExamine">
          <el-radio-group v-model="form.isExamine" style="margin-right: 20px;">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <!-- <el-form-item v-if="form.isExamine" label="考核id" prop="examineId">
          <el-select v-model="form.examineId" placeholder="请输"></el-select>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="活动照片"
      :visible.sync="openPics"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <imageUpload v-model="form.pictures" :limit="9999" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPics">保 存</el-button>
        <el-button @click="cancelPics">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="活动二维码"
      :visible.sync="openQrCode"
      width="500px"
      v-dialogDrag
      append-to-body
    >
      <div style="text-align: center">
        <img
          width="220"
          height="220"
          :src="'data:image/png;base64,' + curQrcode"
        />
      </div>
    </el-dialog>

    <el-dialog
      title="参加学员数详情页面"
      :visible.sync="openStudentDetail"
      width="800px"
      v-dialogDrag
      append-to-body
    >
      <div>
        <el-table v-loading="studentDetailLoading" :data="studentDetailList">
          <el-table-column label="学员姓名" align="center" prop="nickname" />
          <el-table-column label="学员类型" align="center" prop="studentType">
            <template slot-scope="scope">
              <dict-tag
                :type="DICT_TYPE.SYSTEM_STUDENT_TYPE"
                :value="scope.row.studentType"
              />
            </template>
          </el-table-column>
          <el-table-column label="培训专业" align="center" prop="majorName" />
          <el-table-column label="年级" align="center" prop="grade" />
          <el-table-column label="扫码时间" align="center" prop="scanningTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.scanningTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="参与形式" align="center" prop="joinType">
            <template v-slot="scope">
              <dict-tag
                :type="DICT_TYPE.ROTATION_JOIN_TYPE"
                :value="scope.row.joinType"
              ></dict-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="180"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-if="!scope.row.scanningTime"
                size="mini"
                type="text"
                @click="handleJoin(scope.row)"
                >确认参加</el-button
              >
              <el-button
                v-else
                size="mini"
                type="text"
                @click="handleRevoke(scope.row)"
                >撤销参加</el-button
              >
              <el-button
                v-if="!scope.row.scanningTime"
                size="mini"
                type="text"
                @click="handleDelUser(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPics">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div> -->
    </el-dialog>

    <appraise-score-dialog
      appraise-active-type="4"
      ref="appraiseScoreDialog"
    ></appraise-score-dialog>
  </div>
</template>

<script>
import ImageUpload from "@/components/ImageUpload";
import AppraiseScoreDialog from "@/views/components/appraiseScoreDialog";
import {
  getDepartmentSimpleList,
  getDepartmentPermissionList,
} from "@/api/system/department";
import {
  getUserWorkerSimpleList,
  getUserWorkerPermissionList,
} from "@/api/system/userWorker";
import { getStudentTypes } from "@/api/system/user";
import {
  createEnrollmentEdu,
  updateEnrollmentEdu,
  deleteEnrollmentEdu,
  getEnrollmentEdu,
  getEnrollmentEduPage,
  exportEnrollmentEduExcel,
  getStudentsList,
  confirmJoin,
  revokeJoin,
  deleteTeachingUser,
} from "@/api/rotation/enrollmentEdu";

export default {
  name: "EnrollmentEdu",
  components: { ImageUpload, AppraiseScoreDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      studentDetailLoading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 入科教育列表
      list: [],
      studentDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openPics: false,
      openStudentDetail: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        departmentName: null,
        studentType: null,
        developDates: [],
        // startTime: null,
        // endTime: null,
        // developWay: "1",
        isNeedQrcode: true,
        isUploadPicture: null,
      },
      // 表单参数
      form: {},
      opt: "",
      // 表单校验
      rules: {
        name: [
          { required: true, message: "入科教育名称不能为空", trigger: "blur" },
        ],
        departmentId: [
          { required: true, message: "医院科室Id不能为空", trigger: "change" },
        ],
        speakerUserId: [
          { required: true, message: "主讲人用户id不能为空", trigger: "blur" },
        ],
        studentTypes: [
          {
            type: "array",
            required: true,
            message: "学员类型集合,逗号分隔不能为空",
            trigger: "change",
          },
        ],
        timeValue: [
          { required: true, message: "请选择开展时间", trigger: "change" },
        ],
        // startTime: [{ required: true, message: "开始时间不能为空", trigger: "blur" }],
        // endTime: [{ required: true, message: "结束时间不能为空", trigger: "blur" }],
        developWay: [
          { required: true, message: "开展方式不能为空", trigger: "change" },
        ],
        joinWay: [
          { required: true, message: "参与方式不能为空", trigger: "blur" },
        ],
        isExamine: [
          { required: true, message: "是否考核不能为空", trigger: "blur" },
        ],
      },
      departmentOptions: [],
      departmentPermissionOptions: [],
      userWorkerOptions: [],
      studentTypesOptions: [],
      openQrCode: false,
      curQrcode: "",
    };
  },
  created() {
    this.getList();
    this.getDepartment();
    this.getPermissionDepartment();
    // this.getUserworkData();
    this.getStudentTypesList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getEnrollmentEduPage(this.queryParams).then((response) => {
        const list = response.data.list;
        list.forEach((item) => {
          if (!item.score) {
            item.evaluationScore = 0;
          } else {
            const _score = (item.evaluationScore / item.score) * 5;
            item.evaluationScore = _score;
          }
        });
        this.list = list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then((res) => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      });
    },
    getPermissionDepartment() {
      // 获得科室列表
      const params = { component: "rotation/enrollmentEdu/index" };
      getDepartmentPermissionList(params).then((res) => {
        // 处理 roleOptions 参数
        this.departmentPermissionOptions = [];
        this.departmentPermissionOptions.push(...res.data);
      });
    },
    getStudentTypesList() {
      const params = { component: "rotation/enrollmentEdu/index" };
      getStudentTypes(params).then((res) => {
        this.studentTypesOptions = [];
        this.studentTypesOptions.push(...res.data);
      });
    },
    async getUserworkData(val) {
      this.form.speakerUserId = "";
      const params = {
        departmentId: val,
        // 'component': 'rotation/enrollmentEdu/index'
      };
      const { data } = await getUserWorkerPermissionList(params);
      this.userWorkerOptions = data || [];
    },
    // getUserworkData() {
    //   getUserWorkerSimpleList().then(res => {
    //     this.userWorkerOptions = []
    //     this.userWorkerOptions.push(...res.data);
    //   })
    // },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelPics() {
      this.openPics = false;
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        departmentId: undefined,
        speakerUserId: undefined,
        studentTypes: [],
        timeValue: [],
        startTime: undefined,
        endTime: undefined,
        developWay: "1",
        joinWay: undefined,
        isExamine: false,
        examineId: undefined,
        pictures: undefined,
      };
      this.resetForm("form");
    },
    timeValueChange(values) {
      this.form.startTime = undefined;
      this.form.endTime = undefined;
      if (values) {
        this.form.startTime = values[0];
        this.form.endTime = values[1];
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$confirm(
        "请确认本月轮转学员是否已经全部办理入科，未办理入科学员不会抓取到出勤名单中。",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.reset();
          this.open = true;
          this.opt = "add";
          this.title = "添加入科教育";
        })
        .catch(() => {});
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getEnrollmentEdu(id).then(async (response) => {
        await this.getUserworkData(response.data.departmentId);
        this.form = response.data;
        this.form.studentTypes = this.form.studentTypes.split(",");
        this.form.timeValue = [this.form.startTime, this.form.endTime];
        this.open = true;
        this.opt = "edit";
        this.title = "修改入科教育";
      });
    },
    handlePics(row) {
      this.reset();
      const id = row.id;
      getEnrollmentEdu(id).then((response) => {
        this.form = response.data;
        this.openPics = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        const params = { ...this.form };
        delete params.timeValue;
        params.studentTypes = params.studentTypes.join(",");
        // 修改的提交
        if (this.form.id != null) {
          updateEnrollmentEdu(params).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createEnrollmentEdu(params).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    submitPics() {
      const params = { ...this.form };
      delete params.timeValue;
      updateEnrollmentEdu(params).then((response) => {
        this.$modal.msgSuccess("保存成功");
        this.openPics = false;
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除入科教育编号为"' + id + '"的数据项?')
        .then(function () {
          return deleteEnrollmentEdu(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;
      this.$modal
        .confirm("是否确认导出所有入科教育数据项?")
        .then(() => {
          this.exportLoading = true;
          return exportEnrollmentEduExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "入科教育.xlsx");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    viewStudentDetail(row) {
      this.getStudentList(row.id, () => {
        this.openStudentDetail = true;
      });
    },
    getStudentList(id, callback) {
      const params = {
        enrollmentEduId: id,
      };
      this.studentDetailLoading = true;
      getStudentsList(params).then((response) => {
        this.studentDetailList = response.data || [];
        if (callback) {
          callback();
        }
        this.studentDetailLoading = false;
      });
    },
    handleJoin(row) {
      const params = {
        enrollmentEduId: row.enrollmentEduId,
        userId: row.userId,
      };
      confirmJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getStudentList(row.enrollmentEduId);
      });
    },
    handleRevoke(row) {
      const params = {
        enrollmentEduId: row.enrollmentEduId,
        userId: row.userId,
      };
      revokeJoin(params).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getStudentList(row.enrollmentEduId);
      });
    },
    handleDelUser(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(() => {
          this.handleDelUserFun(row);
        })
        .catch(() => {});
    },
    handleDelUserFun(row) {
      const params = {
        enrollmentEduStudentId: row.id,
      };
      deleteTeachingUser(params).then((res) => {
        this.$modal.msgSuccess("删除成功");
        this.getStudentList(row.enrollmentEduId);
      });
    },
    handleViewQrcode(row) {
      this.curQrcode = row.qrcode;
      this.openQrCode = true;
    },
    // 查看评分情况
    handleScoreClick(item) {
      this.$refs.appraiseScoreDialog.openScoreDialog({
        ...item,
        activeType: item.trainingType,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.half-item {
  display: inline-block;
  width: 50%;
}

.valid-tag {
  position: absolute;
  top: 1px;
  right: 0;
}

.rate-wrapper {
  position: relative;
}

.rate-click {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
</style>
