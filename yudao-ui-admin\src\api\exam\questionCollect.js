import request from "@/utils/request";

// 创建考试题目收藏
export function createQuestionCollect(data) {
  return request({
    url: "/exam/question-collect/create",
    method: "post",
    data: data,
  });
}

// 更新考试题目收藏
export function updateQuestionCollect(data) {
  return request({
    url: "/exam/question-collect/update",
    method: "put",
    data: data,
  });
}

// 删除考试题目收藏
export function deleteQuestionCollect(id) {
  return request({
    url: "/exam/question-collect/delete?id=" + id,
    method: "delete",
  });
}

// 获得考试题目收藏
export function getQuestionCollect(id) {
  return request({
    url: "/exam/question-collect/get?id=" + id,
    method: "get",
  });
}

// 获得考试题目收藏分页
export function getQuestionCollectPage(query) {
  return request({
    url: "/exam/question-collect/page",
    method: "get",
    params: query,
  });
}

// 导出考试题目收藏 Excel
export function exportQuestionCollectExcel(query) {
  return request({
    url: "/exam/question-collect/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获得考试试题
export function getQuestionAnswer(query) {
  return request({
    url: "/exam/question/get",
    method: "get",
    params: query,
  });
}
