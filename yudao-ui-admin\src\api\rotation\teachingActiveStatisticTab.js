import request from "@/utils/request";

// 获得教学活动统计分页(按培训人)
export function getTeachingActivePagePerson(query) {
  return request({
    url: "/rotation/teaching-active/statistics/page-person",
    method: "get",
    params: query,
    // headers: {'component': 'rotation/teachingActiveDevelopment/index'}
  });
}

// 获得教学活动统计分页(按培训科室)
export function getTeachingActivePageDepartment(query) {
  return request({
    url: "/rotation/teaching-active/statistics/page-department",
    method: "get",
    params: query,
    // headers: {'component': 'rotation/teachingActiveDevelopment/index'}
  });
}

// 导出-培训人
export function exportTeachingActiveExcelPerson(query) {
  return request({
    url: "/rotation/teaching-active/statistics/export-excel-person",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 导出-培训科室
export function exportTeachingActiveExcelDepartment(query) {
  return request({
    url: "/rotation/teaching-active/statistics/export-excel-department",
    method: "get",
    params: query,
    responseType: "blob",
  });
}
