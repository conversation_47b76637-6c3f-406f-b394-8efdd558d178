<template>
  <el-dialog
    :title="curRow.releaseYear + '年度考评'"
    :visible="visible"
    width="500px"
    v-dialogDrag
    append-to-body
    @close="$emit('update:visible', $event)"
  >
    <div>
      <div class="submit-form">
        <el-form
          :model="form"
          ref="form"
          size="small"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="考评结果" prop="result">
            <el-radio-group v-model="form.result" :disabled="opt === 'view'">
              <el-radio
                v-for="dict in resultList"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div>
          <el-button type="text" @click="viewTemplate">查看报告</el-button>
        </div>
      </div>

      <div></div>
    </div>
    <div slot="footer" class="dialog-footer" v-if="opt === 'edit'">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { DICT_TYPE } from "@/utils/dict";
import { updateAssessmentResult } from "@/api/rotation/annualSummaryAssessment";
import {
  getTemplateByType,
  previewDocTemplateUrl,
} from "@/api/system/template";

export default {
  name: "annualSummaryViewDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    curRow: {
      type: Object,
      default: {},
    },
    opt: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      queryLoading: false,
      total: 0,
      form: {
        result: "1",
      },
      userIds: [],
      userTypes: [],
      tableList: [],
      roleOptions: [],
    };
  },
  computed: {
    resultList() {
      const list = this.getDictDatas(DICT_TYPE.ASSESSMENT_RESULT);
      return list.filter((item) => item.value !== "0");
    },
  },
  watch: {
    visible(value) {
      if (value) {
        if (this.opt === "view") {
          this.form.result = this.curRow.result;
        } else {
          this.form.result = "1";
        }
      }
    },
  },
  created() {},
  methods: {
    viewTemplate() {
      getTemplateByType({
        templateType: "annual_summary",
        studentType: this.curRow.studentType,
      }).then((res) => {
        const url = previewDocTemplateUrl(
          res.data.id,
          this.curRow.id,
          `annualSummary${this.curRow.id}`
        );
        window.open(url);
      });
    },
    handleSubmit() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }

        this.$modal
          .confirm("是否确认提交考评结果，提交后不可修改!")
          .then(() => {
            this.submit();
          })
          .catch(() => {});
      });
    },

    submit() {
      const params = {
        ...this.form,
        id: this.curRow.id,
      };

      updateAssessmentResult(params).then((response) => {
        this.$modal.msgSuccess("提交成功");
        this.$emit("update:visible", false);
        this.$emit("refresh");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.submit-form {
  display: flex;
  justify-content: space-between;
}
</style>
