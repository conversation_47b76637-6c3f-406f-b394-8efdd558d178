<template>
  <div>
    <h3 class="form-name">操作技能直接观察评估量表</h3>

    <el-form class="form-info" size="mini" inline>
      <el-form-item class="quarter-item" label="时间:">
        {{ formData.date_m }}月{{ formData.date_d }}日
      </el-form-item>
      <el-form-item class="majority-item" label="地点:">
        <el-radio-group v-model="formData.address" :disabled="check || feedback">
          <el-radio label="病房"></el-radio>
          <el-radio label="门诊"></el-radio>
          <el-radio label="急诊"></el-radio>
          <el-radio label="ICU"></el-radio>
          <el-radio label="其他"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="quarter-item" label="指导医师:">{{ formData.medical_name }}</el-form-item>
      <el-form-item class="majority-item" label-width="45px">
        <el-radio-group v-model="formData.medical_title" :disabled="check || feedback">
          <el-radio label="主任医师"></el-radio>
          <el-radio label="副主任医师"></el-radio>
          <el-radio label="主治医师"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="quarter-item" label="学生姓名:">{{ formData.student_name }}</el-form-item>
      <el-form-item class="majority-item" label-width="45px">
        <el-radio-group v-model="formData.student_year" :disabled="check || feedback">
          <el-radio label="第一年"></el-radio>
          <el-radio label="第二年"></el-radio>
          <el-radio label="第三年"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="quarter-item" label="患者:">
        <el-input style="width: 100px" v-model="formData.patient_name" :disabled="check || feedback"></el-input>
      </el-form-item>
      <el-form-item class="quarter-item" label="年龄:">
        <el-input-number
          style="width: 90px;"
          :min="0"
          controls-position="right"
          v-model="formData.patient_age"
          :disabled="check || feedback"
        ></el-input-number>
      </el-form-item>
      <el-form-item class="quarter-item" label="性别:">
        <el-radio-group v-model="formData.patient_gender" :disabled="check || feedback">
          <el-radio label="男"></el-radio>
          <el-radio label="女"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="quarter-item">
        <el-radio-group v-model="formData.patient_diagnose" :disabled="check || feedback">
          <el-radio label="初诊"></el-radio>
          <el-radio label="复诊"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="half-item" label="操作名称:">
        <el-input style="width: 240px;" v-model="formData.operation" :disabled="check || feedback"></el-input>
      </el-form-item>
      <el-form-item class="half-item" label="操作复杂程度:">
        <el-radio-group v-model="formData.operation_complexity" :disabled="check || feedback">
          <el-radio label="低"></el-radio>
          <el-radio label="中"></el-radio>
          <el-radio label="高"></el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <table class="evaluate-table">
      <tr>
        <th rowspan="3">评级项目</th>
        <th rowspan="3" style="width: 8.6%;">未观察到</th>
        <th colspan="9">项目评级结果</th>
      </tr>
      <tr>
        <th colspan="3">不符合要求</th>
        <th colspan="3">符合要求</th>
        <th colspan="3">表现优秀</th>
      </tr>
      <tr>
        <th v-for="i in 9" :key="i" style="width: 8.6%;">{{ i }}</th>
      </tr>
      <tr v-for="item in items" :key="item.prop">
        <td>{{ item.name }}</td>
        <td v-for="i in 10" :key="i">
          <el-radio v-model="formData[item.prop]" :label="i-1" :disabled="check || feedback"></el-radio>
        </td>
      </tr>
    </table>

    <el-form class="form-info" inline size="mini">
      <el-form-item class="half-item" label="直接观察时间:">
        <el-input-number v-model="formData.observation_time" :min="0" :disabled="check || feedback" controls-position="right"></el-input-number>
        分钟
      </el-form-item>
      <el-form-item class="half-item" label="反馈时间:">
        <el-input-number v-model="formData.feedback_time" :min="0" :disabled="check || feedback" controls-position="right"></el-input-number>
        分钟
      </el-form-item>
      <el-form-item class="full-item" label="指导医师对评估的满意程度:">
        低
        <el-radio-group v-model="formData.mentor_satisfaction" :disabled="check || feedback">
          <el-radio v-for="i in 9" :key="i" :value="i" :label="i"></el-radio>
        </el-radio-group>
        高
      </el-form-item>
      <el-form-item class="full-item" label="考评学员对评估的满意程度:">
        低
        <el-radio-group v-model="formData.student_satisfaction" :disabled="check || evaluate">
          <el-radio v-for="i in 9" :key="i" :value="i" :label="i"></el-radio>
        </el-radio-group>
        高
      </el-form-item>
      <el-form-item class="comment-item" label="指导医师的评语:">
        <el-input type="textarea" :rows="3" v-model="formData.comment"  :disabled="check || feedback"></el-input>
      </el-form-item>
    </el-form>

    <div class="signature-bar">
      <div>学员签字:{{ formData.student_signature }}</div>
      <div>指导医师签字:{{ formData.mentor_signature }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'formSecond',
  props: {
    formData: Object,
    evaluate: Boolean, // 指导医师评价
    feedback: Boolean, // 学员反馈
    check: Boolean, // 仅查看
  },
  data() {
    return {
      items: [
        { name: "操作适应证、相关解剖和操作技术的理解", prop: "technique_comprehension" },
        { name: "知情同意", prop: "informed_consent" },
        { name: "操作前准备", prop: "pre_operative_preparation" },
        { name: "止痛镇静", prop: "analgesia_sedation" },
        { name: "技术能力", prop: "technical_ability" },
        { name: "无菌技术", prop: "aseptic_technique" },
        { name: "根据需要寻求帮助", prop: "seek_assistance" },
        { name: "操作后处理", prop: "post_operative_management" },
        { name: "沟通技能", prop: "communication_skills" },
        { name: "人文关怀/职业素养", prop: "humanistic_professionalism" },
        { name: "整体表现", prop: "overall_performance" },
      ]
    }
  },
  methods: {
    validData() {
      if (!this.formData.address ||
        !this.formData.medical_title ||
        !this.formData.student_year ||
        !this.formData.patient_name ||
        !this.formData.patient_age ||
        !this.formData.patient_gender ||
        !this.formData.patient_diagnose ||
        !this.formData.operation ||
        !this.formData.operation_complexity ||
        this.formData.observation_time === null ||
        this.formData.feedback_time === null ||
        !this.formData.mentor_satisfaction ||
        (this.feedback && !this.formData.student_satisfaction) ||
        !this.formData.comment ||
        this.items.some(item => !this.formData[item.prop] && this.formData[item.prop] !== 0)
      ) {
        this.$message.warning(this.feedback ? "请选择考评学员对评估的满意程度" : "请填写完所有考评项目～");
        return false;
      }
      return true;
    },
  }
}
</script>

<style scoped lang="scss">
.form-name {
  font-size: 16px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 16px;
}

.form-info {
  margin-bottom: 10px;

  ::v-deep .el-form-item {
    margin: 0 0 10px 0;
  }

  ::v-deep .el-radio-group {
    margin-left: 10px;
  }

  ::v-deep .el-radio {
    margin-right: 16px;
  }

  .full-item {
    width: 100%;
  }

  .half-item {
    width: 50%;
  }

  .quarter-item {
    width: 25%;
  }

  .majority-item {
    width: 75%;
  }

  .comment-item {
    width: 100%;
    ::v-deep .el-form-item__content {
      display: block;
    }
  }
}

.evaluate-table {
  width: 100%;
  border: 1px solid #dfe6ec;
  border-collapse: collapse;
  margin-bottom: 10px;

  td, th {
    border: 1px solid #dfe6ec;
    padding: 8px 0;
    text-align: center;
  }

  ::v-deep .el-radio__label {
    display: none;
  }

  ::v-deep .el-radio__inner {
    vertical-align: middle;
  }
}

.signature-bar {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  margin-bottom: 20px;
  padding-top: 40px;
}
</style>
