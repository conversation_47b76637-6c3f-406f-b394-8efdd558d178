import request from "@/utils/request";

// 创建院级培训
export function createHospitalTraining(data) {
  return request({
    url: "/rotation/hospital-training/create",
    method: "post",
    data: data,
  });
}

// 更新院级培训
export function updateHospitalTraining(data) {
  return request({
    url: "/rotation/hospital-training/update",
    method: "put",
    data: data,
  });
}

// 更新院级培训附件
export function updateHospitalTrainingFiles(data) {
  return request({
    url: "/rotation/hospital-training/update-files",
    method: "put",
    data: data,
  });
}

// 更新院级培训照片
export function updateHospitalTrainingPictures(data) {
  return request({
    url: "/rotation/hospital-training/update-pictures",
    method: "put",
    data: data,
  });
}

// 删除院级培训
export function deleteHospitalTraining(id) {
  return request({
    url: "/rotation/hospital-training/delete?id=" + id,
    method: "delete",
  });
}

// 获得院级培训
export function getHospitalTraining(id) {
  return request({
    url: "/rotation/hospital-training/get?id=" + id,
    method: "get",
  });
}

// 获得院级培训分页
export function getHospitalTrainingPage(query) {
  return request({
    url: "/rotation/hospital-training/page",
    method: "get",
    params: query,
    headers: { component: "rotation/hospitalTraining/index" },
  });
}

// 导出院级培训 Excel
export function exportHospitalTrainingExcel(query) {
  return request({
    url: "/rotation/hospital-training/export-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

export function getTrainingSelectedNumber(query) {
  return request({
    url: "/rotation/hospital-training/get-training-selected-number",
    method: "get",
    params: query,
  });
}

// 撤销
export function hospitalTrainingRevoke(data) {
  return request({
    url: `/rotation/hospital-training/revoke?id=${data.id}`,
    method: "put",
    data: data,
  });
}

// 发布
export function hospitalTrainingPublish(data) {
  return request({
    url: `/rotation/hospital-training/publish?id=${data.id}`,
    method: "put",
    data: data,
  });
}

export function getStudentPage(query) {
  return request({
    url: "/rotation/hospital-training-user/page-student",
    method: "get",
    params: query,
  });
}

export function getWorkerPage(query) {
  return request({
    url: "/rotation/hospital-training-user/page-work",
    method: "get",
    params: query,
  });
}

// 确认参加
export function confirmJoin(data) {
  return request({
    url: "/rotation/hospital-training-user/confirm-join",
    method: "put",
    data: data,
  });
}

// 撤销参加
export function revokeJoin(data) {
  return request({
    url: "/rotation/hospital-training-user/revoke-join",
    method: "put",
    data: data,
  });
}

// 删除活动用户
export function deleteTeachingUser(params) {
  return request({
    url: "/rotation/hospital-training-user/deleteUser",
    method: "delete",
    params: params,
  });
}

// 发布教学活动自评
export function updateSelfAssessment(data) {
  return request({
    url: "/rotation/hospital-training/update-self-assessment",
    method: "put",
    data,
  });
}

// 导出院级培训学员情况 Excel
export function exportUserStudentExcel(query) {
  return request({
    url: "/rotation/hospital-training-user/export-user-student-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 导出院级培训职工情况 Excel
export function exportUserWorkerExcel(query) {
  return request({
    url: "/rotation/hospital-training-user/export-user-work-excel",
    method: "get",
    params: query,
    responseType: "blob",
  });
}

// 获得待参加学员分页
export function getNotJoinedStudentsPage(query) {
  return request({
    url: "/rotation/hospital-training-user/page-user-students-not-joined",
    method: "get",
    params: query,
  });
}

// 获得待参加职工分页
export function getNotJoinedWorkersPage(query) {
  return request({
    url: "/rotation/hospital-training-user/page-user-workers-not-joined",
    method: "get",
    params: query,
  });
}

// 添加院级培训用户
export function addUsers(data) {
  return request({
    url: "/rotation/hospital-training-user/addUsers",
    method: "put",
    data,
  });
}

// 导入学员模版
export function exportStudentTemplate() {
  return request({
    url: "/rotation/hospital-training-user/get-import-user-student-template",
    method: "get",
    responseType: "blob",
  });
}

// 导入职工模版
export function exportWorkTemplate() {
  return request({
    url: "/rotation/hospital-training-user/get-import-user-work-template",
    method: "get",
    responseType: "blob",
  });
}

// 复制院级培训
export function copyHospitalTraining(id) {
  debugger;
  return request({
    url: `/rotation/hospital-training/copy?id=${id}`,
    method: "post",
  });
}
