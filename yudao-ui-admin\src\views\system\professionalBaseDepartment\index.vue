<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="专业基地" prop="professionalBaseName">
        <el-input v-model="queryParams.professionalBaseName" placeholder="请输入专业基地名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="专业基地" align="left" width="200" prop="professionalBaseName"></el-table-column>
      <el-table-column label="科室配置" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)">配置</el-button>
        </template>
      </el-table-column>
      <el-table-column label="管理科室" align="center" prop="departmentNames" />
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="720px" v-dialogDrag append-to-body>
      <el-checkbox-group v-model="departmentIds" size="small">
        <el-checkbox v-for="item in departmentOptions" :key="parseInt(item.id)" :label="item.id.toString()" border
                     style="margin-bottom: 10px; width: 140px; margin-left: 0">{{ item.name }}</el-checkbox>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDepartmentSimpleList } from "@/api/system/department";
import {
  getProfessionalBaseDepartment,
  getProfessionalBaseDepartmentPage,
  updateProfessionalBaseDepartment,
} from "@/api/system/professionalBaseDepartment";

export default {
  name: "ProfessionalBaseDepartment",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        professionalBaseName: null,
      },
      // 总条数
      total: 0,
      // 教研室科室关系,教研室配置列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      professionalBaseValue: "",
      departmentOptions: [],
      departmentIds: [],
    };
  },
  created() {
    this.getList();
    this.getDepartment();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getProfessionalBaseDepartmentPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getDepartment() {
      // 获得科室列表
      getDepartmentSimpleList(0).then(res => {
        // 处理 roleOptions 参数
        this.departmentOptions = [];
        this.departmentOptions.push(...res.data);
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        professionalBaseName: null,
      };
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.professionalBaseValue = row.professionalBaseValue;
      this.open = true;
      this.departmentIds = row.departmentIds ? row.departmentIds.split(',') : [];
      this.title = `${row.professionalBaseName}配置`;
    },
    /** 提交按钮 */
    submitForm() {
      updateProfessionalBaseDepartment({
        departmentIds: this.departmentIds,
        professionalBaseValue: this.professionalBaseValue
      }).then(response => {
        this.$modal.msgSuccess("配置成功");
        this.open = false;
        this.getList();
      });
    },
  }
};
</script>
